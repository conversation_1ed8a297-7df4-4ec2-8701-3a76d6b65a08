{"name": "listing-admin-product", "version": "0.0.1", "description": "listing admin portal with menu product", "main": "index.js", "author": "ISFE", "license": "ISC", "private": true, "scripts": {"preinstall": "npx only-allow pnpm", "lint": "eslint . --fix && listing-sc-lint lint --no-eslint", "dev": "NODE_ENV=development BUILD_ENV=local lac build --watch --forkTsChecker=false --stylelint=false --eslint=false --fsStrict=false", "build:local": "NODE_ENV=production BUILD_ENV=local HOST_ENV=https://admin.listing.test.shopee.com lac build", "build:test": "NODE_ENV=production BUILD_ENV=test HOST_ENV=https://admin.listing.test.shopee.com lac build", "build:staging": "NODE_ENV=production BUILD_ENV=staging HOST_ENV=https://admin.listing.staging.shopee.com lac build", "build:uat": "NODE_ENV=production BUILD_ENV=uat HOST_ENV=https://admin.listing.uat.shopee.com lac build", "build:live": "NODE_ENV=production BUILD_ENV=live HOST_ENV=https://admin.listing.shopee.com lac build", "api": "lac generate"}, "devDependencies": {"@seller/eslint-config-next": "^0.0.7", "@shopee/toast-pack": "^0.1.7", "@types/classnames": "2.2.10", "@types/enzyme": "^3.10.3", "@types/enzyme-adapter-react-16": "^1.0.5", "@types/express": "^4.17.13", "@types/history": "^4.7.5", "@types/jest": "^24.0.18", "@types/lodash": "^4.14.4", "@types/lodash-es": "^4.17.4", "@types/papaparse": "^5.2.5", "@types/puppeteer": "^1.19.1", "@types/reach__router": "^1.3.5", "@types/react": "18.2.6", "@types/react-router": "^5.1.4", "@types/react-router-dom": "^5.1.3", "@types/react-test-renderer": "^16.9.2", "@types/styled-components": "^5.1.3", "@types/uuid": "^7.0.2", "antd": "4.24.8", "classnames": "2.3.2", "eslint": "^8.57.0", "husky": "4.2.5", "lint-staged": "^11.1.2", "listing-sc-lint": "0.1.11", "moment": "2.29.4", "react": "18.2.0", "react-dom": "18.2.0", "react-router": "6.4.3", "react-router-dom": "6.4.3", "stylelint": "^13.2.1", "stylelint-config-standard": "^19.0.0", "stylelint-order": "^3.1.1", "stylelint-scss": "^3.11.1", "typescript": "4.7.4"}, "dependencies": {"@ant-design/codemod-v4": "^1.1.0", "@ant-design/compatible": "^1.0.2", "@ant-design/icons": "^4.0.5", "@classification/admin-solution": "0.0.2", "@classification/audit-log-table": "^1.0.3", "@classification/category-selector": "3.0.2", "@classification/export-csv": "^3.0.0", "@classification/file-upload": "^2.0.13", "@classification/mass-upload": "4.0.5", "@classification/table": "3.0.2", "@listing/ls-upload-cmpt-sdk-react": "0.0.7", "@shopee_common/currency": "2.8.1", "@shopee_common/currency3": "npm:@shopee_common/currency@3.0.1", "admin-upload-common": "1.0.31", "ahooks": "^3.7.7", "array-move": "^3.0.0", "axios": "^0.18.0", "core-decorators": "^0.20.0", "fast-diff": "1.2.0", "history": "^4.10.1", "immutability-helper": "^3.1.1", "listing-reporter": "1.0.6", "lodash": "4.17.15", "lodash-es": "^4.17.21", "moment-timezone": "^0.5.31", "papaparse": "^5.3.0", "react-dnd": "^11.1.3", "react-dnd-html5-backend": "^11.1.3", "react-query": "^3.21.1", "uuid": "^7.0.2"}, "lint-staged": {"*.{js,json,md}": ["git add"], "*.{ts,tsx}": ["eslint --fix", "git add"], "*.scss": ["stylelint --fix", "git add"]}, "husky": {"hooks": {"pre-commit": "echo 'Typescript checking...' && listing-sc-lint lint --no-eslint --fix && lint-staged"}}}