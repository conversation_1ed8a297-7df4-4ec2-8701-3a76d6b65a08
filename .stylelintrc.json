{"extends": "stylelint-config-standard", "plugins": ["stylelint-scss", "stylelint-order"], "rules": {"at-rule-no-unknown": [true, {"ignoreAtRules": ["extend", "at-root", "debug", "warn", "error", "if", "else", "for", "each", "while", "mixin", "include", "content", "return", "function"]}], "declaration-empty-line-before": "never", "declaration-block-no-redundant-longhand-properties": [true, {"ignoreShorthands": ["/border/", "/grid/"]}], "selector-pseudo-class-no-unknown": [true, {"ignorePseudoClasses": ["global"]}], "order/order": ["custom-properties", "declarations"], "order/properties-alphabetical-order": null, "declaration-block-no-duplicate-properties": null, "no-descending-specificity": null}}