const sellerESLintConfig = require('@seller/eslint-config-next').default;

module.exports = sellerESLintConfig({
  ignores: [
    '**/dist/*',
    '/*.js',
    '**/build/**',
    '**/mock/**',
    'src/_shared/**',
    '/static/index.ts',
  ],
  typescript: {
    overrides: {
      // overrides rules
      '@typescript-eslint/naming-convention': 'off',
      '@typescript-eslint/no-var-requires': 'off',
      '@typescript-eslint/no-magic-numbers': 'off',
      '@typescript-eslint/prefer-enum-initializers': 'off',
      '@typescript-eslint/no-explicit-any': 'off',
      '@typescript-eslint/ban-types': 'off',
      '@typescript-eslint/ban-ts-comment': 'off',
      'require-await': 'off',
      'no-use-before-define': 'off',
      'no-empty': 'off',
      eqeqeq: ['warn', 'smart'],

      '@typescript-eslint/no-duplicate-enum-values': 'off',
      'no-prototype-builtins': 'off',
      "@typescript-eslint/no-non-null-asserted-optional-chain": "off",
      "no-unsafe-optional-chaining": "off",
      "no-await-in-loop": "off",
      "no-useless-escape": "off",
      "prefer-destructuring": "off",
      "no-case-declarations": "off",
    },
  },
});
