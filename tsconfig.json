{"compilerOptions": {"downlevelIteration": true, "lib": ["dom", "es2019"], "strict": true, "strictFunctionTypes": false, "module": "esnext", "moduleResolution": "node", "experimentalDecorators": true, "removeComments": true, "target": "es2019", "jsx": "react", "importHelpers": true, "noEmitHelpers": true, "noUnusedLocals": true, "baseUrl": "./", "allowSyntheticDefaultImports": true, "skipLibCheck": true, "paths": {"src/*": ["src/*"], "_shared/*": ["admin-upload-common/es/*"]}}, "include": ["./src", "./typings/**/*.d.ts"], "exclude": ["./src/_archives"]}