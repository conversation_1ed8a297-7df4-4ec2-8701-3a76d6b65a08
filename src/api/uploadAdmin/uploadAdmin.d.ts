export namespace uploadAdmin {
  // proto syntax: proto2

  // proto package name: uploadAdmin

  export interface IConstant {
    /** [*********, *********) */
    /** Different attr format types */
  }

  export interface IGetLatestUpdatedItemsRequest {
    /** required. will check if the region is valid */
    region?: string;
    /** optional. default value is 50, max is 100 */
    limit?: number;
  }

  export interface IGetLatestUpdatedItemsResponse {
    /** error msg for debug */
    spDebugMsg?: string;
    /** item related info for catalog preview */
    itemPreviews?: IItemPreview[];
    /** total count of items, used for pagination */
    total?: number;
    /** record err msg for failed fields */
    errDetails?: string[];
    spErrorCode?: number;
  }

  export interface IItemPreview {
    itemId?: number;
    productName?: string;
    shopId?: number;
    /** first image of item image list, it would be empty if item has no images */
    image?: string;
    /** fetch from account.core api response */
    userId?: number;
    /** fetch from account.core api response */
    userName?: string;
    /** fetch from account.core api response */
    shopName?: string;
    /** the category name of the second category id (L2) */
    category?: string;
    /** read from item.brand */
    brand?: string;
    /** read from item.price. inflated value stored in DB, and need to trans into correct value */
    itemPrice?: number;
    /** read from item.sold */
    sold?: number;
    /** read from item.stock */
    stock?: number;
    /** deprecated, since remove old qc logic. ref [SPLT-2545](https://jira.shopee.io/browse/SPLT-2545) */
    oldQcStatus?: number;
    /** read from http apis http://qc-gateway-api-sg.deep.shopee.io/v1/listitemreviewstatus */
    ruleQcStatus?: number;
    /** read from listing_qc.gateway_management.list_mqc_item_review_status */
    modelQcStatus?: number;
    /** read from item.status, enum is here https://git.garena.com/beetalk-server/beeshop_common/-/blob/master/protocol/beeshop_db.proto */
    itemStatus?: number;
    /** 0:local, 1:CB non-SIP AShop, 2:CB SIP AShop. read from cb_option and ShopDetail.is_sip_primary/is_sip_affiliated */
    cbType?: number;
    /** 0:normal, 1:official, 2:preferred */
    officialType?: number;
    /** true means there's someone from Shopee to help manage this shop. read from `account.admin.get_managed_seller` */
    isManaged?: boolean;
    itemCreatedTime?: number;
    itemUpdatedTime?: number;
    /** read from item.extinfo, need to trans into correct value */
    weight?: number;
    /** read from item.currency */
    currency?: string;
    /** the complete global category name list */
    globalCategoryList?: string[];
    /** item global brand id */
    globalBrandId?: number;
    /** item global brand name */
    globalBrandName?: string;
    /** specific format for export function */
    formattedGlobalCategory?: string;
    /** specific format for export function */
    formattedGlobalBrand?: string;
    /** the second local category (L2) info */
    formattedLocalCategory?: string;
    cqcStatus?: number;
  }

  export interface ISearchItemsRequest {
    /** required. will check if the region is valid */
    region?: string;
    /** request need to send at least one of item_id, shop_id, shop_name, user_id and user_name */
    itemId?: number;
    shopId?: number;
    shopName?: string;
    userId?: number;
    userName?: string;
    /** deprecated, since remove old qc logic. ref [SPLT-2545](https://jira.shopee.io/browse/SPLT-2545) */
    oldQcStatusList?: number[];
    /** enum type, can ref `git.garena.com/shopee-server/shopee_protobuf/beeshop_db.pb` */
    itemStatusList?: number[];
    /** return the items whose create time is equal or greater than the min_create_time(second) */
    minCreateTime?: number;
    /** return the items whose create time is equal or less than the max_create_time(second) */
    maxCreateTime?: number;
    /** return the items whose update time is equal or greater than the min_update_time(second) */
    minUpdateTime?: number;
    /** return the items whose update time is equal or less than the max_update_time(second) */
    maxUpdateTime?: number;
    /** default value is 0 if empty */
    offset?: number;
    /** default value is 50 if empty */
    limit?: number;
    /** refer to enum ItemQueryType */
    queryTypeList?: number[];
  }

  export interface ISearchItemsResponse {
    /** error msg for debug */
    spDebugMsg?: string;
    /** item related info for catalog preview */
    itemPreviews?: IItemPreview[];
    /** total count of items after filtering, used for pagination */
    total?: number;
    /** record err msg for failed fields */
    errDetails?: string[];
    spErrorCode?: number;
  }

  export interface IGetItemDetailRequest {
    /** required */
    shopId?: number;
    /** required */
    itemId?: number;
    /** required. will check if the region is valid */
    region?: string;
  }

  export interface IGetItemDetailResponse {
    itemId?: number;
    productName?: string;
    itemStatus?: number;
    currency?: string;
    brand?: string;
    description?: string;
    itemLikedCount?: number;
    createTime?: number;
    updateTime?: number;
    sold?: number;
    stock?: number;
    condition?: number;
    parentSku?: string;
    daysToShip?: number;
    weight?: number;
    dimensions?: IItemDimensions;
    attrQcStatus?: number;
    /** deprecated, since remove old qc logic. ref [SPLT-2545](https://jira.shopee.io/browse/SPLT-2545) */
    qcMode?: number;
    productImages?: IImageList;
    variationImages?: IImageList;
    whiteBackgroundImage?: string;
    sizeChart?: string;
    videoInfoList?: IVideoInfo[];
    hasTierVariation?: boolean;
    tierVariationList?: ITierVariation[];
    canUseWholesale?: boolean;
    wholesaleTierList?: IWholesaleTier[];
    showItemGtin?: boolean;
    gtin?: string;
    isChildVsku?: boolean;
    parentVskuList?: ISKUMapping[];
    flagList?: number[];
    cbOption?: number;
    isCounterfeit?: boolean;
    seoDescription?: boolean;
    nonSearchable?: boolean;
    isHidden?: boolean;
    restock?: boolean;
    isUnlisted?: boolean;
    visibleInFe?: boolean;
    useNewStockStructure?: boolean;
    showItemPricePanel?: boolean;
    isItemOriTaxed?: boolean;
    originalPrice?: number;
    originalPriceBeforeTax?: number;
    isItemSipSellingTaxed?: boolean;
    sipSellingPrice?: number;
    sipSellingPriceBeforeTax?: number;
    isItemPromoTaxed?: boolean;
    sellerPromotionId?: number;
    promotionPrice?: number;
    promotionPriceBeforeTax?: number;
    categoryList?: ICategory[];
    categoryHsCode?: string;
    /** deprecated, since remove old qc logic. ref [SPLT-2545](https://jira.shopee.io/browse/SPLT-2545) */
    oldQcStatus?: number;
    /** deprecated, since remove old qc logic. ref [SPLT-2545](https://jira.shopee.io/browse/SPLT-2545) */
    isOldQcEditable?: boolean;
    ruleQcStatus?: number;
    modelQcStatus?: number;
    shopId?: number;
    shopName?: string;
    shopStatus?: number;
    userId?: number;
    userName?: string;
    userStatus?: number;
    isPreferred?: boolean;
    isOfficial?: boolean;
    isCb?: boolean;
    oldShippingList?: IOldShippingInfo[];
    newShippingList?: INewShippingInfo[];
    productPageView?: string;
    itemInstallmentTenure?: IItemInstallmentTenure;
    spDebugMsg?: string;
    promotionType?: number;
    isPreOrder?: boolean;
    minPurchaseLimit?: number;
    dangerousGoods?: number;
    globalCategoryList?: ICategory[];
    /** global brand name */
    globalBrandName?: string;
    globalBrandId?: number;
    /** record err msg for the failed fields */
    errDetails?: string[];
    /** for OPL1, order level */
    orderMaxPurchaseLimit?: number;
    /** for OPL2, customized level. the current ongoing OPL round or the next upcoming OPL if there is no ongoing OPL */
    customizedPurchaseLimit?: ICustomizedOPL;
    /** local display name of global brand */
    globalBrandLocalDisplayName?: string;
    itemHsCode?: string;
    itemTaxCode?: string;
    /** refer to item.tax enum EuInvoiceOption. */
    euInvoiceOption?: number;
    euVatRate?: number;
    complaintPolicy?: IComplaintPolicy;
    descriptionType?: string;
    brGetItemInvoiceOptions?: IBrGetItemInvoiceOptions;
    spErrorCode?: number;
    cqcStatus?: number;
    feStockCeiling?: number;
    taxType?: number;
    autoCorrectCategoryInfo?: IAutoCorrectCategoryInfo;
    shopWarehouseFlag?: number;
    nonPreOrderLimit?: number;
    /** unix timestamp */
    scheduledPublishTime?: number;
    skuScenario?: number;
    isFbsShop?: boolean;
    dangerousCategorizationByShopee?: number;
    shopeeDangerousGoods?: number;
    certificationInfo?: ICertificationInfo;
    pqrInfo?: IPQRInfo;
  }

  export interface ICertificationInfo {
    certificationList?: ICertification[];
  }

  export interface ICertification {
    permitId?: number;
    certificationNo?: string;
    certificationProofs?: ICertificationProof[];
    expiryDate?: number;
  }

  export interface ICertificationProof {
    imageId?: string;
    ratio?: number;
    fileName?: string;
  }

  export interface IAutoCorrectCategoryInfo {
    appealStatus?: number;
    verifiedCatNodes?: IVerifiedCatNode[];
  }

  export interface IVerifiedCatNode {
    catNodeId?: number;
    catNodeName?: string;
  }

  export interface IBrGetItemInvoiceOptions {
    ncm?: string;
    /** can be empty */
    cest?: string;
    measureUnit?: string;
    sameStateCfop?: string;
    diffStateCfop?: string;
    csosn?: string;
    origin?: string;
  }

  export interface IComplaintPolicy {
    /** refer to enum TimeForWarrantyClaim */
    timeForWarranty?: number;
    excludeEntrepreneursWarranty?: boolean;
    /** iis would not do the address id validation */
    addressId?: number;
    /** up to 3K */
    additionalInfo?: string;
  }

  export interface ICustomizedOPL {
    /** latest OPL2 start time, on second level */
    startTime?: number;
    /** latest OPL2 end time, on second level */
    endTime?: number;
    /** include current ongoing round and upcoming rounds */
    remainingRepeatedTimes?: number;
    purchaseLimit?: number;
    /** last OPL2 end time, on second level */
    recurrenceEndTime?: number;
  }

  export interface IItemInstallmentTenure {
    shopLevelEnabled?: boolean;
    itemLevelEnabled?: boolean;
    tenures?: number[];
    errMsg?: string;
    availableTenure?: number[];
  }

  export interface ICategory {
    catId?: number;
    catName?: string;
    subCategories?: ICategory[];
    /** ref: marketplace.listing.upload.upload_admin.CategoryStatus */
    status?: number;
    /** for global category's display name in default language version */
    defaultLangName?: IValWithLang;
  }

  export interface IImageList {
    images?: string[];
    options?: string[];
    /** high priority than images */
    imageDetailSettings?: IImageDetailSetting[];
    productLongImages?: string[];
  }

  export interface IImageDetailSetting {
    image?: string;
    height?: number;
    width?: number;
  }

  export interface IVideoInfo {
    videoId?: string;
    url?: string;
    thumbUrl?: string;
    duration?: number;
    version?: number;
  }

  export interface ITierVariation {
    name?: string;
    options?: string[];
    images?: string[];
    /** mapping with same index of options, in the future options and images will be moved here */
    properties?: ITierProperty[];
    /** lipstick:1 ref: TierVariationType */
    type?: number;
  }

  export interface ITierProperty {
    /** 1,2 reserved for option name and image */
    /** e.g. ffeebb */
    color?: string;
  }

  export interface IItemDimensions {
    width?: number;
    length?: number;
    height?: number;
    unit?: number;
  }

  export interface IWholesaleTier {
    minCount?: number;
    maxCount?: number;
    price?: number;
    inputPrice?: number;
  }

  export interface IOldShippingInfo {
    channelName?: string;
    property?: string;
    value?: string;
  }

  export interface INewShippingInfo {
    channelId?: number;
    channelName?: string;
    shopLogisticsInfo?: IShopLogisticsInfo;
    itemLogisticsInfo?: IItemLogisticsInfo;
  }

  export interface IShopLogisticsInfo {
    enabled?: boolean;
    codEnabled?: boolean;
    codToggleVisible?: boolean;
    codWhitelistEnabled?: boolean;
  }

  export interface IItemLogisticsInfo {
    enabled?: boolean;
    coverShippingFee?: boolean;
    defaultPrice?: number;
    sizeid?: number;
    flag?: number;
  }

  export interface ISKUMapping {
    parentItemId?: number;
    parentModelId?: number;
    parentShopId?: number;
    parentRegion?: string;
    quantity?: number;
    costPrice?: number;
    /** whether this parent sku is main sku or not */
    mainSku?: boolean;
  }

  export interface IItemModel {
    modelId?: number;
    modelName?: string;
    /** promotion_type == 0, after tax */
    originalPrice?: number;
    /** promotion_type == 0, before tax */
    originalPriceBeforeTax?: number;
    /** current ongoing promotion price after tax. current_price_stock */
    promotionPrice?: number;
    /** current ongoing promotion price before tax. current_price_stock */
    promotionPriceBeforeTax?: number;
    /** added after promotion price according to PRD */
    promotionType?: number;
    /** SIP selling price after tax */
    sellingPrice?: number;
    /** SIP selling price before tax */
    sellingPriceBeforeTax?: number;
    stock?: number;
    currentPromotionId?: number;
    status?: number;
    sku?: string;
    gtin?: string;
    isDefault?: boolean;
    link?: string;
    feStockCeiling?: number;
    /** SIP item price */
    itemPrice?: number;
    itemPriceCurrency?: string;
    weight?: number;
    dimension?: IModelDimension;
    modelDtsSetting?: IModelDtsSetting;
    tierIndex?: number[];
  }

  export interface IModelDtsSetting {
    dayToShip?: number;
    isPreOrder?: boolean;
  }

  export interface IPaginateIndiaHsCodeListRequest {
    offset?: number;
    /** max limit is 50 */
    limit?: number;
    /** optional, if set non-empty string, then search this hscode settings and return pagination result */
    hsCode?: string;
  }

  export interface IPaginateIndiaHsCodeListResponse {
    infos?: IIndiaHsCodeInfo[];
    spDebugMsg?: string;
    hasNext?: boolean;
    spErrorCode?: number;
  }

  export interface IIndiaHsCodeInfo {
    taxRate?: number;
    unitStartPrice?: number;
    unitEndPrice?: number;
    hsCode?: string;
    createTime?: number;
    updateTime?: number;
    operator?: string;
  }

  export interface IGetItemModelListRequest {
    /** required */
    shopId?: number;
    /** required */
    itemId?: number;
    /** required */
    region?: string;
    /** repeated, if it is empty then we return all status, otherwise only return selected model status */
    status?: number[];
    needLink?: boolean;
  }

  export interface IGetItemModelListResponse {
    spDebugMsg?: string;
    modelList?: IItemModel[];
    spErrorCode?: number;
  }

  export interface IStockBreakDownList {
    modelId?: number;
    locationId?: string;
    sellableStock?: number;
    reservedStock?: number;
    lockedStock?: number;
  }

  export interface IGetItemModelStockListRequest {
    /** required */
    itemId?: number;
    /** limit's default value is 20 if empty */
    page?: IPaginateReqInfo;
  }

  export interface IGetItemModelStockListResponse {
    spDebugMsg?: string;
    stockList?: IStockBreakDownList[];
    /** has_next = false means reach the end, offset_list need to pass it for the next or previous page */
    page?: IPaginateRespInfo;
    floatingStockLists?: IFloatingStockList[];
    spErrorCode?: number;
  }

  export interface IFloatingStockList {
    modelId?: number;
    totalFloatingStock?: number;
    floatingStockBreakdowns?: IFloatingStockBreakdown[];
  }

  export interface IFloatingStockBreakdown {
    ruleId?: number;
    ruleType?: number;
    floatingStock?: number;
  }

  export interface IGetProductAttributesRequest {
    /** required */
    shopId?: number;
    /** required */
    itemId?: number;
    /** optional. will set leafCatId in itemExtInfo as default value */
    catId?: number;
    /** required. will check if the region is valid */
    region?: string;
  }

  export interface IGetProductAttributesResponse {
    spDebugMsg?: string;
    attrModelSnapshot?: IAttrModelSnapshotInfo;
    attrSnapshotList?: IAttrSnapshotInfo[];
    productAttrModel?: IProductAttrModel;
    errDetails?: string[];
    spErrorCode?: number;
  }

  export interface IAttrModelSnapshotInfo {
    modelId?: number;
    modelName?: string;
  }

  export interface IAttrSnapshotInfo {
    attribute?: IAttributeInfo;
    value?: string;
    status?: number;
  }

  export interface IAttributeInfo {
    attrId?: number;
    attrName?: string;
    attrType?: number;
  }

  export interface IProductAttrModel {
    modelId?: number;
    modelName?: string;
    attributes?: IModelAttributes;
  }

  export interface IModelAttributes {
    dependentAttrs?: IProductAttr[];
    independentAttrs?: IProductAttr[];
  }

  export interface IProductAttr {
    attrId?: number;
    attrName?: string;
    inputType?: number;
    attrType?: number;
    validateType?: number;
    values?: string[];
  }

  export interface IPaginateReqInfo {
    offset?: number;
    limit?: number;
    offsetList?: string[];
  }

  export interface IPaginateRespInfo {
    offsetList?: string[];
    hasNext?: boolean;
  }

  export interface IGetOldQcLogsRequest {
    /** required */
    itemId?: number;
    /** optional. default value is 50 */
    limit?: number;
    /** optional. default value is 0 */
    offset?: number;
    /** required. since dbName is different according to the region */
    region?: string;
  }

  export interface IGetOldQcLogsResponse {
    spDebugMsg?: string;
    logs?: IOldQCLog[];
    total?: number;
    spErrorCode?: number;
  }

  export interface IOldQCLog {
    auditId?: number;
    createTime?: number;
    updateTime?: number;
    reviewer?: string;
    reason?: string;
    note?: string;
    failType?: number;
    classificationReason?: string;
    status?: number;
    classification?: number;
  }

  export interface IGetRuleQcLogsRequest {
    /** required */
    itemId?: number;
    /** default value is 50 */
    limit?: number;
    /** default value is 0 */
    offset?: number;
    /** required. since dbName is different according to the region */
    region?: string;
  }

  export interface IGetRuleQcLogsResponse {
    spDebugMsg?: string;
    logs?: IRuleQCLog[];
    hasMore?: boolean;
    spErrorCode?: number;
  }

  export interface IQcAction {
    action?: number;
    failedReason?: string;
    hint?: string;
  }

  export interface IQcAddOnAction {
    deboosts?: number[];
    failedReason?: string;
    hint?: string;
  }

  export interface IRuleQCLog {
    logId?: string;
    createTime?: string;
    updateTime?: string;
    reviewer?: string;
    /** -->in QcAction */
    action?: number;
    /** -->in QcAction */
    failReason?: string;
    qcAction?: IQcAction;
    qcAddOnAction?: IQcAddOnAction;
    qcQueue?: number;
    qcLevel?: number;
    remark?: string;
    deboostRemark?: string;
  }

  export interface IGetModelQcLogsRequest {
    /** required */
    itemId?: number;
    /** default value is 50 */
    limit?: number;
    /** default value is 0 */
    offset?: number;
  }

  export interface IGetModelQcLogsResponse {
    spDebugMsg?: string;
    logs?: IModelQCLog[];
    hasMore?: boolean;
    spErrorCode?: number;
  }

  export interface IModelQCLog {
    logId?: number;
    createTime?: string;
    updateTime?: string;
    reviewer?: string;
    action?: number;
    failReason?: string;
    modelName?: string;
    qcLevel?: number;
    remark?: string;
  }

  export interface IGetCqcLogsRequest {
    /** required */
    itemId?: number;
    /** default value is 50 */
    limit?: number;
    /** default value is 0 */
    offset?: number;
    /** default value is 0 */
    region?: string;
  }

  export interface IGetCqcLogsResponse {
    spDebugMsg?: string;
    logs?: ICQCItemLog[];
    hasMore?: boolean;
    spErrorCode?: number;
  }

  export interface ICQCItemLog {
    reviewEventId?: number;
    finalAction?: number;
    /** will filter onhold; every queueResult will only have one History. */
    queueResult?: IFailQueueResult[];
  }

  export interface ICQCAction {
    action?: number;
    failedReason?: string;
    hint?: string;
  }

  export interface ICQCAddOnAction {
    deboosts?: number[];
    failedReason?: string;
    hint?: string;
  }

  export interface IFailQueueResult {
    reviewLogId?: string;
    createTime?: string;
    updateTime?: string;
    reviewer?: string;
    cqcAction?: ICQCAction;
    cqcAddOnAction?: ICQCAddOnAction;
    qcQueue?: number;
    qcLevel?: number;
    remark?: string;
    deboostRemark?: string;
  }

  export interface IGetItemInfoAuditRequest {
    /** required */
    itemId?: number;
    /** default value for `limit` is 20 */
    page?: IPaginateReqInfo;
    /** optional */
    filter?: IItemAuditFilter;
  }

  export interface IItemAuditFilter {
    /** optional, only valid when item_id != nil */
    source?: string;
    /** optional, only valid when item_id != nil */
    valueType?: string;
    /** optional, only valid when item_id != nil */
    modelId?: number;
    /** optional */
    auditTypes?: number[];
    /** optional */
    status?: number[];
  }

  export interface IGetItemInfoAuditResponse {
    spDebugMsg?: string;
    itemAudits?: IItemAudit[];
    page?: IPaginateRespInfo;
    spErrorCode?: number;
  }

  export interface IItemAudit {
    auditId?: number;
    shopId?: number;
    itemId?: number;
    modelId?: number;
    ctime?: number;
    data?: IAuditDelta[];
    auditType?: number;
    operator?: string;
    reason?: string;
    source?: string;
    status?: number;
    clientId?: string;
    region?: string;
  }

  export interface IAuditDelta {
    name?: string;
    old?: string;
    new?: string;
  }

  export interface IFilterCriteria {
    source?: string;
    ruleType?: number;
    ruleId?: number;
    modelId?: number;
    isOrder?: boolean;
  }

  export interface IPriceAudit {
    shopId?: number;
    itemId?: number;
    modelId?: number;
    ruleType?: number;
    ruleId?: number;
    auditDeltas?: IAuditDelta[];
    refEntityId?: string;
    caller?: string;
    operator?: string;
    reason?: string;
    eventTimeInMs?: number;
    createTime?: number;
    source?: string;
  }

  export interface IGetStockAuditsRequest {
    /** required */
    itemId?: number;
    /** optional */
    filter?: IFilterCriteria;
    /** required */
    startTime?: number;
    /** optional corresponds to offset/limit/offset_list */
    page?: IPaginateReqInfo;
  }

  export interface IGetStockAuditsResponse {
    spDebugMsg?: string;
    audits?: IStockAudit[];
    /** indicates if there is any more data for the given range/filter */
    page?: IPaginateRespInfo;
    startTime?: number;
    spErrorCode?: number;
  }

  export interface IStockAudit {
    /** ref audit */
    shopId?: number;
    itemId?: number;
    modelId?: number;
    ruleType?: number;
    ruleId?: number;
    auditDeltas?: IAuditDelta[];
    refEntityId?: string;
    caller?: string;
    operator?: string;
    reason?: string;
    eventTimeInMs?: number;
    createTime?: number;
    source?: string;
    /** set true if stock changed by place order or cancelled order. if empty will return all */
    isOrder?: boolean;
  }

  export interface IUpdateItemBasicInfoRequest {
    /** required */
    shopId?: number;
    /** required */
    itemId?: number;
    /** required */
    region?: string;
    auditParam?: IAuditParam;
    daysToShip?: number;
    /** inflated value stored in db */
    weight?: number;
    dimensions?: IItemDimensions;
    /** update local cat info if not empty, and also need to set global category info even no any changes */
    catList?: ICatList;
    /** will update still if modelId is validate and attribute is empty or [] */
    attrSnapshotSetting?: IAttributeSnapshotSetting;
    productName?: string;
    description?: string;
    condition?: number;
    brand?: string;
    productImages?: IImageList;
    deleteSizeChart?: boolean;
    inputPrice?: number;
    stock?: number;
    gtin?: string;
    variationList?: ITierVariationList;
    modelSettingList?: IModelSettingList;
    /** set "" if wants to delete the image */
    whiteBackgroundImage?: string;
    /** ItemFlags_IS_FAKE_ITEM */
    isCounterfeit?: boolean;
    /** ItemFlags_SEO_DESCRIPTION */
    seoDescription?: boolean;
    /** ItemFlags_NO_SEARCHABLE */
    nonSearchable?: boolean;
    /** ItemFlags_IS_HIDDEN */
    isHidden?: boolean;
    isUnlisted?: boolean;
    isNonrestock?: boolean;
    /** set True if wants to check whether the updated category list is in preOrder when item is preOrdered */
    checkOnlyForCats?: boolean;
    isPreOrder?: boolean;
    minPurchaseLimit?: number;
    /** now iis can only support 0(means not) and 1(means yes) for dangerous goods */
    dangerousGoods?: number;
    /** update global category info if not empty, and also need to set local category info even no any changes */
    globalCatList?: ICatList;
    /** set brand id = 0 if wants to remove (for "no brand" case) */
    globalBrandId?: number;
    /** for global attributes */
    globalAttrSnapshotSetting?: IGlobalAttributeSnapshotSetting;
    descriptionType?: string;
    taxType?: number;
    bffMeta?: IRequestMeta;
    installmentSetting?: IInstallmentSetting;
    dangerousCategorizationByShopee?: number;
  }

  export interface IInstallmentSetting {
    enableInstallment?: boolean;
    enabledTenures?: number[];
  }

  export interface IGlobalAttributeSnapshotSetting {
    attrValueSettingList?: IGlobalAttributeValueSetting[];
  }

  export interface IGlobalAttributeValueSetting {
    /** required. cannot be 0 */
    attrId?: number;
    /** set as 0 if it is a customized value */
    attrValueId?: number;
    /** attribute value extend info. set this if it is a customized value (with unit or non-unit) */
    attrValExtInfo?: IItemAttrValueExtInfo;
  }

  export interface IItemAttrValueExtInfo {
    /** only used for customized value with unit. e.g. if value is "1kg" then raw_val = "1" */
    rawVal?: string;
    /** only used for customized value with unit. e.g. if value is "1kg" then unit = "kg" */
    unit?: string;
    /** only used for customized value without unit. And set timestamp on second level for AttrInputValidatorType.VALIDATOR_DATE */
    customisedValue?: string;
    /** record which client sets this item attr value */
    source?: string;
    /** we keep this as int32 as we may need to use negative numbers in the future. */
    /** e.g. use negative number to represent special confidence score, such as -1 to indicate highest priority */
    /** confidence score, inflated by 10^5 */
    confidenceScore?: number;
  }

  export interface IAuditParam {
    /** optional, default is "" */
    reason?: string;
    /** optional only if call api thorough http gateway, and will add or overwrite by http gateway */
    operator?: string;
    /** optional, will overwrite by cmdName */
    source?: string;
  }

  export interface IGetCategoryListRequest {
    /** required */
    region?: string;
    /** optional. set nil if no need to filter by status */
    status?: number[];
  }

  export interface IGetCategoryListResponse {
    spDebugMsg?: string;
    cats?: ICategory[];
    spErrorCode?: number;
  }

  export interface IPurgeMallCacheRequest {
    /** required. will check if the region is valid */
    region?: string;
    /** required */
    shopId?: number;
    /** required */
    itemId?: number;
  }

  export interface IPurgeMallCacheResponse {
    spDebugMsg?: string;
    spErrorCode?: number;
  }

  export interface IPurgeCsCacheRequest {
    /** required */
    shopId?: number;
    /** required */
    itemId?: number;
  }

  export interface IPurgeCsCacheResponse {
    spDebugMsg?: string;
    spErrorCode?: number;
  }

  export interface IGetItemInfoForCodingMonkeyRequest {
    /** required */
    itemId?: number;
    /** required */
    shopId?: number;
    /** required */
    region?: string;
  }

  export interface IGetItemInfoForCodingMonkeyResponse {
    spDebugMsg?: string;
    /** json format */
    data?: string;
    spErrorCode?: number;
  }

  export interface IUpdateItemBasicInfoResponse {
    spDebugMsg?: string;
    errDetails?: IErrDetail[];
    spErrorCode?: number;
  }

  export interface IErrDetail {
    field?: string;
    value?: string;
  }

  export interface IAttributeSnapshotSetting {
    attrValues?: IAttributeValueSetting[];
    attrModelId?: number;
  }

  export interface IAttributeValueSetting {
    attrId?: number;
    value?: string;
  }

  export interface ICatList {
    /** Sequence is: Parent category -> sub category -> sub-sub category ... */
    catIds?: number[];
    /** the region of cat, use WW for global region */
    region?: string;
    /** input source, 0:seller input, 1:ds model recommendation */
    source?: number;
    /** not nil if source  == 1 */
    dsInformation?: ICategoryDSInformation;
  }

  export interface ICategoryDSInformation {
    /** root category confidence score */
    rootConfidenceScore?: number;
    /** leaf category confidence score */
    leafConfidenceScore?: number;
  }

  export interface ITierVariationList {
    variations?: ITierVariation[];
  }

  export interface IModelSettingList {
    modelSettings?: IModelSetting[];
  }

  export interface IModelSetting {
    itemId?: number;
    modelId?: number;
    name?: string;
    indexList?: ITierIndexList;
    normalInputPrice?: number;
    normalStock?: number;
    gtin?: string;
    stockSettingList?: ILocationDetail[];
    weight?: number;
    dimension?: IModelDimension;
    modelDtsSetting?: IModelDtsSetting;
  }

  export interface ILocationDetail {
    locationId?: string;
    sellableStock?: number;
  }

  export interface IModelDimension {
    width?: number;
    length?: number;
    height?: number;
    unit?: number;
  }

  export interface ITierIndexList {
    tierIndex?: number[];
  }

  export interface IGetQcTagsRequest {
    /** required */
    itemId?: number;
    /** required */
    shopId?: number;
    /** required */
    region?: string;
  }

  export interface IGetQcTagsResponse {
    spDebugMsg?: string;
    /** refer to QCTagType */
    qcTagType?: number[];
    spErrorCode?: number;
    reasons?: string[];
  }

  export interface IResetItemNormalRequest {
    /** required */
    shopId?: number;
    /** required */
    itemId?: number;
    auditParam?: IAuditParam;
    bffMeta?: IRequestMeta;
  }

  export interface IResetItemNormalResponse {
    spDebugMsg?: string;
    spErrorCode?: number;
  }

  export interface IGetGlobalTreeToggleRequest {
    /** Mandatory */
    toggleName?: string;
    /** country */
    region?: string;
  }

  export interface IGetGlobalTreeToggleResponse {
    /** 0 if the toggle is OFF (Local) , 1 if ON (Global) */
    toggleState?: number;
    /** error message if any */
    spDebugMsg?: string;
    spErrorCode?: number;
  }

  export interface IGetGlobalCategoryListRequest {
    /** required */
    region?: string;
    /** optional, set nil if no need to filter. ref enum item.category.CategoryStatus */
    statusList?: number[];
    /** optional. set true if no need cb/local block categories for the seller type, and also need to set `is_cb_shop` field */
    filterBlock?: boolean;
    /** optional. set true if shop is cb type. If the value is nil, won't check cb/local block for the category status */
    isCbShop?: boolean;
  }

  export interface IGetGlobalCategoryListResponse {
    spDebugMsg?: string;
    cats?: ICategory[];
    spErrorCode?: number;
  }

  export interface IGetGlobalBrandListRequest {
    /** required. leaf category id */
    catId?: number;
    /** optional, set nil if no need to filter. ref item.global_brand.GlobalBrandStatus */
    statusList?: number[];
    /** required. filter by the block status for the region */
    region?: string;
  }

  export interface IGetGlobalBrandListResponse {
    spDebugMsg?: string;
    brandList?: IGlobalBrand[];
    spErrorCode?: number;
  }

  export interface IGlobalBrand {
    brandId?: number;
    /** for global brand name */
    brandName?: string;
    /** "WW" for global-global brands */
    region?: string;
    /** for local display name */
    brandLocalDisplayName?: string;
  }

  export interface IGetGlobalProductAttributesRequest {
    /** one of [`item_id`, `cat_id`] must be required */
    itemId?: number;
    /** leaf cat id. one of [`item_id`, `cat_id`] must be required */
    catId?: number;
    /** required. used for local display name, and won't check if this item belongs to this region */
    region?: string;
  }

  export interface IGetGlobalProductAttributesResponse {
    spDebugMsg?: string;
    /** current attributes info and attributes value info for the item */
    attrSnapshotList?: IGlobalAttrSnapshotInfo[];
    /** possible attributes info and attributes value info for the category */
    productAttrList?: IGlobalProductAttrList;
    /** err msg for partial fail when try to get attributes info by item id and cat id */
    errDetails?: string[];
    spErrorCode?: number;
  }

  export interface IGlobalAttrSnapshotInfo {
    attrId?: number;
    attrName?: string;
    attrStatus?: number;
    /** for AttrInputValidatorType.VALIDATOR_DATE, will store formatted value instead of timestamp */
    attrValue?: string;
    /** for current global attribute's display name in default language */
    attrDefaultLangName?: IValWithLang;
    /** for current global attribute's display value in default language */
    attrDefaultLangValue?: IValWithLang;
  }

  export interface IValWithLang {
    /** language code, refer to const item_core.lang */
    lang?: string;
    /** local value */
    val?: string;
  }

  export interface IGlobalProductAttrList {
    /** will include child attributes also */
    globalAttributes?: IGlobalProductAttr[];
    /** will store parent-child mapping relationship, won't include brand attributes */
    parentChildMappingAttrs?: IGlobalProductAttrParentChild[];
  }

  export interface IGlobalProductAttr {
    attrId?: number;
    attrName?: string;
    /** ref item.attribute.AttrInputType */
    inputType?: number;
    /** ref item.attribute.AttrInputValidatorType */
    validateType?: number;
    /** ref item.attribute.AttrFormatType */
    attrFormatType?: number;
    attrExtInfo?: IGlobalAttrExtInfo;
    attrValueList?: IAttributeValue[];
    isChild?: boolean;
    /** for global attribute's local display name in default language */
    attrDefaultLangName?: IValWithLang;
    maxAttrValue?: number;
    isIsbn?: boolean;
  }

  export interface IGlobalAttrExtInfo {
    /** for AttrFormatType.FORMAT_QUANTITATIVE_WITH_UNIT */
    unitList?: IGlobalAttrUnitList;
    /** for AttrInputValidatorType.VALIDATOR_DATE. ref item.attribute.AttrDateTimeFormat */
    datetimeFormat?: number;
  }

  export interface IGlobalAttrUnitList {
    /** for AttrFormatType.FORMAT_QUANTITATIVE_WITH_UNIT */
    units?: string[];
  }

  export interface IAttributeValue {
    id?: number;
    /** for AttrInputValidatorType.VALIDATOR_DATE, will store as timestamp on second level */
    value?: string;
    /** for global attribute's local display value in default language */
    attrDefaultLangValue?: IValWithLang;
  }

  export interface IGlobalProductAttrParentChild {
    parentAttrId?: number;
    parentValueId?: number;
    childAttrId?: number;
    childValueId?: number;
  }

  export interface IAddVirtualSkuForAdminRequest {
    /** all fields are required */
    itemInfo?: IVirtualSKUItemSetting;
    model?: IVirtualSKUModelSetting;
    /** childItemId, childModelId must be nil or 0,  childShopId should be virtual sku shopId. Region Filed no need to fill, IIS will fill it by child shopid or parent itemid */
    parentSkuSetting?: ISKUMappingList;
    /** only can fill email address, have to fill one of audit_param or operator. Http gateway will fill operator */
    operator?: string;
    /** true: unlist new item ; false: publish new item */
    unlisted?: boolean;
    /** If this toggle is on, listing side will only pass changed/related fields to LUC. */
    skipCompleteLuc?: boolean;
    /** use_global may be different for each region, so needs FE provides the region */
    region?: string;
  }

  export interface IAddVirtualSkuForAdminResponse {
    itemId?: number;
    modelId?: number;
    /** detailed item info */
    item?: IItem;
    /** completed models info. Since virtual sku item follows main sku item. If we provide multiple virtual models, this rule would be broken. Thus we need to migrate cat,attr and some fields from item to model first, before we provide the multiple virtual sku models */
    model?: IItemModelForVSkuAdmin;
    /** record error info details, this field only return when errcode is not SUCCESS */
    spDebugMsg?: string;
    spErrorCode?: number;
  }

  export interface IVskuTierVariationList {
    tierVariations?: IVskuTierVariation[];
  }

  export interface IVskuTierVariation {
    name?: string;
    options?: string[];
  }

  export interface IVirtualSKUItemSetting {
    /** required, item title */
    name?: string;
    /** required, item description */
    description?: string;
    /** required, item images */
    imageList?: IImageList;
    /** required, item logistics */
    logistics?: IVirtualItemLogistics;
    /** toggle to turn on/off virtual sku */
    useGlobal?: boolean;
    /** optional, global cat setting of virtual sku, by default copy from main mtsku */
    globalCatSetting?: ICategoryPathSetting;
    /** optional, global attribute setting of virtual sku, by default copy from main mtsku */
    globalAttributeSetting?: IGlobalAttributeSnapshotSetting;
    /** optional, global brand setting of virtual sku, by default copy from main mtsku */
    globalBrandSetting?: IGlobalBrandSetting;
    /** if "multiple_virtual_sku_model_setting" is not nil, this field is required. */
    tierVariationList?: IVskuTierVariationList;
    /** if ture will auto sync dts and preorder from parent */
    autoSyncVskuDtsAndPreorder?: boolean;
    /** if ture will auto sync weight from parent */
    autoSyncVskuWeight?: boolean;
    sku?: string;
  }

  export interface ICategoryPathSetting {
    /** category information */
    cats?: ICatList[];
  }

  export interface IGlobalBrandSetting {
    /** brand id */
    brandId?: number;
  }

  export interface IVirtualItemLogistics {
    estimatedDays?: number;
    /** json logistics. The format is like channel_id -> channel info, used for request */
    logisticsInfo?: string;
    isPreOrder?: boolean;
    dimension?: IItemDimensions;
    weight?: number;
    displayShippingFee?: string;
    /** display_weight only used to be displayed on add/edit item page */
    displayWeight?: string;
    /** display_dimension only used to be displayed on add/edit item page */
    displayDimension?: string;
    collectAddress?: string;
    /** used for response */
    displayLogisticsInfo?: IDisplayLogistics[];
  }

  export interface IModelLogistics {
    estimatedDays?: number;
    isPreOrder?: boolean;
  }

  export interface IVirtualSKUModelSetting {
    /** model title */
    name?: string;
    /** model normal price */
    price?: number;
    modelStock?: IModelStockSetting;
    /** if multiple_virtual_sku_model_setting is not null, and use `multiple_virtual_sku_model_setting` to add, this field is require */
    tierIndexList?: ITierIndexList;
    /** Pre-order,DTS move from item level to model level. if use new structure, this field is require. */
    modelLogistics?: IModelLogistics;
    sku?: string;
  }

  export interface IModelStockSetting {
    /** 0 if item has no model */
    modelId?: number;
    /** This is used for interface which don't need the locations, otherwise use stock_setting_list */
    stock?: number;
    /** don't set stock_setting_list and stock at same time, stock_setting_list also support update stock */
    stockSettingList?: IStockSettingList;
  }

  export interface IStockSettingList {
    stockSettings?: IStockSetting[];
  }

  export interface IStockSetting {
    /** refer to ips promotion_type */
    ruleType?: number;
    stock?: number;
    /** for normal seller items, this can be nil. For PFF items, this MUST not be nil */
    locationId?: string;
    fulfilmentType?: number;
  }

  export interface ISKUMappingList {
    parentSkus?: ISKUMapping[];
    childItemId?: number;
    childModelId?: number;
    childShopId?: number;
    childRegion?: string;
    /** refer to enum SKU_TYPE */
    type?: number;
  }

  export interface IItem {
    /** this is required */
    itemid?: number;
    /** this is required */
    shopid?: number;
    name?: string;
    description?: string;
    /** item images, this field concat image list with comma, e.g. "image1_hash,image2_hash,image3_hash". */
    images?: string;
    /** this is the price displayed, in seller promotion, this is the price after discount */
    price?: number;
    currency?: string;
    /** For multi-model item, this is sum of the stock of all the models */
    stock?: number;
    /** ref: ITEM_NORMAL, if item status is nil means this item is not found. If status is not NORMAL(1) then buyer cannot see it */
    status?: number;
    ctime?: number;
    mtime?: number;
    /** sold count, for display should not used for statistics */
    sold?: number;
    priceMin?: number;
    priceMax?: number;
    recommend?: number;
    collectAddress?: string;
    catid?: number;
    pop?: number;
    likedCount?: number;
    offerCount?: number;
    brand?: string;
    condition?: number;
    ratingGood?: number;
    ratingNormal?: number;
    ratingBad?: number;
    cmtCount?: number;
    country?: string;
    /** web used field */
    option?: number;
    /** ref: ItemExtInfo */
    extinfo?: string;
    /** For multi-model item, stockout_time is when any model become out of stock. Reseted to 0 when have stock again */
    stockoutTime?: number;
    touchTime?: number;
    /** flag of item, see ItemFlags */
    flag?: number;
    /** true: is_cross_border_user */
    cbOption?: number;
    sku?: string;
  }

  export interface IUpdateVirtualSkuForAdminRequest {
    shopId?: number;
    itemId?: number;
    /** since virtual sku must has model, thus this model_id is required */
    modelId?: number;
    itemInfo?: IVirtualSKUItemSetting;
    /** Since virtual sku item follows main sku item. If we provide multiple virtual models, this rule would be broken. Thus we need to migrate cat,attr and some fields from item to model first, before we provide the multiple virtual sku models */
    model?: IVirtualSKUModelSetting;
    /** only can fill email address, have to fill one of audit_param or operator. Http gateway will fill operator */
    operator?: string;
    unlisted?: boolean;
    /** admin region information */
    region?: string;
    /** If this toggle is on, listing side will only pass changed/related fields to LUC. */
    skipCompleteLuc?: boolean;
  }

  export interface IUpdateVirtualSkuForAdminResponse {
    /** this field only return when errcode is not SUCCESS */
    spDebugMsg?: string;
    /** record reason for item edit lock */
    productInfoLock?: IProductInfoLock;
    spErrorCode?: number;
  }

  export interface IProductInfoLock {
    /** indicate the type of lock e.g. promotion lock or seller penalty or holiday mode. Refer to enum LockType */
    lockType?: number;
    /** indicate which info change trigger the lock e.g. item.name. Refer to enum ProductInfoCheckType */
    infoType?: number;
  }

  export interface IItemModelForVSkuAdmin {
    /** This is required */
    modelid?: number;
    /** This is required */
    itemid?: number;
    /** Model's name */
    name?: string;
    /** This is required */
    price?: number;
    /** currency for this model, for example: SGD */
    currency?: string;
    /** This is required */
    stock?: number;
    /** model status, by default only NORMAL(1) status could be viewed for seller and buyer */
    status?: number;
    priceBeforeDiscount?: number;
    promotionid?: number;
    rebatePrice?: number;
    /** sold count */
    sold?: number;
    /** ref: ItemModelExtInfo */
    extinfo?: string;
    /** create time */
    ctime?: number;
    /** last modify time */
    mtime?: number;
    /** stock keeping basic unit; stock is based on sku */
    sku?: string;
  }

  export interface IGetChannelInfoByShopIdRequest {
    /** required */
    shopId?: number;
    /** required */
    region?: string;
  }

  export interface IGetChannelInfoByShopIdResponse {
    spDebugMsg?: string;
    logisticsInfo?: IDisplayLogistics[];
    spErrorCode?: number;
  }

  export interface IDisplayLogistics {
    channelId?: number;
    name?: string;
    displayName?: string;
    flag?: number;
    enabled?: boolean;
  }

  export interface IFetchVskuForAdminEditRequest {
    /** required */
    shopId?: number;
    /** required */
    itemId?: number;
    /** required */
    modelId?: number;
    /** optional, only used to keep compatibility with http gateway */
    operator?: string;
    /** required */
    region?: string;
  }

  export interface IFetchVskuForAdminEditResponse {
    spDebugMsg?: string;
    shopId?: number;
    itemId?: number;
    modelId?: number;
    item?: IVirtualSKUItemSetting;
    model?: IVirtualSKUModelSetting;
    /** correspond to item create time */
    createTime?: number;
    /** corresponds to virtual sku item update time */
    updateTime?: number;
    /** true means item is unlisted, false mean item is published */
    unlisted?: boolean;
    /** item level status of virtual sku */
    itemStatus?: number;
    /** category list of main sku. */
    displayCats?: string[];
    availableStockList?: IStockSettingList;
    parentSkuInfos?: IParentSkuInfo[];
    /** refer to enum SKU_TYPE */
    skuType?: number;
    /** global category name of main sku. no need to display local name */
    globalCats?: string[];
    spErrorCode?: number;
  }

  export interface IParentSkuInfo {
    skuMapping?: ISKUMapping;
    locationStocks?: IStockBreakdownByLocation[];
  }

  export interface IStockBreakdownByLocation {
    locationId?: string;
    availableStock?: number;
    fulfilmentType?: number;
    allocatedStock?: number;
  }

  export interface IMultipleVirtualSkuModelSetting {
    /** required */
    modelAndMappingList?: ICombineModelAndMapping[];
  }

  export interface ICombineModelAndMapping {
    modelId?: number;
    /** required */
    model?: IVirtualSKUModelSetting;
    /** required */
    virtualSkuMappingSetting?: IVirtualSkuMappingSetting;
  }

  export interface IVirtualSkuMappingSetting {
    /** required */
    virtualSkuParentSettings?: IVirtualSkuParentInfo[];
    /** required, shop_id of virtual sku */
    childShopId?: number;
    /** required, refer to enum iis.SkuType */
    type?: number;
    /** required, region of virtual sku, it will pass to IIS directly */
    childRegion?: string;
  }

  export interface IVirtualSkuParentInfo {
    /** required for update/create */
    parentMtskuItemId?: number;
    /** required for update/create */
    parentMtskuModelId?: number;
    /** required for update/create */
    parentMtskuMerchantId?: number;
    /** required for create */
    quantity?: number;
    /** required for create */
    costPrice?: number;
    /** whether this parent sku is main sku or not */
    mainSku?: boolean;
    /** update/create no need fill such param */
    parentCurrency?: string;
    /** parent mtsku item name */
    parentMtskuItemName?: string;
    /** parent mtsku model name */
    parentMtskuModelName?: string;
  }

  export interface IAddVirtualSkuForAdminV2Request {
    /** required */
    itemInfo?: IVirtualSKUItemSetting;
    /** required */
    model?: IVirtualSKUModelSetting;
    /** required */
    virtualSkuMappingSetting?: IMTSKUParentMappingList;
    auditParam?: IAuditParam;
    /** true: unlist new item ; false: publish new item */
    unlisted?: boolean;
    /** If this toggle is on, listing side will only pass changed/related fields to LUC. */
    skipCompleteLuc?: boolean;
    /** required. child region */
    region?: string;
    /** support multiple model, if "model" and this filed both input, we will return error. */
    multipleVirtualSkuModelSetting?: IMultipleVirtualSkuModelSetting;
    bffMeta?: IRequestMeta;
    shopId?: number;
  }

  export interface IAddVirtualSkuForAdminV2Response {
    /** record error info details for debug */
    spDebugMsg?: string;
    /** mpsku info */
    mpskuInfo?: IMpskuInfo;
    spErrorCode?: number;
    /** for mass, record error info details for debug */
    debugMsg?: string;
  }

  export interface IMpskuInfo {
    mpskuItemId?: number;
    mpskuModelIds?: number[];
    /** detailed item info */
    mpskuItem?: IItem;
    /** completed models info */
    mpskuModels?: IItemModelForVSkuAdmin[];
  }

  export interface IUpdateVirtualSkuForAdminV2Request {
    /** required */
    shopId?: number;
    /** required */
    itemId?: number;
    /** required */
    modelId?: number;
    /** optional, item level setting */
    itemInfo?: IVirtualSKUItemSetting;
    /** optional. model level setting */
    model?: IVirtualSKUModelSetting;
    /** required */
    auditParam?: IAuditParam;
    /** true: unlist item */
    unlisted?: boolean;
    /** required */
    region?: string;
    /** If this toggle is on, listing side will only pass changed/related fields to LUC. */
    skipCompleteLuc?: boolean;
    /** if set this field, can not set "model" field. */
    modelAndMappingList?: ICombineModelAndMapping[];
    bffMeta?: IRequestMeta;
  }

  export interface IUpdateVirtualSkuForAdminV2Response {
    /** this field only return when errcode is not SUCCESS */
    spDebugMsg?: string;
    /** if error due to product lock info */
    productLockInfo?: IProductInfoLock;
    spErrorCode?: number;
    debugMsg?: string;
  }

  export interface IActiveVirtualSkuForAdminV2Request {
    /** required */
    shopId?: number;
    /** required */
    itemId?: number;
    /** required */
    modelId?: number;
    /** required */
    auditParam?: IAuditParam;
    bffMeta?: IRequestMeta;
  }

  export interface IActiveVirtualSkuForAdminV2Response {
    /** record error info details, this field only return when errcode is not SUCCESS */
    spDebugMsg?: string;
    spErrorCode?: number;
  }

  export interface IMultipleModelParentSkuInfo {
    /** for new item, is 0 */
    itemId?: number;
    /** for new model, is 0 */
    modelId?: number;
    stockBreakdownSettings?: IStockBreakdownSetting[];
    virtualSkuParentSettings?: IVirtualSkuParentInfo[];
  }

  export interface IStockBreakdownSetting {
    /** the stock you want to validate, for current exist vsku model can not put. for new vsku mdoel, put stock you want to set. */
    stock?: number;
    locationId?: string;
  }

  export interface IValidateParentSkuForAdminV2Request {
    /** required */
    virtualSkuParentSettings?: IMTSKUParentMapping[];
    /** required. vsku shop id */
    shopId?: number;
    /** required. refer to enum iis.SkuType */
    type?: number;
    /** required */
    auditParam?: IAuditParam;
    /** required */
    region?: string;
    multipleModelParentSkuInfoList?: IMultipleModelParentSkuInfo[];
    bffMeta?: IRequestMeta;
  }

  export interface IMultipleModelValidateStockInfo {
    itemId?: number;
    modelId?: number;
    /** the available stock that have calculated the floating stock */
    availableStock?: number;
    /** this field means this min reserved stock of vsku model, for new model, it should be 0. */
    floatingPromotionStock?: number;
    /** Max available stock breakdown list calculated from parents' breakdown list. Its allocated_stock field shall    be nil. */
    availableStockSettings?: IStockSetting[];
    /** location stock setting. for exsit mdoel, it is current stock. */
    stockSettings?: IStockSetting[];
    /** all parent sku for each model. */
    parentSkuInfos?: IParentSkuInfoV2[];
  }

  export interface IValidateParentSkuForAdminV2Response {
    /** if shop/item/model not found or other error, it will return the err details in this field */
    spDebugMsg?: string;
    infos?: IParentSkuValidateInfo[];
    /** this stock is calculated based on request ParentSkuIds, calculated stock info for virtual sku. */
    /** If the parentSkus have no common highest-priority location id, then this field would be empty. */
    /** This vsku_stocks value is calced by min (psku location 1 stock /qty, ....) */
    vskuAvaibleStock?: IVskuStockInfo;
    multipleModelValidateStockInfoList?: IMultipleModelValidateStockInfo[];
    catNonPreorderDts?: number;
    dtsMin?: number;
    dtsMax?: number;
    spErrorCode?: number;
  }

  export interface IParentSkuValidateInfo {
    /** virtual sku parent information */
    parentSkuInfo?: IMTSKUParentMapping;
    /** this always returned even when there is no common highest-priority locationid with other parent sku. */
    stock?: IStock;
    /** The parent's available stock calculated from warehouse location stocks. */
    availableStock?: number;
  }

  export interface IStock {
    skuStockData?: ISkuStockDatum[];
  }

  export interface ISkuStockDatum {
    locationId?: string;
    fulfilmentType?: number;
    sellableStock?: number;
    reservedStock?: number;
    lockedStock?: number;
    availableStock?: number;
  }

  export interface IVskuStockInfo {
    locationStock?: IStock;
    /** for reserved mode, its available stock value. For weak mode, its total avaiailable stock value */
    availableStock?: number;
    locationStockSetting?: IStockSetting[];
  }

  export interface IFetchVskuForAdminEditV2Request {
    shopId?: number;
    itemId?: number;
    modelId?: number;
    auditParam?: IAuditParam;
    region?: string;
    /** optional, set to true for multiple model vsku */
    isItemLevel?: boolean;
    bffMeta?: IRequestMeta;
  }

  export interface IMultipleModelAndStock {
    modelId?: number;
    model?: IVirtualSKUModelSetting;
    availableStock?: number;
    floatingPromotionStock?: number;
    availableStockList?: IStockListSetting;
    parentSkuInfos?: IParentSkuInfoV2[];
    /** model level status of virtual sku */
    modelStatus?: number;
  }

  export interface IStockListSetting {
    stockSettings?: IStockSetting[];
  }

  export interface IFetchVskuForAdminEditV2Response {
    shopId?: number;
    itemId?: number;
    modelId?: number;
    item?: IVirtualSKUItemSetting;
    model?: IVirtualSKUModelSetting;
    /** correspond to item create time */
    createTime?: number;
    /** corresponds to virtual sku item update time */
    updateTime?: number;
    /** true means item is unlisted, false mean item is published */
    unlisted?: boolean;
    /** item level status of virtual sku */
    itemStatus?: number;
    /** category list of main sku. */
    displayCats?: string[];
    availableStockList?: IStockSettingList;
    parentSkuInfos?: IParentSkuInfoV2[];
    /** refer to enum SKU_TYPE */
    skuType?: number;
    /** global category name of main sku. no need to display local name */
    globalCats?: string[];
    /** its available stock value excluding the reserved-mode vsku existing vsku stock */
    availableStock?: number;
    /** all model under request item_id */
    multipleModelAndStockList?: IMultipleModelAndStock[];
    spErrorCode?: number;
  }

  export interface IParentSkuInfoV2 {
    skuMapping?: IMTSKUParentMapping;
    locationStocks?: IStockBreakdownByLocation[];
    /** The parent's available stock calculated from warehouse location stocks. */
    availableStock?: number;
  }

  export interface IMTSKUParentMapping {
    parentMtskuItemId?: number;
    parentMtskuModelId?: number;
    parentMtskuMerchantId?: number;
    /** this is used for cost price rounding */
    parentCurrency?: string;
    quantity?: number;
    costPrice?: number;
    /** whether this parent sku is main sku or not */
    mainSku?: boolean;
    /** in response */
    parentMtskuItemName?: string;
    /** in response */
    parentMtskuModelName?: string;
  }

  export interface IMTSKUParentMappingList {
    /** required */
    parentSkus?: IMTSKUParentMapping[];
    /** required, shop_id of virtual sku */
    childShopId?: number;
    /** required, refer to enum iis.SkuType */
    type?: number;
  }

  export interface IChangeVskuStockV2Request {
    shopId?: number;
    itemId?: number;
    /** since virtual sku must has model, thus this model_id is required */
    modelId?: number;
    /** stock value inside StockSettingList indicate the stock change delta. positive means top up stock. Current phase, negative stock would return error due to product requirement */
    stockChangeList?: IStockSettingList;
    /** only can fill email address, have to fill one of audit_param or operator. Http gateway will fill operator */
    operator?: string;
    /** to prevent unexpected duplicate requests, we need client send us the uniqId. (need ips api provide this) */
    uniqId?: string;
    bffMeta?: IRequestMeta;
  }

  export interface IChangeVskuStockV2Response {
    /** this field only return when errcode is not SUCCESS */
    spDebugMsg?: string;
    models?: IUpdateChildSkuModelData[];
    /** record reason for item edit lock */
    productInfoLock?: IProductInfoLock;
    spErrorCode?: number;
  }

  export interface IUpdateChildSkuModelData {
    modelId?: number;
    price?: number;
    stock?: number;
    inputPrice?: number;
    stockBreakdownByLocation?: IStockBreakdownByLocation[];
  }

  export interface IGetChildSkuFromParentV2Request {
    /** limit size is 10 */
    parentSkuIds?: IMtskuModelId[];
  }

  export interface IMtskuModelId {
    mtskuItemId?: number;
    mtskuModelId?: number;
  }

  export interface IGetChildSkuFromParentV2Response {
    childSkuInfos?: ISKUInfoV2List[];
    spDebugMsg?: string;
    spErrorCode?: number;
  }

  export interface ISKUInfoV2List {
    skuInfos?: ISKUInfoV2[];
  }

  export interface ISKUInfoV2 {
    childItemId?: number;
    childModelId?: number;
    childShopId?: number;
    childRegion?: string;
    parentMtskuItemId?: number;
    parentMtskuModelId?: number;
    parentMerchantId?: number;
    skuType?: number;
    costPrice?: number;
    quantity?: number;
    mainSku?: boolean;
    /** refer to VskuMappingStatus */
    mappingStatus?: number;
  }

  export interface IGetParentSkuFromChildV2Request {
    /** limit size is 30 */
    childSkuIds?: IItemModelId[];
  }

  export interface IItemModelId {
    itemId?: number;
    modelId?: number;
  }

  export interface IGetParentSkuFromChildV2Response {
    parentSkuInfos?: ISKUInfoV2List[];
    spDebugMsg?: string;
    spErrorCode?: number;
  }

  export interface IGetVirtualSkuListForAdminV2Request {
    /** query vsku list by virtual_sku_shop_id */
    virtualSkuShopId?: number;
    /** query vsku list by virtual_sku_item_id */
    virtualSkuItemId?: number;
    /** optional. query vsku list by region */
    region?: string;
    /** query vsku list by merchant_id */
    merchantId?: number;
    /** query vsku list by parent item id */
    parentItemId?: number;
    /** default maximum limit is 100 */
    limit?: number;
    /** If query condition has no itemid or shopid, then we doesn't support pagination and return error if offset > 0 */
    offset?: number;
    /** optional, set to true for multiple model vsku */
    useItemLevelStructure?: boolean;
  }

  export interface IGetVirtualSkuListForAdminV2Response {
    /** if req.info_type == 1, then this field */
    details?: ISKUDetailForAdminV2[];
    /** has_next only return when request have shopid or itemids. True means we can move offset to next page */
    hasNext?: boolean;
    spDebugMsg?: string;
    spErrorCode?: number;
  }

  export interface IVirtualSKUMultipleModel {
    modelId?: number;
    modelStatus?: number;
    /** required */
    model?: IVirtualSKUModelSetting;
    /** required */
    parentSkuSetting?: IMTSKUMappingList;
  }

  export interface ISKUDetailForAdminV2 {
    shopId?: number;
    itemId?: number;
    modelId?: number;
    item?: IVirtualSKUItemSetting;
    model?: IVirtualSKUModelSetting;
    parentSkuSetting?: IMTSKUMappingList;
    /** correspond to item create time */
    createTime?: number;
    /** corresponds to virtual sku item update time */
    updateTime?: number;
    /** true means item is unlisted, false mean item is published */
    unlisted?: boolean;
    /** item level status of virtual sku */
    itemStatus?: number;
    /** model level status of virtual sku */
    modelStatus?: number;
    virtualSkuMultipleModelList?: IVirtualSKUMultipleModel[];
  }

  export interface IMTSKUMappingList {
    parentSkus?: IMTSKUParentMapping[];
    childItemId?: number;
    childModelId?: number;
    childShopId?: number;
    childRegion?: string;
    /** refer to enum SKU_TYPE */
    type?: number;
    /** sum up of parent cost_price * quantity */
    totalCost?: number;
    /** read from any parent currency since current all parents cost currency read from child shop */
    childCurrency?: string;
  }

  export interface IGetAutoSyncDtsFromParentRequest {
    parentSkuList?: IVskuAutoSyncParentSkuList[];
    shopId?: number;
  }

  export interface IVskuAutoSyncParentSkuList {
    parentSkus?: IVskuAutoSyncParentSku[];
  }

  export interface IVskuAutoSyncParentSku {
    mtskuItemId?: number;
    mtskuModelId?: number;
    quantity?: number;
    mainSku?: boolean;
  }

  export interface IGetAutoSyncDtsFromParentResponse {
    /** item level dts */
    itemDts?: IVskuAutoSyncDTS;
    /** model level dts */
    modelDts?: IVskuAutoSyncDTS[];
    spDebugMsg?: string;
    spErrorCode?: number;
  }

  export interface IVskuAutoSyncDTS {
    isPreOrder?: boolean;
    estimatedDays?: number;
  }

  export interface IGetAutoSyncWeightFromParentRequest {
    parentSkuList?: IVskuAutoSyncParentSkuList[];
  }

  export interface IGetAutoSyncWeightFromParentResponse {
    /** item level weight */
    weight?: number;
    spDebugMsg?: string;
    spErrorCode?: number;
  }

  export interface IUnavailableModelV2Request {
    itemId?: number;
    modelId?: number;
    /** true -> change model status from normal to unavailable */
    unavailable?: boolean;
    auditParam?: IAuditParam;
    bffMeta?: IRequestMeta;
  }

  export interface IUnavailableModelV2Response {
    spDebugMsg?: string;
    spErrorCode?: number;
  }

  export interface IExportVskuRequest {
    /** query vsku list by virtual_sku_shop_id */
    virtualSkuShopId?: number;
    /** query vsku list by virtual_sku_item_id */
    virtualSkuItemId?: number;
    /** optional. query vsku list by region */
    region?: string;
    /** query vsku list by merchant_id */
    merchantId?: number;
    /** query vsku list by parent item id */
    parentItemId?: number;
    bffMeta?: IRequestMeta;
  }

  export interface IExportVskuResponse {
    fileName?: string;
    spDebugMsg?: string;
    spErrorCode?: number;
  }

  export interface IGetCategoryTreeDimensionRuleRequest {
    /** refer to enum CategoryType */
    categoryType?: number;
    /** refer to enum SellerCategory */
    sellerCategory?: number;
    /** refer to enum SellerType */
    sellerType?: number;
    region?: string;
  }

  export interface IGetCategoryTreeDimensionRuleResponse {
    spDebugMsg?: string;
    categoryList?: ICategoryDimensionRule[];
    spErrorCode?: number;
  }

  export interface ICategoryDimensionRule {
    catId?: number;
    name?: string;
    isDimensionMandatory?: boolean;
    children?: ICategoryDimensionRule[];
  }

  export interface ISetCategoryDimensionRuleRequest {
    /** refer to enum CategoryType */
    categoryType?: number;
    /** refer to enum SellerCategory */
    sellerCategory?: number;
    /** refer to enum SellerType */
    sellerType?: number;
    region?: string;
    rules?: ICategoryDimensionRuleSetting[];
    bffMeta?: IRequestMeta;
  }

  export interface ISetCategoryDimensionRuleResponse {
    spDebugMsg?: string;
    spErrorCode?: number;
  }

  export interface ICategoryDimensionRuleSetting {
    catId?: number;
    isDimensionMandatory?: boolean;
  }

  export interface IGetRegionTranslationLangRequest {
    region?: string;
  }

  export interface IGetRegionTranslationLangResponse {
    spDebugMsg?: string;
    languageInfo?: ILanguageInfo;
    spErrorCode?: number;
  }

  export interface ILanguageInfo {
    defaultLanguage?: ILanguageBasicInfo;
    supportLanguageList?: ILanguageBasicInfo[];
  }

  export interface ILanguageBasicInfo {
    language?: string;
    displayName?: string;
  }

  export interface IGetItemTranslationRequest {
    shopId?: number;
    itemId?: number;
    region?: string;
  }

  export interface IGetItemTranslationResponse {
    spDebugMsg?: string;
    itemId?: number;
    translationInfo?: ITranslationInfo;
    spErrorCode?: number;
  }

  export interface ITranslationInfo {
    basicInfoList?: IItemBasicTranslationInfo[];
    descInfoList?: IDescriptionTranslationInfo[];
    modelInfoList?: IModelTranslationInfo[];
  }

  export interface IItemBasicTranslationInfo {
    lang?: string;
    name?: string;
    tierVariationList?: ITierVariationTranslationInfo[];
  }

  export interface ITierVariationTranslationInfo {
    name?: string;
    options?: string[];
  }

  export interface IDescriptionTranslationInfo {
    lang?: string;
    description?: string;
    descriptionType?: string;
  }

  export interface IModelTranslationInfo {
    lang?: string;
    modelList?: IModelBasicTranslationInfo[];
  }

  export interface IModelBasicTranslationInfo {
    modelId?: number;
    modelName?: string;
  }

  export interface IUpdateItemTranslationRequest {
    shopId?: number;
    itemId?: number;
    region?: string;
    translationSetting?: ITranslationSetting;
    bffMeta?: IRequestMeta;
  }

  export interface ITranslationSetting {
    basicSettingList?: IItemBasicTranslationInfo[];
    descSettingList?: IDescriptionTranslationInfo[];
    modelSettingList?: IModelTranslationInfo[];
  }

  export interface IUpdateItemTranslationResponse {
    spDebugMsg?: string;
    spErrorCode?: number;
  }

  export interface IGetShopItemLimitSettingListRequest {
    shopid?: number;
    offset?: number;
    limit?: number;
    region?: string;
  }

  export interface IGetShopItemLimitSettingListRow {
    shopid?: number;
    shopUsername?: string;
    uploadLimit?: number;
    modifyTime?: number;
    operator?: string;
  }

  export interface IGetShopItemLimitSettingListResponse {
    spDebugMsg?: string;
    hasNext?: boolean;
    nextOffset?: number;
    rows?: IGetShopItemLimitSettingListRow[];
    spErrorCode?: number;
  }

  export interface ICreateGroupRequest {
    groupType?: number;
    region?: string;
    groupName?: string;
    groupDesc?: string;
    operator?: string;
    bffMeta?: IRequestMeta;
  }

  export interface ICreateGroupResponse {
    code?: number;
    debugMessage?: string;
    groupId?: number;
    spErrorCode?: number;
  }

  export interface IUpdateGroupRequest {
    groupId?: number;
    groupName?: string;
    groupDesc?: string;
    operator?: string;
    bffMeta?: IRequestMeta;
  }

  export interface IUpdateGroupResponse {
    code?: number;
    debugMessage?: string;
    spErrorCode?: number;
  }

  export interface IGetGroupDetailRequest {
    groupId?: number;
  }

  export interface IGetGroupDetailResponse {
    code?: number;
    debugMessage?: string;
    group?: IGroup;
    spErrorCode?: number;
  }

  export interface ISearchGroupRequest {
    filterType?: number;
    groupType?: number;
    region?: string;
    groupId?: number;
    groupName?: string;
    groupDesc?: string;
    element?: number;
    pageSize?: number;
    currentPage?: number;
  }

  export interface ISearchGroupResponse {
    code?: number;
    debugMessage?: string;
    count?: number;
    groups?: IGroup[];
    spErrorCode?: number;
  }

  export interface IDeleteGroupRequest {
    groupId?: number;
    operator?: string;
    bffMeta?: IRequestMeta;
  }

  export interface IDeleteGroupResponse {
    code?: number;
    debugMessage?: string;
    spErrorCode?: number;
  }

  export interface IDeleteGroupRuleRequest {
    groupId?: number;
    operator?: string;
    bffMeta?: IRequestMeta;
  }

  export interface IDeleteGroupRuleResponse {
    code?: number;
    debugMessage?: string;
    spErrorCode?: number;
  }

  export interface IAddElementListRequest {
    groupType?: number;
    region?: string;
    groupId?: number;
    elementList?: number[];
    operator?: string;
    bffMeta?: IRequestMeta;
  }

  export interface IAddElementListResponse {
    code?: number;
    debugMessage?: string;
    spErrorCode?: number;
  }

  export interface IDeleteElementRequest {
    elementId?: number;
    operator?: string;
  }

  export interface IDeleteElementResponse {
    code?: number;
    debugMessage?: string;
    spErrorCode?: number;
  }

  export interface IGetElementListRequest {
    groupId?: number;
    element?: number;
    pageSize?: number;
    currentPage?: number;
  }

  export interface IGetElementListResponse {
    code?: number;
    debugMessage?: string;
    elements?: IElement[];
    count?: number;
    spErrorCode?: number;
  }

  export interface IUploadElementListRequest {
    fileKey?: string;
    groupId?: number;
    operator?: string;
    bffMeta?: IRequestMeta;
  }

  export interface IUploadElementListResponse {
    code?: number;
    debugMessage?: string;
    spErrorCode?: number;
  }

  export interface IDownloadElementListRequest {
    groupId?: number;
  }

  export interface IDownloadElementListResponse {
    code?: number;
    debugMessage?: string;
    fileKey?: string;
    fileName?: string;
    spErrorCode?: number;
  }

  export interface IGroup {
    id?: number;
    groupType?: number;
    region?: string;
    groupName?: string;
    groupDesc?: string;
    combineRuleId?: number;
    status?: number;
    ctime?: number;
    mtime?: number;
    createdBy?: string;
    modifiedBy?: string;
    groupRuleMtime?: number;
    groupRuleModifiedBy?: string;
  }

  export interface IElement {
    id?: number;
    groupType?: number;
    region?: string;
    element?: number;
    groupId?: number;
    status?: number;
    ctime?: number;
    mtime?: number;
    createdBy?: string;
    modifiedBy?: string;
  }

  export interface IGetBasicRulesRequest {
    productType?: number;
    sellerType?: number;
    sellerCategory?: number;
    region?: string;
    filterType?: number;
    combineRuleId?: number;
  }

  export interface IGetBasicRulesResponse {
    code?: number;
    debugMessage?: string;
    combineRuleId?: number;
    basicRules?: IEntireBasicRule[];
    spErrorCode?: number;
  }

  export interface IGetRuleSubCatListRequest {
    productType?: number;
    sellerType?: number;
    sellerCategory?: number;
    region?: string;
    filterType?: number;
    combineRuleId?: number;
  }

  export interface IGetRuleSubCatListResponse {
    code?: number;
    debugMessage?: string;
    ruleSubCat?: number[];
    spErrorCode?: number;
  }

  export interface IUpdateCombineRuleRequest {
    combineRuleId?: number;
    basicRules?: IEntireBasicRule[];
    deleteSubCat?: number[];
    groupId?: number;
    operator?: string;
    bffMeta?: IRequestMeta;
  }

  export interface IUpdateCombineRuleResponse {
    code?: number;
    debugMessage?: string;
    combineRuleId?: number;
    spErrorCode?: number;
  }

  export interface ICheckBasicRuleRequest {
    groupId?: number;
    basicRuleType?: number;
    basicRuleStrType?: string;
  }

  export interface ICheckBasicRuleResponse {
    code?: number;
    debugMessage?: string;
    canUpdate?: boolean;
    spErrorCode?: number;
  }

  export interface IGetProhibitedCharRecordRequest {
    basicRuleId?: number;
    pageSize?: number;
    currentPage?: number;
  }

  export interface IGetProhibitedCharRecordResponse {
    records?: IBasicRuleFileRecord[];
    count?: number;
    code?: number;
    debugMessage?: string;
    spErrorCode?: number;
  }

  export interface IGetDimensionCategoryConfigRequest {
    basicRuleId?: number;
  }

  export interface IGetDimensionCategoryConfigResponse {
    code?: number;
    debugMessage?: string;
    basicRules?: IDimensionBasicRule[];
    spErrorCode?: number;
  }

  export interface IGetAllCategoryListRequest {
  }

  export interface IGetAllCategoryListResponse {
    code?: number;
    debugMessage?: string;
    catList?: ICategory[];
    spErrorCode?: number;
  }

  export interface IGetDtsCategoryConfigRequest {
    basicRuleId?: number;
  }

  export interface IGetDtsCategoryConfigResponse {
    code?: number;
    debugMessage?: string;
    basicRules?: IDtsBasicRule[];
    spErrorCode?: number;
  }

  export interface IEntireBasicRule {
    basicRule?: IBasicRule;
    configValues?: IBasicRuleConfigValue[];
    dimensionCatConfig?: IDimensionCatConfig[];
    dtsCatConfig?: IDtsCatConfig[];
  }

  export interface IBasicRule {
    id?: number;
    subCatType?: number;
    basicRuleType?: number;
    status?: number;
    combineRuleId?: number;
    ctime?: number;
    mtime?: number;
    createdBy?: string;
    modifiedBy?: string;
    basicRuleStrType?: string;
  }

  export interface IBasicRuleConfigValue {
    id?: number;
    configFieldType?: string;
    configValue?: string;
    basicRuleId?: number;
    status?: number;
    ctime?: number;
    mtime?: number;
    createdBy?: string;
    modifiedBy?: string;
  }

  export interface IBasicRuleFileRecord {
    id?: number;
    basicRuleId?: number;
    fileKey?: string;
    fileName?: string;
    modifiedBy?: string;
    mtime?: number;
  }

  export interface IDimensionBasicRule {
    basicRule?: IBasicRule;
    dimensionCatConfig?: IDimensionCatConfig[];
  }

  export interface IDimensionCatConfig {
    catId?: number;
    catName?: string;
    isDimensionMandatory?: boolean;
    children?: IDimensionCatConfig[];
  }

  export interface IDtsBasicRule {
    basicRule?: IBasicRule;
    dtsCatConfig?: IDtsCatConfig[];
  }

  export interface IDtsCatConfig {
    dtsConfigId?: number;
    dtsMin?: number;
    dtsMax?: number;
    desc?: string;
    catIdList?: number[];
    isDtsEnabled?: boolean;
    modifiedBy?: string;
    mtime?: number;
    isUpdate?: boolean;
  }

  export interface IGetSizeChartAdminConfigsRequest {
  }

  export interface IUnitList {
    units?: string[];
  }

  export interface IRegionSupportLang {
    region?: string;
    langList?: string[];
  }

  export interface IRegionDefaultLang {
    region?: string;
    lang?: string;
  }

  export interface ILocalMaxMeasurementNumberUnderCategory {
    region?: string;
    maxLimit?: number;
  }

  export interface IGetSizeChartAdminConfigsResponse {
    units?: IUnitList;
    regionSupportLangList?: IRegionSupportLang[];
    regionDefaultLangList?: IRegionDefaultLang[];
    maxMeasurementNumberUnderCategory?: number;
    inputBoxLimit?: number;
    templateUnits?: IUnitList;
    maxMeasurementNameLength?: number;
    maxDropDownListPerMeasurement?: number;
    measurementRangeMaxLimit?: number;
    measurementRangeMinLimit?: number;
    keyMeasurementLimit?: number;
    spErrorCode?: number;
    localMaxMeasurementNumberUnderCategory?: ILocalMaxMeasurementNumberUnderCategory[];
    buyerRegionLangList?: IRegionSupportLang[];
    measurementBuyerSettingConstraint?: IMeasurementBuyerSettingConstraint;
    virtualCategoryBuyerSettingConstraint?: IVirtualCategoryBuyerSettingConstraint;
    maxDropDownValueLength?: number;
  }

  export interface IMeasurementBuyerSettingConstraint {
    nameLengthMaxLimit?: number;
    buyerUnits?: IUnitList;
    selectedUnitNumMaxLimit?: number;
    unitValueDecimalPlaces?: number;
    unitValueMinLimit?: number;
    unitValueMaxLimit?: number;
  }

  export interface IVirtualCategoryBuyerSettingConstraint {
    /** no more than 10 */
    selectedMeasurementNumMaxLimit?: number;
  }

  export interface IUnitConstrain {
    unit?: string;
    limitMin?: number;
    limitMax?: number;
  }

  export interface IUnitConstrainList {
    unitConstrains?: IUnitConstrain[];
  }

  export interface IOption {
    value?: string;
    /** only for size convention */
    attrValueId?: number;
    sortWeight?: number;
    cbDisplayNames?: IMultiLangList;
  }

  export interface IOptionList {
    options?: IOption[];
  }

  export interface IMeasurementOptionLocalSetting {
    optionLocalSettings?: IOptionLocalSetting[];
  }

  export interface IOptionLocalSetting {
    optionValue?: string;
    localDisplayNames?: IMultiLangList;
  }

  export interface IMultiLangList {
    /** multiple language display */
    multiLangValues?: IValWithLang[];
  }

  export interface ISimilarNameList {
    similarNames?: string[];
  }

  export interface IMeasurementLocalSetting {
    region?: string;
    localDisplayNames?: IMultiLangList;
    similarNameList?: ISimilarNameList;
    optionsLocalSetting?: IMeasurementOptionLocalSetting;
  }

  export interface IMeasurementLocalSettingList {
    localSettings?: IMeasurementLocalSetting[];
    buyerSettings?: IMeasurementBuyerLocalSetting[];
  }

  export interface IGetMeasurementDetailRequest {
    id?: number;
    region?: string;
  }

  export interface IGetMeasurementDetailResponse {
    id?: number;
    name?: string;
    specialUnit?: string;
    inputType?: number;
    unitConstrainList?: IUnitConstrainList;
    optionList?: IOptionList;
    localSettingList?: IMeasurementLocalSettingList;
    spErrorCode?: number;
    /** buyer local setting */
    buyerLocalSetting?: IMeasurementBuyerLocalSetting;
    cbDisplayNames?: IMultiLangList;
  }

  export interface IGetMeasurementListRequest {
    id?: number;
    region?: string;
    name?: string;
    inputType?: number;
    offset?: number;
    limit?: number;
    /** 1:seller ; 2:buyer */
    userType?: number;
  }

  export interface IMeasurementInfo {
    id?: number;
    name?: string;
    inputType?: number;
    localDisplayNames?: IMultiLangList;
    unitValueSettings?: IBuyerMeasurementUnitValue[];
    buyerLocalDisplayNames?: IMultiLangList;
    cbDisplayNames?: IMultiLangList;
  }

  export interface IGetMeasurementListResponse {
    measurementInfos?: IMeasurementInfo[];
    total?: number;
    spErrorCode?: number;
  }

  export interface ICreateMeasurementRequest {
    name?: string;
    inputType?: number;
    specialUnit?: string;
    unitConstrainList?: IUnitConstrainList;
    optionList?: IOptionList;
    auditParam?: IAuditParam;
    bffMeta?: IRequestMeta;
    cbDisplayNames?: IMultiLangList;
  }

  export interface ICreateMeasurementResponse {
    id?: number;
    /** optional string debug_message=2; */
    spDebugMsg?: string;
    spErrorCode?: number;
  }

  export interface IUpdateMeasurementRequest {
    id?: number;
    name?: string;
    specialUnit?: string;
    unitConstrainList?: IUnitConstrainList;
    optionList?: IOptionList;
    auditParam?: IAuditParam;
    bffMeta?: IRequestMeta;
    inputType?: number;
    cbDisplayNames?: IMultiLangList;
  }

  export interface IUpdateMeasurementResponse {
    /** optional string debug_message=1; */
    spDebugMsg?: string;
    spErrorCode?: number;
  }

  export interface IUpdateMeasurementLocalSettingRequest {
    measurementId?: number;
    delete?: boolean;
    localSetting?: IMeasurementLocalSetting;
    auditParam?: IAuditParam;
    bffMeta?: IRequestMeta;
    /** buyer local setting */
    buyerLocalSetting?: IMeasurementBuyerLocalSetting;
  }

  export interface IMeasurementBuyerLocalSetting {
    region?: string;
    localDisplayNames?: IMultiLangList;
    unitValueSettings?: IBuyerMeasurementUnitValue[];
  }

  export interface IBuyerMeasurementUnitValue {
    unit?: string;
    rangeBegin?: number;
    rangeEnd?: number;
    stepSize?: number;
    startPoint?: number;
  }

  export interface IUpdateMeasurementLocalSettingResponse {
    /** optional string debug_message=1; */
    spDebugMsg?: string;
    spErrorCode?: number;
  }

  export interface ILinkCategoryList {
    categoryIds?: number[];
  }

  export interface IVirtualCategoryMeasurementMutation {
    id?: number;
    /** 1: normal, 2: size convention */
    type?: number;
    sortWeight?: number;
  }

  export interface IVirtualCategoryMeasurementMutationList {
    measurements?: IVirtualCategoryMeasurementMutation[];
  }

  export interface ICreateVirtualCategoryRequest {
    name?: string;
    linkCategoryList?: ILinkCategoryList;
    units?: IUnitList;
    measurementList?: IVirtualCategoryMeasurementMutationList;
    auditParam?: IAuditParam;
    bffMeta?: IRequestMeta;
    cbDisplayNames?: IMultiLangList;
  }

  export interface ICreateVirtualCategoryResponse {
    id?: number;
    spDebugMsg?: string;
    spErrorCode?: number;
  }

  export interface IUpdateVirtualCategoryRequest {
    id?: number;
    name?: string;
    linkCategoryList?: ILinkCategoryList;
    units?: IUnitList;
    measurementList?: IVirtualCategoryMeasurementMutationList;
    auditParam?: IAuditParam;
    bffMeta?: IRequestMeta;
    cbDisplayNames?: IMultiLangList;
  }

  export interface IUpdateVirtualCategoryResponse {
    spDebugMsg?: string;
    spErrorCode?: number;
  }

  export interface IVirtualCategoryMeasurementLocalSetting {
    /** measurement_id */
    id?: number;
    /** ActiveType */
    status?: number;
    /** 1: normal, 2: size convention */
    type?: number;
    isMandatory?: boolean;
    name?: string;
    inputType?: number;
    isKeyMeasurement?: boolean;
    isDefaultSelected?: boolean;
  }

  export interface IVirtualCategoryMeasurementLocalSettingList {
    measurements?: IVirtualCategoryMeasurementLocalSetting[];
  }

  export interface IVirtualCategoryLocalSetting {
    region?: string;
    /** ActiveType */
    status?: number;
    localDisplayName?: IMultiLangList;
    measurementList?: IVirtualCategoryMeasurementLocalSettingList;
  }

  export interface IUpdateVirtualCategoryLocalSettingRequest {
    id?: number;
    region?: string;
    localSetting?: IVirtualCategoryLocalSetting;
    auditParam?: IAuditParam;
    bffMeta?: IRequestMeta;
    /** buyer local setting */
    buyerLocalSetting?: IVirtualCategoryBuyerLocalSetting;
  }

  export interface IVirtualCategoryBuyerLocalSetting {
    region?: string;
    guideImages?: IGuideImage[];
    measurementList?: IVirtualCategoryBuyerMeasurementLocalSettingList;
    /** ActiveType */
    status?: number;
  }

  export interface IGuideImage {
    language?: string;
    imageId?: string;
  }

  export interface IVirtualCategoryBuyerMeasurementLocalSettingList {
    measurements?: IVirtualCategoryBuyerMeasurementLocalSetting[];
  }

  export interface IVirtualCategoryBuyerMeasurementLocalSetting {
    /** measurement_id */
    id?: number;
    name?: string;
    /** 1: normal, 2: size convention */
    type?: number;
    inputType?: number;
    weightage?: number;
    /** ActiveType */
    status?: number;
  }

  export interface IUpdateVirtualCategoryLocalSettingResponse {
    spDebugMsg?: string;
    spErrorCode?: number;
  }

  export interface IGetVirtualCategoryRequest {
    /** category id */
    id?: number;
    region?: string;
  }

  export interface IVirtualCategoryMeasurement {
    id?: number;
    name?: string;
    /** 1: normal, 2: size convention */
    type?: number;
    sortWeight?: number;
    inputType?: number;
  }

  export interface IVirtualCategoryMeasurementList {
    measurements?: IVirtualCategoryMeasurement[];
  }

  export interface IVirtualCategory {
    id?: number;
    name?: string;
    units?: IUnitList;
    mtime?: number;
    linkCategoryList?: ILinkCategoryList;
    measurementList?: IVirtualCategoryMeasurementList;
    localSetting?: IVirtualCategoryLocalSetting;
    /** buyer local setting */
    buyerLocalSetting?: IVirtualCategoryBuyerLocalSetting;
    cbDisplayNames?: IMultiLangList;
  }

  export interface IGetVirtualCategoryResponse {
    spDebugMsg?: string;
    virtualCategory?: IVirtualCategory;
    spErrorCode?: number;
  }

  export interface IGetVirtualCategoryListWithPagingRequest {
    categoryId?: number;
    /** delete */
    virtualCategoryName?: string;
    offset?: number;
    limit?: number;
    region?: string;
  }

  export interface IGetVirtualCategoryListWithPagingResponse {
    spDebugMsg?: string;
    virtualCategories?: IVirtualCategory[];
    total?: number;
    spErrorCode?: number;
  }

  export interface IGetSizeConventionListRequest {
    categoryIds?: number[];
  }

  export interface ISizeConvention {
    id?: number;
    name?: string;
    optionList?: IOptionList;
    status?: number;
    localSettings?: IMeasurementLocalSetting[];
  }

  export interface IGetSizeConventionListResponse {
    spDebugMsg?: string;
    sizeConventions?: ISizeConvention[];
    spErrorCode?: number;
  }

  export interface IScanSizeChartAuditLogRequest {
    bizId?: number;
    /** measurement, virtual category */
    fieldType?: number;
    beginCtime?: number;
    endCtime?: number;
    limit?: number;
    offset?: number;
    /** need be Upper. if need all, pass "WW" */
    region?: string;
    /** 0 => return all;1=>return regional;2=>return local */
    flag?: number;
  }

  export interface IAuditLog {
    bizId?: number;
    fieldType?: number;
    region?: string;
    operator?: string;
    operateType?: number;
    oldValue?: string;
    newValue?: string;
    operateTime?: number;
    operateScope?: number;
  }

  export interface IScanSizeChartAuditLogResponse {
    audits?: IAuditLog[];
    nextOffset?: number;
    spErrorCode?: number;
  }

  export interface IGetCategoryTreeWithVirtualCategoryRequest {
  }

  export interface ILinkedCategory {
    categoryId?: number;
    name?: string;
    isLinkedVirtualCategory?: boolean;
    linkedVirtualCategoryId?: number;
    linkedVirtualCategoryName?: string;
    children?: ILinkedCategory[];
  }

  export interface IGetCategoryTreeWithVirtualCategoryResponse {
    spDebugMsg?: string;
    categories?: ILinkedCategory[];
    spErrorCode?: number;
  }

  export interface IUpdateMtskuStockRequest {
    /** mtsku-model to be updated with stock changes */
    merchantId?: number;
    /** mtsku-model to be updated with stock changes */
    modelStockSetting?: IMtskuModelStockSetting[];
    /** audit log */
    auditParam?: IAuditParam;
    bffMeta?: IRequestMeta;
  }

  export interface IMtskuModelStockSetting {
    mtskuModelId?: number;
    /** mtsku stock */
    stockListSetting?: IStockListSettings;
  }

  export interface IUpdateMtskuStockResponse {
    errMtskuModelIds?: number[];
    /** record error info details for debug */
    spDebugMsg?: string;
    spErrorCode?: number;
  }

  export interface IStockListSettings {
    stockSettings?: IStockSettingModel[];
  }

  export interface IStockSettingModel {
    locationId?: string;
    fulfilmentType?: number;
    sellableStock?: number;
  }

  export interface IUpdateMtskuItemForAdminRequest {
    itemSetting?: IUpdateMtskuItemForAdminSetting;
    modelListSetting?: IUpdateMtskuModelListForAdminSetting;
    auditParam?: IAuditParam;
    bffMeta?: IRequestMeta;
  }

  export interface IUpdateMtskuItemForAdminSetting {
    mtskuItemId?: number;
    basicSetting?: IMtskuItemBasicSetting;
    /** reference MtskuItemCustomisedField by default, by default all allow customised field won't be sync */
    mtskuSyncFieldListSetting?: ISyncFieldListSetting;
  }

  export interface IMtskuItemBasicSetting {
    /** mtsku name */
    name?: string;
    /** mtsku description */
    description?: string;
    /** mtsku images */
    imageSetting?: IImageSetting;
    /** same as item.condition */
    condition?: number;
    /** size chart */
    sizeChart?: string;
    /** tier variation list */
    tierVariationSetting?: ITierVariationSetting;
    /** seller sku  for associated with external system, e.g. seller will use this field to associated with themSelf listing management. */
    sellerSku?: string;
    /** category information */
    categoryPathSetting?: ICategoryPathSetting;
    /** video information */
    videoListSetting?: IVideoInfoListSetting;
    /** brand information */
    brandSetting?: IGlobalBrandSetting;
    /** attribute information */
    attributeSetting?: IGlobalAttributeSetting;
    /** check mtsku global validation but ignore the error */
    mtskuGlobalValidation?: boolean;
    /** optional, by default use WW region for create/update mtsku only API, use mpsku region for create/update mtsku and mpsku at same time API */
    regionForCheckMandatoryAttribute?: string;
    /** optional, used for global tree validation */
    globalTreeValidationSetting?: IGlobalTreeValidationSetting;
    isDescriptionRichText?: boolean;
    richTextDescription?: IItemRichTextDescriptionSetting;
  }

  export interface IImageSetting {
    images?: string[];
    /** high priority than images */
    imageDetailSettings?: IImageDetailSetting[];
  }

  export interface ITierVariationSetting {
    /** one tier property may have multiple value, keep same order with tier_variations */
    tierProperties?: IMtskuTierProperty[];
  }

  export interface IMtskuTierProperty {
    /** MtskuTierProperty equal to TierProperty in mtsku.proto */
    name?: string;
    /** one property may have multiple value */
    tierValues?: ITierVariationValue[];
  }

  export interface ITierVariationValueList {
    values?: ITierVariationValue[];
  }

  export interface ITierVariationValue {
    value?: string;
    /** // one value may have multiple feature */
    features?: ITierVariationFeature[];
  }

  export interface ITierVariationFeature {
    /** reference TierVariationFeatureType */
    type?: number;
    value?: string;
  }

  export interface IVideoInfoListSetting {
    /** video information */
    videoLists?: IVideoInfoList[];
  }

  export interface IVideoInfoList {
    videos?: IMtskuVideoInfo[];
    region?: string;
  }

  export interface IMtskuVideoInfo {
    /** MtskuVideoInfo equal to VideoInfo in mtsku.proto */
    /** video id for version 0 and 1 */
    videoId?: string;
    thumbUrl?: string;
    duration?: number;
    /** video version, refer to iis.ItemVideoVersion */
    version?: number;
    /** MMS video id for version 2 */
    vid?: string;
  }

  export interface IGlobalAttributeSetting {
    /** global attribute */
    attrs?: IGlobalAttributeValueSetting[];
  }

  export interface IGlobalTreeValidationSetting {
    /** used by create/update APIs */
    /** CB, Local */
    type?: number;
    regions?: string[];
  }

  export interface IItemRichTextDescriptionSetting {
    richTextDescriptionParagraphList?: IItemRichTextDescriptionParagraph[];
  }

  export interface IItemRichTextDescriptionParagraph {
    text?: string;
    img?: IItemRichTextDescriptionImage;
  }

  export interface IItemRichTextDescriptionImage {
    id?: string;
    ratio?: number;
  }

  export interface IUpdateMtskuModelListForAdminSetting {
    modelSettings?: IUpdateMtskuModelForAdminSetting[];
  }

  export interface IUpdateMtskuModelForAdminSetting {
    /** mtsku model id */
    modelId?: number;
    /** set this value if need delete model */
    needDelete?: boolean;
    /** model basic setting */
    basicSetting?: IMtskuModelBasicSetting;
    /** reference MtskuModelCustomisedField, by default all allow mtsku customised field won't be sync */
    mtskuModelSyncFieldListSetting?: ISyncFieldListSetting;
  }

  export interface IMtskuModelBasicSetting {
    /** used for shipping fee calculation */
    weight?: number;
    /** dimension */
    dimension?: IItemDimension;
    /** DTS */
    estimatedDays?: number;
    /** seller sku for associated with external system, e.g. seller will use this field to associated with themSelf listing management. */
    sellerSku?: string;
    /** mtsku stock */
    stockListSetting?: IMtskuStockListSetting;
    /** is pre order */
    isPreOrder?: boolean;
    /** tier_index is used to illustrate the item properties */
    tierIndexList?: ITierIndexList;
    /** mtsku model normal price setting */
    priceSetting?: IMtskuPriceSetting;
  }

  export interface IItemDimension {
    width?: number;
    length?: number;
    height?: number;
    unit?: number;
  }

  export interface IMtskuStockListSetting {
    /** MtskuStockListSetting euqal to StockListSetting in mtsku.proto */
    stockSettings?: IMtskuStockSetting[];
  }

  export interface IMtskuStockSetting {
    /** MtskuStockSetting equal to StockSetting in mtsku.proto */
    locationId?: string;
    fulfilmentType?: number;
    sellableStock?: number;
  }

  export interface IMtskuPriceSetting {
    normalPriceSetting?: INormalPriceSetting;
  }

  export interface INormalPriceSetting {
    inputPrice?: number;
    currency?: string;
  }

  export interface ISyncFieldListSetting {
    syncField?: ISyncFieldSetting[];
  }

  export interface ISyncFieldSetting {
    /** reference MtskuItemCustomisedField  MtskuModelCustomisedField */
    field?: number;
    /** shop ids, no need fill if want sync to all of shop */
    shopList?: ISyncShopList;
  }

  export interface ISyncShopList {
    shopId?: number[];
  }

  export interface IUpdateMtskuItemForAdminResponse {
    /** error to sync to mpsku item */
    errMpskuItems?: IErrorMpskuItemInfo[];
    /** mtsku model id */
    mtskuModelIds?: number[];
    /** the mapping with mpsku item */
    mtskuItemMappingEntries?: IMtskuItemMappingEntry[];
    /** record error info details for debug */
    spDebugMsg?: string;
    spErrorCode?: number;
  }

  export interface IErrorMpskuItemInfo {
    mpskuItemId?: number;
    shopId?: number;
    /** reference iis SetItemErrorDetail */
    detail?: IErrorMpskuDetail;
    /** record iis error code */
    errorCode?: number;
    /** if error due to call iis fail, record iis debug message in this field */
    debugMsg?: string;
    /** if error due to product lock */
    productLockInfo?: IProductInfoLock;
    /** true if contains invalid global cat */
    hasInvalidGlobalCat?: boolean;
  }

  export interface IErrorMpskuDetail {
    /** refer to SetCatAndAttrErrorDetail */
    catAndAttr?: ISetMpskuCatAndAttrErrorDetail;
    /** length of new item name, return this value when errcode = ERROR_NAME_LENGTH_LIMIT */
    nameLen?: number;
    /** length limitation of item name, return this value when errcode = ERROR_NAME_LENGTH_LIMIT */
    nameLenLimit?: number;
    /** count of hash tags in item.description,return this value when errcode = ERROR_DESC_HASH_TAG_OVER_LIMIT */
    hashTagCount?: number;
    /** count limitation of hash tags in item.description,return this value when errcode = ERROR_DESC_HASH_TAG_OVER_LIMIT */
    hashTagLimit?: number;
    /** refer to SetLogisticsErrorDetail */
    logistics?: ISetMpskuLogisticsErrorDetail;
    /** count of published items in shop, return this value when errcode = ERROR_REACH_SHOP_ITEM_LIMIT */
    shopItemsCount?: number;
    /** count limitation of published items in shop, return this value when errcode = ERROR_REACH_SHOP_ITEM_LIMIT */
    shopItemsLimit?: number;
    /** min price of all item models, return this value if errcode = ERROR_PRICE_OUT_OF_RANGE */
    priceMin?: number;
    /** max price of all item models,  return this value if errcode = ERROR_PRICE_OUT_OF_RANGE */
    priceMax?: number;
    /** min price of all item models */
    stockMin?: number;
    /** max price of all item models */
    stockMax?: number;
    /** min wholesale price */
    wholesaleMin?: number;
  }

  export interface ISetMpskuCatAndAttrErrorDetail {
    /** length of request attribute value, return this value when errcode = ERROR_ATTRIBUTE_VAL_TOO_LONG */
    attributeValueLen?: number;
    /** length limitation of attribute value, return this value when errcode = ERROR_ATTRIBUTE_VAL_TOO_LONG */
    attributeValueLimit?: number;
  }

  export interface ISetMpskuLogisticsErrorDetail {
    /** requested days_to_ship, return this value when errcode = ERROR_ESTIMATED_DAYS_LIMIT */
    estimateDays?: number;
    /** days_to_ship limitation, return this value when errcode = ERROR_ESTIMATED_DAYS_LIMIT */
    estimateDaysLimit?: number;
    /** day-to-ship min value */
    dtsMin?: number;
    /** data-to-shop max value */
    dtsMax?: number;
  }

  export interface IMtskuItemMappingEntry {
    /** mpsku id */
    mpskuItemId?: number;
    /** mpsku shop id */
    shopId?: number;
    /** mpsku region */
    region?: string;
    mtskuModelMappingEntries?: IMtskuModelMappingEntry[];
    /** reference IIS.MtskuIndexStatus */
    indexStatus?: number;
  }

  export interface IMtskuModelMappingEntry {
    /** mpsku id */
    mtskuModelId?: number;
    /** mpsku model id */
    mpskuModelId?: number;
  }

  export interface IDeleteMtskuModelForAdminRequest {
    itemSetting?: IUpdateMtskuItemForAdminSetting;
    modelListSetting?: IUpdateMtskuModelListForAdminSetting;
    auditParam?: IAuditParam;
    bffMeta?: IRequestMeta;
  }

  export interface IDeleteMtskuModelForAdminResponse {
    /** error to sync to mpsku item */
    errMpskuItems?: IErrorMpskuItemInfo[];
    /** mtsku model id */
    mtskuModelIds?: number[];
    /** the mapping with mpsku item */
    mtskuItemMappingEntries?: IMtskuItemMappingEntry[];
    /** record error info details for debug */
    spDebugMsg?: string;
    spErrorCode?: number;
  }

  export interface ICookie {
    name?: string;
    value?: string;
    domain?: string;
    maxAge?: number;
    path?: string;
    secure?: boolean;
    httpOnly?: boolean;
  }

  export interface IHeader {
    name?: string;
    value?: string;
  }

  export interface ICacheControl {
    noCache?: boolean;
    noStore?: boolean;
    mustRevalidate?: boolean;
    maxAge?: number;
  }

  export interface IUserAgent {
    model?: string;
    brand?: string;
    os?: string;
    browser?: string;
    osVersionMajor?: string;
    osVersionMinor?: string;
  }

  export interface IRequestMeta {
    /** cookies with name and value */
    cookies?: ICookie[];
    /** current user ID of the logged-in user */
    userid?: number;
    /** session_id */
    sessionId?: string;
    /** the real ip of the client who initiate this request */
    clientIp?: string;
    /** TrackingPlatformType, used by a few backend services */
    platform?: number;
    /** FE requested URL */
    url?: string;
    userAgent?: string;
    /** encrypted sso_token */
    ssoToken?: string;
    /** decrypted token used by backend service, such as coreserver */
    shopeeToken?: string;
    /** cookie key is REC_T_ID */
    trackingSessionId?: string;
    /** configured cid of this gateway instance, "SG", "ID", "TH", and etc. */
    country?: string;
    /** this flag informs BFF to downgrade */
    downgrade?: boolean;
    /** i18n language, "en", "sg", "zhHans", "ms_my", and etc. */
    language?: string;
    /** GeoIP country code */
    clientIpCountry?: string;
    /** fingerprint used for tracking and anti-fraud */
    deviceFingerprint?: string;
    /** app version, eg. "25001" */
    appVersion?: string;
    /** RN version, eg. "4015012" */
    rnVersion?: string;
    /** current shop ID of the logged-in user */
    shopid?: number;
    /** 1 for Shopee, and 2 for Shopee Pay Merchant */
    appType?: number;
    /** client_id for cookie 'SPC_CLIENTID' */
    clientId?: string;
    /** packing the platform information in to an integer flag */
    clientPlatform?: number;
    /** the flag indicates that whether the request is from xiapibuy */
    isFromXiapibuy?: boolean;
    /** Referer header value */
    referer?: string;
    /** list of ip obtained from X-Forwarded-For header */
    forwardedFor?: string[];
    /** X-Real-Ip header value */
    realIp?: string;
    /** SPC_DID cookie, 32-byte random string */
    deviceid?: string;
    /** business account userid */
    tobUserid?: number;
    /** business account token returned from account shared service */
    tobShopeeToken?: string;
    /** business account business_id */
    tobBusinessId?: number;
    /** SPC_B_SI cookie, business account session identifier */
    tobSessionId?: string;
    /** crawler flag to indicate if request comes from crawler */
    crawler?: boolean;
    /** rn_bundle_version is from cookie SPC_RNBV */
    rnBundleVersion?: string;
    /** admin_region is the region for which some admin service resource is required */
    adminRegion?: string;
    /** soup_email is the email of the soup authenticated user */
    soupEmail?: string;
    /** User Agent */
    userAgentInfo?: IUserAgent;
    /** http/https request */
    isFromHttps?: boolean;
    /** request host */
    host?: string;
    /** this flag indicates whether or not the request comes from office IP */
    isFromOfficeIp?: boolean;
    /** anti-fraud verification */
    isSignatureVerificationFailed?: boolean;
    /** the SOUP Object Code this request was authorised against and is for */
    soupObjectCode?: string;
  }

  export interface IResponseMeta {
    /** set cookies to FE response header */
    cookies?: ICookie[];
    /** set to return raw data instead of json */
    contentType?: string;
    /** set cache control header values from BFF response */
    cacheControl?: ICacheControl;
    /** set headers */
    headers?: IHeader[];
    /** set HTTP status code */
    statusCode?: number;
  }

  export interface IReqApplicationHeaderWrapper {
    bffMeta?: IRequestMeta;
    disableCache?: boolean;
  }

  export interface IResApplicationHeaderWrapper {
    bffMeta?: IResponseMeta;
  }

  export interface IGetVirtualCategoryListRequest {
    needSizeChartCount?: boolean;
    categoryId?: number;
    shopId?: number;
  }

  export interface IGetVirtualCategoryListResponse {
    data?: IGetVirtualCategoryListData;
    spErrorCode?: number;
    spDebugMsg?: string;
  }

  export interface IGetVirtualCategoryListData {
    list?: IShopVirtualCategory[];
  }

  export interface IShopVirtualCategory {
    virtualCategoryId?: number;
    name?: string;
    displayName?: string;
    sizeChartCount?: number;
  }

  export interface IGetConvertedSizeChartListWithPagingRequest {
    status?: number;
    virtualCategoryId?: number;
    pageNumber?: number;
    pageSize?: number;
    shopId?: number;
    sizeChartId?: number;
    region?: string;
  }

  export interface IGetConvertedSizeChartListWithPagingResponse {
    data?: IGetConvertedSizeChartListWithPagingData;
    spErrorCode?: number;
    spDebugMsg?: string;
  }

  export interface IGetConvertedSizeChartListWithPagingData {
    list?: ISizeChartDetail[];
    pageInfo?: IPageInfo;
  }

  export interface ISizeChartDetail {
    sizeChartId?: number;
    name?: string;
    accuracy?: number;
    imageSizeChart?: string;
    status?: number;
    virtualCategoryId?: number;
    table?: ISizeChartTable;
    linkedItemNum?: number;
  }

  export interface IPageInfo {
    total?: number;
    pageNumber?: number;
    pageSize?: number;
    cursor?: string;
  }

  export interface IColumn {
    /** column header */
    header?: IMeasurementHeader;
    /** column cells */
    cells?: ICell[];
  }

  export interface IMeasurementHeader {
    /** measurement id */
    measurementId?: number;
    /** measurement name */
    name?: string;
    /** 1: normal, 2: size convention */
    type?: number;
    /** 1: dropdown, 2: single input number 3; range input number */
    inputType?: number;
    /** unit of column, if uint is null, use table unit */
    unit?: string;
    /**  */
    displayNames?: IMultiLangList;
    isKeyMeasurement?: boolean;
  }

  export interface ICell {
      /** DropDownOption, RangeInputNumber, SingleInputNumber */
      dropDownValue?: IDropDownOption;
      /** DropDownOption, RangeInputNumber, SingleInputNumber */
      rangeInputValue?: IRangeInputNumber;
      /** DropDownOption, RangeInputNumber, SingleInputNumber */
      singleInputValue?: ISingleInputNumber;
  }

  export interface IDropDownOption {
    /** attribute value id if measurement is size covetion */
    attrValueId?: number;
    value?: string;
    /** multiple language display */
    displayValues?: IMultiLangList;
  }

  export interface IRangeInputNumber {
    /** true value multi 100000 */
    begin?: number;
    /** true value multi 100000 */
    end?: number;
  }

  export interface ISingleInputNumber {
    /** true value multi 100000 */
    value?: number;
  }

  export interface IConfirmConvertedSizeChartRequest {
    sizeChartIdList?: number[];
    shopId?: number;
    bffMeta?: IRequestMeta;
  }

  export interface IConfirmConvertedSizeChartResponse {
    data?: IConfirmConvertedSizeChartData;
    spErrorCode?: number;
    spDebugMsg?: string;
  }

  export interface IConfirmConvertedSizeChartData {
    failedSizeChartList?: IFailedSizeChart[];
  }

  export interface IFailedSizeChart {
    sizeChartId?: number;
    mpskuItemIdList?: IFailedItem[];
  }

  export interface IFailedItem {
    mpskuItemId?: number;
    errorMsg?: string;
  }

  export interface IGetSizeChartDetailRequest {
    sizeChartId?: number;
    virtualCategoryId?: number;
    categoryId?: number;
    shopId?: number;
  }

  export interface IGetSizeChartDetailResponse {
    data?: ISizeChartDetail;
    spErrorCode?: number;
    spDebugMsg?: string;
  }

  export interface IUpdateSizeChartRequest {
    sizeChartId?: number;
    name?: string;
    table?: ISizeChartTable;
    shopId?: number;
    bffMeta?: IRequestMeta;
  }

  export interface IUpdateSizeChartResponse {
    spErrorCode?: number;
    spDebugMsg?: string;
  }

  export interface IDeleteSizeChartRequest {
    sizeChartId?: number;
    shopId?: number;
    bffMeta?: IRequestMeta;
  }

  export interface IDeleteSizeChartResponse {
    spErrorCode?: number;
    spDebugMsg?: string;
  }

  export interface IGetVirtualCategoryWithMeasurementRequest {
    virtualCategoryId?: number;
    region?: string;
  }

  export interface IGetVirtualCategoryWithMeasurementResponse {
    spErrorCode?: number;
    spDebugMsg?: string;
    data?: IGetVirtualCategoryDetailData;
  }

  export interface IGetVirtualCategoryDetailData {
    virtualCategoryId?: number;
    name?: string;
    unitList?: string[];
    linkedCategoryIdList?: number[];
    measurementList?: IMeasurement[];
  }

  export interface IMeasurement {
    id?: number;
    name?: string;
    type?: number;
    inputType?: number;
    sortWeight?: number;
    displayName?: string;
    isMandatory?: boolean;
    valueList?: IMeasurementValue[];
    unitList?: IMeasurementUnit[];
  }

  export interface IMeasurementValue {
    attrValueId?: number;
    value?: string;
    displayName?: string;
  }

  export interface IMeasurementUnit {
    unit?: string;
    isSpecial?: boolean;
    inputConstrain?: IUnitInputConstrain;
  }

  export interface IUnitInputConstrain {
    limitMin?: number;
    limitMax?: number;
  }

  export interface ISizeChartTableColumn {
    header?: ISizeChartHeader;
    cellList?: ISizeCharCell[];
  }

  export interface ISizeChartHeader {
    measurementId?: number;
    name?: string;
    type?: number;
    inputType?: number;
    unit?: string;
    displayName?: string;
  }

  export interface ISizeCharCell {
    attrValueId?: number;
    value?: string;
    minValue?: string;
    maxValue?: string;
    displayName?: string;
  }

  export interface ISizeChartTable {
    unit?: string;
    columnList?: ISizeChartTableColumn[];
  }

  export interface ICheckSizeChartForEditRequest {
    sizeChartId?: number;
    name?: string;
    shopId?: number;
  }

  export interface ICheckSizeChartForEditResponse {
    spErrorCode?: number;
    spDebugMsg?: string;
    data?: ICheckSizeChartForEditData;
  }

  export interface ICheckSizeChartForEditData {
    fieldErrorList?: IFieldError[];
  }

  export interface IFieldError {
    fieldType?: number;
    errorDetail?: IErrorDetail;
  }

  export interface IErrorDetail {
    errorCode?: number;
  }

  export interface IGetStockBatchWithLatestBreakdownRequest {
    items?: IItemModelPromotionId[];
  }

  export interface IItemModelPromotionId {
    promotionType?: number;
    promotionId?: number;
    /** Deprecated */
    shopId?: number;
    itemId?: number;
    modelId?: number;
  }

  export interface IGetStockBatchWithLatestBreakdownResponse {
    spErrorCode?: number;
    spDebugMsg?: string;
    debugMsg?: string;
    /** List of item stocks that contains */
    /** - General stock info */
    /** - Location breakdown for each stock */
    stocks?: IItemStock[];
  }

  export interface IItemStock {
    shopId?: number;
    itemId?: number;
    modelId?: number;
    promotionId?: number;
    promotionType?: number;
    stock?: number;
    /** Max reserved stock for a promotion. */
    allocatedStock?: number;
    stockoutTime?: number;
    /** Only available when promotion_type == 0, refer to StockType enum. */
    stockType?: number;
    stockBreakdownByLocation?: IStockBreakdownByLocations[];
    blockedFulfilmentType?: number;
    settingType?: number;
  }

  export interface IStockBreakdownByLocations {
    locationId?: string;
    availableStock?: number;
    fulfilmentType?: number;
    allocatedStock?: number;
    isEnabled?: boolean;
    status?: number;
    /** Indicate if the breakdown can be discarded. */
    /** For child vsku (both weak and reserved) which has parents that don't share any common location, */
    /** an empty breadown will be given and it is discardable. */
    isDiscardable?: boolean;
  }

  export interface IUpdateModelSettingV2Request {
    region?: string;
    modelsSetting?: IModelSettingV2[];
    auditParam?: IAudit;
    bffMeta?: IRequestMeta;
  }

  export interface IModelSettingV2 {
    shopId?: number;
    itemId?: number;
    modelId?: number;
    blockedFulfilmentType?: number;
    locationSettings?: ILocationSetting[];
    dayToShip?: number;
    isPreOrder?: boolean;
  }

  export interface ILocationSetting {
    locationId?: string;
    /** either WHS or SELLER, internal use, no need to pass */
    fulfilmentType?: number;
    isEnabled?: boolean;
  }

  export interface IAudit {
    /** Operator email */
    /** Required */
    operator?: string;
    /** Reason for the action */
    /** Optional */
    reason?: string;
    refEntityId?: string;
    auditType?: number;
    source?: string;
  }

  export interface IUpdateModelSettingV2Response {
    debugMsg?: string;
    failedEntries?: IFailedEntry[];
    spErrorCode?: number;
    spDebugMsg?: string;
  }

  export interface IFailedEntry {
    /** required */
    modelId?: number;
    /** required */
    reason?: string;
  }

  export interface IGetEntityAttrValRequest {
    entity?: IEntity;
    region?: string;
    tagName?: string;
    attrKey?: string;
    queryFlag?: number;
  }

  export interface IEntity {
    id?: number;
    /** length <= 8 */
    type?: string;
  }

  export interface IGetEntityAttrValResponse {
    error?: number;
    errorMsg?: string;
    entity?: IEntity;
    value?: IAttrValue;
    spErrorCode?: number;
    spDebugMsg?: string;
  }

  export interface IAttrValue {
    /** best practice: "business.group.attr" */
    attrKey?: string;
    /** we can't use google.protobuf.Struct or google.protobuf.Value */
    attrValue?: string;
    /** octet for single value, json for structured value */
    valueType?: string;
    /** the tag that this attr belongs to */
    tagName?: string;
    /** the tag set that this attr belongs to */
    tagSet?: string[];
    /** the weight of the attribute, */
    /** if tag_name is `default`, any other attribute can overwrite */
    /** the current attribute. */
    /** otherwise, attribute with larger weight overwrites smaller ones */
    weight?: number;
  }

  export interface IGetItemListByItemIdsRequest {
    /** required. item_ids size can be configured via batch_query_size_limit_config */
    itemIds?: number[];
    /** optional. if false, then will not return item with status == 0 */
    needDeleted?: boolean;
    /** optional. if true, then query the models under the item */
    needModels?: boolean;
    /** optional. if true, resp.item.extinfo will be nil */
    ignoreExtinfo?: boolean;
    /** optional. default value false means do the fe cat filtering and return normal-fe cat list only, if true no need to do the fe cat status filtering */
    needAllFeCats?: boolean;
    /** if true, description field in returned items will be nil, and response will be faster */
    ignoreDescription?: boolean;
    /** optional. default value is false, means won't return overlay image info */
    needOverlayImage?: boolean;
    /** optional. default value is false */
    needOptimizedName?: boolean;
    /** if true,customized field would be returned on item info api */
    needCustomizedTaxInfo?: boolean;
    /** default value is false. If set as true, will return all additional info, including overlay image, auto title, highlight video and so on */
    needAdditionalInfo?: boolean;
  }

  export interface IGetItemListByItemIdsResponse {
    /** record error info details */
    debugMsg?: string;
    /** if item not found, only fill itemid and shopid, item.status == nil */
    items?: IItem[];
    /** if req.need_models == true, then return this list. It's the same order as items */
    models?: IItemModelList[];
    /** length is same as request. And if item not found, then corresponding ItemAdditionalInfo would be empty structure (i.e. no item_id) */
    additionalInfos?: IItemAdditionalInfo[];
    spErrorCode?: number;
    spDebugMsg?: string;
  }

  export interface IItemModelList {
    models?: IItemModelSingle[];
  }

  export interface IItemModelSingle {
    /** This is required */
    modelid?: number;
    /** This is required */
    itemid?: number;
    name?: string;
    /** This is required */
    price?: number;
    currency?: string;
    /** This is required */
    stock?: number;
    /** model status, by default only NORMAL(1) status could be viewed for seller and buyer */
    status?: number;
    priceBeforeDiscount?: number;
    promotionid?: number;
    rebatePrice?: number;
    sold?: number;
    /** ref: ItemModelExtInfo */
    extinfo?: string;
    ctime?: number;
    mtime?: number;
    sku?: string;
  }

  export interface IItemAdditionalInfo {
    itemId?: number;
    /** overlay images are introduced in SPLT-2948 */
    overlayImages?: IOverlayImage[];
    /** optimized names are introduced in SPLT-3541 */
    optimizedNames?: IOptimizedName[];
    /** deprecated */
    video?: IHighlightVideo;
    /** highlight video is introduced in SPLT-3592 */
    videos?: IHighlightVideo[];
  }

  export interface IOverlayImage {
    /** overlay image hash value */
    overlayImage?: string;
    /** overlay imag ids, and used for FE event tracking */
    overlayIds?: string[];
  }

  export interface IOptimizedName {
    nameValue?: string;
    nameId?: string;
  }

  export interface IHighlightVideo {
    duration?: number;
    /** video version, refer to ItemVideoVersion. Hard code to 2 (i.e. MMS) */
    version?: number;
    /** MMS video id for version 2. Highlight video only uses MMS */
    vid?: string;
  }

  export interface IGetLicensesForItemRequest {
    itemId?: number;
    nextOffset?: string;
    limit?: number;
  }

  export interface IGetLicensesForItemResponse {
    debugMsg?: string;
    licenses?: ILicenseWithMetaInfo[];
    nextOffset?: string;
    hasMore?: boolean;
    spErrorCode?: number;
    spDebugMsg?: string;
  }

  export interface ILicenseWithMetaInfo {
    itemId?: number;
    shopId?: number;
    region?: string;
    uploadTime?: number;
    license?: ILicense;
  }

  export interface ILicense {
    licenseMd5?: string;
  }

  export interface IGetComplaintAddressRequest {
    shopid?: number;
    addressId?: number;
    region?: string;
  }

  export interface IGetComplaintAddressResponse {
    complaintAddress?: IBuyerAddress;
    debugMsg?: string;
    errorMsg?: string;
    spErrorCode?: number;
    spDebugMsg?: string;
  }

  export interface IBuyerAddress {
    id?: number;
    userid?: number;
    name?: string;
    phone?: string;
    country?: string;
    state?: string;
    city?: string;
    address?: string;
    status?: number;
    ctime?: number;
    mtime?: number;
    zipcode?: string;
    /** deprecated */
    deftime?: number;
    fullAddress?: string;
    /** newly added fields used for different level address */
    district?: string;
    town?: string;
    /** last recent logistics success status, 0-default, 1-success, 2-failed */
    logisticsStatus?: number;
    icno?: string;
    extinfo?: IBuyerAddressExtInfo;
    geolocation?: IGeoLocation;
  }

  export interface IBuyerAddressExtInfo {
    /** json for Go-Jek geo location */
    geoinfo?: string;
    userLocationId?: number;
    /** ref: PreferredDeliveryOption */
    preferredDeliveryOption?: number;
    deliveryInstruction?: IDeliveryInstruction;
    label?: string;
    addressInstruction?: string;
    /** id defined by SLS to specify the actual timeslot */
    deliveryTimeslotId?: number;
    placeId?: string;
    /** ref: AddressClientInfoType */
    clientId?: number;
    stateCode?: string;
    /** ref: AddressLabelType */
    labelId?: number;
  }

  export interface IGeoLocation {
    latitude?: number;
    longitude?: number;
    /** User/POI/Geocoding */
    source?: string;
    /** deprecated - result for this API would be stale. We'll remove it in future */
    /** Formatted address of the returned geolocation (for POI and geocoding) */
    formattedAddress?: string;
    /** 0 - Unvalidated, 1 - Validated (for POI and geocoding) */
    locationConfidence?: boolean;
    /** deprecated */
    locationType?: number;
    /** for these 2 fields, only meaningful if source=User */
    userAdjusted?: boolean;
    geoinfoConfirm?: boolean;
  }

  export interface IDeliveryInstruction {
    authorizationToLeaveParcel?: boolean;
    directionMessage?: string;
    /** deprecated */
    foodyAddressInstruction?: string;
  }

  export interface IGetForStockAuditsRequest {
    /** required */
    itemId?: number;
    /** optional */
    filter?: IFilterCriteria;
    /** required */
    startTime?: number;
    /** optional corresponds to offset/limit/offset_list */
    page?: IPaginateReqInfo;
    /** required if not passed through context */
    region?: string;
  }

  export interface IGetForStockAuditsResponse {
    debugMsg?: string;
    audits?: IStockAudit[];
    /** indicates if there is any more data for the given range/filter */
    page?: IPaginateRespInfo;
    startTime?: number;
    spErrorCode?: number;
    spDebugMsg?: string;
  }

  export interface IGetMtskuRStockBreakdownByLocationRequest {
    skuId?: number;
    locationId?: string;
    isFloating?: boolean;
  }

  export interface IGetMtskuRStockBreakdownByLocationResponse {
    debugMsg?: string;
    stockBreakdowns?: IMtskuRStockBreakdown[];
    spErrorCode?: number;
    spDebugMsg?: string;
  }

  export interface IMtskuRStockBreakdown {
    ruleId?: number;
    modelId?: number;
    stock?: number;
    stockType?: number;
  }

  export interface IGetMtskuByConditionForAdminRequest {
    /** query condition */
    queryCondition?: IMtskuQueryConditionForAdmin;
    /** if not specified, we will use a configured default. */
    limit?: number;
    /** Offset to skip over some data, used for pagination. */
    /** For the first query, client should not set this field. If the response of */
    /** first query has non empty `offset`, then, to get next page, client should */
    /** send the same query, but set this field to the offset from  first page */
    /** response. */
    offset?: string;
    /** reference MtskuQueryOrderByField */
    orderBy?: number;
    /** default asc */
    orderDesc?: boolean;
  }

  export interface IMtskuQueryConditionForAdmin {
    /** merchant id */
    merchantId?: number;
    /** mtsku item id */
    mtskuItemId?: number;
    /** reference MtSkuStatus */
    status?: number;
    /** seller sku */
    sellerSku?: string;
    /** mpsku_item_id */
    mpskuItemId?: number;
    /** mtsku item name */
    mtskuItemName?: string;
  }

  export interface IGetMtskuByConditionForAdminResponse {
    /** mtsku information */
    mtskuList?: IMtskuItemForAdmin[];
    /** used for next scan */
    offset?: string;
    /** if query end */
    hasNext?: boolean;
    /** total record */
    total?: number;
    /** record error info details for debug */
    debugMsg?: string;
    spErrorCode?: number;
    spDebugMsg?: string;
  }

  export interface IMtskuItemForAdmin {
    /** mtsku id */
    mtskuItemId?: number;
    /** mtsku basic information */
    mtskuBasic?: IMtskuItemBasic;
    /** mtsku description */
    description?: string;
    /** mtsku model list */
    mtskuModels?: IMtskuModelForAdmin[];
    /** statistics information */
    statistics?: IMtskuItemStatistics;
    /** merchant name */
    merchantName?: string;
    /** cat information */
    cats?: ICategoryPathInformation[];
    /** global attribute information */
    globalAttributeValues?: IGlobalAttributeValue[];
    /** global brand information */
    globalBrand?: IGlobalBrandV2;
    /** new structure to return rich text desc */
    mtskuItemRichTextDescription?: IItemRichTextDescription;
  }

  export interface IMtskuItemBasic {
    /** merchant id */
    merchantId?: number;
    /** mtsku name */
    name?: string;
    /** mtsku images */
    imageList?: string[];
    /** reference MtSkuStatus */
    status?: number;
    /** mtsku created time */
    createTime?: number;
    /** this time will update if any field change */
    updateTime?: number;
    /** same as item.condition */
    condition?: number;
    /** size chart */
    sizeChart?: string;
    /** tier variation list */
    tierVariation?: ITierVariationV2;
    /** seller sku for associated with external system, e.g. seller will use this field to associated with themSelf listing management. */
    sellerSku?: string;
    /** category information */
    cats?: ICategoryPath[];
    /** mtsku item level price */
    price?: IMtskuItemAggregatedPrice;
    /** mtsku item level stock */
    stock?: IMtskuItemAggregatedStock;
    /** mtsku create time ms */
    createTimeMs?: number;
    /** mtsku update time ms */
    updateTimeMs?: number;
    /** video information */
    videoLists?: IVideoInfoList[];
    /** means turn off if haven't return that toggle */
    bitToggles?: IBitToggle[];
    /** global tree validation info */
    globalTreeValidation?: IGlobalTreeValidation;
    brand?: IBrand;
    attribute?: IMtAttribute;
  }

  export interface IMtAttribute {
    attrs?: IAttributeValueV2[];
  }

  export interface IAttributeValueV2 {
    /** attribute id */
    attrId?: number;
    /** attribute value id */
    attrValId?: number;
    /** // customised_value is only used for non-quantitative user customised val */
    customisedValue?: string;
    /** raw val without unit; only used for quantitative customised val */
    rawVal?: string;
    /** for VAL_WITH_UNIT; only used for quantitative customised val */
    unit?: string;
    /** who sets this item attr value */
    source?: string;
    /** confidence score */
    confidenceScore?: number;
  }

  export interface IBrand {
    brandId?: number;
  }

  export interface IGlobalTreeValidation {
    /** used by get APIs and pipelines */
    /** CB, Local */
    type?: number;
    regions?: string[];
  }

  export interface IBitToggle {
    /** item level toggle refer MtskuItemToggleBit, model level toggle refer MtskuModelToggleBit */
    toggleBit?: number;
    /** true means turn on; off means turn off */
    turnOn?: boolean;
  }

  export interface IMtskuItemAggregatedPrice {
    /** The lowest price between all the MTSKU-models under this MTSKU-item */
    priceMin?: number;
    /** The highest price between all the MTSKU-models under this MTSKU-item */
    priceMax?: number;
    /** all the MTSKU-models should has same currency */
    currency?: string;
  }

  export interface IMtskuItemAggregatedStock {
    /** The total sellable stock of all the MTSKU-models under this MTSKU-item */
    totalSellableStock?: number;
  }

  export interface ICategoryPath {
    /** Sequence is: Parent category -> sub category -> sub-sub category ... */
    catIds?: number[];
    /** the region of cat, use WW for global region */
    region?: string;
    /** input source, 0:seller input, 1:ds model recommendation */
    source?: number;
    /** not nil if source  == 1 */
    dsInformation?: ICategoryDSInformation;
  }

  export interface ITierVariationV2 {
    tierModels?: ITierModel[];
    /** one tier property may have multiple value, keep same order with tier_variations */
    tierProperties?: ITierPropertyV2[];
  }

  export interface ITierModel {
    /** model id */
    mtskuModelId?: number;
    /** tier_index is used to illustrate the item properties */
    tierIndices?: number[];
  }

  export interface ITierPropertyV2 {
    name?: string;
    /** one property may have multiple value */
    tierValues?: ITierVariationValue[];
  }

  export interface IMtskuModelForAdmin {
    /** mtsku model basic information */
    mtskuModel?: IMtskuModel;
    /** mpsku model basic information */
    mpskuModelList?: IMpskuModelBasicInfo[];
  }

  export interface IMtskuItemStatistics {
    /** The total sold count of all MTSKU-models */
    soldCount?: number;
  }

  export interface ICategoryPathInformation {
    /** Sequence is: Parent category -> sub category -> sub-sub category ... */
    catIds?: number[];
    /** keep same sequence with cat_ids */
    catNames?: string[];
    /** the region of cat, use WW for global region */
    region?: string;
  }

  export interface IGlobalAttributeValue {
    /** this structure reference item.attribute.ItemAttrWithValue */
    /** global_attr here can be set or not with this item. */
    /** we still return the `non-mapped global_attr` here because DEEP team require the `empty value` as well. */
    /** so we return all attributes which "can be mapped" with this item */
    /** `empty value` is the value of of unmapped attribute, then `attr_value` will be nil */
    globalAttr?: IGlobalAttr;
    /** - attr_value.id=0 if it is a customised value. */
    /** + attr_value.value: formatted value, in this case we will calculate data based on user_customised_value */
    /** - attr_value = nil if this is a empty value */
    attrValue?: IAttrValueV2;
    extInfo?: IMtskuAttrValueExtInfo;
  }

  export interface IAttrValueV2 {
    id?: number;
    /** full formatted val, including units if present */
    value?: string;
    globalAttrId?: number;
    status?: number;
    /** this can only be updated via UpdateAttrValueLocalSettingRequest; */
    localSetting?: IAttrValueLocalSettingList;
    extInfo?: IAttrValueExtInfo;
    /** a list of cb display names in different languages */
    cbDisplayNames?: IMultiLangListV2;
    ctime?: number;
    mtime?: number;
  }

  export interface IAttrValueLocalSettingList {
    settingList?: IAttrValueLocalSetting[];
  }

  export interface IAttrValueLocalSetting {
    region?: string;
    multiLangNames?: IMultiLangListV2;
    /** ref: LocalStatus */
    status?: number;
  }

  export interface IAttrValueExtInfo {
    /** raw val without unit */
    rawVal?: string;
    /** for VAL_WITH_UNIT */
    unit?: string;
    /** for linking an attr val to other similar vals under the same attr */
    similarValList?: ISimilarValueList;
  }

  export interface ISimilarValueList {
    attrValueId?: number[];
  }

  export interface IMtskuAttrValueExtInfo {
    /** raw val without unit; only used for quantitative customised val */
    rawVal?: string;
    /** for VAL_WITH_UNIT; only used for quantitative customised val */
    unit?: string;
    /** // customised_value is only used for non-quantitative user customised val */
    customisedValue?: string;
    /** who sets this item attr value */
    source?: string;
    /** we keep this as int32 as we may need to use negative numbers in the future. */
    /** e.g. use negative number to represent special confidence score, such as -1 to indicate highest priority */
    /** confidence score, inflated by 10^5 */
    confidenceScore?: number;
  }

  export interface IGlobalAttr {
    id?: number;
    name?: string;
    status?: number;
    /** ref: AttrType; cannot be updated */
    attrType?: number;
    /** ref: AttrInputType */
    inputType?: number;
    /** ref: AttrInputValidatorType; cannot be updated */
    inputValidator?: number;
    ctime?: number;
    mtime?: number;
    /** this can only be updated via UpdateAttrLocalSettingRequest; return only for local request */
    localSetting?: IAttrLocalSettingList;
    /** ref: AttrFormatType */
    formatType?: number;
    extInfo?: IAttrExtInfo;
    /** a list of cb display names in different languages */
    cbDisplayNames?: IMultiLangListV2;
  }

  export interface IAttrLocalSettingList {
    settingList?: IAttrLocalSetting[];
  }

  export interface IAttrLocalSetting {
    region?: string;
    multiLangNames?: IMultiLangListV2;
  }

  export interface IAttrExtInfo {
    /** for FORMAT_QUANTITATIVE_WITH_UNIT */
    unitList?: IAttrUnitList;
    /** this be set when validation=VALIDATOR_DATE. refer: AttrDateTimeFormat */
    datetimeFormat?: number;
  }

  export interface IAttrUnitList {
    /** for FORMAT_QUANTITATIVE_WITH_UNIT */
    units?: string[];
  }

  export interface IMultiLangListV2 {
    multiLangValues?: IValWithLangV2[];
  }

  export interface IValWithLangV2 {
    lang?: string;
    value?: string;
  }

  export interface IGlobalBrandV2 {
    /** brand id */
    brandId?: number;
    brandName?: string;
    /** ref: item.global_brand.GlobalBrandStatus */
    brandStatus?: number;
    settingList?: IGlobalBrandLocalSetting[];
  }

  export interface IGlobalBrandLocalSetting {
    region?: string;
    /** ref: GeneralStatus */
    localStatus?: number;
    displayName?: string;
    /** this info is also saved in BrandSynonym table to support search by synonyms */
    synonyms?: string[];
  }

  export interface IItemRichTextDescription {
    richTextDescriptionVersion?: number;
    isRichTextDescription?: boolean;
    description?: string;
    richTextDescriptionParagraphList?: IItemRichTextDescriptionParagraph[];
  }

  export interface IMtskuModel {
    /** mtsku model id */
    mtskuModelId?: number;
    /** mtsku model basic */
    mtskuModelBasic?: IMtskuModelBasic;
    /** come from ips */
    stock?: IStockV2;
    /** The price info of model */
    price?: IPrice;
    /** statistics information */
    statistics?: IMtskuModelStatistics;
  }

  export interface IMtskuModelStatistics {
    /** The total sold count of all linked MPSKU-models in all markets */
    soldCount?: number;
  }

  export interface IPrice {
    normalPrice?: INormalPrice;
  }

  export interface INormalPrice {
    inputPrice?: number;
    currency?: string;
  }

  export interface IStockV2 {
    skuStockData?: ISkuStockDatum[];
    floatingStock?: number;
  }

  export interface IMtskuModelBasic {
    /** mtsku model created time */
    createTime?: number;
    /** this time will update if any field change */
    updateTime?: number;
    /** used for shipping fee calculation */
    weight?: number;
    /** dimension */
    dimension?: IItemDimension;
    /** DTS */
    estimatedDays?: number;
    /** seller sku for associated with external system, e.g. seller will use this field to associated with themSelf listing management. */
    sellerSku?: string;
    /** is pre order */
    isPreOrder?: boolean;
    /** composed by tier variation option name */
    name?: string;
    /** means turn off if haven't return that toggle */
    bitToggles?: IBitToggle[];
  }

  export interface IMpskuModelBasicInfo {
    mpskuModelId?: number;
    region?: string;
    status?: number;
  }

  export interface IDeleteMtskuItemRequest {
    /** mtsku-item to be deleted */
    mtskuItemId?: number;
    /** for audit log */
    auditParam?: IAuditParam;
    bffMeta?: IRequestMeta;
  }

  export interface IDeleteMtskuItemResponse {
    /** error to delete mpsku item info */
    errMpskuItems?: IErrorMpskuItemInfo[];
    /** record error info details for debug */
    debugMsg?: string;
    spErrorCode?: number;
    spDebugMsg?: string;
  }

  export interface IPrivilDeleteItemRequest {
    itemId?: number;
    shopId?: number;
    /** params for audit log */
    auditParam?: IAuditParam;
    /** this is required for admin_gateway */
    operator?: string;
    bffMeta?: IRequestMeta;
  }

  export interface IPrivilDeleteItemResponse {
    /** record error info details for debug */
    debugMsg?: string;
    /** record reason for item edit lock */
    productInfoLock?: IProductInfoLock;
    spErrorCode?: number;
    spDebugMsg?: string;
  }

  export interface IPrivilUnlistItemRequest {
    itemId?: number;
    shopId?: number;
    /** if unlist == true, unlist item; if unlist == false, publish unlisted item */
    unlist?: boolean;
    /** params for audit log */
    auditParam?: IAuditParam;
    /** this is required for admin gateway */
    operator?: string;
    bffMeta?: IRequestMeta;
  }

  export interface IPrivilUnlistItemResponse {
    errDetail?: ISetItemUnlistErrDetail;
    /** record error info details */
    debugMsg?: string;
    /** record reason for item edit lock */
    productInfoLock?: IProductInfoLock;
    spErrorCode?: number;
    spDebugMsg?: string;
  }

  export interface ISetItemUnlistErrDetail {
    shopItemsCount?: number;
    shopItemsLimit?: number;
  }

  export interface INewTaskRequest {
    filePath?: string;
    massEditType?: string;
    originalFilename?: string;
    extraData?: string;
    region?: string;
    bffMeta?: IRequestMeta;
  }

  export interface INewTaskResponse {
    spErrorCode?: number;
    spDebugMsg?: string;
    taskId?: number;
  }

  export interface IGetTasksRequest {
    massEditTypes?: string[];
    region?: string;
    offset?: number;
    limit?: number;
    dryrunStatus?: number[];
  }

  export interface IGetTasksResponse {
    spErrorCode?: number;
    spDebugMsg?: string;
    data?: ITaskData[];
    hasMore?: boolean;
    totalCount?: number;
  }

  export interface ITaskData {
    taskId?: number;
    ctime?: number;
    mtime?: number;
    massEditType?: string;
    operator?: string;
    inputFilename?: string;
    outputFilename?: string;
    /** 0 Pending; 1 Processing; 2 Done; 3 Error 4 Stopped */
    taskStatus?: number;
    extraData?: string;
    region?: string;
    originalFilename?: string;
    /** 1: Normal Task, 2: Dryrun Task */
    dryrunStatus?: number;
  }

  export interface IGetProgressRequest {
    taskIds?: number[];
  }

  export interface IGetProgressResponse {
    spErrorCode?: number;
    spDebugMsg?: string;
    progress?: number[];
  }

  export interface IStopTaskRequest {
    taskId?: number;
    massEditType?: string;
    bffMeta?: IRequestMeta;
  }

  export interface IStopTaskResponse {
    spErrorCode?: number;
    spDebugMsg?: string;
  }

  export interface ITimeRange {
    /** The start of time range, in Unix second since Epoch */
    start?: number;
    /** The end of time range, in Unix second since Epoch */
    end?: number;
  }

  export interface IGetTagRequest {
    /** Country of the tag. */
    country?: string;
    /** List of tag ID to query. */
    /**  */
    /** If you already known the tag ID, don't need additional filter, then most */
    /** of the time, you can leave other filter fields nil (except country). */
    ids?: number[];
    /** Available sort fields: */
    /**  */
    /** - `id` */
    /** - `name` */
    /** - `status` */
    /** - `type` */
    /** - `level` */
    /** - `start_time` */
    /** - `end_time` */
    /** - `created_time` */
    /** - `updated_time` */
    /**  */
    /** See common docs for how to use `sorts`. */
    sorts?: string[];
    /** See common docs for `page_size`. */
    pageSize?: number;
    /** See common docs for `seek_point`. */
    seekPoint?: string;
    /** Pattern to find tag by name, optional. */
    /**  */
    /** - Pattern is case insensitive. E.g: `shopee24h` matches with */
    /** `Shopee24H` and `SHOPEE24H`. */
    /** - We support `*` wildcard. The character `*` in the pattern can be */
    /** match with 0 or more character. E.g: `shopee*` matches `shopee24h` and */
    /** `shopee4h`. `item*service*` matches with `item-tag-service-test` and */
    /** `item-info-service-live`. */
    /**  */
    /** Note that for performance, it is not recommended to have a non empty prefix */
    /** before the first wildcard. I.e: this is invalid pattern `*24h`. This */
    /** restriction might be implemented in the future. */
    namePattern?: string;
    /** Set of tag statuses caller want to see. */
    /**  */
    /** - If this is empty, we will return only those with TAG_NORMAL status. */
    /** - If this is not empty, we will fail the request if any of them is invalid */
    /** value, otherwise, we will return those with status is one of the specified */
    /** values. */
    statuses?: number[];
    /** Set of tag levels caller want to see. */
    /**  */
    /** - If this is empty, we will not apply any default value to filter the tags. */
    /** - If this is not empty, we will fail the request if any of them is invalid */
    /** value, otherwise, we will return those with status is one of the specified */
    /** values. */
    levels?: number[];
    /** Set of tag types caller want to see. */
    /**  */
    /** - If this is empty, we will not apply any default value to filter the tags. */
    /** - If this is not empty, we will fail the request if any of them is invalid */
    /** value, otherwise, we will return those with status is one of the specified */
    /** values. */
    types?: number[];
    /** Find tags which have created_time within the given range, optional. */
    createdTimeRange?: ITimeRange;
    /** Find tags which have updated_time within the given range, optional. */
    updatedTimeRange?: ITimeRange;
    /** Find tags which have start_time within the given range, optional. */
    startTimeRange?: ITimeRange;
    /** Find tags which have end_time within the given range, optional. */
    endTimeRange?: ITimeRange;
    /** Find tags which were created by the given operator, optional. */
    createdBy?: string;
  }

  export interface ITag {
    id?: number;
    /** A short human readable string to identify the tag. */
    /**  */
    /** - Must not exceed 50 characters (defined in the PRD) */
    /** - The name must be unique per country */
    /** - Must only contain alphanumeric characters, hyphen `-`, period `.`, */
    /** underscore `_` */
    name?: string;
    /** A long descriptive string to learn more about the tag. */
    /**  */
    /** - Must not exceed 200 characters. */
    /** - Must contains only ASCII characters. */
    description?: string;
    /** The country where the tag is applicable. */
    country?: string;
    /** Tag status, see enum definition for more detail. */
    /**  */
    /** - Must be one of defined values of TagStatus */
    status?: number;
    /** Tag type, see enum definition for more detail. */
    /**  */
    /** - Must be one of defined values of TagType */
    type?: number;
    /** Tag level, see enum definition for more detail. */
    /**  */
    /** - Must be one of defined values of TagLevel. */
    level?: number;
    /** This contains operator email for creating the tag. */
    /**  */
    /** - Must be a valid email or bot name. */
    /** - Operator must has valid role in order to create tags. */
    /** Contact ITS PIC to apply for access permission for an email or white list a */
    /** bot name. */
    /**  */
    /** This should be left empty when update. The service will ignore the given */
    /** value anyways. */
    createdBy?: string;
    /** Created time, UNIX second since epoch. Set by system, if this is set when */
    /** creating, the value will be ignored. */
    createdTime?: number;
    /** Last updated time, UNIX second since epoch. Set by system, if this is set */
    /** when updating, the value will be ignored. */
    updatedTime?: number;
    /** When the tag start taking effect on its items. */
    /**  */
    /** - If this is not nil, must not be a moment in the past. */
    /** - If this is nil, then the end_time must be also nil. */
    startTime?: number;
    /** When the tag stop taking effect on its items. */
    /**  */
    /** - If this is not nil, must not be a moment in the past. */
    /** - If this is nil, then the start_time must be also nil. */
    endTime?: number;
    /** List of image hash from Garena File Server. */
    /**  */
    /** - Must contains at most 3 hashes. */
    /** - Each hash must has valid length as generated from the Garena File Server. */
    images?: string[];
    /** List of operators who has editing permission on this tag. */
    /**  */
    /** - This must contains a list of valid email of operator or white listed bot */
    /** name. */
    editors?: string[];
  }

  export interface IGetTagResponse {
    debugMsg?: string;
    tags?: ITag[];
    /** See common docs for `seek_point`. */
    seekPoint?: string;
    spErrorCode?: number;
    spDebugMsg?: string;
  }

  export interface IGetItemTagRequest {
    /** Country of the item. */
    /**  */
    /** Deprecated. This field is no longer used for filtering. */
    country?: string;
    /** List of item IDs to query for. Must not empty. */
    itemIds?: number[];
    /** List of tag IDs to query for, optional. */
    tagIds?: number[];
    /** List of shop IDs to query for, optional. */
    shopIds?: number[];
    /** List of model IDs to query for, optional. */
    modelIds?: number[];
    /** Find item-tag which has created_time within the given range, optional. */
    createdTimeRange?: ITimeRange;
    /** Find item-tag which has updated_time within the given range, optional. */
    updatedTimeRange?: ITimeRange;
  }

  export interface IGetItemTagResponse {
    spErrorCode?: number;
    spDebugMsg?: string;
    debugMsg?: string;
    itemTags?: IItemTag[];
  }

  export interface IItemTag {
    /** - Must be ID of a valid item and belong to the given shop_id. */
    itemId?: number;
    /** - Must be ID of a valid tag. */
    tagId?: number;
    /** - Must be ID of a valid shop. */
    shopId?: number;
    /** - For `CreateItemTagRequest` and `UpdateItemTagRequest` with item-level */
    /** tag, model_id is set to 0 by the system. */
    /**  */
    /** - For `CreateItemTagRequest` and `UpdateItemTagRequest` with model-level */
    /** tag, model_id must be ID of a valid model belong to the given item_id. In */
    /** case the item does not have any model, it is allowed to set this field to 0 */
    /** or leaving it nil. If the item has at least 1 model, and this field is 0 or */
    /** nil, an error will be returned. */
    modelId?: number;
    /** - Country of item. Set by system, if this field is set when */
    /** creating, the value will be ignored. */
    country?: string;
    /** - When creating, status is set by system to NORMAL. */
    /** - When updating, status is required. */
    /** - When getting, GET APIs only return NORMAL item-tags, DELETED item-tags */
    /** will not be returned, this field will not present in responses, do not rely */
    /** on this field when processing GET APIs responses. */
    /**  */
    /** - Must be one of defined statuses of ItemTagStatus. */
    status?: number;
    /** Created time, UNIX second since epoch. Set by system, if this is set when */
    /** creating, the value will be ignored. */
    createdTime?: number;
    /** Last updated time, UNIX second since epoch. Set by system, if this is set */
    /** when updating, the value will be ignored. */
    updatedTime?: number;
  }

  export interface IGetMtskuInfoAuditByMtskuIdRequest {
    /** mtsku id */
    mtskuItemId?: number;
    /** refer MtskuAuditType */
    auditType?: number;
    source?: string;
    operatorFilter?: string;
    /** required, to search for event happen between the "from" and the "to". */
    /** If "to" is not specified, it's set to request time. "from" must always be */
    /** specified. */
    eventTimeInMsRange?: IDateRange;
    /** optional, if not specified, we will use a configured default. */
    limit?: number;
    /** Offset to skip over some data, used for pagination. */
    /** For the first query, client should not set this field. */
    /** If the response of first query has non empty `offset`, */
    /** then, to get next page, client should send the same query, */
    /** but set this field to the offset from first page response. */
    offset?: string;
  }

  export interface IDateRange {
    /** in millis */
    from?: number;
    /** in millis, should be higher than from */
    to?: number;
  }

  export interface IMtskuInfoAudit {
    /** mtsku id */
    mtskuItemId?: number;
    /** merchant id */
    merchantId?: number;
    /** refer MtskuAuditType */
    auditType?: number;
    /** mtsku fields that have been changed */
    mtskuFields?: IAuditDelta[];
    source?: string;
    operator?: string;
    reason?: string;
    eventTimeMs?: number;
    createTimeMs?: number;
  }

  export interface IGetMtskuInfoAuditByMtskuIdResponse {
    audits?: IMtskuInfoAudit[];
    /** Offset to get the next page. See Offset structure. */
    /** If this is empty, that means no more data for the given query. */
    offset?: string;
    /** has_next is false if scan end */
    hasNext?: boolean;
    debugMsg?: string;
    spErrorCode?: number;
    spDebugMsg?: string;
  }

  export interface IGetMtskuItemForAdminRequest {
    /** mtsku id */
    mtskuId?: number;
    /** the version of rich_text_desc, default value is 1 */
    richTextDescriptionVersion?: number;
  }

  export interface IMpskuBasicInfo {
    mpskuItemId?: number;
    shopId?: number;
    region?: string;
    status?: number;
    autoCorrectInfo?: IAutoCorrectInfo;
  }

  export interface IAutoCorrectInfo {
    isAutoCorrectCategory?: boolean;
    autoCorrectStatus?: number;
  }

  export interface IGetMtskuItemForAdminResponse {
    /**  */
    mtsku?: IMtskuItemForAdmin;
    /** mpsku information */
    mpskuList?: IMpskuBasicInfo[];
    /** record error info details for debug */
    debugMsg?: string;
    mtskuExtInfo?: IMtskuExtInfo;
    spErrorCode?: number;
    spDebugMsg?: string;
  }

  export interface IMtskuExtInfo {
    merchantWarehouseFlag?: number;
  }

  export interface IGetSkuStockAuditsRequest {
    /** required */
    skuId?: number;
    /** required */
    range?: IDateRange;
    /** optional, corresponds to Offset */
    offset?: string;
    /** optional, if provided will override internal default */
    limit?: number;
    /** required if not passed through context */
    region?: string;
  }

  export interface ISkuStockAudit {
    skuId?: number;
    fulfilmentType?: number;
    locationId?: string;
    auditDeltas?: IAuditDelta[];
    itemId?: number;
    modelId?: number;
    refEntityId?: string;
    caller?: string;
    operator?: string;
    reason?: string;
    eventTimeInMs?: number;
    createTime?: number;
    source?: string;
  }

  export interface IGetSkuStockAuditsResponse {
    audits?: ISkuStockAudit[];
    offset?: string;
    /** indicates if there is any more data for the given range */
    hasMore?: boolean;
    debugMsg?: string;
    spErrorCode?: number;
    spDebugMsg?: string;
  }

  export interface IGetPriceAuditsRequest {
    /** required */
    itemId?: number;
    /** optional */
    filter?: IFilterCriteria;
    /** required */
    range?: IDateRange;
    /** optional, corresponds to Offset */
    offset?: string;
    /** optional, if provided will override internal default */
    limit?: number;
  }

  export interface IGetPriceAuditsResponse {
    audits?: IPriceAudit[];
    /** corresponds to Offset */
    offset?: string;
    /** indicates if there is any more data for the given range/filter */
    hasMore?: boolean;
    debugMsg?: string;
    spErrorCode?: number;
    spDebugMsg?: string;
  }

  export interface IGetTaxGroupTypeRequest {
    shopId?: number;
    categoryIdList?: number[];
    region?: string;
  }

  export interface IGetTaxGroupTypeResponse {
    spErrorCode?: number;
    spDebugMsg?: string;
    data?: ITaxGroupData;
  }

  export interface ITaxGroupData {
    groupType?: number;
  }

  export interface IGetBooksInfoByIsbnRequest {
    isbn?: string;
    region?: string;
  }

  export interface IGetBooksInfoByIsbnResponse {
    spErrorCode?: number;
    spDebugMsg?: string;
    taxType?: number;
  }

  export interface IGetTopicalQcLogsRequest {
    /** required */
    itemId?: number;
    /** default value is 50 */
    limit?: number;
    /** default value is 0 */
    offset?: number;
    /** default value is 0 */
    region?: string;
  }

  export interface IGetTopicalQcLogsResponse {
    spDebugMsg?: string;
    logs?: ITopicalQcItemLog[];
    hasMore?: boolean;
    spErrorCode?: number;
  }

  export interface ITopicalQcItemLog {
    reviewLogId?: string;
    createTime?: string;
    updateTime?: string;
    reviewer?: string;
    topicalQcAction?: IQcAction;
    topicalQcAddOnAction?: IQcAddOnAction;
    project?: string;
    taskId?: number;
    remark?: string;
  }

  export interface IValidateAnatelAttributeRequest {
    massProcessingMeta?: IMassProcessingMeta;
    /** the rest of the request according to the schema */
    shopId?: number;
    itemId?: number;
    categoryId?: number;
    registrationId?: string;
    manufacturer?: string;
    model?: string;
    parentAttributeId?: number;
    parentAttributeValueId?: number;
    /** any extra data will be added here */
    region?: string;
  }

  export interface IMassProcessingMeta {
    operatorEmail?: string;
  }

  export interface IValidateAnatelAttributeResponse {
    error?: number;
    errorMsg?: string;
    data?: IValidateAnatelAttributeExtData;
    isErrorRetryable?: boolean;
  }

  export interface IValidateAnatelAttributeExtData {
    itemName?: string;
    itemPrice?: string;
    /** last 30 day */
    itemAdo?: number;
    /** return from item.attribute.check_specific_attr_val, success or error */
    apiReturn?: string;
    /** 0. no validation needed; 1. invalid number; 2. model don't match number; 3. manufacturer don't match number; 4. model+manufacturer don't match number) */
    errorType?: string;
  }

  export interface IExportHighBrandItemRequest {
    shopId?: number;
    region?: string;
    bffMeta?: IRequestMeta;
  }

  export interface IExportHighBrandItemResponse {
    fileKey?: string;
    spDebugMsg?: string;
    spErrorCode?: number;
  }

  export interface IGetDefaultSizeChartListRequest {
    region?: string;
    virtualCategoryId?: number;
    pageSize?: number;
    pageNumber?: number;
  }

  export interface IGetDefaultSizeChartListResponse {
    spErrorCode?: number;
    spDebugMsg?: string;
    data?: IGetDefaultSizeChartListData;
  }

  export interface IGetDefaultSizeChartListData {
    list?: IDefaultSizeChartInfo[];
    pageInfo?: IPageInfo;
  }

  export interface IDefaultSizeChartInfo {
    sizeChartId?: number;
    name?: string;
    virtualCategoryId?: number;
    virtualCategoryName?: string;
    table?: ISizeChartTable;
    updateTime?: number;
    operator?: string;
  }

  export interface IUpdateDefaultSizeChartRequest {
    region?: string;
    /** only for create */
    virtualCategoryId?: number;
    /** only for update */
    sizeChartId?: number;
    name?: string;
    table?: ISizeChartTable;
    bffMeta?: IRequestMeta;
  }

  export interface IUpdateDefaultSizeChartResponse {
    spErrorCode?: number;
    spDebugMsg?: string;
  }

  export interface IPQRInfo {
    pqr?: number;
    ratingBadCnt?: number;
    order?: number;
    reviewRatings?: number;
    consecutiveWeeks?: number;
  }

  export interface IValidateANPAttributeRequest {
    massProcessingMeta?: IMassProcessingMeta;
    /** the rest of the request according to the schema */
    shopId?: number;
    itemId?: number;
    categoryId?: number;
    anpBoolean?: number;
    anpProcessNumber?: string;
    anpProducer?: string;
    region?: string;
    /** any extra data will be added here */
    /** optional string region = 8; */
  }

  export interface IValidateANPAttributeResponse {
    error?: number;
    errorMsg?: string;
    data?: IValidateANPAttributeExtData;
    isErrorRetryable?: boolean;
  }

  export interface IValidateANPAttributeExtData {
    itemId?: number;
    shopId?: number;
    itemName?: string;
    itemCategory?: string;
    anpBoolean?: number;
    anpProcessNumber?: string;
    anpProducer?: string;
    itemPrice?: string;
    /** last 30 day */
    itemAdo?: number;
    /** return from item.attribute.check_specific_attr_val, success or error */
    apiReturn?: string;
    /** 0. no validation needed; 1. invalid number; 2. model don't match number; 3. manufacturer don't match number; 4. model+manufacturer don't match number) */
    errorType?: string;
  }

  export interface IGetKitDetailRequest {
    /** required */
    itemId?: number;
  }

  export interface IGetDefaultSizeChartListRequest {
    region?: string;
    virtualCategoryId?: number;
    pageSize?: number;
    pageNumber?: number;
  }

  export interface IGetDefaultSizeChartListResponse {
    spErrorCode?: number;
    spDebugMsg?: string;
    data?: IGetDefaultSizeChartListData;
  }

  export interface IGetDefaultSizeChartListData {
    list?: IDefaultSizeChartInfo[];
    pageInfo?: IPageInfo;
  }

  export interface IDefaultSizeChartInfo {
    sizeChartId?: number;
    name?: string;
    virtualCategoryId?: number;
    virtualCategoryName?: string;
    table?: ISizeChartTable;
    updateTime?: number;
    operator?: string;
  }

  export interface IUpdateDefaultSizeChartRequest {
    region?: string;
    /** only for create */
    virtualCategoryId?: number;
    /** only for update */
    sizeChartId?: number;
    name?: string;
    table?: ISizeChartTable;
    bffMeta?: IRequestMeta;
  }

  export interface IUpdateDefaultSizeChartResponse {
    spErrorCode?: number;
    spDebugMsg?: string;
  }

  export interface IPQRInfo {
    pqr?: number;
    ratingBadCnt?: number;
    order?: number;
    reviewRatings?: number;
    consecutiveWeeks?: number;
  }

  export interface IGetProductTaxRatesRequest {
    shopId?: number;
    categoryPath?: number[];
  }

  export interface IGetProductTaxRatesResponse {
    spDebugMsg?: string;
    spErrorCode?: number;
    data?: IGetProductTaxRatesData;
  }

  export interface IGetProductTaxRatesData {
    summaryRates?: string;
    taxRegulationList?: ITaxRegulation[];
  }

  export interface ITaxRegulation {
    /** see TaxRegulationType enum */
    regulationType?: number;
    taxRates?: string;
  }

}
