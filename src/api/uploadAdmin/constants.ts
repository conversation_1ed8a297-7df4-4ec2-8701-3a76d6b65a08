export enum ErrorCode {
  /** Incorrect request type */
  ERROR_REQUEST_ASSERTION = 249900000,
  /** Incorrect response type */
  ERROR_RESPONSE_ASSERTION = 249900001,
  /** Invalid request params */
  ERROR_ABNORMAL_PARAM = 249900002,
  /** Database related error */
  ERROR_DATABASE = 249900003,
  /** Programming mistake, or some other problems that we can't blame external dependencies or API users */
  ERROR_INTERNAL = 249900004,
  /** Dep Call related error */
  ERROR_DEPENDENCY = 249900005,
  /** unmarshal and marshal error */
  ERROR_MARSHAL = 249900006,
  /** deprecated api */
  ERROR_DEPRECATED = 249900007,
  /** Not found in the region for filtering */
  ERROR_NOTFOUND = 249901000,
  /** Partially fail */
  ERROR_PARTIALLY_UPDATE = 249901001,
  /** Get empty info */
  ERROR_GET_INFO_NIL = 249901002,
  /** When the item is preOrder, and try to update new categories that do not support preOrder */
  ERROR_CATEGORY_NOT_ALLOWED_PREORDER = 249901003,
  /** err create task */
  ERROR_MASS_ADD_TASK = 249901004,
  ERROR_BR_SITE_BLOCK = 249901005,
}

export enum CBType {
  CB_TYPE_LOCAL = 0,
  CB_TYPE_NON_SIP_ASHOP = 1,
  CB_TYPE_SIP_ASHOP = 2,
}

export enum OfficialType {
  OFFICIAL_TYPE_NORMAL = 0,
  OFFICIAL_TYPE_OFFICIAL = 1,
  OFFICIAL_TYPE_PREFERRED = 2,
}

export enum Shop3pfWarehouseFlag {
  SHOP_WAREHOUSE_FLAG_NORMAL = 1,
  SHOP_WAREHOUSE_FLAG_3PF_ONLY = 2,
  SHOP_WAREHOUSE_FLAG_3PF_HYBRID = 3,
}

export enum Merchant3pfWarehouseFlag {
  MERCHANT_WAREHOUSE_FLAG_3PF = 1,
}

export enum CategoryStatus {
  STATUS_CAT_DELETE = 0,
  STATUS_CAT_NORMAL = 1,
  STATUS_CAT_DISABLE = 2,
  /** only used for global category. cb block/local block */
  STATUS_CAT_BLOCK = 3,
}

export enum ItemQueryType {
  /** return filtered items info */
  ITEM_INFO = 0,
  /** return the item total count satisfy query condition and will ignore offset and limit in request */
  ITEM_COUNT_ONLY = 1,
}

export enum CategoryType {
  /** "global category" */
  CATEGORY_TYPE_GLOBAL = 1,
  /** "local category" */
  CATEGORY_TYPE_LOCAL = 2,
}

export enum SellerCategory {
  /** "local" */
  SELLER_CATEGORY_LOCAL = 1,
  /** "cross_border", only seller type Official, Normal, Preferred are valid */
  SELLER_CATEGORY_CROSS_BORDER = 2,
  /** "merchant", only seller type CNCB is valid */
  SELLER_CATEGORY_MERCHANT = 3,
  /** "default" */
  SELLER_CATEGORY_DEFAULT = 4,
  /** for customized group of item rules */
  SELLER_CATEGORY_ITEM_GROUP = 5,
}

export enum SellerType {
  /** "normal" */
  SELLER_TYPE_NORMAL = 1,
  /** "official_seller" */
  SELLER_TYPE_OFFICIAL_SELLER = 2,
  /** "preferred_seller" */
  SELLER_TYPE_PREFERRED_SELLER = 3,
  /** "cncb_seller" */
  SELLER_TYPE_CNCB_SELLER = 4,
}

export enum InputType {
  IT_UNKNOWN = 0,
  IT_DOWN_DROP_LIST = 1,
  IT_SINGLE_INPUT_NUMBER = 2,
  IT_RANGE_INPUT_NUMBER = 3,
}

export enum MeasurementType {
  MT_UNKNOWN = 0,
  /** normal measurement */
  MT_NORMAL = 1,
  /** size convention */
  MT_SIZE_CONVENTION = 2,
}

export enum ActiveType {
  AT_ACTIVE = 0,
  AT_INACTIVE = 1,
}

export enum AuditLogFieldType {
  MEASUREMENT = 1,
  VIRTUAL_CATEGORY = 2,
}

export enum ConvertedStatus {
  CONVERTED_SUCCESS = 1,
  CONVERTED_FAILURE = 2,
}

export enum MtskuItemStatus {
  /** mtsku-item marked as deleted */
  ITEM_STATUS_DELETED = 0,
  /** mtsku-item in normal status */
  ITEM_STATUS_NORMAL = 1,
}

export enum SkuType {
  /** original sku which is default type */
  NORMAL_SKU = 0,
  /** virtual sku and has weak-stock mode */
  VIRTUAL_SKU_WEAK_STOCK = 1,
  /** virtual sku and has reserved-stock mode */
  VIRTUAL_SKU_RESERVED_STOCK = 2,
  /** This model is linked to a mtsku model and its stock is weak-stock mode */
  MTSKU_WEAK_STOCK = 3,
}

export enum AuditType {
  /** Reserved range for backward compat. */
  /** DO NOT ADD TO THIS RANGE */
  AUDIT_TYPE_SIP_UPDATE = 105,
  AUDIT_TYPE_DP_UPDATE = 106,
  /** Reserved range ends here */
  AUDIT_TYPE_UNKNOWN = 0,
  AUDIT_TYPE_PLACE_ORDER = 1,
  AUDIT_TYPE_CANCEL_ORDER = 2,
  /** Can be used updating both price/stock. */
  AUDIT_TYPE_UPDATE_NORMAL = 3,
  /** For adding/reducing. */
  AUDIT_TYPE_NORMAL_STOCK = 4,
  AUDIT_TYPE_APPROVE_PROMOTION = 5,
  AUDIT_TYPE_CANCEL_PROMOTION = 6,
  AUDIT_TYPE_PROMOTION_END = 7,
  AUDIT_TYPE_PROMOTION_UPDATE = 8,
  AUDIT_TYPE_CREATE_CHILD_SKU = 9,
  AUDIT_TYPE_UPDATE_CHILD_SKU = 10,
  AUDIT_TYPE_RESTORE_CHILD_SKU = 11,
  AUDIT_TYPE_DELETE_CHILD_SKU = 12,
  /** Only for internal use; DO NOT use this audit type. */
  AUDIT_TYPE_SYSTEM_UPDATE = 13,
  /** For WMS to add/reduce sellable stock. */
  AUDIT_TYPE_WMS_STOCK_UPDATE = 14,
  /** For securing stock. */
  AUDIT_TYPE_SECURE_STOCK = 15,
  /** For migrate sku stock. */
  AUDIT_TYPE_MIGRATE_SKU = 16,
  /** Can be used to create/update/delete mtsku */
  AUDIT_TYPE_UPDATE_MTSKU = 17,
  /** For moving stock from 1 location to another. */
  AUDIT_TYPE_MOVE_SKU = 18,
  /** For toggling of blocking or unblocking fulfilment in stock value. */
  AUDIT_TYPE_TOGGLE_FULFILMENT = 19,
  /** For moving stock from other location to a secured location, only used by IPS. */
  AUDIT_TYPE_SECURE_STOCK_MOVE_SKU = 20,
  AUDIT_TYPE_UPDATE_STOCK_CONFIG = 21,
  /** For setting holiday mode logic. */
  AUDIT_TYPE_SET_HOLIDAY_MODE = 22,
  AUDIT_TYPE_CREATE_STOCK_CEILING = 23,
  AUDIT_TYPE_DELETE_STOCK_CEILING = 24,
  AUDIT_TYPE_UPDATE_STOCK_CEILING = 25,
  /** For sku reverse index cleanup. */
  AUDIT_TYPE_DELETE_SKU_REVERSE_INDEX = 26,
}

export enum TimeForWarrantyClaim {
  ONE_YEAR = 1,
  TWO_YEARS = 2,
  OVER_TWO_YEARS = 3,
}

export enum PromotionType {
  PROMOTION_TYPE_NORMAL = 0,
  PROMOTION_TYPE_PRODUCT_PROMOTION_SG = 1,
  PROMOTION_TYPE_PRODUCT_PROMOTION_MY = 2,
  PROMOTION_TYPE_PRODUCT_PROMOTION_TH = 3,
  PROMOTION_TYPE_PRODUCT_PROMOTION_VN = 4,
  PROMOTION_TYPE_PRODUCT_PROMOTION_ID = 5,
  PROMOTION_TYPE_PRODUCT_PROMOTION_PH = 6,
  PROMOTION_TYPE_PRODUCT_PROMOTION_TW = 7,
  PROMOTION_TYPE_PRODUCT_PROMOTION_BR = 8,
  PROMOTION_TYPE_PRODUCT_PROMOTION_IN = 9,
  PROMOTION_TYPE_PRODUCT_PROMOTION_MX = 10,
  PROMOTION_TYPE_PRODUCT_PROMOTION_CO = 11,
  PROMOTION_TYPE_PRODUCT_PROMOTION_CL = 12,
  PROMOTION_TYPE_PRODUCT_PROMOTION_AR = 13,
  PROMOTION_TYPE_PRODUCT_PROMOTION_PL = 14,
  PROMOTION_TYPE_PRODUCT_PROMOTION_ES = 15,
  PROMOTION_TYPE_PRODUCT_PROMOTION_FR = 16,
  PROMOTION_TYPE_PRODUCT_PROMOTION_XX = 300,
  PROMOTION_TYPE_SELLER_DISCOUNT = 301,
  PROMOTION_TYPE_FLASH_SALE = 302,
  PROMOTION_TYPE_WHOLESALE = 303,
  PROMOTION_TYPE_GROUP_BUY = 304,
  PROMOTION_TYPE_BUNDLE_DEAL = 305,
  PROMOTION_TYPE_WELCOME_PACKAGE_FREE_GIFT = 306,
  PROMOTION_TYPE_WELCOME_PACKAGE_EXCLUSIVE_ITEMS = 307,
  PROMOTION_TYPE_SLASH_PRICE = 308,
  PROMOTION_TYPE_ADD_ON_DEAL_MAIN = 309,
  PROMOTION_TYPE_ADD_ON_DEAL_SUB = 310,
  PROMOTION_TYPE_BRAND_SALE = 311,
  PROMOTION_TYPE_IN_SHOP_FLASH_SALE = 312,
  PROMOTION_TYPE_ADD_ON_FREE_GIFT_MAIN = 313,
  PROMOTION_TYPE_ADD_ON_FREE_GIFT_SUB = 314,
  RULE_TYPE_XTRA_DISC_FLASH_SALE = 315,
  RULE_TYPE_XTRA_DISC_BRAND_SALE = 316,
  RULE_TYPE_ADD_ON_PURCHASE_MAIN = 317,
  RULE_TYPE_ADD_ON_PURCHASE_SUB = 318,
  RULE_TYPE_SYNCED_PROMOTION = 319,
  RULE_TYPE_SELLING_PRICE = 401,
  RULE_TYPE_SETTLEMENT_PRICE = 402,
  RULE_TYPE_CB_SIP_CAMPAIGN_SETTLEMENT_PRICE = 403,
  RULE_TYPE_EXCLUSIVE_PRICE = 501,
  RULE_TYPE_OVERALL_PURCHASE_LIMIT = 601,
  /** Used for cross-region price sync to CBSIP A-shop. */
  /** SIP system calculates CBSIP A-sku settlement price, discount price, */
  /** and original price based on the P-shop price with this rule type. */
  /** See SPPT-41534 or https://confluence.shopee.io/x/GM24U */
  RULE_TYPE_SHOPEE_MANAGE_ITEM_PRICE = 701,
  /** Price with enum code greater than 1000,000 would not be stored on item price stock db. */
  /** These price types are used for other services as price type definition e.g. tax calculation. */
  RULE_TYPE_CHAT_OFFER_PRICE = 1000001,
  /** NOTE: 10000000 ~ 99999999 are reserved by stock side */
  RULE_TYPE_STOCK_CEILING = 10000001,
  /** https://confluence.shopee.io/display/SPPT/%5BTD%5D+Price+Advisor+Support+New+Promo+Type */
  RULE_TYPE_ADVISOR_PRICE = 325,
}

export enum PromotionStatus {
  STATUS_PROMOTION_DELETED = 0,
  STATUS_PROMOTION_NORMAL = 1,
}

export enum ItemPriceStatus {
  STATUS_ITEM_PRICE_DELETED = 0,
  STATUS_ITEM_PRICE_NORMAL = 1,
  STATUS_ITEM_PRICE_ENDED = 2,
}

export enum ItemStockStatus {
  STATUS_ITEM_STOCK_DELETED = 0,
  STATUS_ITEM_STOCK_NORMAL = 1,
  STATUS_ITEM_STOCK_ENDED = 2,
}

export enum StockFulfilmentMainType {
  WMS_ON_HAND = 1,
  SELLER_ON_HAND = 2,
}

export enum StockType {
  NORMAL_PROMOTION = 1,
  VSKU_RESERVED_CHILD = 2,
  VSKU_PROMOTION = 3,
  NORMAL_FLOATING_PROMOTION = 4,
  VSKU_FLOATING_PROMOTION = 5,
}

export enum AttrDateTimeFormat {
  /** DD/MM/YYYY */
  YEAR_MONTH_DATE = 0,
  /** MM/YYYY */
  YEAR_MONTH = 1,
}

export enum AttrInputType {
  SINGLE_DROP_DOWN = 1,
  SINGLE_COMBO_BOX = 2,
  FREE_TEXT_FILED = 3,
  MULTI_DROP_DOWN = 4,
  MULTI_COMBO_BOX = 5,
}

export enum AttrInputValidatorType {
  VALIDATOR_NOT_REQUIRED = 0,
  VALIDATOR_INTEGERS = 1,
  VALIDATOR_STRING = 2,
  /** Decimal */
  VALIDATOR_NUMBERS = 3,
  /** Timestamp; for more info on DATE type, check comments on ItemAttrWithValue */
  VALIDATOR_DATE = 4,
}

export enum AttrFormatType {
  FORMAT_NORMAL = 1,
  /** only VALIDATOR_INTEGERS and VALIDATOR_NUMBERS are supported */
  FORMAT_QUANTITATIVE_WITH_UNIT = 2,
}

export enum AttrStatus {
  ATTR_DELETED = 0,
  ATTR_NORMAL = 1,
  ATTR_DISABLE = 2,
}

export enum GlobalBrandStatus {
  BRAND_DELETED = 0,
  BRAND_NORMAL = 1,
  BRAND_PENDING = 2,
  BRAND_UPGRADED = 3,
  BRAND_COMBINED = 4,
}

export enum MtskuAuditType {
  /** audit for adding mtsku item */
  MTSKU_AUDIT_TYPE_MTSKU_ITEM_ADD = 1,
  /** audit for updating and deleting mtsku item */
  MTSKU_AUDIT_TYPE_MTSKU_ITEM_EDIT = 2,
  /** audit for adding mtsku item model */
  MTSKU_AUDIT_TYPE_MTSKU_MODEL_ADD = 3,
  /** audit for updating mtsku item model */
  MTSKU_AUDIT_TYPE_MTSKU_MODEL_EDIT = 4,
  /** audit for deleting mtsku item model separately, since physically */
  MTSKU_AUDIT_TYPE_MTSKU_MODEL_DELETE = 5,
  /** delete */
}
export enum TaxRegulationType {
  TaxRegulationVnWhtVat = 17,
  TaxRegulationVnWhtPit = 18,
}
