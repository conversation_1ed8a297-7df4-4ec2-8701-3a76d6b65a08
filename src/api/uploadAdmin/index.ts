import { request } from 'src/api/helper';
import type { WithResponse } from 'src/api/helper/apiHandler';
import { apiHandler } from 'src/api/helper/apiHandler';
import type { uploadAdmin } from './uploadAdmin';

const URLPrefix = '/wsa/marketplace/listing/upload/upload_admin';

function createRequestFunc<IRequest, IResponse>(url: string) {
  return function (
    requestBody: IRequest,
    config?: { returnFullResponse?: boolean; skipError?: boolean }
  ) {
    return apiHandler(
      request<WithResponse<IResponse>>({
        url: URLPrefix + url,
        data: requestBody,
      }),
      config
    );
  };
}

export const getLatestUpdatedItems = createRequestFunc<
  uploadAdmin.IGetLatestUpdatedItemsRequest,
  uploadAdmin.IGetLatestUpdatedItemsResponse
>('/get_latest_updated_items');

export const searchItems = createRequestFunc<
  uploadAdmin.ISearchItemsRequest,
  uploadAdmin.ISearchItemsResponse
>('/search_items');

export const getItemDetail = createRequestFunc<
  uploadAdmin.IGetItemDetailRequest,
  uploadAdmin.IGetItemDetailResponse
>('/get_item_detail');

export const getKitDetail = createRequestFunc<
  uploadAdmin.IGetKitDetailRequest,
  uploadAdmin.IGetItemDetailResponse
>('/get_kit_detail');

export const getItemModelList = createRequestFunc<
  uploadAdmin.IGetItemModelListRequest,
  uploadAdmin.IGetItemModelListResponse
>('/get_item_model_list');

export const getItemModelStockList = createRequestFunc<
  uploadAdmin.IGetItemModelStockListRequest,
  uploadAdmin.IGetItemModelStockListResponse
>('/get_item_model_stock_list');

export const updateItemBasicInfo = createRequestFunc<
  uploadAdmin.IUpdateItemBasicInfoRequest,
  uploadAdmin.IUpdateItemBasicInfoResponse
>('/update_item_basic_info');

export const getGlobalTreeToggle = createRequestFunc<
  uploadAdmin.IGetGlobalTreeToggleRequest,
  uploadAdmin.IGetGlobalTreeToggleResponse
>('/get_global_tree_toggle');

export const getCategoryList = createRequestFunc<
  uploadAdmin.IGetCategoryListRequest,
  uploadAdmin.IGetCategoryListResponse
>('/get_category_list');

export const getProductAttributes = createRequestFunc<
  uploadAdmin.IGetProductAttributesRequest,
  uploadAdmin.IGetProductAttributesResponse
>('/get_product_attributes');

export const getGlobalCategoryList = createRequestFunc<
  uploadAdmin.IGetGlobalCategoryListRequest,
  uploadAdmin.IGetGlobalCategoryListResponse
>('/get_global_category_list');

export const getGlobalBrandList = createRequestFunc<
  uploadAdmin.IGetGlobalBrandListRequest,
  uploadAdmin.IGetGlobalBrandListResponse
>('/get_global_brand_list');

export const getGlobalProductAttributes = createRequestFunc<
  uploadAdmin.IGetGlobalProductAttributesRequest,
  uploadAdmin.IGetGlobalProductAttributesResponse
>('/get_global_product_attributes');

export const getItemInfoAudit = createRequestFunc<
  uploadAdmin.IGetItemInfoAuditRequest,
  uploadAdmin.IGetItemInfoAuditResponse
>('/get_item_info_audit');

export const getStockAudits = createRequestFunc<
  uploadAdmin.IGetStockAuditsRequest,
  uploadAdmin.IGetStockAuditsResponse
>('/get_stock_audits');

export const getQcTags = createRequestFunc<
  uploadAdmin.IGetQcTagsRequest,
  uploadAdmin.IGetQcTagsResponse
>('/get_qc_tags');

export const getOldQcLogs = createRequestFunc<
  uploadAdmin.IGetOldQcLogsRequest,
  uploadAdmin.IGetOldQcLogsResponse
>('/get_old_qc_logs');

export const getRuleQcLogs = createRequestFunc<
  uploadAdmin.IGetRuleQcLogsRequest,
  uploadAdmin.IGetRuleQcLogsResponse
>('/get_rule_qc_logs');

export const getModelQcLogs = createRequestFunc<
  uploadAdmin.IGetModelQcLogsRequest,
  uploadAdmin.IGetModelQcLogsResponse
>('/get_model_qc_logs');

export const getCqcLogs = createRequestFunc<
  uploadAdmin.IGetCqcLogsRequest,
  uploadAdmin.IGetCqcLogsResponse
>('/get_cqc_logs');

export const getTopicalQcLogs = createRequestFunc<
  uploadAdmin.IGetTopicalQcLogsRequest,
  uploadAdmin.IGetTopicalQcLogsResponse
>('/get_topical_qc_logs');

export const getItemInfoForCodingMonkey = createRequestFunc<
  uploadAdmin.IGetItemInfoForCodingMonkeyRequest,
  uploadAdmin.IGetItemInfoForCodingMonkeyResponse
>('/get_item_info_for_coding_monkey');

export const purgeCsCache = createRequestFunc<
  uploadAdmin.IPurgeCsCacheRequest,
  uploadAdmin.IPurgeCsCacheResponse
>('/purge_cs_cache');

export const purgeMallCache = createRequestFunc<
  uploadAdmin.IPurgeMallCacheRequest,
  uploadAdmin.IPurgeMallCacheResponse
>('/purge_mall_cache');

export const resetItemNormal = createRequestFunc<
  uploadAdmin.IResetItemNormalRequest,
  uploadAdmin.IResetItemNormalResponse
>('/reset_item_normal');

export const getChannelInfoByShopId = createRequestFunc<
  uploadAdmin.IGetChannelInfoByShopIdRequest,
  uploadAdmin.IGetChannelInfoByShopIdResponse
>('/get_channel_info_by_shop_id');

export const addVirtualSkuForAdminV2 = createRequestFunc<
  uploadAdmin.IAddVirtualSkuForAdminV2Request,
  uploadAdmin.IAddVirtualSkuForAdminV2Response
>('/add_virtual_sku_for_admin_v2');

export const updateVirtualSkuForAdminV2 = createRequestFunc<
  uploadAdmin.IUpdateVirtualSkuForAdminV2Request,
  uploadAdmin.IUpdateVirtualSkuForAdminV2Response
>('/update_virtual_sku_for_admin_v2');

export const paginateIndiaHsCodeList = createRequestFunc<
  uploadAdmin.IPaginateIndiaHsCodeListRequest,
  uploadAdmin.IPaginateIndiaHsCodeListResponse
>('/paginate_india_hs_code_list');

export const fetchVskuForAdminEditV2 = createRequestFunc<
  uploadAdmin.IFetchVskuForAdminEditV2Request,
  uploadAdmin.IFetchVskuForAdminEditV2Response
>('/fetch_vsku_for_admin_edit_v2');

export const activeVirtualSkuForAdminV2 = createRequestFunc<
  uploadAdmin.IActiveVirtualSkuForAdminV2Request,
  uploadAdmin.IActiveVirtualSkuForAdminV2Response
>('/active_virtual_sku_for_admin_v2');

export const validateParentSkuForAdminV2 = createRequestFunc<
  uploadAdmin.IValidateParentSkuForAdminV2Request,
  uploadAdmin.IValidateParentSkuForAdminV2Response
>('/validate_parent_sku_for_admin_v2');

export const changeVskuStockV2 = createRequestFunc<
  uploadAdmin.IChangeVskuStockV2Request,
  uploadAdmin.IChangeVskuStockV2Response
>('/change_vsku_stock_v2');

export const getChildSkuFromParentV2 = createRequestFunc<
  uploadAdmin.IGetChildSkuFromParentV2Request,
  uploadAdmin.IGetChildSkuFromParentV2Response
>('/get_child_sku_from_parent_v2');

export const getParentSkuFromChildV2 = createRequestFunc<
  uploadAdmin.IGetParentSkuFromChildV2Request,
  uploadAdmin.IGetParentSkuFromChildV2Response
>('/get_parent_sku_from_child_v2');

export const getVirtualSkuListForAdminV2 = createRequestFunc<
  uploadAdmin.IGetVirtualSkuListForAdminV2Request,
  uploadAdmin.IGetVirtualSkuListForAdminV2Response
>('/get_virtual_sku_list_for_admin_v2');

export const getAutoSyncDtsFromParent = createRequestFunc<
  uploadAdmin.IGetAutoSyncDtsFromParentRequest,
  uploadAdmin.IGetAutoSyncDtsFromParentResponse
>('/get_auto_sync_dts_from_parent');

export const getAutoSyncWeightFromParent = createRequestFunc<
  uploadAdmin.IGetAutoSyncWeightFromParentRequest,
  uploadAdmin.IGetAutoSyncWeightFromParentResponse
>('/get_auto_sync_weight_from_parent');

export const unavailableModelV2 = createRequestFunc<
  uploadAdmin.IUnavailableModelV2Request,
  uploadAdmin.IUnavailableModelV2Response
>('/unavailable_model_v2');

export const exportVsku = createRequestFunc<
  uploadAdmin.IExportVskuRequest,
  uploadAdmin.IExportVskuResponse
>('/export_vsku');

export const getCategoryTreeDimensionRule = createRequestFunc<
  uploadAdmin.IGetCategoryTreeDimensionRuleRequest,
  uploadAdmin.IGetCategoryTreeDimensionRuleResponse
>('/get_category_tree_dimension_rule');

export const setCategoryDimensionRule = createRequestFunc<
  uploadAdmin.ISetCategoryDimensionRuleRequest,
  uploadAdmin.ISetCategoryDimensionRuleResponse
>('/set_category_dimension_rule');

export const getRegionTranslationLang = createRequestFunc<
  uploadAdmin.IGetRegionTranslationLangRequest,
  uploadAdmin.IGetRegionTranslationLangResponse
>('/get_region_translation_lang');

export const getItemTranslation = createRequestFunc<
  uploadAdmin.IGetItemTranslationRequest,
  uploadAdmin.IGetItemTranslationResponse
>('/get_item_translation');

export const updateItemTranslation = createRequestFunc<
  uploadAdmin.IUpdateItemTranslationRequest,
  uploadAdmin.IUpdateItemTranslationResponse
>('/update_item_translation');

export const getShopItemLimitSettingList = createRequestFunc<
  uploadAdmin.IGetShopItemLimitSettingListRequest,
  uploadAdmin.IGetShopItemLimitSettingListResponse
>('/get_shop_item_limit_setting_list');

export const createGroup = createRequestFunc<
  uploadAdmin.ICreateGroupRequest,
  uploadAdmin.ICreateGroupResponse
>('/create_group');

export const updateGroup = createRequestFunc<
  uploadAdmin.IUpdateGroupRequest,
  uploadAdmin.IUpdateGroupResponse
>('/update_group');

export const getGroupDetail = createRequestFunc<
  uploadAdmin.IGetGroupDetailRequest,
  uploadAdmin.IGetGroupDetailResponse
>('/get_group_detail');

export const searchGroup = createRequestFunc<
  uploadAdmin.ISearchGroupRequest,
  uploadAdmin.ISearchGroupResponse
>('/search_group');

export const deleteGroup = createRequestFunc<
  uploadAdmin.IDeleteGroupRequest,
  uploadAdmin.IDeleteGroupResponse
>('/delete_group');

export const deleteGroupRule = createRequestFunc<
  uploadAdmin.IDeleteGroupRuleRequest,
  uploadAdmin.IDeleteGroupRuleResponse
>('/delete_group_rule');

export const addElementList = createRequestFunc<
  uploadAdmin.IAddElementListRequest,
  uploadAdmin.IAddElementListResponse
>('/add_element_list');

export const deleteElement = createRequestFunc<
  uploadAdmin.IDeleteElementRequest,
  uploadAdmin.IDeleteElementResponse
>('/delete_element');

export const getElementList = createRequestFunc<
  uploadAdmin.IGetElementListRequest,
  uploadAdmin.IGetElementListResponse
>('/get_element_list');

export const uploadElementList = createRequestFunc<
  uploadAdmin.IUploadElementListRequest,
  uploadAdmin.IUploadElementListResponse
>('/upload_element_list');

export const downloadElementList = createRequestFunc<
  uploadAdmin.IDownloadElementListRequest,
  uploadAdmin.IDownloadElementListResponse
>('/download_element_list');

export const getBasicRules = createRequestFunc<
  uploadAdmin.IGetBasicRulesRequest,
  uploadAdmin.IGetBasicRulesResponse
>('/get_basic_rules');

export const getRuleSubCatList = createRequestFunc<
  uploadAdmin.IGetRuleSubCatListRequest,
  uploadAdmin.IGetRuleSubCatListResponse
>('/get_rule_sub_cat_list');

export const updateCombineRule = createRequestFunc<
  uploadAdmin.IUpdateCombineRuleRequest,
  uploadAdmin.IUpdateCombineRuleResponse
>('/update_combine_rule');

export const checkBasicRule = createRequestFunc<
  uploadAdmin.ICheckBasicRuleRequest,
  uploadAdmin.ICheckBasicRuleResponse
>('/check_basic_rule');

export const getSizeChartAdminConfigs = createRequestFunc<
  uploadAdmin.IGetSizeChartAdminConfigsRequest,
  uploadAdmin.IGetSizeChartAdminConfigsResponse
>('/get_size_chart_admin_configs');

export const getMeasurementDetail = createRequestFunc<
  uploadAdmin.IGetMeasurementDetailRequest,
  uploadAdmin.IGetMeasurementDetailResponse
>('/get_measurement_detail');

export const getMeasurementList = createRequestFunc<
  uploadAdmin.IGetMeasurementListRequest,
  uploadAdmin.IGetMeasurementListResponse
>('/get_measurement_list');

export const createMeasurement = createRequestFunc<
  uploadAdmin.ICreateMeasurementRequest,
  uploadAdmin.ICreateMeasurementResponse
>('/create_measurement');

export const updateMeasurement = createRequestFunc<
  uploadAdmin.IUpdateMeasurementRequest,
  uploadAdmin.IUpdateMeasurementResponse
>('/update_measurement');

export const updateMeasurementLocalSetting = createRequestFunc<
  uploadAdmin.IUpdateMeasurementLocalSettingRequest,
  uploadAdmin.IUpdateMeasurementLocalSettingResponse
>('/update_measurement_local_setting');

export const createVirtualCategory = createRequestFunc<
  uploadAdmin.ICreateVirtualCategoryRequest,
  uploadAdmin.ICreateVirtualCategoryResponse
>('/create_virtual_category');

export const updateVirtualCategory = createRequestFunc<
  uploadAdmin.IUpdateVirtualCategoryRequest,
  uploadAdmin.IUpdateVirtualCategoryResponse
>('/update_virtual_category');

export const updateVirtualCategoryLocalSetting = createRequestFunc<
  uploadAdmin.IUpdateVirtualCategoryLocalSettingRequest,
  uploadAdmin.IUpdateVirtualCategoryLocalSettingResponse
>('/update_virtual_category_local_setting');

export const getVirtualCategory = createRequestFunc<
  uploadAdmin.IGetVirtualCategoryRequest,
  uploadAdmin.IGetVirtualCategoryResponse
>('/get_virtual_category');

export const getVirtualCategoryListWithPaging = createRequestFunc<
  uploadAdmin.IGetVirtualCategoryListWithPagingRequest,
  uploadAdmin.IGetVirtualCategoryListWithPagingResponse
>('/get_virtual_category_list_with_paging');

export const getSizeConventionList = createRequestFunc<
  uploadAdmin.IGetSizeConventionListRequest,
  uploadAdmin.IGetSizeConventionListResponse
>('/get_size_convention_list');

export const scanSizeChartAuditLog = createRequestFunc<
  uploadAdmin.IScanSizeChartAuditLogRequest,
  uploadAdmin.IScanSizeChartAuditLogResponse
>('/scan_size_chart_audit_log');

export const getDefaultSizeChartList = createRequestFunc<
  uploadAdmin.IGetDefaultSizeChartListRequest,
  uploadAdmin.IGetDefaultSizeChartListResponse
>('/get_default_size_chart_list');

export const updateDefaultSizeChart = createRequestFunc<
  uploadAdmin.IUpdateDefaultSizeChartRequest,
  uploadAdmin.IUpdateDefaultSizeChartResponse
>('/update_default_size_chart');

export const getVirtualCategoryList = createRequestFunc<
  uploadAdmin.IGetVirtualCategoryListRequest,
  uploadAdmin.IGetVirtualCategoryListResponse
>('/get_virtual_category_list');

export const getConvertedSizeChartListWithPaging = createRequestFunc<
  uploadAdmin.IGetConvertedSizeChartListWithPagingRequest,
  uploadAdmin.IGetConvertedSizeChartListWithPagingResponse
>('/get_converted_size_chart_list_with_paging');

export const confirmConvertedSizeChart = createRequestFunc<
  uploadAdmin.IConfirmConvertedSizeChartRequest,
  uploadAdmin.IConfirmConvertedSizeChartResponse
>('/confirm_converted_size_chart');

export const getSizeChartDetail = createRequestFunc<
  uploadAdmin.IGetSizeChartDetailRequest,
  uploadAdmin.IGetSizeChartDetailResponse
>('/get_size_chart_detail');

export const updateSizeChart = createRequestFunc<
  uploadAdmin.IUpdateSizeChartRequest,
  uploadAdmin.IUpdateSizeChartResponse
>('/update_size_chart');

export const deleteSizeChart = createRequestFunc<
  uploadAdmin.IDeleteSizeChartRequest,
  uploadAdmin.IDeleteSizeChartResponse
>('/delete_size_chart');

export const getVirtualCategoryWithMeasurement = createRequestFunc<
  uploadAdmin.IGetVirtualCategoryWithMeasurementRequest,
  uploadAdmin.IGetVirtualCategoryWithMeasurementResponse
>('/get_virtual_category_with_measurement');

export const checkSizeChartForEdit = createRequestFunc<
  uploadAdmin.ICheckSizeChartForEditRequest,
  uploadAdmin.ICheckSizeChartForEditResponse
>('/check_size_chart_for_edit');

export const getCategoryTreeWithVirtualCategory = createRequestFunc<
  uploadAdmin.IGetCategoryTreeWithVirtualCategoryRequest,
  uploadAdmin.IGetCategoryTreeWithVirtualCategoryResponse
>('/get_category_tree_with_virtual_category');

export const getProhibitedCharRecord = createRequestFunc<
  uploadAdmin.IGetProhibitedCharRecordRequest,
  uploadAdmin.IGetProhibitedCharRecordResponse
>('/get_prohibited_char_record');

export const getDimensionCategoryConfig = createRequestFunc<
  uploadAdmin.IGetDimensionCategoryConfigRequest,
  uploadAdmin.IGetDimensionCategoryConfigResponse
>('/get_dimension_category_config');

export const getAllCategoryList = createRequestFunc<
  uploadAdmin.IGetAllCategoryListRequest,
  uploadAdmin.IGetAllCategoryListResponse
>('/get_all_category_list');

export const getDtsCategoryConfig = createRequestFunc<
  uploadAdmin.IGetDtsCategoryConfigRequest,
  uploadAdmin.IGetDtsCategoryConfigResponse
>('/get_dts_category_config');

export const updateMtskuStock = createRequestFunc<
  uploadAdmin.IUpdateMtskuStockRequest,
  uploadAdmin.IUpdateMtskuStockResponse
>('/update_mtsku_stock');

export const updateMtskuItemForAdmin = createRequestFunc<
  uploadAdmin.IUpdateMtskuItemForAdminRequest,
  uploadAdmin.IUpdateMtskuItemForAdminResponse
>('/update_mtsku_item_for_admin');

export const deleteMtskuModelForAdmin = createRequestFunc<
  uploadAdmin.IDeleteMtskuModelForAdminRequest,
  uploadAdmin.IDeleteMtskuModelForAdminResponse
>('/delete_mtsku_model_for_admin');

export const getStockBatchWithLatestBreakdown = createRequestFunc<
  uploadAdmin.IGetStockBatchWithLatestBreakdownRequest,
  uploadAdmin.IGetStockBatchWithLatestBreakdownResponse
>('/get_stock_batch_with_latest_breakdown');

export const updateModelSettingV2 = createRequestFunc<
  uploadAdmin.IUpdateModelSettingV2Request,
  uploadAdmin.IUpdateModelSettingV2Response
>('/update_model_setting_v2');

export const getEntityAttrVal = createRequestFunc<
  uploadAdmin.IGetEntityAttrValRequest,
  uploadAdmin.IGetEntityAttrValResponse
>('/get_entity_attr_val');

export const getItemListByItemIds = createRequestFunc<
  uploadAdmin.IGetItemListByItemIdsRequest,
  uploadAdmin.IGetItemListByItemIdsResponse
>('/get_item_list_by_item_ids');

export const getLicensesForItem = createRequestFunc<
  uploadAdmin.IGetLicensesForItemRequest,
  uploadAdmin.IGetLicensesForItemResponse
>('/get_licenses_for_item');

export const getComplaintAddress = createRequestFunc<
  uploadAdmin.IGetComplaintAddressRequest,
  uploadAdmin.IGetComplaintAddressResponse
>('/get_complaint_address');

export const getForStockAudits = createRequestFunc<
  uploadAdmin.IGetForStockAuditsRequest,
  uploadAdmin.IGetForStockAuditsResponse
>('/get_for_stock_audits');

export const getMtskuRStockBreakdownByLocation = createRequestFunc<
  uploadAdmin.IGetMtskuRStockBreakdownByLocationRequest,
  uploadAdmin.IGetMtskuRStockBreakdownByLocationResponse
>('/get_mtsku_r_stock_breakdown_by_location');

export const getMtskuByConditionForAdmin = createRequestFunc<
  uploadAdmin.IGetMtskuByConditionForAdminRequest,
  uploadAdmin.IGetMtskuByConditionForAdminResponse
>('/get_mtsku_by_condition_for_admin');

export const deleteMtskuItem = createRequestFunc<
  uploadAdmin.IDeleteMtskuItemRequest,
  uploadAdmin.IDeleteMtskuItemResponse
>('/delete_mtsku_item');

export const privilDeleteItem = createRequestFunc<
  uploadAdmin.IPrivilDeleteItemRequest,
  uploadAdmin.IPrivilDeleteItemResponse
>('/privil_delete_item');

export const privilUnlistItem = createRequestFunc<
  uploadAdmin.IPrivilUnlistItemRequest,
  uploadAdmin.IPrivilUnlistItemResponse
>('/privil_unlist_item');

export const getTasks = createRequestFunc<
  uploadAdmin.IGetTasksRequest,
  uploadAdmin.IGetTasksResponse
>('/get_tasks');

export const getProgress = createRequestFunc<
  uploadAdmin.IGetProgressRequest,
  uploadAdmin.IGetProgressResponse
>('/get_progress');

export const newTask = createRequestFunc<
  uploadAdmin.INewTaskRequest,
  uploadAdmin.INewTaskResponse
>('/new_task');

export const stopTask = createRequestFunc<
  uploadAdmin.IStopTaskRequest,
  uploadAdmin.IStopTaskResponse
>('/stop_task');

export const getTag = createRequestFunc<
  uploadAdmin.IGetTagRequest,
  uploadAdmin.IGetTagResponse
>('/get_tag');

export const getItemTag = createRequestFunc<
  uploadAdmin.IGetItemTagRequest,
  uploadAdmin.IGetItemTagResponse
>('/get_item_tag');

export const getMtskuInfoAuditByMtskuId = createRequestFunc<
  uploadAdmin.IGetMtskuInfoAuditByMtskuIdRequest,
  uploadAdmin.IGetMtskuInfoAuditByMtskuIdResponse
>('/get_mtsku_info_audit_by_mtsku_id');

export const getMtskuItemForAdmin = createRequestFunc<
  uploadAdmin.IGetMtskuItemForAdminRequest,
  uploadAdmin.IGetMtskuItemForAdminResponse
>('/get_mtsku_item_for_admin');

export const getSkuStockAudits = createRequestFunc<
  uploadAdmin.IGetSkuStockAuditsRequest,
  uploadAdmin.IGetSkuStockAuditsResponse
>('/get_sku_stock_audits');

export const getPriceAudits = createRequestFunc<
  uploadAdmin.IGetPriceAuditsRequest,
  uploadAdmin.IGetPriceAuditsResponse
>('/get_price_audits');

export const getTaxGroupType = createRequestFunc<
  uploadAdmin.IGetTaxGroupTypeRequest,
  uploadAdmin.IGetTaxGroupTypeResponse
>('/get_tax_group_type');

export const getBooksInfoByIsbn = createRequestFunc<
  uploadAdmin.IGetBooksInfoByIsbnRequest,
  uploadAdmin.IGetBooksInfoByIsbnResponse
>('/get_books_info_by_isbn');

export const validateAnatelAttribute = createRequestFunc<
  uploadAdmin.IValidateAnatelAttributeRequest,
  uploadAdmin.IValidateAnatelAttributeResponse
>('/validate_anatel_attribute');

export const validateAnpAttribute = createRequestFunc<
  uploadAdmin.IValidateANPAttributeRequest,
  uploadAdmin.IValidateANPAttributeResponse
>('/validate_anp_attribute');

export const exportHighBrandItem = createRequestFunc<
  uploadAdmin.IExportHighBrandItemRequest,
  uploadAdmin.IExportHighBrandItemResponse
>('/export_high_brand_item');

export const getProductTaxRates = createRequestFunc<
  uploadAdmin.IGetProductTaxRatesRequest,
  uploadAdmin.IGetProductTaxRatesResponse
>('/get_product_tax_rates');
