syntax = "proto2";

package PBData.beeshop.db;
option java_package = "com.shopee.protocol.shop";

enum Status
{
    option allow_alias = true;
    ITEM_DELETE          = 0; // EK: user delete
    ITEM_NORMAL          = 1;
    ITEM_REVIEWING       = 2;
    ITEM_BANNED          = 3;
    ITEM_INVALID         = 4; // EK: BE delete, also known as ADMIN_DELETED
    ITEM_INVALID_HIDE    = 5;
    ITEM_OFFENSIVE_HIDE  = 6; // https://www.pivotaltracker.com/n/projects/1248076/stories/*********
    ITEM_AUDITING        = 7; // Deprecated. this status was used for cb listing. Not used anymore.
    ITEM_NORMAL_UNLIST   = 8; // after system/user unlist an normal item [SPCS-1308][SPCS-1309]

    SNAPSHOT_DELETE  = 0;
    SNAPSHOT_NORMAL  = 1;

    ORDER_DELETE     = 0;
    ORDER_UNPAID     = 1;
    ORDER_PAID       = 2;
    ORDER_SHIPPED    = 3;  // no use
    ORDER_COMPLETED  = 4;
    ORDER_CANCEL     = 5;

    ORDER_EXT_DELETE            = 0;
    ORDER_EXT_UNPAID            = 1;
    ORDER_EXT_PAID              = 2;
    ORDER_EXT_SHIPPED           = 3;  // no use
    ORDER_EXT_COMPLETED         = 4;
    //ORDER_EXT_CANCEL            = 5;
    ORDER_EXT_INVALID           = 6;
    ORDER_EXT_CANCEL_PROCESSING = 7;
    ORDER_EXT_CANCEL_COMPLETED  = 8;
    ORDER_EXT_RETURN_PROCESSING = 9;
    ORDER_EXT_RETURN_COMPLETED  = 10;
    ORDER_EXT_ESCROW_PAID       = 11;  // OR9,PY11
    ORDER_EXT_ESCROW_CREATED    = 12;  // PY8
    ORDER_EXT_ESCROW_PENDING    = 13;  // PY9
    ORDER_EXT_ESCROW_VERIFIED   = 14;  // PY10
    ORDER_EXT_ESCROW_PAYOUT     = 15;  // PY12
    ORDER_EXT_CANCEL_PENDING    = 16;

    // Logistics status
    LOGISTICS_NOT_STARTED       = 0;
    LOGISTICS_REQUEST_CREATED   = 1;
    LOGISTICS_PICKUP_DONE       = 2;
    LOGISTICS_PICKUP_RETRY      = 3;
    LOGISTICS_PICKUP_FAILED     = 4;
    LOGISTICS_DELIVERY_DONE     = 5;
    LOGISTICS_DELIVERY_FAILED   = 6;
    LOGISTICS_REQUEST_CANCELED  = 7;
    LOGISTICS_COD_REJECTED  = 8;
    LOGISTICS_READY     = 9;
    LOGISTICS_INVALID       = 10;
    LOGISTICS_LOST      = 11;
    LOGISTICS_PENDING_ARRANGE = 12;

    OITEM_DELETE     = 0;
    OITEM_UNRATED    = 1;
    OITEM_RATED      = 2;
    OITEM_RETURNED   = 3;
    OITEM_RETURN_RATED = 4;
    OITEM_CANCEL     = 5;

    PROMOTION_DISABLED = 0;
    PROMOTION_ENABLED = 1;
    PROMOTION_DELETED = 2;

    RETURN_DELETE       = 0;
    RETURN_REQUESTED    = 1;
    RETURN_ACCEPTED     = 2;
    RETURN_CANCELLED    = 3;
    RETURN_JUDGING      = 4;
    RETURN_REFUND_PAID  = 5;
    RETURN_CLOSED       = 6;
    RETURN_PROCESSING   = 7; //Start reverse logistics
    RETURN_SELLER_DISPUTE      = 8; //Seller dispute after item is returned

    REFUND_DELETE    = 0;
    REFUND_CREATED   = 1;
    REFUND_VERIFIED  = 2;
    REFUND_PENDING   = 3;
    REFUND_PAID      = 4;
    REFUND_PAYOUT    = 5;

    PAYMENT_DELETE           = 0;
    PAYMENT_MATCHING         = 1;
    PAYMENT_NO_MATCH         = 2;
    PAYMENT_UNDERPAID        = 3;
    PAYMENT_PAID             = 4;
    PAYMENT_OVERPAID         = 5;
    PAYMENT_WRONGPAID        = 6;
    PAYMENT_FAILED           = 7;
    PAYMENT_CHECKOUT_SPLITED = 8;
    PAYMENT_FRAUD        = 9;

    SHOP_DELETE      = 0;
    SHOP_NORMAL      = 1;
    SHOP_BANNED      = 2; // no use

    SHOP_ENABLE_MAKE_OFFER = 0;
    SHOP_DISABLE_MAKE_OFFER = 1;

    ACCOUNT_DELETE   = 0;
    ACCOUNT_NORMAL   = 1;
    ACCOUNT_BANNED   = 2;
    ACCOUNT_FROZEN   = 3;

    MODEL_DELETE     = 0;
    MODEL_NORMAL     = 1;
    MODEL_UNAVAILABLE = 2; // indicate this item model is not available for buyer. But this status still can be reverted to normal. If parent item status change from normal to abnormal, its child model status would change to unavailable

    FOLLOW_DELETE    = 0; // ShopFollowStatus
    FOLLOW_NORMAL    = 1;

    LIKED_DELETE     = 0;
    LIKED_NORMAL     = 1;

    ADDRESS_DELETE   = 0;
    ADDRESS_NORMAL   = 1;
    ADDRESS_DEFAULT  = 2; // will not used when logistics version released

    CARD_NORMAL      = 0;
    CARD_DELETE      = 1;
    CARD_DEFAULT     = 2;

    SLIP_NOT_UPLOAD  = 0;
    SLIP_UPLOADED    = 1;
    SLIP_APPROVED    = 2;

    OFFER_NONE       = 0;
    OFFER_NEW        = 1;
    OFFER_ACCEPT     = 2;
    OFFER_REJECT     = 3;
    OFFER_CANCEL     = 4;

    MSG_DELETE       = 0;
    MSG_NOT_ACKED    = 1;
    MSG_ACKED        = 2;

    CART_SHOP_HAS_ITEMS     = 0;
    CART_SHOP_HAS_NO_ITEMS  = 1;

    CART_ITEM_DELETED     = 0;
    CART_ITEM_NORMAL      = 1;
    CART_ITEM_UPDATED     = 2;
    CART_ITEM_SELLER_INACTIVE   = 3;
    CART_ITEM_SELLER_FROZEN     = 4;
    CART_ITEM_MISSING_MODEL     = 5;
    CART_ITEM_DELETED_MODEL     = 6;
    CART_ITEM_NO_MODEL          = 7;
    CART_ITEM_BLOCKED           = 8;
    CART_ITEM_FLASH_SALE        = 9;
    CART_ITEM_ADD_ON_DEAL_INVALID = 10;
    CART_ITEM_ADD_ON_DEAL_EXPIRED = 11;

    CMT_DELETE       = 0;
    CMT_NORMAL       = 1;
    CMT_VALID        = 2;
    CMT_HIDDEN       = 3;

    ACTIVITY_DELETE  = 0;
    ACTIVITY_NORMAL  = 1;

    ACTION_DELETE    = 0;
    ACTION_NORMAL    = 1;
    ACTION_GROUPED   = 2;

    AUDIT_DELETE     = 0;
    AUDIT_WAITING    = 1;

    CAT_DELETE       = 0;
    CAT_NORMAL       = 1;
    CAT_DISABLE      = 2;

    COLLECTION_DELETE   = 0;
    COLLECTION_NORMAL   = 1;
    COLLECTION_INACTIVE = 2;

    COLLECTION_ITEM_DELETE   = 0;
    COLLECTION_ITEM_NORMAL   = 1;

    COLLECTION_CLUSTER_DELETE   = 0;
    COLLECTION_CLUSTER_NORMAL   = 1;

    SEARCH_ITEM_INVISIABLE      = 0;
    SEARCH_ITEM_VISIABLE        = 1;
    SEARCH_ITEM_OFFENSIVEHIDE   = 2;

    CONTACT_DELETE   = 0;
    CONTACT_NORMAL   = 1;
    CONTACT_PUSHED   = 2;

    TRANS_HISTORY_NEW       = 0;  // step1. Waiting for buyer to confirm order received
    TRANS_HISTORY_FINISH    = 1;  // step4. Payment transferred successfully
    TRANS_HISTORY_CANCEL    = 2;
    TRANS_HISTORY_VERIFY    = 3;  // won't be generated anymore, replace by TRANS_HISTORY_CREATED
    TRANS_HISTORY_CREATED   = 4;  // step2. Buyer confirmed order received
    TRANS_HISTORY_PAYOUT    = 5;  // step3. Payment transfer has been initiated

    VOUCHER_DELETED = 0;
    VOUCHER_NORMAL = 1;

    CBL_DELETE = 0;
    CBL_NORMAL = 1;

    SELLER_PROMOTION_NORMAL=1;
    SELLER_PROMOTION_DELETED=2;

    // 0 means item unconfirmed in product screening
    SELLER_PROMOTION_ITEM_NORMAL = 1;
    SELLER_PROMOTION_ITEM_DELETED = 2;
    SELLER_PROMOTION_ITEM_END = 3; // This is not END, admin portal treat it as seller_deleted

    DEVICE_NORMAL = 1;
    DEVICE_BAN = 2;

    DEVICE_CHECK_LIMIT = 1;
    DEVICE_CHECK_BAN = 2;
    DEVICE_CHECKOUT_LIMIT_COUNT = 3;
    DEVICE_CHECKOUT_LIMIT_AMOUNT = 4;

    WITHDRAWAL_STATUS_CREATED = 1;
    WITHDRAWAL_STATUS_VERIFIED = 2;
    WITHDRAWAL_STATUS_PAYOUT = 3;
    WITHDRAWAL_STATUS_COMPLETED = 4;
    WITHDRAWAL_STATUS_PENDING = 5;
    WITHDRAWAL_STATUS_CANCELLED = 6;

    TOPUP_UNPAID        = 1;
    TOPUP_PAID          = 2;
    TOPUP_INVALID       = 3;
    TOPUP_ERROR         = 4; // for P&L to silently cancel a topup (no wallet trans OR noti)

    COINRULE_DELETED = 0;
    COINRULE_NORMAL  = 1;

    BA_CHECK_STATUS_NONE        = 0;
    BA_CHECK_STATUS_SUBMITTED   = 1;
    BA_CHECK_STATUS_SUCCEEDED   = 2;
    BA_CHECK_STATUS_FAILED      = 3;  // hideitem
    BA_CHECK_STATUS_RESUBMITTED = 4;  // hideitem

    CARD_PROMOTION_DELETED  = 0;
    CARD_PROMOTION_NORMAL   = 1;

    COMM_RULE_STATUS_DELETED = 0;
    COMM_RULE_STATUS_NORMAL = 1;

    CARD_TXN_FEE_RULE_STATUS_DELETED = 0;
    CARD_TXN_FEE_RULE_STATUS_NORMAL  = 1;

    FLASH_SALE_STATUS_NORMAL = 1;
    FLASH_SALE_STATUS_DELETED = 0;
    FLASH_SALE_STATUS_SYSTEM_REJECT = 3;

    FLASH_SALE_ITEM_STATUS_DISABLED = 0;
    FLASH_SALE_ITEM_STATUS_NORMAL = 1;
    FLASH_SALE_ITEM_STATUS_DELETE = 2;
    FLASH_SALE_ITEM_STATUS_SYSTEM_REJECTED = 4; // 3 already used in SELLER_PROMOTION_ITEM_END
    FLASH_SALE_ITEM_STATUS_MANUAL_REJECTED = 5;

    QC_STATUS_NORMAL = 1;
    QC_STATUS_DELETED = 0;

    ATTR_DELETED = 0;
    ATTR_NORMAL  = 1;

    ATTR_VALUE_DELETED = -1;
    ATTR_VALUE_FAILED = 0;
    ATTR_VALUE_PASSED = 1;
    ATTR_VALUE_NORMAL = 2;
    ATTR_VALUE_PENDING = 3;
    ATTR_VALUE_NA = 4;

    SPU_DELETE = 0;
    SPU_NORMAL = 1;

    CATEGORY_FAILED = 0;
    CATEGORY_PASSED = 1;
    CATEGORY_UPDATED = 2;
    CATEGORY_NA = 3;

    ADDR_TAX_ORDER_DELETED = 0;
    ADDR_TAX_ORDER_NORMAL = 1;

    BUNDLE_DEAL_DELETED = 0;
    BUNDLE_DEAL_NORMAL  = 1;

    PENALTY_HISTORY_HIDE   = 0;
    PENALTY_HISTORY_NORMAL = 1;

    PENALTY_REASON_DELETE = 0;
    PENALTY_REASON_NORMAL = 1;

    SMID_STATUS_NOT_VERIFIED = 0;
    SMID_STATUS_VERIFYING = 1;
    SMID_STATUS_VERIFICATION_FAILED = 2;
    SMID_STATUS_VERIFIED = 3;

    BUNDLE_DEAL_USAGE_DELETED = 0;
    BUNDLE_DEAL_USAGE_NORMAL = 1;

    REFERRAL_RECORDED = 1;
    REFERRAL_SIGNED_UP = 2;
    REFERRAL_COMPLETED = 3;
    REFERRAL_FRAUD = 4;
    REFERRAL_PAID = 5;
    REFERRAL_COIN_REJECTED = 6;

    GB_GROUP_STARTED         = 1; // GroupBuy Group status
    GB_GROUP_PENDING_PAYMENT = 4; // group is timeup but the last buyer hasn't paid yet
    GB_GROUP_DISMISSED       = 5;
    GB_GROUP_FORMED          = 7;

    // voucher_wallet_tab_x.status
    VW_VOUCHER_DELETED = 0;
    VW_VOUCHER_NORMAL  = 1;
    VW_VOUCHER_EXPIRED = 2;

    ITEM_PREVIEW_DELETED   = 0; //ITEM_PREVIEW_DELETED is the status when the preview is deleted before setting
    ITEM_PREVIEW_NORMAL    = 1; //ITEM_PREVIEW_NORMAL is the status when the preview wait for setting or set successfully
    ITEM_PREVIEW_FAILED    = 2;  //ITEM_PREVIEW_FAILED is the status when the corresponding timed task failed

    COIN_REJECT_SUCCESS = 1;
    COIN_REJECT_FAILED = 2;
    COIN_REJECT_PENDING = 3;
    COIN_REJECT_ALLOW_RELEASE = 4;

    SLASH_PRICE_ITEM_DELETE = 0;
    SLASH_PRICE_ITEM_NORMAL = 1;
    SLASH_PRICE_ITEM_HIDDEN = 2;

    SLASH_PRICE_ACTIVITY_DELETE = 0;
    SLASH_PRICE_ACTIVITY_NORMAL = 1;

    SLASH_PRICE_COIN_NORMAL = 1;
    SLASH_PRICE_COIN_REWARDED = 2;

    OS_RESERVE_KEYWORDS_STATUS_DELETED = 0;
    OS_RESERVE_KEYWORDS_STATUS_NORMAL = 1;
}

enum GSTType {
    IGST = 1;
    CGST =2 ;
    SGST =3 ;
}

enum ItemType {
    SHOPEE_ITEM = 0;
    DIGITAL_PURCHASE_ITEM = 1;
}

enum CardTxnFeeRoundingMethod {
    ROUNDING_OFF = 1;
    ROUNDING_UP = 2;
    ROUNDING_DOWN = 3;
}

enum TxnFeeType {
   SELLER_TXN_FEE = 0;
   BUYER_TXN_FEE = 1;
}

enum TxnFeeRuleUserGroup {
    TXN_FEE_RULE_USER_DEFAULT = 0;
    TXN_FEE_RULE_CB_SELLER = 1; // New field use for cb seller
    TXN_FEE_RULE_SPECIAL_GROUP = 2; // used only for special group rules
}

enum AccountWalletSetting {
    DEFAULT_WALLET = 0;
    ENABLE_WALLET = 1;
    DISABLE_WALLET = 2;
    ENABLE_CB_WALLET = 3;
}

enum SellerPromotionSource {
    SHOPEE = 0;
    SELLER = 1;
    FLASH_SALE = 2;
    SELLING_PRICE = 3;
}

enum SellerDiscountSource {
    SELLER_DISCOUNT_SC = 0; // Seller discount is created in Seller center
    SELLER_DISCOUNT_BE = 1; // Seller discount is created in Admin portal
}

enum CoinTransactionType {
    EARN_COMPLETE_ORDER = 1; //when order completed, will earn some coins
    EARN_CANCEL_ESCROW = 2;  //when cancel the order that already completed, will deduct the already earning coins
    SPEND_CREATE_ORDER = 3;  //when create the order, will spend some conis
    SPEND_INVALID_ORDER = 4; //when unpaid order canceled, will add the spending coins back
    SPEND_CANCEL_ORDER = 5;  //when the paid order canceled, will add the spending coins back
    SPEND_RETURN_ORDER = 6;  //when order returned, will add the spending coins back(only return part)
    EXPIRE = 7;              //coin expired
    ADMIN_UPDATE = 8;        //backend can manually update one's coin
    REWARD = 9;
    TOKEN = 10;              //Spend coins on tokens. eg lucky draw chance
    REFERRAL_COIN = 11;      //Coin earn from referral program
    REDEEM_VOUCHER = 12;     //Use Coin to redeem vouchers
    DP_CREATE_ORDER = 13; // create order spend coin
    DP_CANEL_ORDER = 14; // canel order return coin
    DP_COMPLETED_ORDER = 15; // completed order earn coin
    RATE_ORDER_COIN = 16; // rate order earn coin
    SLASH_PRICE_COIN = 17; //Coin earn from slash price
    RATE_ORDER_COIN_RETRIEVED = 18; // Take back the rate order coins
    SPEND_CANCEL_PARCEL = 19;  //when the parcel is canceled, will add the spending coins back
    FOODY_COMPLETED_ORDER = 20;
    FOODY_REDEEM_VOUCHER = 21;
    FOODY_MANUAL_UPDATE = 22;
    AIRPAY_CREATE_TXN = 23;
    AIRPAY_CANCEL_TXN = 24;
    AIRPAY_REFUND_TXN = 25;
    AIRPAY_COMPLETED_TXN = 26;
    AIRPAY_REWARDS = 27;
    SELLER_COIN = 28; // Used when seller credit coin to a buyer's coin account
}

enum CoinCheckShopFlag {
    SPEND = 1; //1<<0 0: can only spend on prefered sellers, 1: can spend on all shops,
    EARN = 2; // 1<<1 0: can earn for all shops, 1: can only earn for prefered sellers
}

enum WithdrawalType {
    NO_SPLIT = 1;
    SPLIT_ROOT = 2;
    SPLIT_SUB = 3;
}

enum WithdrawalSource {
    ONETIME_SELLER = 1;
    ONETIME_ADMIN= 2;
    RECURRING = 3;
}

enum WithdrawalTargetType {
    BANK_ACCOUNT = 1;
    SHOPEEPAY_ACCOUNT = 2;
}

enum TransactionType {
    ESCROW_VERIFIED_ADD = 101;
    ESCROW_VERIFIED_MINUS = 102;
    OFFLINE_ESCROW_ADD = 103;
    WITHDRAWAL_CREATED = 201;
    WITHDRAWAL_COMPLETED = 202;
    WITHDRAWAL_CANCELLED = 203;
    REFUND_VERIFIED_ADD = 301;
    AUTO_REFUND_ADD     = 302;
    FOODY_REFUND_ADD    = 303;
    ADJUSTMENT_ADD      = 401;
    ADJUSTMENT_MINUS    = 402;
    SHOPEE_BUDDY_ADD    = 403;
    PAID_ADS_CHARGE     = 450; // Range 450-499 is reserved for seller services
    PAID_ADS_REFUND     = 451; // -

    FAST_ESCROW_DISBURSE            = 452; // ADD
    FAST_ESCROW_PRINCIPAL_DEDUCT    = 453; // DEDUCT
    FAST_ESCROW_INTEREST_DEDUCT	    = 454; // DEDUCT
    AFFILIATE_ADS_SELLER_FEE	    = 455; // DEDUCT
    AFFILIATE_ADS_SELLER_FEE_REFUND = 456; // ADD
    INFLUENCER_CREDIT	            = 457; // ADD
    FAST_ESCROW_DEDUCT              = 458; // Replaces FAST_ESCROW_PRINCIPAL_DEDUCT and FAST_ESCROW_INTEREST_DEDUCT
    SHOPEE_WALLET_PAY   = 501;
    SPM_DEDUCT          = 502; // SPM deduct balance in Shopee Wallet
    APM_DEDUCT          = 503; // APM deduct balance in Shopee Wallet
    SPM_REFUND_ADD      = 504;
    APM_REFUND_ADD      = 505;
    TOPUP_SUCCESS       = 601;
    TOPUP_FAILD         = 602;
    DP_REFUND_VERIFIED_ADD = 701; // Digital purchase refund verified
    SPM_DEDUCT_DIRECT   = 801;
    SPM_DISBURSE_ADD    = 802;
}

enum TransactionListType {
    MONEY_INOUT = 1;
    MONEY_IN    = 2;
    MONEY_OUT   = 3;
}

enum LogisticsShopFlag {
    ENABLE_SHOPEE_SUPPORTED = 1;
    SHOP_ENABLE_SHOPEE_SUPPORTED = 2;
    SHOW_FREE_SHIPPING = 4;
}

enum PreferredDeliveryOption {
    DELIVERY_ANYTIME = 0;
    DELIVERY_OFFICE_HOUR = 1;
}

enum ItemFlags {
    IS_FAKE_ITEM = 1;
    FREE_SHIPPING = 2;                  // 1 << 1 FreeShippingFilter + FreeShippingIcon in ProductCard
    SEO_DESCRIPTION = 4;                // 1 << 2 means QC find there is seo terms in description
    INTEGRATED_FREE_SHIPPING = 8;       // 1 << 3 maintained by xianyou
    OFFLINE_DEAL = 16;                  // 1 << 4 means QC find this item is not a product, it is offline payment channel - EK
    NO_SEARCHABLE = 32;                 // 1 << 5 search team will use
    IS_HIDDEN = 64;                     // 1 << 6 search team will use

    IS_SYSTEM_UNLIST       = 128;       // 1 << 7  indicate the item is delisted by admin SPCS-1309
    IS_USER_UNLIST         = 256;       // 1 << 8  indicate the item is delisted by seller SPCS-1308
    IS_PREVIEW             = 512;       // 1 << 9  this mean the item preview and user cannot order the item
    IS_PRICE_MASK          = 1024;      // 1 << 10 this mean the item price cannot be displayed to user
    HAS_VIRTUAL_SKU        = 2048;      // 1 << 11 this means this item has virtual sku model.
    HAS_CHILD_SKU          = 4096;      // 1 << 12 this means this item/this item's model is the parent of some virtual sku. This flag would not change if child virtual sku status change
    VIRTUAL_GOODS          = 8192;      // 1 << 13 this flag is used to indicate this item is virtual goods or not. This is used for order fulfill selection. can refer to SPOT-12416

    // P&L: payment channel filter
    COD_FILTER = 65536;                 // 1 << 16 for indexing of SearchFilter
    CREDIT_CARD_FILTER = 131072;        // 1 << 17 for indexing of SearchFilter
    NON_CC_INSTALLMENT_FILTER = 262144; // 1 << 18 for indexing of SearchFilter
    CC_INSTALLMENT_FILTER = 524288;     // 1 << 19 for indexing of SearchFilter

    // P&L: Service by shopee + Badge display type
    SERVICE_BY_SHOPEE_1 = 33554432;     // 1 << 25 TW: 24h | others: SBS
    SERVICE_BY_SHOPEE_2 = 67108864;     // 1 << 26 No use for now, will be substituted by badge_type instead
    SERVICE_BY_SHOPEE_3 = 134217728;    // 1 << 27 No use for now, will be substituted by badge_type instead

    BADGE_TYPE_24H = 268435456;         // 1 << 28 identify which badge to show in ProductCard
    BADGE_TYPE_4H  = 536870912;         // 1 << 29 identify which badge to show in ProductCard
}

enum RequestSource {
    FROM_UNKNOWN = 1;
    FROM_APP = 2;
    FROM_API = 3;
    FROM_SELLER_CENTER_SINGLE = 4;
    FROM_SELLER_CENTER_MASS = 5;
    FROM_ADMIN              = 6;
}

enum MyIncomeType {
    ACTUAL_SHIPPING_COST    = 1;
    SHIPPING_FEE_REBATE     = 2;
    SHIPPING_FEE_PAIDBY_BUYER = 3;
    REBATE_FEE              = 4;
    SHIPPING_FEE_SELLER_DISCOUNT = 5;  // A seller discount column, representing a special discount section to be distinguished from shipping fee rebate.
}

enum UserAgentType { // used in CreateMultiOrder, PromotionRuleSet, etc.
    IOS = 1;
    ANDROID = 2;
    WEB = 4;
}

enum LogisticsOrderFlag {
    HAS_TRACKING = 2; // 1<<1 // Whether logistics tracking is available for this order
    ESCROW_RELEASE_INCLUDES_SHIPPING = 4; // 1<<2 // Whether the escrow release needs to include shipping fee rebates

    MANUAL_CONSIGNMENT = 1; // 1<<0 // Order's logistics channel allows manual input of consignment number
    AUTO_CONSIGNMENT = 8; // 1<<3 // Order's logistics channel is in dropoff mode (tracking number request from 3PL)
    ARRANGE_CONSIGNMENT = 16; // 1<<4 // Order's logistics channel is in pickup mode (arrange pickup)
    IS_SELF_COLLECT = 8192; // 1<<13 // Order's (non-integrated) logistics channel is a form of self collection (no courier involved)

    // ------------------------
    // DEPRECATION NOTICE: These flags will be obsolete once all countries have migrated to the new promotion system (reading from OrderExtInfo.logistics_info.logistics_channel_promotion_rule_id)

    // The following flags are mutually exclusive!
    BUYER_PAID_FLAT_SHIPPING_FEE = 32; // 1<<5 // Order has flat discount
    DISCOUNT_PERCENTAGE = 64; // 1<<6 // Order has percentage discount
    DISCOUNT_FIXED_DEDUCTION = 128; // 1<<7 // Order has fixed deduction discount

    REBATE_SHIPPING_FEE = 256; // 1<<8 // Order's shipping fee rebate logic is customized
    REBATE_SHIPPING_FIXED_FEE = 512; // 1<<9 // Order's shipping fee rebate is up to a fixed amount (depends on flag above)
    // ------------------------

    DISABLE_SHIPPING_REBATE = 1024; // 1<<10 // This order has been banned from receiving shipping fee rebates

    SHOPEE_GENERATED_TRACKING_NUMBER = 2048; // 1<<11 // Order's logistics channel will autogenerate tracking number for order (instead of seller input manually)

    SELLER_COVER_SHIPPING_FEE = 4096; // 1<<12 // Whether this order's estimated shipping fee is absorbed by the seller. This does not control whether the order receives rebates.

    HOLD_ESCROW = 16384;              // 1<<14 // Whether this order can go to ESCROW_CREATED or not

    IS_RAPID_SLA = 268435456; // 1<<28 // The channel is a Rapid SLA Channel

    SUPPORT_CROSS_BORDER = 536870912; // 1<<29 // Order is a cross border channel
    // DISCOUNT_BUYER_LOCATION_CHECK CAN OVERLAP
    DISCOUNT_BUYER_LOCATION_CHECK = 1073741824;  // 1<<30
}

enum OrderDetailFlag {
    SELLER_SEEN_ORDER_DETAIL_AFTER_SHIPPING_READY = 1; // if seller has seen order details page after logistics_ready
    SERVICE_BY_SHOPEE = 2;        // 1 << 1, Service By Shopee order
    BLOCK_MANUAL_SHIPPING = 4;    // 1 << 2, Auto push shipment order (See also: https://confluence.garenanow.com/x/s7e5)
    IS_WELCOME_PACKAGE = 8;       // 1 << 3, Welcome package order
    FULFILLED_BY_SHOPEE = 16; // 1 << 4, order fulfilled by shopee
    FULFILLED_BY_CB_SELLER = 32; // 1 << 5, order fulfilled by cross border seller
    FULFILLED_BY_LOCAL_SELLER = 64; // 1 << 6, order fulfilled by local seller
    IS_SIP_CB = 128; // 1 << 7, order is in SIP CB mode(Shopee International Platform)
    IS_SIP_PRIMARY = 256; // 1 << 8, order is from a primary shop in SIP(Shopee International Platform)
    IS_SIP_AFFILIATED = 512; // 1 << 9, order is from an affiliated shop in SIP(Shopee International Platform)

    SHOP_SUPPORT_RECEIPT = 65536; // 1 << 16 snapshot for is_shop_support_e_receipt
    FAST_ESCROW = 131072;         // 1 << 17, if linked to a fast_escrow entity
}

enum OrderDetailedStatusType {
    ORDER_DETAIL_CREATED = 1; // "buyer clicks on checkout"
    ORDER_DETAIL_SHIPPING_CONFIRMED = 2; // "buyer makes payment / cod confirmed"
    ORDER_DETAIL_PROCESSING = 3; // "seller opens order details for first time"
    ORDER_DETAIL_SHIPPED = 4; // "seller clicks ship"
    ORDER_DETAIL_RECEIVED = 5; // "buyer clicks order received"
    ORDER_DETAIL_CANCELED = 6; // "order is cancelled"
    ORDER_DETAIL_NOTIFY_SHIP = 7; // "seller has notified 3pl to ship order"
}

enum CheckoutPaymentFlag {
    REFUND_BY_API = 1;
    NEED_BANK_ACCOUNT = 2;
    IS_CREDIT_CARD = 4;
    IS_FREE_SHIPPING = 8;
    IS_INSTALLMENT = 16;
    MANUAL_ESCROW = 32;
    IS_SAFE_INTEGRATED = 64;
    SUPPORT_PARTIAL_REFUND = 128;
    IS_SHOPEE_LITE = 256;
}

enum PaymentMethod
{
    PAY_NONE                    = 0;
    PAY_CYBERSOURCE             = 1;
    PAY_BANK_TRANSFER           = 2;
    PAY_OFFLINE_PAYMENT         = 3; // deprecated!!! don't use
    PAY_IPAY88                  = 4; // MY pay method
    PAY_FREE                    = 5;
    PAY_COD                     = 6; // Cash on delivery
    PAY_ESUN                    = 7; // TW used payment
    PAY_BILL_PAYMENT            = 8; // TH bill payment
    PAY_INDOMARET               = 13; // ID offline payment Indomaret
    PAY_KREDIVO                 = 14; // ID non-cc Installment
    PAY_NICEPAY_CC              = 15; // ID cc Installment
    PAY_ESUN_CB                 = 16; // TW CB Payment
    PAY_IPAY88_CC               = 17; // MY CC Payment
    PAY_AIRPAY_CC               = 18; // Airpay CC Payment
    PAY_BCA_ONE_KLIK            = 19; // ID BCA One Klik Payment
    PAY_SHOPEE_WALLET           = 20; // https://docs.google.com/document/d/122BHnwBm9J42RvpONdJPPN1ozaXI0iY5euqWzWdQSpI/edit#
    PAY_AKULAKU                 = 21; // ID Akulaku
    PAY_STRIPE_CC               = 22; // PH Stripe CC
    PAY_AIRPAY_CC_INSTALLMENT   = 23; // Airpay CC Installment (TH)
    PAY_SHOPEE_CREDIT           = 24;
    PAY_ALFAMART                = 25;
    PAY_CYBERSOURCE_INSTALLMENT = 26;
    PAY_SHOPEE_WALLET_V2        = 27;
    PAY_AIRPAY_WALLET_V2        = 28;
    PAY_VN_IBANKING             = 29;
    PAY_AIRPAY_GIRO             = 30;
    PAY_SIPP_VA                 = 31; // SIPP Virtual Account
    PAY_WIRECARD_INSTALLMENT    = 32;
    PAY_JKO_PAY                 = 33;
    PAY_MOLPAY_CASH             = 34;
    PAY_JKO_COD                 = 35;
    PAY_JKO_BT                  = 36;
    PAY_MOLPAY_IBANKING         = 37;
    PAY_EBANX_BOLETO            = 38;
    PAY_EBANX_CC                = 39;
    PAY_EBANX_IBANKING          = 40;
    PAY_SIPP_MIXED_VA           = 41; // SIPP Mixed Virtual Account
    PAY_MPGS_CC                 = 42; // Mastercard Payment Gateway Services CC
    PAY_WECHAT_PAY              = 43;
    PAY_CF_UPI                  = 44;
    PAY_CF_CC                   = 45;
    PAY_CF_IBANKING             = 46;
    PAY_PAY_AT_SHOP             = 47;
    PAY_MAYBANK                 = 48;
    PAY_GOOGLE_PAY              = 49;
    PAY_AIRPAY_IBANKING_V2      = 50;
    PAY_UOB_PAYNOW              = 51;
    PAY_RPP_ONLINE_BANKING      = 52;
    PAY_SIPP_DIRECT_DEBIT       = 53;
    PAY_CORP_BILLING            = 54;
    PAY_EBANX_SPEI              = 55;
    PAY_OXXO                    = 56;
    PAY_SIPP_CC                 = 57;
    PAY_RPP_DIRECT_DEBIT        = 58;
    PAY_SEABANK_DIRECT_DEBIT    = 59;
    PAY_BALOTO                  = 60;
    PAY_EFECTY                  = 61;
    PAY_PSE                     = 62;
    PAY_SERVIPAG                = 63;
    PAY_BPI_GIRO                = 64;
    PAY_WEBPAY                  = 65;
}

enum VoucherPaymentType
{
    VPT_CREDIT_CARD_NO_INSTALLMENT    = 1; // paid by credit card without installment
    VPT_CREDIT_CARD_INSTALLMENT       = 2; // paid by credit card with installment
    VPT_COD = 3; // paid by cash on delivery
}

enum CompareMethod
{
    EQUAL = 1;
    LE = 2;
    GE = 3;
    LESS = 4;
    GREATER = 5;
    IN = 6;
    NOTIN = 7;
}

enum PaymentStatus
{
    NONE                    = 0;
    SUCCESS                 = 1;
    BANK_TRANSFER_INIT      = 100;
    BANK_TRANSFER_VERIFYING = 101;
    BANK_TRANSFER_REINIT    = 102;
    BANK_TRANSFER_PENDING   = 103;
    OFFLINE_PAYMENT         = 200;
}

enum EscrowPaymentMethod
{
    BANK_TRANSFER   = 1;
    SEVEN_ELEVEN    = 2;
}

enum StatsType
{
    ORDERS = 0;
    INCOME = 1;
    VISITS = 2;
    SEARCH = 3;
}

enum AuditType
{
    ITEM_NEW                = 1;
    ITEM_EDIT               = 2;
    ITEM_REPORT             = 3;
    ITEM_DEL                = 4;
    USER_REPORT             = 5;
    BANKACC                 = 6;
    CHECKOUT                = 7;
    REFUND                  = 8;
    ESCROW_RELEASE          = 9;
    PAYMENT_STATUS          = 10;
    MANUAL                  = 11;
    ITEM_STATUS             = 12;
    SHOP_UPDATE             = 13;
    MODEL_EDIT              = 14;
    MODEL_ADD               = 15;
    MODEL_DEL               = 16;
    VIDEO_ADD               = 17;
    VIDEO_EDIT              = 18;
    VIDEO_DELETE            = 19;
    ACCOUNT_UPDATE          = 20;
    REFERRAL_UPDATE         = 21;
    ITEM_UNLIST             = 22;
    SLASH_PRICE_RULE_UPDATE = 23;
    ITEM_LICENSE            = 24;
    SLASH_PRICE_ITEM_CREATED = 25;
    SLASH_PRICE_ITEM_UPDATED = 26;
    SLASH_PRICE_ITEM_DELETED = 27;
    BRAND_NEW                = 28;
    BRAND_EDIT               = 29;
    BRAND_DELETE             = 30;
    JKO_SELLER_UPDATE        = 31;
    JKO_BUYER_UPDATE         = 32;
    ITEM_FLAG_EDIT             = 33;
}

enum ItemOption
{
    FIRSTITEM_DISMISSED = 1;
    INVALID_DISMISSED = 2;
}

enum OrderType
{
    UNKNOWN = 0;
    SIMPLE = 1; // deprecated: offline order, won't be used anymore
    ESCROW = 2;
}

enum BankaccVerified
{
    UNSUBMIT    = 0;
    REQUESTED   = 1;
    APPROVED    = 2;
    REJECTED    = 3;
    CHECKED     = 4;
    BANNED      = 5;
}

enum EscrowOption
{
    ESCROW_OFF = 0;
    ESCROW_ON  = 1;
}

enum TransType
{
    OTHER_ORDER    = 0;
    ESCROW_ORDER   = 1;
    WITHDRAW       = 2;
}

enum ActivityType
{
    FOLLOW_YOU              = 0;
    LIKE_YOUR_ITEM          = 1;
    YOUR_LIKEITEM_UPDATED   = 2;
    AT_YOU_IN_COMMENT       = 3;
    COMMENT_YOUR_ITEM       = 4;
    CONTACT_REGISTERED      = 5;
    ITEM_RATED              = 6;
    CUSTOMIZED_ACTIVITY     = 7;
    AT_YOU_IN_FEED_CMT      = 8;
    COMMENT_YOUR_FEED       = 9;
}

enum ProductListWeightType
{
    BOOST       = 0;
    ARRIVAL     = 1;
    SOCIAL      = 2;
    SALE        = 3;
    RANDOM      = 4;
    SOCIAL_N    = 5;
    SALE_M      = 6;
    SHOP_SCORE  = 7;
    ITEM_RATING  = 8;
}
enum PredefinedLabelFlag {
    UNDER_REVIEW                        = 1001; // if shop,item,etc label extinfo contains this flag,shop and item can not be searchable
    //block buy platform for item or shop label using to define which paltform will be block when search item or shop
    BLOCK_BUYER_PLATFORM_OTHERS         = 1000000;
    BLOCK_BUYER_PLATFORM_IOS_WEB        = 1000001;
    BLOCK_BUYER_PLATFORM_IOS_APP        = 1000002;
    BLOCK_BUYER_PLATFORM_ANDROID_WEB    = 1000003;
    BLOCK_BUYER_PLATFORM_ANDROID_APP    = 1000004;
    BLOCK_BUYER_PLATFORM_PC_MALL        = 1000005;
}

enum ActivityUpdateType
{
    ITEM_DELETED        = 0;
    ITEM_OUTOF_STOCK    = 1;
    ITEM_EDITED         = 2;
}

enum ActionRedirctType
{
    REDIRECT_NONE                       = 0;    //NA
    REDIRECT_ORDERS_DETAIL              = 1;
    REDIRECT_MY_PRODUCTS                = 2;
    REDIRECT_MY_INCOME                  = 3;
    REDIRECT_ORDERS_RETURNREFUND        = 4;
    REDIRECT_ORDERS_CANCEL              = 5;
    REDIRECT_OUTOF_STOCK                = 6;
    //REDIRECT_REFUND_SELLERCANCEL        = 7;
    REDIRECT_PURE_REFUND                = 8;
    REDIRECT_NEW_WEB_PAGE               = 10;   // go to the action_redirect_url
    REDIRECT_UPLOAD_RECEIPT_PAGE        = 11;   //Upload receipt page of that checkout
    REDIRECT_SHOPING_CART               = 12;
    REDIRECT_BUNCH_ORDERS_DETAIL        = 13;
    //REDIRECT_BUY_PAGE                   = 14;
    //REDIRECT_CHAT_PAGE                  = 15;
    REDIRECT_ORDER_CHAT_PAGE            = 16;
    //REDIRECT_PRODUCT_OFFER_CHAT_PAGE    = 17;
    REDIRECT_RELATED_PRODUCT_PAGE       = 18;
    REDIRECT_CREDIT_CARD_PAYMENT_PAGE   = 19;
    REDIRECT_MY_WALLET                  = 20;
    REDIRECT_EDIT_SHOP_PROFILE          = 21;
    REDIRECT_APP_PATH                   = 22;    // use ActionContent.action_app_path
    REDIRECT_MY_ACCOUNT                 = 23;
    REDIRECT_APP_ROUTE                  = 24;
    REDIRECT_REACTNATIVE_PATH           = 25;    // use ActionContent.action_reactnative_path
    REDIRECT_EXTERNAL_WEBPAGE           = 26;
}

enum ItemCondition
{
    //NOT_SET           = -1; // just for BI note; request by EK
    NOT_SET             = 0;
    NEW_WITH_TAGS       = 1;
    NEW_WITHOUT_TAGS    = 2;
    NEW_WITH_DEFECTS    = 3;
    USED                = 4;
    NEW_OTHERS          = 5;
    USED_LIKE_NEW       = 6;
    USED_GOOD           = 7;
    USED_ACCEPTABLE     = 8;
    USED_WITH_DEFECTS   = 9;
}

enum NotiMethod
{
    NM_ALL                = 1;
    NM_AR                 = 2;
    NM_ACTIVITY           = 4;
    NM_EMAIL              = 8;
    NM_SMS                = 16;
    NM_PUSH               = 32;
    NM_WEB_PUSH           = 64;
    NM_INAPP              = 128;
}

enum NotiGroupType
{
    GROUPBY_CHECKOUT  = 1;
    GROUPBY_ORDER     = 2;
    GROUPBY_TRANS     = 3;
    GROUPBY_TOPUP     = 4;
    GROUPBY_DP_ORDER  = 5;
    GROUPBY_RETURN_ID = 6;
}

enum PNOption
{
    // values are in bit shift format
    NOTI_ALL                = 1; // 1 << 0
    NOTI_ACTION_REQUIRED    = 2;  // Order Updates - 1 << 1
    NOTI_ACTIVITY           = 4; // 1 << 2
    NOTI_CHATS              = 8; // 1 << 3
    NOTI_STOCK              = 16; // List Updates - // 1 << 4
    NOTI_GROUP_NOTI_OFF     = 32; // 1 << 5
    NOTI_SHOPEE_PROMOTION   = 64; // 1 << 6
    NOTI_RATING             = 128;  // 1 << 7

    EMAIL_ALL               = 256; // 1 << 8
    EMAIL_ORDER_UPDATES     = 512; // 1 << 9
    EMAIL_LIST_UPDATES      = 1024; // 1 << 10
    EMAIL_NEWS_LETTER       = 2048; // 1 << 11
    EMAIL_PERSONALISED      = 4096; // 1 << 12

    NOTI_WALLET             = 8192; // 1 << 13
    NOTI_ADS                = 16384; // 1 << 14

    PN_FEED_COMMENTED       = 32768; // 1 << 15
    PN_FEED_LIKED           = 65536; // 1 << 16
    PN_FEED_MENTIONED       = 131072; // 1 << 17

    NOTI_SHOPEEFOOD = 262144; // 1 << 18

    NOTI_SHOPEEPAY_TRANSACTIONAL_UPDATES = 524288; // 1 << 19

    NOTI_FEED            = 1048576; // 1 << 20
    NOTI_LIVESTREAM      = 2097152; // 1 << 21
    NOTI_GAMES           = 4194304; // 1 << 22
    NOTI_SHOPEEPAY_LATER = 8388608; // 1 << 23

    NOTI_SELLER_ORDER_UPDATES = 16777216; // 1 << 24
    NOTI_SELLER_RETURN_REFUND = 33554432; // 1 << 25
    NOTI_SELLER_WALLET        = 67108864; // 1 << 26
    NOTI_SELLER_MARKET_CENTER = 134217728; // 1 << 27
    NOTI_SELLER_PERFORMANCE   = 268435456; // 1 << 28
}

enum ActionCategory
{
    ACTION_PROMOTIONS         = 1;  // NOTI_SHOPEE_PROMOTION || NOTI_PERSONALISED depends on TriggerCustomizedNoti.type
    ACTION_LISTING_UPDATES    = 2;
    ACTION_ACTIVITY           = 3;
    ACTION_ORDER_UPDATES      = 4;
    ACTION_CHAT               = 5;
    ACTION_IMPT_UPDATES       = 6;  // NOTI_SHOPEE_PROMOTION
    ACTION_RATING             = 7;
    ACTION_WALLET             = 8;
    ACTION_ADS                = 9;
    ACTION_FEED               = 10;
    ACTION_NOW                = 11;
    ACTION_DISCUSS_MESSAGES   = 12; // NOTI DRC MESSAGE
    ACTION_SHOPEE_PAYLATER    = 13; //used by kredit team only
    ACTION_NOMINATIONS        = 14; //used by shop flash sale

    ACTION_FBS_PRD_NOMINATION_UPDATES = 15;
    ACTION_FBS_INBOUND_UPDATES = 16;

    ACTION_SELLER_ORDER_UPDATES = 17;
    ACTION_SELLER_RATING = 18;
    ACTION_SELLER_RETURN_REFUND = 19;
    ACTION_SELLER_LISTING = 20;
    ACTION_SELLER_WALLET = 21;
    ACTION_SELLER_MARKETING_CENTRE = 22;
    ACTION_SELLER_PERFORMANCE = 23;
    ACTION_SELLER_SHOPEE_UPDATES = 24;

    ACTION_DISCUSS_MESSAGES_BUYER_MIGRATION = 25;
    ACTION_DISCUSS_MESSAGES_SELLER_MIGRATION = 26;
    ACTION_WALLET_SELLER_LISTING_MIGRATION = 27;
    ACTION_LISTING_SELLER_WALLET_MIGRATION = 28;
    ACTION_LISTING_PERF_MIGRATION = 29;
    ACTION_LISTING_SHOPEE_UPDATE_MIGRATION = 30;
    ACTION_WALLET_SHOPEE_UPDATE_MIGRATION = 31;
    ACTION_GAMES = 32;
    ACTION_SHOPEE_FOOD_UPDATES= 33;// Folder name: ShopeeFood Updates, only appears in merchant app.
    ACTION_SHOPEE_PAY_TRANSACTIONAL_UPDATES = 34; // Folder name: Transactional Updates, for ShopeePay App only
    ACTION_SHOPEE_FOOD_BUYER_UPDATES= 35;// Folder name: ShopeeFood Updates, only appears in buyer app.
    ACTION_SHOPEE_FOOD_DRIVER_UPDATES = 36; // Action Category only shown in ShopeeFoodDriverApp
}


enum ContactAcctype
{
    PHONE    = 1;
    FACEBOOK = 2;
    EMAIL    = 3;
    PHONE_AND_EMAIL    = 4;
    BEETALK  = 5;
}

enum CheckoutBuyerCancelReason
{
    BUYER_CANCEL_CHECKOUT_REASON_SELLER_UNRESPONSIVE = 1;
    BUYER_CANCEL_CHECKOUT_REASON_SELLER_REQUEST = 2;
    BUYER_CANCEL_CHECKOUT_REASON_MODIFY_ORDER = 3;
    BUYER_CANCEL_CHECKOUT_REASON_BAD_PRODUCT_REVIEWS = 4;
    BUYER_CANCEL_CHECKOUT_REASON_ORDER_TAKES_TOO_LONG_TO_SHIP = 5;
    BUYER_CANCEL_CHECKOUT_REASON_UNTRUSTWORTHY_SELLER = 6;
    BUYER_CANCEL_CHECKOUT_REASON_OTHERS = 7;
    BUYER_CANCEL_CHECKOUT_REASON_UPDATE_VOUCHER_CODE = 8;
    BUYER_CANCEL_CHECKOUT_REASON_CHANGE_OF_MIND = 9;
    BUYER_CANCEL_CHECKOUT_REASON_UPDATE_DELIVERY_ADDRESS = 10;
}

enum OrderBuyerCancelReason
{
    BUYER_CANCEL_REASON_UPDATE_DELIVERY_ADDRESS = 501;
    BUYER_CANCEL_REASON_UPDATE_VOUCHER_CODE = 502;
    BUYER_CANCEL_REASON_MODIFY_ORDER = 503;
    BUYER_CANCEL_REASON_PAYMENT_PROCEDURE_TROUBLESOME = 504;
    BUYER_CANCEL_REASON_FOUND_CHEAPER_ALTERNATIVE = 505;
    BUYER_CANCEL_REASON_CHANGE_OF_MIND = 506;
    BUYER_CANCEL_REASON_OTHERS = 507;
    BUYER_CANCEL_REASON_APPROVAL_REJECTED = 508;
    BUYER_CANCEL_REASON_CANNOT_PLACE_ORDER = 509;
}

enum OrderCancelReason
{
    CANCEL_REASON_NONE               = 0;
    CANCEL_REASON_OUT_OF_STOCK       = 1;
    CANCEL_REASON_CUSTOMER_REQUEST   = 2;
    CANCEL_REASON_UNDELIVERABLE_AREA = 3;
    CANCEL_REASON_CANNOT_SUPPORT_COD = 4;
    CANCEL_REASON_LOST_PARCEL        = 5;
    CANCEL_REASON_GAME_COMPLETED     = 6; // Used by $1 game shop to cancel the order

    CANCEL_REASON_SYSTEM_UNPAID           = 100;
    CANCEL_REASON_SYSTEM_UNDERPAID        = 101;
    CANCEL_REASON_SYSTEM_PAYMENT_REJECTED = 102;

    CANCEL_REASON_LOGISTICS_REQUEST_CANCELED = 200;
    CANCEL_REASON_LOGISTICS_PICKUP_FAILED    = 201;
    CANCEL_REASON_LOGISTICS_DELIVERY_FAILED  = 202;
    CANCEL_REASON_LOGISTICS_COD_REJECTED     = 203;
    CANCEL_REASON_BACKEND_LOGISTICS_NOT_STARTED = 204;
    CANCEL_REASON_TWS_CANCEL = 205;

    CANCEL_REASON_BACKEND_ESCROW_TERMINATED = 300;
    CANCEL_REASON_BACKEND_INACTIVE_SELLER = 301;
    CANCEL_REASON_BACKEND_SELLER_DID_NOT_SHIP = 302;
    CANCEL_REASON_ORDER_NOT_REACH_WAREHOUSE = 303;

    CANCEL_REASON_RULE_ENGINE_AUTO_CANCEL = 400;

    CANCEL_REASON_CUSTOM_KYC_EXPIRY = 701;
    CANCEL_REASON_CUSTOM_KYC_CANCEL = 702;

    CANCEL_REASON_LOGISTICS_ISSUE = 801;
    CANCEL_REASON_FRAUD_CASE = 901;

    CANCEL_REASON_CUSTOM_CORPBILLING_EXPIRE = 1001;
    CANCEL_REASON_CUSTOM_CORPBILLING_BLACKLIST = 1002;
}

enum ReturnReason
{
    RETURN_REASON_NONE         = 0;
    RETURN_REASON_NONRECEIPT   = 1;
    RETURN_REASON_WRONG_ITEM   = 2;
    RETURN_REASON_ITEM_DAMAGED = 3;
    RETURN_REASON_DIFF_DESC    = 4;
    RETURN_REASON_MUTUAL_AGREE = 5;
    RETURN_REASON_OTHER        = 6;
    RETURN_REASON_USED         = 7;

    RETURN_REASON_ITEM_WRONGDAMAGED     = 101;
    RETURN_REASON_CHANGE_MIND           = 102;
    RETURN_REASON_ITEM_MISSING          = 103;
    RETURN_REASON_EXPECTATION_FAILED    = 104;
    RETURN_REASON_ITEM_FAKE             = 105;

    RETURN_REASON_PHYSICAL_DMG = 106;
    RETURN_REASON_FUNCTIONAL_DMG = 107;
}

enum ReturnDisputeReason
{
    REJECT_NONRECEIPT = 1;
    REJECT_OTHER      = 2;
    NOT_RECEIVED      = 3;
}

enum ReturnFlag
{
    RETURN_IS_OFFICIAL_SHOP = 1;
    RETURN_IS_AUTO_RETURN_JUDGING = 2;
    RETURN_IS_ELIGIBLE_FOR_SELLER_COMPENSATION_AFTER_RETURN_ACCEPTED = 4;
    RETURN_IS_ELIGIBLE_FOR_NEGOTIATION_DURING_RETURN_PROCESSING = 8;
}

enum SellerCompensationStatus
{
  COMPENSATION_NOT_APPLICABLE = 0; // For old Returns
  COMPENSATION_PENDING_REQUEST = 1;
  COMPENSATION_NOT_REQUIRED = 2;
  COMPENSATION_REQUESTED = 3;
  COMPENSATION_APPROVED = 4;
  COMPENSATION_REJECTED = 5;
  COMPENSATION_CANCELLED = 6;
}

enum WalletPaymentStatus
{
  WALLET_PAYMENT_SUCCESSFUL=1;
  WALLET_PAYMENT_FAILURE=2;
}

enum NegotitationStatus
{
    NEGOTIATION_NOT_APPLICABLE = 0; // For old Returns
    NEGOTIATION_INITIAL_STAGE = 1; //Initial stage of negotiation
    NEGOTIATION_PENDING = 2;
    NEGOTIATION_NOT_INITIATED = 3;
    NEGOTIATION_ONGOING = 4;
    NEGOTIATION_TERMINATED = 5;
}

enum ArchiveFlag
{
    BUYER_ARCHIVE  = 1;
    SELLER_ARCHIVE = 2;
}

enum BankTransferOption
{
    NORMAL           = 1;
    ATM_PAYMENT      = 2;
    INTERNET_BANKING = 3;
    ATM_MOBILE       = 4;
    ATM_INTERNET     = 5;
}

enum ChatMsgOpt
{
    MSG_OPT_NORMAL     = 0;
    MSG_OPT_AUTO_REPLY = 1;
    MSG_OPT_BLOCKED    = 2;
    MSG_OPT_USER_CHAT  = 4;
    MSG_OPT_WEB_CHAT   = 8;
    MSG_OPT_CENSORED_BLACKLIST = 16;
    MSG_OPT_CENSORED_WHITELIST = 32;
    MSG_OPT_IGNORE_UNREAD_FOR_RECEIVER = 64; // if set, the receiver's unread count shouldn't be changed
    MSG_OPT_IGNORE_UNREAD_FOR_SENDER = 128; // if set, the sender's unread count shouldn't be changed
    MSG_OPT_INVISIBLE_FOR_RECEIVER = 256; // if set, receiver will not see this msg
    MSG_OPT_INVISIBLE_FOR_SENDER = 512; // if set, sender will not see this msg
    MSG_OPT_ADS = 1024; // this msg is ads
    MSG_OPT_FAQ_SESSION = 2048; // tag all messages in a FAQ session
    MSG_OPT_TRIGGER_FAQ = 4096; // mark the message triggering FAQ
    MSG_OPT_SOCIAL_MESSAGE = 8192; // causual chat messages. not related to the role of buyer and seller.
    MSG_OPT_OFFWORK_AUTOREPLY = 16384; // with this, MSG_OPT_AUTO_REPLY means "welcome autoreply".
    MSG_OPT_CANCELORDER_WARNING_SENT = 32768;
    MSG_OPT_CANCELORDER_WARNING_RETRACT = 65536;
    MSG_OPT_CHATBOT_SESSION = 131072; // mark messages sent in chatbot session
    MSG_OPT_TRIGGER_CHATBOT = 262144; // this message triggers chatbot session
    MSG_OPT_PRIVATE_MESSAGE = 524288; // need special handling of private message; should only set by server
    MSG_OPT_SILENT_FOR_SENDER = 1048576; // messages that are temporarily invisible to sender
}

enum ShopCollectionType
{
    SCT_CUSTOMIZED  = 1;
    SCT_CATEGORY    = 2;
    SCT_HASHTAG     = 3;
    SCT_NEW_ARRIVAL = 4;
    SCT_ON_SALE     = 5;
}

enum CmtOpt
{
    CMT_OPT_AUTO_RATE = 1;
    CMT_OPT_VALID_ITEM_RATING = 2;
}

enum CmtFilter
{
    CMT_FILTER_CONTEXT  = 1;
    CMT_FILTER_IMAGE    = 2;
    // bit operation: value must be 2^n
    CMT_FILTER_VIDEO    = 4;
}

enum FulfillmentRateFlag
{
    FULFILLMENT_NORMAL                 = 1;  // normal user
    FULFILLMENT_WARNING                = 2;  // legacy popup, will be removed
    FULFILLMENT_MANUAL_DISABLE_WARNING = 3;  // BE disable popup
    FULFILLMENT_DISPLAY_WARN           = 4;
    FULFILLMENT_DISPLAY_PUNISH         = 5;  // popup
}

enum LateShipmentRateFlag
{
    LATE_SHIPMENT_NORMAL     = 1;
    LATE_SHIPMENT_WARNING    = 2;
    LATE_SHIPMENT_PUNISH     = 3;
}

enum VoucherUseType
{
    PRIVATE = 0;
    PUBLIC = 1;
}

enum WebOption
{
    NEVER_SHOW_FEEDS_BANNER = 1;
    NEVER_ALERT_PRIVACY_MSG = 2;
    NEVER_SHOW_BOOST_MSG = 4;
    NEVER_SHOW_WALLET_BANNER = 8;
    KYC_CONSENT = 16;
    KYC_SIMPLE = 32;
    KYC_FULL = 64;
}

enum VcodeActionType
{
    SEND_SMS_OTP       = 1;  // default type
    SEND_VOICE_OTP     = 2;
    SEND_WHATS_APP_OTP = 3;
    SEND_EMAIL_OTP     = 4;
}

enum ShopMetricType
{
    LATE_SHIPMENT_RATE     = 1;
    ONTIME_SHIPMENT_RATE   = 2;
    NON_FULFILLMENT_RATE   = 3;  // cancellation rate and return refund rate store in extinfo, ref: FulfillmentRateExtinfo
    AVG_PREPARATION_TIME   = 4;
    ITEM_BANNED_COUNT      = 5; //deprecated!
    ITEM_SEVERE_VIOLATE    = 9;
    ITEM_NORMAL_VIOLATE    = 10;
    CHAT_RESPONSE_RATE     = 11;
    PREORDER_RATE          = 12;
}

enum ShopMetricExtraFlag
{
    SHOP_METRIC_FIRST_THRESHOLD = 1; // 1 << 0
    SHOP_METRIC_SECOND_THRESHOLD = 2; // 1 << 1

    SHOP_METRIC_PUNISH_SECOND_THRESHOLD = 1024; // 1 << 10
}

enum CategoryReturnType
{
    RETURN_DEFAULT = 0;
    RETURN_NO_AUTO_PASS = 1;
}

enum AddressTaxFlag
{
    ADDR_FLAG_NONE = 0;
    ADDR_FLAG_TAXED = 1;
}

// For penalty types calculated by the system
enum ShopPenaltyType
{
    PENALTY_TYPE_FLUSH                       = -1;
    PENALTY_TYPE_MANUAL                      = 0;
    PENALTY_TYPE_HIGH_NON_FULFILMENT         = 1;
    PENALTY_TYPE_HIGH_LATE_SHIPMENT          = 2;
    PENALTY_TYPE_PROHIBITED_LISTING          = 3;
    PENALTY_TYPE_COUNTERFEIT                 = 4;
    PENALTY_TYPE_SPAM                        = 5;
    PENALTY_TYPE_OFFICIAL_SHOP_FRAUD         = 6;
    PENALTY_TYPE_OFFICIAL_SHOP_FRAUD_VOUCHER = 7;
    PENALTY_TYPE_CHAT_RESPONSE_RATE_OFFICIAL = 8; // Chat response rate of official shops and preferred sellers
    PENALTY_TYPE_CHAT_RESPONSE_RATE_NORMAL   = 9; // Chat response rate for other shops
    PENALTY_TYPE_COPY_IMAGE                  = 10; // Listing with copied or steal images
    PENALTY_TYPE_RUDE_CHAT                   = 11;
    PENALTY_TYPE_FAKE_RETURN_ADDRESS         = 12;
}

enum ShopPenaltySource
{
    PENALTY_SOURCE_AUTOMATIC = 0;
    PENALTY_SOURCE_MANUAL    = 1;
}

enum TierVariationType
{
    LIPSTICK = 1;
}

enum ShopPenaltyFlag
{
    LISTING_NON_BROWSABLE  = 1;
    LISTING_NON_SEARCHABLE = 2;
    DISABLE_CREATE_LISTING = 4;
    DISABLE_EDIT_LISTING   = 8;

    PUNISHMENT_DISABLE_MARKETING = 16;
    PUNISHMENT_DISABLE_FREE_SHIP = 32;
    PUNISHMENT_FREEZE_SHOP = 64;
}

enum ShopMetricOrderType
{
    SHOP_METRIC_ORDER_LATE_SHIPMENT = 1;
    SHOP_METRIC_ORDER_RETURN        = 2;
    SHOP_METRIC_ORDER_CANCEL        = 3;
}

enum BundleDealFlagType
{
    BUNDLE_DEAL_FLAG_SHOPEE = 0;
    BUNDLE_DEAL_FLAG_SHOPEE_MULTI_SHOP = 1;
    BUNDLE_DEAL_FLAG_SELLER = 2;
}

enum BundleDealRuleType
{
    BUNDLE_DEAL_RULE_FIX_PRICE = 1;
    BUNDLE_DEAL_RULE_DISCOUNT_PERCENTAGE = 2;
    BUNDLE_DEAL_RULE_DISCOUNT_VALUE = 3;
}

enum MarketPushSendOption
{
    MP_PUSH_ACTION = 0;
    MP_PUSH        = 1;
    MP_AR          = 2;
    MP_WEB_PUSH    = 4;
}

enum MarketPushTaskType
{
    MP_REAL    = 1;
    MP_TEST    = 2;
    MP_FAIL    = 3;
    MP_AUTOGEN = 100;
}

enum VoucherPurpose
{
    VOUCHER_WELCOME = 1;
    VOUCHER_REFERRAL = 2;
    VOUCHER_SHOP_FOLLOW = 3; // shop follow voucher
    VOUCHER_PURPOSE_SHOP_GAME = 4; // shop game voucher
}

enum PromotionFraudRule
{
    EXCLUDE_BLACKLISTED_PHONE_PREFIX     = 1;
    EXCLUDE_EXTISTING_DEVICE_FINGERPRINT = 2;
}

enum AttrTypeFlag
{
    KEY_ATTR    = 1; // 1 << 0
    SALES_ATTR  = 2; // 1 << 1
}

enum AttrMandatoryType
{
    NORMAL_SELLER = 1; // 1 << 0
    MALL_SELLER   = 2; // 1 << 1
}

enum AttrInputType
{
    INPUT_ENUM        = 1;
    INPUT_COMBOBOX    = 2;
    INPUT_TEXT        = 3;
}

enum AttrInputValidateType
{
    VALID_ENUM        = 1;
    VALID_STRING      = 2;
    VALID_INT         = 3;
    VALID_DECIMAL     = 4;
    VALID_DATE        = 5;
}

enum SpuLabal
{
    NEW = 1;
    HOT = 2;
}

enum CollectionType {
    COLLECTION_ALL = 0;
    COLLECTION_MANUAL = 1;
    COLLECTION_AUTOMATED = 2;
}

enum ClusterSource {
    CLUSTER_SOURCE_UNIDENTIFIED = 0;
    CLUSTER_SOURCE_DEEP = 1;
    CLUSTER_SOURCE_DATA_SCIENCE = 2;
}

enum RateControlType {
    RATE_CONTROL_BACKEND = 1000; // cmd = RateControlType + cmd
}

enum OTPHistory{
    OTP_NONE = 0;
    VOICE_OTP_ONCE = 1;
}

enum TongdunDecodeStatus {
    DECODE_NOT_NEEDED = 0;
    DECODE_PENDING = 1;
    DECODE_SUCCESS = 2;
    DECODE_FAILED = 3;
    DECODE_MISSING_BLACKBOX = 4;
}

enum TongdunDecodeReason {
    DECODE_VOUCHER = 1;
    DECODE_WELCOME_PACKAGE = 2;
    DECODE_FREE_SHIPPING_VOUCHER = 3;
    DECODE_SHOPEE_REBATE = 4;
    DECODE_COINS_REBATE = 5;
}

enum FraudCheckSkipReason {
    SKIP_REASON_NOT_SKIPPED = 0;
    SKIP_REASON_TOGGLE_OFF = 1;
    SKIP_REASON_NETWORK_ERROR = 2;
}

enum SortPosition {
     NON_SPECIFIED_POSITION = 2147483647;
}

enum WelcomePackageV2ItemType {
    ITEM_TYPE_NON_WELCOME_PACKAGE = 0;
    ITEM_TYPE_FREE_GIFT           = 1;
    ITEM_TYPE_EXCLUSIVE_DEAL      = 2;
}

enum WelcomePackageV2ItemStatus {
    ITEM_STATUS_DELETED = 0;
    ITEM_STATUS_NORMAL  = 1;
}

enum WelcomePackageV2ItemModelStatus {
    ITEM_MODEL_STATUS_DELETED = 0;
    ITEM_MODEL_STATUS_NORMAL  = 1;
}

enum ShipmentModelId {
    SELF_ARRANGE = 0;
    PICK_UP = 1;
    DROP_OFF = 2;
}

enum IntegrationType {
    NON_INTEGRATED = 0;
    INTEGRATED = 1;
}

enum GSTInfoType {
    GST_ORDER_ITEM = 1;
    GST_SHIPPING_FEE = 2;
    GST_BUYER_TXN_FEE = 3;
}


enum CheckoutListType
{
    CHECKOUT_TOPAY = 1; // CHECKOUT_TOPAY represents PENDING as well - Checkouts that are unpaid or KYC not approved.
}

enum AddressClientInfoType {
    WSA_MALL = 1;
    WSA_PC  = 2;
    WSA_RN = 3;
    WSA_RWEB = 4;
    MALL_CENTRAL = 5;
    SC_PC = 6;
    SC_SFF = 7;
    GEO_ADDRESS_PIPELINE = 8;
    GEO_WH_SCRIPT = 9; // warehouse sync script
    GEO_ONETIME_SCRIPT = 10; // geo team one time data fix script
    // auto stands for automatic detect inside coreserver with requestid parse
    AUTO_MALL_API = 11;
    AUTO_MALL_CENTRAL = 12;
    AUTO_SELLER_APP = 13;
    AUTO_SFF = 14; // sellerfulfillment
    ADMIN_PORTAL = 15; // legacy admin portal
    SLS = 16; // for future use case
    AUTO_SLS = 17;
}

enum AccountAdminConfigType {
    VOICE_OTP_BLACKLIST  = 1;
    ANTI_FRAUD_CONFIG    = 2;
    BOT_REG_IP_WHITELIST = 3;
}

message Gid
{
    optional int64  id = 1;
    optional string type = 2;
}

message Config
{
    optional string name = 1;
    optional string value = 2;
}

message SubCategory
{
    optional int32 parent_category = 1;
    optional int32 sub_category = 2;
}

message CategoryPath
{
    repeated int32 catid = 1; //Sequence is: Parent category -> sub category -> sub-sub category ...

    // Indicates the source that adds this item category path, e.g. `data science team`
    // This is used during item global category migration in which global categories for all existing items are computed based on DS team' model
    optional string source = 2;

    optional int32 confidence_score = 3; // deprecated, use l1_cat_confidence_score & leaf_cat_confidence_score

    // The confidence score from data science team's model that creates this item category path.
    // This value is inflated by 100000, e.g. `0.1` is represented as 100000
    // score * 100k; e.g. 88% confidence score will be stored as 88000
    optional int32 l1_cat_confidence_score = 4;
    optional int32 leaf_cat_confidence_score = 5;
}

message FeCategoryPath
{
    repeated uint64 fe_cat_ids = 1; //Sequence is: Parent category -> sub category -> sub-sub category ...
}

message ItemRating
{
    optional double rating_star = 1;
    repeated int32  rating_count = 2; // index by star, so length should be 6
    optional int32  rcount_with_context = 3;
    optional int32  rcount_with_image   = 4;
}

message VideoInfo
{
    optional string video_id    = 1; // version 0 and 1 video id
    optional string thumb_url   = 2;
    optional int32  duration    = 3;
    optional uint32 version     = 4; // video version, refer to IIS.ItemVideoVersion
    optional string vid         = 5; // version 2 video id stored on MMS
}

message WholesaleTier
{
    optional int32 min_count = 1;
    optional int32 max_count = 2;
    optional int64 price = 3; // this price include tax
    optional int64 input_price = 4; // price without tax
}

message ItemTierInfo
{
    required int64  itemid = 1;
    optional bool   can_use_wholesale = 2;
    repeated WholesaleTier wholesale_tier_list = 3;
}

message TierVariation {
    optional string name = 1;
    repeated string options = 2;
    repeated string images = 3;
    repeated TierProperty properties = 5; // mapping with same index of options, in the future options and images will be moved here
    optional uint32  type            = 6;  // lipstick:1 ref: TierVariationType
}

message TierProperty {
    optional string name  = 1; // e.g. red, blue
    optional string image = 2;
    optional string color = 3; // e.g. ffeebb
}

message ItemExtInfo
{
    optional int32  estimated_days = 1;
    optional int64  price_before_discount = 2;  // origin price before the seller promotion
    optional bytes  logistics_info = 3; // json defined by xianyou
    optional int64  seller_promotionid = 4; // if price_before_discount != price, this is the seller promotionid
    optional string display_shipping_fee = 5; // calculated shipping fee used for client to display
    optional string instagram_media_id = 6; // instagram item id
    optional int64 rebate_price = 7; // Cost of Good Sold, for seller promotion
    repeated SubCategory categories = 8; //Deprecated. Use cats instead. This only has maximum 1 subcategory
    optional int64  price_min_before_discount = 9;  // origin min price before the seller promotion
    optional int64  price_max_before_discount = 10; // origin max price before the seller promotion
    optional int32  seller_promotion_limit = 11; // limit how many a user can buy. If item has models, this value=0
    optional int32  model_discount = 12; // to be deprecated, pls use price_info.discount in the response of GetItemDetail, GetItemBatch API etc.
    optional int32  seller_promotion_refresh_time = 13; // next need re calc seller promotion time.
    repeated CategoryPath cats = 14;  // this is new version category id for multi-level subcategories.
    optional int64  weight = 15; // weight of item for shipping fee calculation
    optional ItemRating item_rating = 16;
    optional int32  holiday_mode_old_stock   = 17; // Deprecated
    repeated VideoInfo video_info_list  = 18;
    optional AttributeSnapshot attributes = 19;
    optional bool has_shopee_promo = 20; //whether item has ongoing or upcoming shopee promo (SellerPromotionSource = Shopee)
    optional int64 price_min_sp = 21;  //for multi model, the min price in seller promotion //[SPSS-1608] It is deprecated.
    optional int64 price_min_before_discount_sp = 22;  //[SPSS-1608] It is deprecated.
    optional int32 show_discount = 23;
    repeated WholesaleTier wholesale_tier_list = 24;
    optional bool can_use_wholesale = 25;
    optional string display_weight = 26;  // weight of item to be display on add or edit item page
    optional bool is_pre_order = 27; // is pre order item
    optional int32 attr_status = 28;
    optional string dimension = 29;  // deprecated
    optional string display_dimension = 30;  // product dimension display
    optional int32 item_qc_status = 31; // stores the status of the most recent qc action
    optional ItemDimensions dimensions = 32;  // product dimension
    optional int32 promo_source = 33;  //see defind SellerPromotionSource
    optional int32 stock_before_discount = 34; // record the stock when item go into flash sale. and will add back when flash sale is over
    optional int32 total_stock_for_discount = 35; // this is the ongoing promotion reserved stock, if no ongoing promotion or promotion reserved stock is 0, then this field is 0 or nil
    optional int32 cat_status = 36; //identify the category status, calculated by BI team according title and description, 0: need seller to update cat 1: correct category
    repeated CategoryPath cats_recommend = 37;//if category is wrong, this is the recommended right category
    optional bool has_lowest_price_guarantee = 38;  //whether item has lowest price guarantee or not
    optional int64 bundle_deal_id = 39;
    optional bool can_use_bundle_sale = 40;
    optional RequestSource request_source = 41;
    repeated TierVariation tier_variation = 42; // For 2-tier variation feature
    optional int64 upcoming_fs_promo_id = 43; //upcoming flash sale promoid
    optional GroupBuyInfo group_buy_info = 44;
    optional int32 fs_stockout_time = 45; //First stockout time of flash sale item, ignoring order cancellations/failure
    optional bool  non_restock      = 46; //true: won't return stock if buyer cancel checkout when stock==0 [SPCS-1226]
    optional int32 preview_end_time = 47; // the preview end time, default is 0. If the item flag is is_preview, then preview_end_time is the actual value
    optional bool for_new_user_only = 48; // deprecated
    optional string size_chart      = 49; // only apply to some categories, [SPCS-1418]
    repeated int64 label_ids        = 50; // item label ids
    optional int32 welcome_package_type = 51; //refer to WELCOME_PACKAGE_TYPE
    optional int32 low_stock_threshold = 52; // use for low stock value threshold
    optional int32 sustain_days_threshold  = 53; // use for forecast sustainable days of item stock out
    optional bool is_slash_price = 54; // Indicating an item is going to be used as slash price item
    optional uint64 add_on_deal_id = 55; // Add On Main Items will have this field set
    optional string gtin = 56; // field for record GTIN in item level.
    optional uint32 item_type                    = 57;      // correspond to Enum ItemType
    optional string reference_item_id            = 58; // 3rd party item's itemid, e.g. digital purchase
    // For the transparent background image, refer to https://confluence.shopee.io/x/3l0DBQ.
    optional string transparent_background_image = 60; // Stores the hash of transparent background image of item.
    optional string first_frame_image = 62; // Stores the hash of the first frame image of the item video.
   // optional uint32 current_allocated_stock = 61; //this is deprecated, pls continue with number 62
//    optional uint32 hs_code = 63;
    optional string hs_code = 64;
    optional uint64 mtsku_item_id = 65; //This is linked mtSKU item id
    optional bool is_model_immutable = 66; // If true, this item is not allowed to change its model status (create model, delete model). This is used for some client who do not have model definition and would store the default model id when item is created. Currently, digital purchase item would turn on this toggle.
    optional uint32 dangerous_goods = 67; // Short-term solution, may be deprecated in future. Value scope is 0 and 1. 0 means the item is not dangerous, while 1 means it is. Use uint32 in case we need to support dangerous good type, such as battery, liquid and flammable. See [SPLT-713]
    optional GlobalAttributeSnapshot global_attributes = 68;
    optional uint32 min_purchase_limit = 69; // Minimum purchase quantity for this item
    optional CategoryPath global_cat = 70; // indicate the global category path
    optional CategoryPath local_cat = 71; // indicate the local category path, this field would be nil after global tree project complete
    optional bool use_global_cat = 72; // toggle indicate whether item share service using global category or local category.The toggle would not store in db
    optional ItemGlobalBrand global_brand = 73; // indicate item global brand info
    repeated FeCategoryPath fe_cat_paths = 74; // List of list containing all linked fe category paths
    optional uint32 order_max_purchase_limit = 75; // It decides the max quantity a buyer can purchase under one order for the associated item.
    // *************** before adding new field, pls check with item shared service team first *************** //
    // *************** https://mattermost.garenanow.com/sea/channels/support-item-service ******************* //
}

message ItemGlobalBrand
{
    optional uint32 global_brand_id = 1;
}

message ItemDimensions
{
    required int64 width = 1;
    required int64 length = 2;
    required int64 height = 3;
    required int32 unit = 4;  // see enum MeasurementUnit
}

enum MeasurementUnit {
    CM = 1;
}

message Item
{
    optional int64  itemid = 1;
    required int32  shopid = 2;
    optional string name = 3;
    optional string description = 4;
    optional string images = 5; // image hash, multiple images joint by comma
    optional int64  price = 6; // this is the price displayed, in seller promotion, this is the price after discount
    optional string currency = 7;
    optional int32  stock = 8; //For multi-model item, this is sum of the stock of all the models
    optional int32  status = 9; // ref: ITEM_NORMAL
    optional int32  ctime = 10; // create time
    optional int32  mtime = 11; // modified time, only critical change will update mtime. e.g. seller update title will, place order won't.
    optional int32  sold = 14; // sold count, for display should not used for statistics
    optional int64  price_min = 15; // to be deprecated, pls use price_info.price_min in the response of GetItemDetail, GetItemBatch API etc.
    optional int64  price_max = 16; // to be deprecated, pls use price_info.price_max in the response of GetItemDetail, GetItemBatch API etc.
    optional int32  recommend = 17; // DEEP team use this field
    optional string collect_address = 18;
    optional int32  catid = 19; // to be deprecated, root level catid, detail category path pls use itemextinfo.cats
    optional int32  pop = 20; // DEEP team use this field
    optional int32  liked_count = 21; // total liked count
    optional int32  offer_count = 22; // total offer count made by buyer
    optional string brand = 23; // deprecated
    optional int32  condition = 24; // item is brand new or second hand, value is set by APP/SC
    optional int32  rating_good = 25; // forward compatibility, pls use ItemExtinfo.ItemRating instead
    optional int32  rating_normal = 26;
    optional int32  rating_bad = 27;
    optional int32  cmt_count = 28; // total item comment count (comment is not rating)
    optional string country = 29;
    optional int32  option = 30;    // web used field
    optional bytes  extinfo = 31;   // ref: PBData.beeshop.db.ItemExtInfo
    optional int32  stockout_time = 32; //For non-model item, stockout_time is time when item is out of stock. For multi-model item, stockout_time is when 'any' model become out of stock, don't use this time unless you know the risk. Reseted to 0 when all models have stock again.
    optional int32  touch_time = 33; //The touch time is initialized by ctime, modified by boost time.
    optional int32  flag = 34;  // flag of item, see ItemFlags Enum
    optional int32  cb_option = 35;  // true: is_cross_border_user
    optional string sku = 36; // freetext filled by seller
}

// this is a copy but less field Item message
message ItemNoDescription
{
    optional int64  itemid = 1;
    required int32  shopid = 2;
    optional string name = 3;
    optional string images = 5;
    optional int64  price = 6; // this is the price displayed, in seller promotion, this is the price after discount
    optional string currency = 7;
    optional int32  stock = 8;
    optional int32  status = 9;
    optional int32  ctime = 10;
    optional int32  mtime = 11;
    optional int32  sold = 14; // sold count, for display should not used for statistics
    optional int64  price_min = 15;
    optional int64  price_max = 16;
    optional int32  recommend = 17;
    optional int32  catid = 19;
    optional int32  pop = 20;
    optional int32  liked_count = 21;
    optional int32  offer_count = 22;
    optional string brand = 23;
    optional int32  condition = 24;
    optional int32  rating_good = 25;
    optional int32  rating_normal = 26;
    optional int32  rating_bad = 27;
    optional int32  cmt_count = 28;
    optional string country = 29;
    optional int32  option = 30;    // web used field
    optional bytes  extinfo = 31; // ref: PBData.beeshop.db.ItemExtInfo
    optional int32  stockout_time = 32;
}

message ItemSimpleInfo
{
    optional int64  itemid      = 1;
    required int32  shopid      = 2;
    optional string name        = 3;
    optional string description = 4;
    optional string images      = 5;
    optional int64  price       = 6; // same as item.Price
    optional string currency    = 7;
    optional int32  stock       = 8;
    optional int32  status      = 9;
    optional int32  liked_count = 21;
    optional int32  cmt_count   = 28;
    optional int32  option      = 30;
    optional int32  ctime       = 10;
    optional int32  sold        = 14;
    optional int32  catid       = 19;
    optional string country     = 29;
    optional bytes extinfo      = 31; // ref: PBData.beeshop.db.ItemExtInfo
}

message ItemIdOnly
{
    optional int64  itemid      = 1;
    required int32  shopid      = 2;
    optional int32  mtime       = 11;
}

message ItemModelExtInfo
{
    optional int32  seller_promotion_limit = 1; // same as ItemExtInfo.seller_promotion_limit. Only the model in promotion has this value.
    optional int32  seller_promotion_refresh_time = 2; // next need re calc seller promotion time.
    optional int32  holiday_mode_old_stock        = 3; // Deprecated
    optional bool has_shopee_promo = 4;
    optional int32 promo_source = 5;  //see defind SellerPromotionSource
    optional int32 stock_before_discount = 6; // record the stock when item go into flash sale. and will add back when flash sale is over
    optional int32 total_stock_for_discount = 7; // this is the ongoing promotion reserved stock, if no ongoing promotion or promotion reserved stock is 0, then this field is 0 or nil
    optional int32 stockout_time = 8; //time when model goes out of stock
    repeated int32 tier_index = 9; // For 2-tier variation feature
    optional int64 upcoming_fs_promo_id = 10; //upcoming flash sale promoid
    optional GroupBuyInfo group_buy_info = 11;
    optional int32 low_stock_threshold = 12; // use for low stock value threshold
    optional int32 sustain_days_threshold  = 13; // use for forecast sustainable days
    optional string gtin = 14; //field for record GTIN
    optional uint32 shop_id = 15;
    //optional uint32 current_allocated_stock = 16; //this is deprecated, pls continue with number 17
    optional string image = 17;
    optional uint32 sku_type = 18;// detailed enum refer to SKU_TYPE in item.item_info proto
    optional bool is_default = 19; // this toggle indicate whether this model is default or not.
    optional uint64 mtsku_model_id = 20; //This is the linked mtSKU model id
    optional string reference_model_id = 21; // this is for client to set, e.g. seller_psku
    // *************** before adding new field, pls check with item shared service team first *************** //
    // *************** https://mattermost.garenanow.com/sea/channels/support-item-service ******************* //
}

message ItemModel
{
    optional int64  modelid = 1;
    optional int64  itemid = 2;
    optional string name = 3;
    optional int64  price = 4;
    optional string currency = 5;
    optional int32  stock = 6;
    optional int32  status = 7;
    optional int64  price_before_discount = 8;
    optional int64  promotionid = 9;
    optional int64  rebate_price = 10;
    optional int32  sold = 11;
    optional bytes  extinfo = 12; // ref: PBData.beeshop.db.ItemModelExtInfo
    optional int32  ctime = 13;
    optional int32  mtime = 14;
    optional string sku = 15; // freetext filled by seller
}

message PromotionItem
{
    optional int32 shopid = 1;
    optional int64 itemid = 2;
    optional int64 modelid = 3;
}

message PromotionInfo
{
    optional string url         = 1;
    optional string description = 2;
    repeated int32 categories   = 3; // used for return & refund
    repeated int32 shopids      = 4;
    repeated PromotionItem items = 5;
    optional string voucher_code = 6;
    optional int64 promotionid = 7;
    optional int64 used_price = 8;  // the amount of voucher discount this voucher provides. e.g. order = 200, discounted order = 160, used_price = 40. This is a static value that is not affected by refunds
    repeated int32 carriers = 9;
    repeated int32 banks = 10;
    optional int32 percentage = 11;
    optional int64 fix_value = 12;  // This value is directly copied from Promotion.value, see that field for comments
    optional string success_show_message = 13; // success show message
    optional bool is_seller_absorbed = 14; //seller pay for voucher
    repeated string credit_card_bins = 15; //From promo rule set, set by BE
    optional int64 coin_earn = 16; // Coins earned from coin cashback voucher, not affected by return/refund
    optional int32 type = 17; // ref: PBData.beeshop.db.PromotionType
}

// cashback (actual money)
message Cashback
{
    optional int64 value       = 1; // flat amount cashback, eg: $10 cashback
    optional int32 percentage  = 2; // percentage based cashback, eg: 50% cashback
    optional int64 cap         = 3; // percentage based cashback, eg: 50% cashback capped at $100
}

message OfflinePaymentVoucherRule
{
    repeated int64 merchant_ids = 1; // including rule, OR logic
    repeated int64 shop_ids = 2; // including rule, OR logic
}

message VoucherCardDisplaySetting
{
    optional VoucherCustomisedLabel customised_header = 1; // customised voucher header
    optional VoucherIcon icon = 2;
    repeated VoucherCustomisedLabel customised_labels = 3;
    optional int32 ui_display_type = 4; // ref: VoucherDisplayType
    optional VoucherCustomisedLabel customised_mall_name = 5; // only for shopping mall OPV(OPV_DISPLAY_TYPE_SHOPPING_MALL)
    repeated VoucherIcon small_icon_list = 6; // only for shopping mall OPV(OPV_DISPLAY_TYPE_SHOPPING_MALL)
}

message VoucherIcon
{
    optional string icon_hash = 1; // image url hash
    optional VoucherCustomisedLabel icon_text = 2; // text below the icon image, supports multi language
}

message VoucherTAndC
{
    optional string section_title = 1; // eg: "How to Use"
    optional string section_content = 2; // eg: "a paragraph to describe how to use"
    optional string section_title_key = 3; // transify key for the section_title
}

message PromotionRule
{
    required string field = 1;
    required int32 compare = 2; // ==, >=, <=, >, < CompareMethod
    required int32 value = 3;
}

message UserAttribute
{
    optional string key = 1; // attribute key
    optional bytes value = 2; // attribute value, cannot be zero length
}

/*
User Criteria for Promotion
1. All Users -> all_user set to true
2. By Register time -> register_time_start and register_time_end(optional) should be set
3. By Userids (Only for backend) -> by_userids should be set.
4. DP New User -> new_user set to true
5. By User Tags -> user_attributes should be set
*/
message PromotionUsers
{
    optional bool all_user = 1;
    repeated int32 userids = 2; //depcrecated. BE should now store the userid instead and just set the by_userids to true
    optional int64 register_time_start = 3;
    optional int64 register_time_end = 4;
    optional bool by_userids = 5;
    optional int64 noti_task_id = 6; //maintained by BE, to store corresponding notification template task_id for FSV+all_user+notification_on, coreserver doesn't use this field
    optional bool new_user = 7; // if true: voucher can only be used by users who never place orders in Promotion.rule.voucher_market_type before, currently only allow DP Public Voucher to set this rule
    repeated UserAttribute user_attributes = 8; // integrated with UserTagging system, a list of user attributes work by OR logic
    optional bool by_user_tags = 9;
}

message PromotionDelayedNoti
{
    optional int32 send_time = 1; //Send at the specific time of the day (seconds since midnight).
    optional int32 delay_days = 2; //Delay for this many of calendar days.
    optional string action_url = 3;
    optional string action_icon = 4;
    optional string action_title = 5;
    optional string action_content = 6;
    optional string push_content = 7;
}

enum PromoShopType
{
    Official = 1;
    Prefered = 2;
}

enum WalletType
{
    Buyer = 1;
    Seller = 2;
    BuyerAndSeller = 3;
}

enum PromotionType
{
    NORMAL_VOUCHER = 0;
    COIN_CASHBACK_VOUCHER = 1;
    FREE_SHIPPING_VOUCHER = 2;
    REWARD_TYPE_PREPAID_CASHBACK = 3; // prepaid, cashback (actual money)
    REWARD_TYPE_PREPAID_COIN_CASHBACK = 4; // prepaid, coin-cashback (shopee coins)
    REWARD_TYPE_PREPAID_DISCOUNT = 5; // pre-paid, discount (actual money)
    REWARD_TYPE_SHIPPING_FEE = 6;
}

enum LockType {
    LOCK_TYPE_NONE = 0; // no lock
    LOCK_TYPE_EXCLUSIVE = 1; // Only lock owner can edit this voucher
}

enum OrderCoinType
{
    BASIC_COIN_RULE = 0;
    CASHBACK_VOUCHER = 1;
}

message CoinCashbackVoucher
{
    optional int64 earn_cash_unit = 1; // used by coreserver only
    optional int64 earn_coin_unit = 2; // used by coreserver only
    optional int64 max_coin = 3;
    optional int32 coin_percentage_real = 4; // voucher face value, eg: 25% coin cashback --> 25
}

enum ShopeeFoodStoreScopeType {
    SHOPEE_FOOD_STORE_SCOPE_ALL = 1;
    SHOPEE_FOOD_STORE_SCOPE_BY_STORE_IDS = 2;
    SHOPEE_FOOD_STORE_SCOPE_BY_COMPOSITE_CONDITION = 3;
}

message ShopeeFoodStoreScope {
    optional int32 scope_type = 1; // Ref: ShopeeFoodStoreScopeType
    repeated uint64 store_ids = 2;
    repeated uint64 exclude_store_ids = 3;
    repeated uint32 store_partner_types = 4;
}

/*
Scope of Promotion
Main types of criteria that are checked independently:
- Item criteria (Whether item can be used in this promo)
- User criteria (Whether user can use this promo)
- Others: eg. banks/carriers/user_agent_types

Item Criteria
Disable rules (eg. disable_shopids) are checked first and as long as any is met, item criteria is NOT met.
Inclusive rules (eg. shopids) are then checked and as long as any is met, item criteria is met.
To include all items, set categories to -1

User Criteria
- See PromotionUsers
*/
message PromotionRuleSet
{
    repeated PromotionRule rules = 1;
    optional PromotionUsers choose_users = 2;
    repeated int32 categories = 3;
    repeated int32 shopids = 4;
    repeated PromotionItem items = 5;
    repeated PromotionDelayedNoti delay_info = 6;
    optional bool push_existsing_user = 7;
    repeated int32 carriers = 8; // logistics carriers
    repeated int32 banks = 9;
    repeated int32 user_agent_types = 10;  // see enum UserAgentType
    optional int32 disable_new_user_dispatch = 11; // don't dispatch voucher for new user if set
    optional string success_show_message = 12; // success show message
    repeated int32 disable_categories = 13;
    repeated int32 disable_shopids = 14;
    repeated PromotionItem disable_items = 15;
    optional int32 valid_days = 16;
    optional bool is_seller_absorbed = 17; //true: seller pay for voucher
    repeated int32 shop_types = 18; //ref: PromoShopType
    repeated int32 exclude_shop_types = 19; //ref: PromoShopType
    optional bool backend_created = 20; //true only for seller vouchers created by backend
    optional bool hide = 21; //hide the voucher in GetVoucherList
    optional int32 purpose = 22; //ref: VoucherPurpose
    repeated string credit_card_bins = 23; // Set by backend
    optional int32 type = 24; // ref: PBData.beeshop.db.PromotionType
    optional CoinCashbackVoucher coin_cashback_voucher = 25;
    optional int64 promotion_fraud_check = 26;  // bitflag value ref: PBData.beeshop.db.PromotionFraudRule
    repeated int32 payment_method = 27;
    optional int32 usage_limit_per_user = 28; // Limit on how many times a user can use the voucher, by default is 1. Sometimes it can be greater than 1, eg: free-shipping-voucher
    repeated int64 spm_channels = 29; // a list of specific spm_channel_id, enum defined by Payment Team, in 7 digit format
    repeated int32 voucher_payment_types = 30; // a list of voucher_payment_type, enum VoucherPaymentType, ref: PBData.beeshop.db.VoucherPaymentType
    optional int64 voucher_landing_page = 31; // bitflag enum VoucherLandingPage, ref: PBData.beeshop.db.VoucherLandingPage
    repeated int64 product_labels = 32; // including rule, labels can be linked to shops and items
    repeated int64 disable_product_labels = 33; // excluding rule, labels can be linked to shops and items
    optional DigitalPurchaseVoucherRule dp_voucher_rule = 34; // ref: PBData.beeshop.db.DigitalPurchaseVoucherRule
    optional int64 claim_start_time = 35; // only after this time, user can claim voucher
    repeated int32 display_labels = 36; // for voucher UI display purpose only, ref: VoucherDisplayLabel
    repeated int64 shipping_promotion_rules = 37; // associated shipping promotion rule ids
    optional string icon_hash = 38; // FE display icon; Nil means default icon
    optional string icon_text = 39; // FE display text under the icon; Nil means default text
    optional string use_link = 40; // customised link of the use button; Nil means default link
    optional string title = 41; // customised voucher title; Nil means default title
    optional string usage_term_product = 42; // applicable product info displayed in voucher T&C page
    repeated VoucherCustomisedLabel customised_labels = 43; // a list of customised display labels
    optional bool non_claimable = 44; // false by default (allow user to manually claim the voucher), set to true if don't allow user to manually claim it
    optional bool not_show_product_scope_tag = 45; // true: don't display product scope tags in FE
    repeated VoucherCustomisedLabel customised_product_scope_tags = 46; // a list of customised product scope tags
    optional Cashback cashback = 47; // cashback (actual money)
    optional OfflinePaymentVoucherRule offline_payment_voucher_rule = 48;
    optional VoucherCardDisplaySetting voucher_card_display_setting = 49;
    repeated VoucherTAndC tc_sections = 50;
    repeated int64 card_brand_ids = 51; // a list of SPM card brand id
    optional int32 quota_type = 52; // ref: PBData.beeshop.db.VoucherQuotaType
    optional bool trigger_friend = 53; // true: will trigger friend activity on successful claim
    optional VoucherUserScopeErrorMessage user_scope_error_message = 54; // Contain CTA and messages to show meaningful info to user
    optional AbsorbRule absorb_rule = 55;
    optional LocalServiceRules local_service_rules = 56;
    optional LockRule lock_rule = 57;
    optional int32 created_by = 58; // ref: CallerSource
    optional bool non_claimable_by_code = 59;
    optional ShopeeFoodVoucherRule shopee_food_voucher_rule = 60;
    optional ShopeeFoodVoucherInfo shopee_food_voucher_info = 61;
    repeated int64 mp_include_fe_categories = 62;
    repeated int64 mp_exclude_fe_categories = 63;
}

message ShopeeFoodVoucherRule {
    optional ShopeeFoodStoreScope store_scope = 1;
}

message ShopeeFoodVoucherInfo {
    repeated VoucherCustomisedLabel browse_list_tags = 1;
}

message LockRule{
    optional int32 lock_owner = 1; // caller source
    optional int32 lock_type = 2; // ref: LockType
    optional int64 lock_expiry_time = 3;
}

message UserPurchaseRules {
    repeated UserAttribute user_attributes = 1; // a list of user attributes work by OR logic, VSS side only store this field but no validation logic
}

message LocalServiceRules {
    optional UserPurchaseRules user_purchase_rules = 1; // VSS side only store this field but no validation logic
}

message AbsorbRule {
    optional int32 type = 1 ;// ref :AbsorbRuleType
    optional int64 max_count = 2; // quota, it limit seller redemption. only for seller when type is ABSORB_PLATFORM_SELLER
    optional int64 amount = 3; // fix mount absorbed by shopee, if 0 means no limit. only for shopee when type is ABSORB_PLATFORM_SELLER
}

message VoucherUserScopeErrorMessage {
    optional VoucherCustomisedLabel title = 1;
    optional VoucherCustomisedLabel content = 2;
    optional string custom_link = 3;
    optional VoucherCustomisedLabel custom_link_text = 4;
}

// voucher customised label supports multi language
message VoucherCustomisedLabel
{
    repeated MultiLangTxt label = 1;
    optional int32 color = 2; // ref: Color
}

enum VoucherLandingPage
{
    DEFAULT = 1; // shop home page, product detail page and shopping cart
    ORDER_PAID_PAGE = 2;
    FEED = 4;
    LIVE_STREAMING = 8;
    CHAT = 16;
    SHOPEE_FOOD_STORE = 32;
}

message DigitalPurchaseVoucherRule
{
    repeated int64 carriers = 1; // dp item carriers, include scope
    repeated int64 disable_carriers = 2; // dp item carriers, exclude scope
    repeated int64 group_ids = 3; // dp item group level validation, include scope
    repeated int64 disable_group_ids = 4; // dp item group level validation, exclude scope
}

enum VoucherDisplayLabel
{
    NEW_USER = 1;  // display new user label in shopee vouchers
    SHOPEEPAY = 2; // display shopeepay label
    SHOPEEPAY_LATER = 3; // display "shopeepay later" label
    JKO_PAY = 4; // display JKO label
}

enum VoucherQuotaType
{
    VOUCHER_CLAIM_QUOTA = 0;
    VOUCHER_USAGE_QUOTA = 1;
}

enum Color
{
    COLOR_ORANGE = 1;
    COLOR_YELLOW = 2;
}

message CartPriceRuleOperator
{
    optional int32 operator = 1; // in,not-in,<,>,<=,>=,==,!=
    repeated int32 value_list = 2;
    optional int32 value = 3;
}

message CartPriceCondition
{
    optional CartPriceRuleOperator credit_banks = 1;
}

message CartPriceRule
{
    optional int32 ruleid = 1;
    optional int64 min_price = 2;
    optional string name = 3;
    optional int64 start_time = 4;
    optional int64 end_time = 5;
    optional int64 value = 6;
    optional int32 percentage = 7;
    optional int64 percentage_max_value = 8;
    optional string country = 9;
    optional int32  status = 10;
    optional int64 ctime = 11;
    optional int64 mtime = 12;
    optional string description = 13;
    optional bytes extinfo = 14; // ref: PBData.beeshop.db.CartPriceCondition
    optional string title = 15;
}

message CartPriceInfo
{
    optional int32 ruleid = 1;
    optional int64 value = 2;
    optional int32 percentage = 3;
    optional CartPriceCondition condition = 4;
    optional string description = 5;
    optional string title = 6;
}

message Promotion
{
    optional int64 promotionid = 1;
    optional string name = 2;
    optional string url = 3;
    optional int64 start_time = 4;
    optional int64 end_time = 5;
    optional int64 notice_start_time = 6; // deprecated
    optional int64 notice_end_time = 7;   // deprecated
    optional int32 discount = 8;  // For percentage discount voucher, this is the amount of percentage discount (out of 100) to be multiplied to order price to make the discounted amount. e.g. discount = 30, order = 200, discounted order = 200 - (200 * 30 / 100) = 140
    optional int64 value = 9;  // For absolute discount, this is the amount of absolute discount to be provided. value = 30, order = 200, discounted order = 200 - 30 = 170
    optional string banner = 10;
    optional bytes rule = 11; // ref: PBData.beeshop.db.PromotionRuleSet
    optional string prefix = 12;        // voucher code for private voucher, voucher code prefix for public voucher
    optional int32  length = 13;        // Length needed for voucher code. If length > len(prefix), will pad voucher code with random characters
    optional int32  usage_limit = 14;   // This is just a storage place for BE to retreive the value and set it in public voucher. Coreserver does not use this value at all to do checks.
    optional int32 total_count = 15;    // Limit on how many private vouchers can be distributed. Check against distributed_count during private voucher dispatching
    optional string country = 16;
    optional int64 min_price = 17;      // cart item total price > this can use the voucher
    optional int32 status = 18;         // Status_PROMOTION_x, 0: disabled, 1: enabled, 2 : deleted
    optional int32 distributed_count = 19;  // Number of private voucher code already distributed (dispatched) to users
    optional int32 use_type = 20;   // ref: PBData.beeshop.db.VoucherUseType
    optional int32 total_usage = 21;    // How many times this promotion can be used
    optional int32 current_usage = 22; // Actual usage count of voucher. If voucher is returned/cancelled, this number is also reduced.
    optional int64 ctime = 23;
    optional int64 mtime = 24;
    optional string description = 25;   // voucher T&C, usage term
    optional bool action_trigger    = 26; // true: send PN when dispatching voucher
    optional string action_content  = 27;
    optional int64 stop_dispatch_time = 28; //After this time, dispatching voucher will fail
    optional string action_title = 29;
    optional string push_content  = 30;
    optional int64 max_value = 31; //Max amount of voucher discount (useful for percentage discount types). 0 to indicate no max.
    optional int32 allow_seller_promotion = 32; // 1: allow. If not set on creation, will be auto set to 1
    optional int32 shopid = 33; //0: Shopee Voucher created by backend.
    optional int32 voucher_market_type = 34; // bitflag value, ref: PBData.beeshop.db.VoucherMarketType
}

enum VoucherMarketType {
    DEFAULT_MARKET_PLACE = 1;
    DIGITAL_PURCHASE = 2;
    VMT_OFFLINE_PAYMENT = 4; // eg: ShopeePay, AirPay
    VMT_SHOPEE_FOOD = 8;
}

enum AbsorbRuleType {
    ABSORB_PLATFORM = 0; // fully absorb by shopee
    ABSORB_PLATFORM_SELLER = 1; // amount is absorb by shopee first then remaining by seller
}

message PublicVoucher
{
    optional string voucher_code = 1;
    optional int64 promotionid = 2;
    optional int32 usage_limit = 3;  // same as promotion.usage_limit
    optional int32 used_count = 4;   //This is used for comparing to usage_limit to check whether quota reached.
    optional int32 status = 5;      //0: deleted, 1: normal
    optional int32 ctime = 6;
    optional string country = 7;
}

message Voucher
{
    optional int32 userid = 1;          // table is sharded by userid
    optional int64 promotionid = 2;
    optional string voucher_code = 3;
    optional int64 creation_time = 4;
    optional int32 collection_status = 5; //deprecated
    optional int64 collection_time = 6; //deprecated
    optional int32 usage_status = 7;    //0: not used, 1: used
    optional int64 usage_time = 8; //will be reset to 0 if voucher is returned
    optional int32 status = 9; // 1: normal, 0: deleted
    optional int32 start_time = 10; //some vouchers eg. bday voucher has a user specific start time that is different from promo start_time. Zero/Empty to indicate no restriction.
    optional int32 end_time = 11; //Zero/Empty to indicate no restriction.
}

message VoucherAudit
{
    optional int32 userid = 1;
    optional string voucher_code = 2;
    optional int64 promotionid = 3;
    optional int64 checkoutid = 4;
    optional int32 usage_time = 5;
    optional int32 status = 6; //0: voucher returned, 1: voucher used
    optional int32 voucher_market_type = 7; // the voucher is used in which market? ref: PBData.beeshop.db.VoucherMarketType
}

message CartPriceRuleAudit
{
    optional int32 id = 1;
    optional int32 userid = 2;
    optional int64 checkoutid = 3;
    optional int32 ruleid = 4;
    optional int32 usage_time = 5;
    optional int32 status = 6;
}

message Snapshot
{
    optional Item       item = 1;
    repeated ItemModel  model = 2;
}

message ItemSnapshot
{
    optional int64  snapshotid = 1;
    optional int32  shopid = 2;
    optional int64  itemid = 3;
    optional int32  status = 4;
    optional int32  ctime = 5;
    optional bytes  snapshot = 6; // ref: PBData.beeshop.db.Snapshot
}

message ShopSnapshot
{
    optional int64  snapshotid = 1;
    optional int32  shopid = 2;
    optional int32  ctime = 3;
    optional bytes  snapshot = 4;  // Shop ref: PBData.beeshop.db.Shop
    optional int32  status = 5;
}

enum RedirectUrlType
{
    PRODUCT_DETAIL_PAGE = 1;
    SHOP_CATEGORY = 2;
    CUSTOM_URL = 3;
}

message ShopCover
{
    required int32  type              = 1;    // 0 : image, 1 : video
    required string image_url         = 2;
    optional string video_url         = 3;
    optional string redirect_url      = 4;
    optional int32 redirect_url_type  = 5;
}

message ShopAdminInfo
{
    optional bool power_seller = 1; // the seller support fast escrow
    optional bool star_seller = 2; // the user is power seller marked by backend
    optional double boosting = 3; // shop's boosting in calculate seller credit
    optional int32 days_to_confirm = 4; //override default days_to_confirm for order
    optional bool official_shop = 5; // the seller is an official shop
    optional bool has_whs = 6; // the seller has warehouse
    optional int32 item_limit = 7; // item limit for this shop
    optional int32 days_to_ship = 8; // override category level days_to_ship setting
    optional string shipping_from_country = 9; // which country the orders shipping from
    optional uint32 preferred_image_cap       = 10; // this value corresponds to the image capacity of each item in the shop. if this value is 0 or nil then choose the default config value
    optional bool supermarket_shop = 11;
}

message SellerRating
{
    repeated int32  rating_count = 1; // index by star, so length should be 6
    repeated int32  rating_byme_count = 2; // index by star, so length should be 6
}

message PreparationTime
{
    optional int64  total_time = 1;
    optional int64  count = 2;
    optional int64  mtime = 3;
    optional int64  average_time = 4;
}

message TxnFeeCustomisedSetting
{
    optional int32 payment_channel_id = 1;
    optional int32 logistics_channel_id = 2;
    optional string option_info = 3; // SPM option_info data
}

message TxnFeeSpecialGroupSetting
{
    optional int32 special_group_id = 1;
    optional int32 payment_channel_id = 2;
    optional int32 logistics_channel_id = 3;
    optional string option_info = 4; // SPM option_info data
    optional int64 spm_payment_channel_id = 5;
    optional int32 priority = 6;
}

message ShopCardTxnFeeSetting
{
    repeated int32 free_channel_id = 1;                 // Will check this first
    repeated TxnFeeCustomisedSetting free_rules = 2;    // Core server will use this field to exempt seller from seller transaction fee with the combination of payment_channel_id and logistics_channel_id, or payment_channel_id and option_info
    repeated TxnFeeSpecialGroupSetting group_rules = 3; // Core server will check this field for group rules which can be applied to the particular order to shop
}

// IMPORTANT: any changes for sub message should sync to the same message in account.core
message ShopExtInfo
{
    optional BuyerAddress address = 1; // not used after logistics version release, checked with  XY, shop.extinfo.address.id is obsolete
    optional int32 disable_make_offer = 2;
    optional bytes logistics_info = 3; // defined by xianyou
    optional int32 pickup_address_id = 4; // default pickup address id in BuyerAddress
    optional string display_default_shipping_info = 5; // default shipping info to display when adding/editing item, if item doesn't have shipping
    optional int64 logistics_flag = 6; //LogisticsShopFlag
    optional bytes default_item_logistics_info = 7; // default logistics info to submit to server when adding/editing item, if item doesn't have shipping
    repeated string covers = 8; // cover images displayed in shop
    optional bool star_seller = 11;
    optional bool auto_reply_on = 12;
    optional bytes auto_reply_msg = 13; // ChatTextInfo
    optional int32 response_rate = 14; // 0 ~ 100, accurate response rate
    optional bool  description_banned = 15;
    optional bool  covers_banned = 16;
    optional int32 display_response_rate = 17; // 0 ~ 100, rounded response rate, e.g. if 70 < response_rate < 80, display_rate=78
    optional ShopAdminInfo admininfo = 18;
    optional int32 auto_reply_mtime = 19;
    optional double rating_star    = 20; // https://www.pivotaltracker.com/n/projects/1140056/stories/110558290
    optional bool  chat_disabled = 21;
    optional int32 response_time = 22; // average response time in seconds
    optional bool  enable_display_unitno = 23;
    repeated ShopCover shop_covers      = 24;  // to replace covers = 8
    optional bool  update_shop_covers   = 25;
    optional int32 last_update_time     = 26;  // latest timestamp seller upload item
    optional SellerRating seller_rating = 27;
    optional int32 webchat_response_rate = 28; // 0 ~ 100
    optional int32 shopee_verified_flag = 29; //1: prefered_seller
    optional PreparationTime preparation_time = 30;
    optional bool  had_order = 31;
    optional int32 fulfillment_rate_flag = 33; // https://docs.google.com/document/d/1cZBvpI4CSBY7NM4f5qosDirbMW6grEi1PNjdX7nuDT4/edit?ts=581fe4ac#
    optional double non_fulfillment_rate = 34;
    optional double fulfillment_rate = 35;
    optional int32 comm_group_id = 36;
    optional ShopCardTxnFeeSetting card_txn_fee_setting = 38;
    optional int32 return_address_id = 39;
    optional int32 seller_penalty_flag = 40;
    optional int32 seller_penalty_score = 41;
    optional int32 seller_penalty_tier = 42;
    optional int32 late_shipment_rate_flag = 43; // [SPCS-918]  // ref: LateShipmentRateFlag
    optional double late_shipment_rate = 44; // [SPCS-918]
    optional int32 last_change_shopname = 45; // latest timestamp for sheller change shopname
    optional int32 cb_return_address_id = 46; // cb seller have this additional return address
    optional bool sync_stock_from_wms = 47; // seller whether sync stock from WMS
    repeated int64 label_ids  = 48; // shop label ids
    optional bool stock_alert_allow = 49; // whitelist for shop to allow show `Low Stock Alert` toggle
    optional bool stock_alert_on = 50; // low stock alert toggle
    optional int32 max_variations_count = 51; // limit for variations on for tier 1.
    optional int32 sustain_days_threshold  = 52; // use for forecast sustainable days of item stock out
    optional double cancellation_rate = 53; // [SPCS-1702] To show the cancellation_rate in FE
    optional bool has_decoration = 54; // [SPSM-3] Set this field when a shop has decoration setting
    optional int32 preorder_listing_target = 55; // The target rate multiplied by 100000. If the target is 0.2 (20%), the value is 20000
    optional bool is_sip_primary = 56; // [SPSM-304] set to True if is a sip primary shop
    optional bool is_sip_affiliated = 57; // [SPSM-304] set to True if is a sip affiliated shop
    optional bool is_ship_from_overseas = 58; // [SPCS-2888]
    optional string visible_regions = 59; // i.e. "TW,SG", set by rundeck job
    optional bool block_cb_user = 60; // this shop is not visible for cb user (logged in user is cb user), set by rundeck job
    // don't add new field here, or messages in account.core will be inconsistent
    optional bool is_bbc_seller = 61; // make if seller is bbc seller, default false
    optional uint32 hs_code_group_id = 62; //The HS Code group ID assigned to this shop. If `is_bbc_seller` false, should be ignored.
    optional bytes onboarding_info = 63; // [SPUS-758] // ref: account.core.SellerOnboardingRecord
    optional bool is_sip_cb = 64; // [SPUS-1566] for order team to distinguish the order is CB SIP or local SIP
    optional string sip_primary_region = 65; // [SPUS-2545] for order team to know the primary shop's country of a given SIP affiliated shop
}

message Shop
{
    optional int32  shopid = 1;
    optional int32  userid = 2;
    optional string name = 3;
    optional string description = 4;
    optional string images = 5;
    optional bytes  contacts = 6;
    optional int32  ctime = 7;
    optional int32  mtime = 8;
    optional int32  status = 9; // ref: SHOP_NORMAL
    optional string collect_address = 10;
    optional bytes  payment = 11;
    optional bytes  shipment = 12;
    optional int32  escrow_option = 13;
    optional bytes  escrow_payment = 14;
    optional string cover = 15;
    optional double latitude = 16;
    optional double longitude = 17;
    optional string place = 18;
    optional int32  pop = 19;
    optional int32  rating_good = 20;
    optional int32  rating_normal = 21;
    optional int32  rating_bad = 22;
    optional int32  sold_total = 23;
    optional int32  item_count = 24;
    optional int32  follower_count = 25;
    optional bytes  extinfo = 26;  // ref: PBData.beeshop.db.ShopExtInfo
    optional string country = 27;
    optional int64  flag = 28;     //reserved do not use
    optional int32  cb_option = 29; // cross border option
}

message ShopBalance
{
    optional int32  shopid = 1;
    optional int32  userid = 2;
    optional string currency = 3;
    optional int64  available = 4; // transfered to seller ==sum up(paid out frozen)
    optional int64  frozen = 5; // under shopee escrow
    optional int64  total_withdrawn = 6; // no use ?
    optional int32  last_withdraw_time = 7; // no use ?
    optional int64  processing = 8; // no use ?
    optional int64  other = 9; // not under shopee escrow
    optional int64  withdraw_limit = 10; // no use ?
}

message ShopContactList
{
    repeated ShopContact contact = 1;
}

message ShopContact
{
    optional string platform = 1;
    optional string account = 2;
}

message PaymentOptionList
{
   repeated PaymentOption option = 1;
}

message PaymentOption
{
   optional int32 payment_method = 1;
   optional int32 enabled = 2;
   optional bytes param = 3;
}

message PaymentBankAccount
{
   optional string bank_name = 1;
   optional string bank_account = 2;
   optional string bank_owner_name = 3;
}

message ShopShipmentList
{
   repeated ShopShipment shop_shipment = 1;
}

message ShopShipment
{
   optional int32 carrierid = 1;
   optional string name = 2;
   optional int64 shipping_fee = 3;
   optional string currency = 4;
   optional int32 is_custom = 5;
}

message ShopEscrowPaymentOptionList
{
    repeated ShopEscrowPaymentOption options= 1;
}

message ShopEscrowPaymentOption
{
    optional int32 payment_method = 1;
    optional int32 enabled = 2;
    optional int32 service_fee_type = 3; // 0-seller, 1-buyer
    optional int64 service_fee = 4; //Set by server
    optional string currency = 5;
}

message Vcode
{
    optional string phone = 1;
    optional int32  userid = 2;
    optional string vcode = 3;
    optional int32  mtime = 4;
    optional int32  count = 6;
    optional int32  id    = 7;
}

message VcodeHistory
{
    optional uint64 id = 1;
    optional uint32 user_id = 2;
    optional string phone = 3;
    optional uint32 otp_type = 4; // see enum VcodeOperationType
    optional uint32 send_time = 5;
    optional uint32 source = 6;
    optional uint32 otp_channel = 7;
    optional string ip = 8;
}

// changes should be synced to account.gateway proto definition.
enum VcodeOperationType
{
    GENERAL = 0;

    ACCOUNT_REGISTER                               = 1;
    ACCOUNT_LOGIN                                  = 2;
    ACCOUNT_LOGIN_NEW_DEVICE                       = 3; // only used internally in CS. Dont use this type in FE
    ACCOUNT_SMS_LOGIN                              = 4;
    ACCOUNT_PASSWORD_NEW_DEVICE_LOGIN              = 5;
    ACCOUNT_THIRDPARTY_NEW_DEVICE_LOGIN            = 6;
    ACCOUNT_BIND_PHONE                             = 11; // deprecated
    ACCOUNT_ADD_PHONE_TO_UPDATE_EMAIL              = 12;
    ACCOUNT_VERIFY_PHONE_TO_UPDATE_EMAIL           = 13;
    ACCOUNT_FORGET_PASSWORD                        = 14;
    ACCOUNT_SET_PASSWORD                           = 15;
    ACCOUNT_CHANGE_PASSWORD                        = 16;
    ACCOUNT_ADD_PHONE_TO_SET_PASSWORD              = 17;
    ACCOUNT_SET_PHONE                              = 18;
    ACCOUNT_UPDATE_PASSWORD                        = 21;
    ACCOUNT_UPDATE_PHONE_NUMBER                    = 22;
    ACCOUNT_CHANGE_PHONE                           = 23;
    ACCOUNT_CHANGE_EMAIL                           = 24; // deprecated
    ACCOUNT_UPDATE_BANK_ACCOUNT                    = 25;
    ACCOUNT_SHOPEEPAY_REGISTER_LOGIN               = 26;
    ACCOUNT_SHOPEEPAY_LOGIN_NEW_DEVICE             = 27; // only used internally in CS. Dont use this type in FE
    ACCOUNT_SHOPEEPAY_ADD_PHONE_TO_ACTIVATE_WALLET = 28;
    ACCOUNT_UNBIND_PHONE                           = 31; // deprecated
    ACCOUNT_DELETE_ACCOUNT                         = 32;

    // === Seller OTP operation: 40 ~ 99
    SELLER_EXPORT_REPORT           = 41;
    SELLER_SUBACCOUNT_BIND_SHOP    = 42;
    SUBACCOUNT_SC_LOGIN            = 43;
    SUBACCOUNT_SAP_LOGIN           = 44;
    SUBACCOUNT_SAP_LOGIN_FORGET_PW = 45;
    SUBACCOUNT_SAP_EDIT_FORGET_PW  = 46;
    SUBACCOUNT_SAP_BIND_SHOP       = 47;
    SUBACCOUNT_SAP_BIND_MERCHANT   = 48;
    SUBACCOUNT_SAP_EDIT_PHONE      = 49;
    DROPSHIPPER_VERIFY_PHONE       = 50; // special case: doesn't belong to seller, this is a growth team's use case
    SUBACCOUNT_SAP_EDIT_EMAIL      = 51;
    SUBACCOUNT_SAP_EDIT_PW         = 52;
    SUBACCOUNT_SAP_EDIT_PMT        = 53;
    SUBACCOUNT_EMAIL_NEW_NUMBER    = 54;
    SUBACCOUNT_SAP_NEW_NUMBER      = 55;
    SUBACCOUNT_SC_APPLY_MAINACC    = 56;
    SUBACCOUNT_EMAIL_APPLY_MAINACC = 57;
    SUBACCOUNT_CB_INVITE           = 58;
    SUBACCOUNT_CB_VERIFY_PHONE     = 59;
    SUBACCOUNT_CB_BIND_MAINACC     = 60;
    SUBACCOUNT_SHOPEECN_LOGIN      = 61; // deprecated
    SUBACCOUNT_CB_LOGIN            = 62;
    SUBACCOUNT_CNSC_LOGIN          = 63;
    SUBACCOUNT_CNSC_LOGIN_FORGET_PW= 64;
    SUBACCOUNT_CNSC_EDIT_PHONE     = 65;
    SUBACCOUNT_CNSC_EDIT_EMAIL     = 66;
    SUBACCOUNT_CNSC_EDIT_PW        = 67;
    SUBACCOUNT_CNSC_EDIT_PMT       = 68;
    SUBACCOUNT_CNSC_NEW_NUMBER     = 69;
    SUBACCOUNT_DEFAULT             = 70;
    SUBACCOUNT_CNCB_MOBILE_ONBOARDING_VERIFY_PHONE  = 71;
    SUBACCOUNT_SHOPEEHK_ONBOARDING_VERIFY_PHONE     = 72;
    SUBACCOUNT_TWLOCAL_LISTING_VERIFY_PHONE         = 73;
    SUBACCOUNT_OPEN_PLATFORM_LOGIN                  = 74;

    WALLET_UPDATE_SELLER_WALLET_PIN = 101;
    WALLET_UPDATE_BUYER_WALLET_PIN  = 102;

    DP_SELL_GOLD         = 150;

    // Kredit
    SPL_ACTIVATION           = 300;
    SPL_CHECK_OUT            = 301;
    CASHLOAN_ACTIVATION      = 302;
    CASHLOAN_APPLICATION     = 303;
    SELLER_LOAN_ACTIVATION   = 310;
    SELLER_LOAN_APPLICATION  = 311;
    SELLER_LOAN_VIEWCONTRACT = 312;

    LDN_BORROWER_LOGIN       = 350;
    LDN_BORROWER_REGISTER    = 351;
    LDN_LENDER_LOGIN         = 352;
    LDN_LENDER_REGISTER      = 353;
    LDN_FORGET_PASSWORD      = 354;

    // seller MKT VERIFY_MEMBERSHIP_PHONE
    SELLER_MKT_VERIFY_MEMBERSHIP_PHONE = 400;

    // === anti fraud operations: 500 ~ 599
    FRAUD_IVS_LOGIN      = 500;
    CHECK_TRUSTED_DEVICE = 501;

    // Live Streaming
    LS_LUCKYDRAW_PHONE_VERIFICATION = 600;

    // === toB account OTP operation starts from 50000 ===

    // ShopeePay Merchant: 50000~50499
    TOB_ACCOUNT_SHOPEEPAY_MERCHANT_OTP_LOGIN = 50000;
    TOB_ACCOUNT_SHOPEEPAY_MERCHANT_PASSWORD_NEW_DEVICE_LOGIN = 50001;
    TOB_ACCOUNT_SHOPEEPAY_MERCHANT_FORGET_PASSWORD = 50002;

    // ShopeeFood Driver: 50500~50999
    TOB_ACCOUNT_SHOPEEFOOD_DRIVER_OTP_LOGIN = 50500;
    TOB_ACCOUNT_SHOPEEFOOD_DRIVER_BANK_WITHDRAW = 50501;

    // SPX Driver: 51000~51499
    TOB_ACCOUNT_SPX_DRIVER_OTP_LOGIN = 51000;
    TOB_ACCOUNT_SPX_DRIVER_PASSWORD_NEW_DEVICE_LOGIN = 51001;
    TOB_ACCOUNT_SPX_DRIVER_FORGET_PASSWORD = 51002;

    // SPX Seller: 51500~51999
    TOB_ACCOUNT_SPX_SELLER_REGISTER = 51500;
    TOB_ACCOUNT_SPX_SELLER_FORGET_PASSWORD = 51501;
    TOB_ACCOUNT_SPX_SELLER_ADD_PHONE = 51502;
    TOB_ACCOUNT_SPX_SELLER_ADD_EMAIL = 51503;
}

message AccountInfo
{
    optional int32 userid = 1;
    optional int32 following_count = 2;
    optional int32 liked_count = 3;
    optional int32 last_logout = 4;
    optional int32 last_login = 5;
    optional int32 web_last_login = 6;
}

message Account
{
    optional int32  userid = 1;
    optional int32  shopid = 2;
    optional string phone = 3;
    optional string email = 4;
    optional string fbid = 5;
    optional string password = 6;
    optional int32  status = 9;  // ref: ACCOUNT_NORMAL
    optional int32  bankacc_verified = 13;
    optional string remark = 14;
    optional string username = 15;
    optional string portrait = 16;
    optional string machine_code = 17;  // type of device, e.g. android, apple
    optional bytes  deviceid = 18;
    optional uint64 pn_option = 19;
    optional string language = 20;
    optional int32  appversion = 21;
    optional string country = 22;
    optional int32  ctime = 23;
    optional int32  mtime = 24;
    optional int32  following_count = 25;
    optional bool   phone_verified = 26;
    optional bool   email_verified = 27;
    optional int32  last_login = 28;
    optional bool   is_seller = 29;
    optional bool   phone_public = 30;
    optional bytes  pn_token = 31; // apple pn key; but pn_token=deviceid when machine_code=="android" and appversion>=16
    optional bytes  extinfo = 32;   // ref: PBData.beeshop.db.AccountExtInfo
    optional int32  liked_count = 33;
    optional int32  web_last_login = 34;
    optional int32  inited = 35;
    optional int32  beetalk_userid = 36;
    optional int32  last_logout = 37;
    optional int32  cb_option = 38;  // true: is_cross_border_user
}

message AccountAccessConfig
{
    optional int32 hide_likes = 1;
    optional int32 wallet_setting = 2;
    optional int32 seller_coin_setting = 3;
    optional int32 seller_wholesale_setting = 4;
    optional int32 seller_ads_setting = 5;
    optional int32 voucher_wallet_setting = 6;
    optional int32 group_buy_setting = 7;
    optional bool  seller_unlimited_reply = 8;
    optional bool  show_imagesearch = 9;
    optional bool  welcome_package_setting = 10;
    optional bool  shopee_credit_setting = 11;
    repeated int32 pilot_test_features = 12;
    optional int32 wallet_provider = 13;  // enum: WalletProvider If not set, means user is using the core server wallet feature
    optional bool has_legacy_wallet = 14;  // If set, means user has legacy wallet & transactions offered by core server
    optional bool auto_translation_enabled = 15;
    optional int32 slash_price_setting = 16;
}

message AccountName
{
    optional int64 id = 1;
    optional string name = 2;
    optional string region = 3;
    optional int32 ns = 4;  // AccountNS
    optional int64 ref = 5;
}

enum AccountNS
{
    ACCOUNT_NS_INVALID = 0;
    ACCOUNT_NS_USER_NAME = 1;
    ACCOUNT_NS_SHOP_NAME = 2;
}

enum WalletProvider {
    SHOPEE_WALLET_V2 = 1;  // Standalone wallet server (ID & MY)
    AIRPAY_WALLET    = 2;  // AirPay wallet (TH)
}

enum JkoLinkageStatus {
    LINKED = 1;
    UNLINKED = 2;
}

enum JkoAccountType {
    INDIVIDUAL = 1;
    MERCHANT = 2;
}

enum JkoKycStatus {
    JKO_APPROVED = 1;
    JKO_REJECTED = 2;
}

message JkoAccountBuyer {
    optional string id     = 1;
    optional string key    = 2;  // secret
    optional int32  kyc_status = 3;  // JkoKycStatus: JKO_APPROVED, JKO_REJECTED
    optional string phone  = 4;
    optional int32  kyc_approved_time = 5;
}

message JkoAccountSeller {
    optional string id     = 1;
    optional string key    = 2;  // secret
    optional int32  account_type   = 3;  // JkoAccountType: INDIVIDUAL, MERCHANT
    optional int32  linkage_status = 4;  // JkoLinkageStatus: LINKED, UNLINKED
    optional string username  = 5; // jko username
}

message ReturnStore{
    optional int32 id = 1;
    optional int32 type = 2;
}

message OrderKyc {
    optional uint32 kyc_program = 1; // KycProgram: TW_CUSTOMS_KYC
    optional uint32 kyc_status = 2; // Follow account.kyc.KycStatus ref: Constant.KycStatus. Additional status on order 0: NO APPLICATION
    optional string kyc_name = 3; // Name of recipient
    optional uint64 kyc_id = 4;
    optional uint64 expiry_time = 5; // Expiry time of kyc application.
    repeated uint32 reject_reasons = 6;
    optional string reject_reason_text = 7;
    optional uint32 blacklist_status = 8;
    optional uint64 kyc_status_update_time = 9;
    optional uint64 blacklist_status_update_time = 10;
}

message OrderKycSnapshot {
    optional uint32 kyc_program = 1; // KycProgram: TW_CUSTOMS_KYC. Always 1.
    optional uint32 kyc_status = 2;
    optional uint32 kyc_status_update_time = 3;
    optional uint32 blacklist_status = 4;
    optional uint32 blacklist_status_update_time = 5;
}

message BuyerOrderHistory
{
    optional int32  delivery_order_count = 1;
    optional int32  delivery_succ_count = 2;
    optional int32  cod_order_count = 3;
    optional int32  cod_succ_count = 4;
}

message BuyerRating
{
    optional int32  rating_good = 1;
    optional int32  rating_normal = 2;
    optional int32  rating_bad = 3;
    optional int32  star = 4; // no use
    optional int32  rating_good_byme = 6;
    optional int32  rating_normal_byme = 7;
    optional int32  rating_bad_byme = 8;
    optional int32  rating_auto = 9; // count of ratings rated by system
    // XXX all fields above are no use after 2016-05-04 version
    optional double rating_star = 5;
    repeated int32  rating_count = 10; // index by star, so length should be 6
    repeated int32  rating_byme_count = 11; // index by star, so length should be 6
}

message UserAddressInfo
{
    optional int32  mcount = 1;
    optional int32  mcount_ctime = 2;
    optional bool   in_white_list = 3;
}

// IMPORTANT: any changes for sub message should sync to the same message in account.core
message AccountExtInfo
{
    repeated CreditCardInfo     credit_card = 1;
    optional BankAccountInfo    bank_account = 2;
    optional string             ic_number = 3;
    optional BankAccountInfo    buyer_bank_account = 4;
    optional int32              default_payment_method = 5; // PaymentMethod
    optional int32              delivery_address_id = 6;  // default delivery address id in BuyerAddress
    optional bool               feed_private = 7;
    optional int64              web_option = 8;  // see enum WebOption
    optional int32              gender = 9; // 0:notset 1:male 2 female
    optional int32              birth_timestamp  = 10;
    optional string             beetalk_uniqueid = 11; //for beetalk login used, no useful now
    optional bool               is_semi_inactive  = 12;
    optional AccountAccessConfig access = 13;
    optional string             web_extra_data = 14; // refer to web_extra_data.txt
    optional BuyerOrderHistory  buyer_history = 15;
    optional BuyerRating        buyer_rating = 16;
    optional int32              cart_item_count = 17;
    optional double             total_avg_star = 18; // for both seller and buyer
    optional bool               holiday_mode_on     = 19;
    optional int32              holiday_mode_mtime  = 20; //when change the holiday time
    optional UserAddressInfo    addr_info           = 21;
    optional bool               hide_from_contact   = 22;
    optional string             payment_password    = 23; //payment password, now for wallet only
    optional bool               had_paied_escrow    = 24;
    optional int32              ba_check_status     = 25; // BA_CHECK_STATUS
    optional string             backup_phone = 26; //only backend can set this field, and if set, will send the otp sms to this mobile also when trigger the otp logic
    optional int32              last_change_passwd_time = 27;  // to validate token when login
    optional bool               is_high_limit = 28; // related to payby wallet limit
    optional bool               already_verified_phone = 29; // if phone once verified, set it true, and always keep it true.
    optional int32              receipt_flag = 30; // used by P&L to distinguish different type of user
    optional int32              last_change_phone_time = 31;
    optional BuyerDropshippingInfo dropshipping_info   = 32; // used by P&L to save last-used dropshipping info
    optional bool               disable_new_device_login_otp = 33;
    optional int32              smid_status = 34; // SMID_STATUS
    optional int32              last_change_payment_passwd_time = 35;
    optional int32              last_change_bank_account_time = 36;
    optional bool               not_new_user = 37; // not_new_user is used to indicate whether the user is not new (new user is user who never create any order (can have canceled or shadow checkout order) ). not_new_user is only updated after order is completed[SPCS-1355]
    optional FeedAccountInfo    feed_account_info = 38; // used by Feed
    optional string             nickname = 39; // used as display name for user
    optional string             lineid = 40; // if not nil indicating there is line id binded to the account
    optional LiveAccountInfo    live_account_info = 41; // used by live-streaming
    optional int32              tos_accepted_time = 42; // Used to store the acceptance of latest TOS by seller
    optional bool               not_dp_new_user = 43; // used by Digital Purchase voucher project, used to indicate whether user has ever placed DP orders
    optional int32              otp_history = 44; // used to determine whether the phone number has received and verified voice OTP
    optional JkoAccountBuyer    jko_buyer = 45; // used for TW jko buyer
    optional JkoAccountSeller   jko_seller = 46; // used for TW jko seller
    optional int32              last_change_email_time = 47;
    optional int32              last_verify_email_time = 48;
    optional int32              adult_consent = 49;
    optional string             full_name = 50; // reserved field
    optional string             tax_id = 51; // Taxpayer identification number
    optional bool               editable_username = 52; // username could be changed once after auto-generated
    repeated ReturnStore        default_return_store = 53;
    optional uint32             business_id = 54; // used for toB account; for toC account, business_id = 0 or nil
    optional string             register_platform = 55; // used for lumos monitoring
    optional int32              last_change_username_time = 56; // used for allowing user to change their username after sometime. Jira: https://jira.shopee.io/browse/SPUS-2740
    // don't add new field here, or messages in account.core will be inconsistent
}

message FeedAccountInfo {
    optional bool  is_kol = 1; // indicate whether the user is a KOL(Key Opinion Leader). only backend can set this field
    optional bool can_post_feed = 2; // indicate whether the user can post feed. we will set this field if ban user post feed at backend
}

message LiveAccountInfo {
    optional bool  is_host = 1;   // indicate whether the user is a host of Live-Streaming. only backend can set this field
    optional bool  is_banned = 2;  // indicate whether the user is banned by backend. we will set this field if ban user at backend
}

message BuyerDropshippingInfo
{
    optional string name = 1;
    optional string phone_number = 2;
}

message BuyerOrderCntInfo
{
    // optional int32  id                  = 1;
    optional int32  userid              = 2;
    optional int32  buyer_unpaid        = 4;
    optional int32  buyer_toship        = 5;
    optional int32  buyer_shipping      = 6;
    optional int32  buyer_return        = 7;
    optional int32  mtime               = 8;
}

message SellerOrderCntInfo
{
    // optional int32  id                  = 1;
    optional int32  shopid              = 2;
    optional int32  seller_topay        = 4;
    optional int32  seller_toship       = 5;
    optional int32  seller_toreceice    = 6;
    optional int32  seller_return       = 7;
    optional int32  mtime               = 8;
}

message CreditCardInfo
{
    required string payment_token = 2;
    required string card_number = 3;
    required int32  card_type = 4;
    optional string expiry_date = 5;
    optional int32  status = 6; // Status_CARD
}

message BankAccountInfo
{
    optional int64  account_no = 1;     // not used
    optional string bank_name = 2;
    optional int32  branch_code = 3;
    optional string account_name = 4;
    optional string real_name = 5;
    optional string icno = 6;
    optional string account_number = 7;
    optional int32  mtime = 8;
    optional int32  bank_code = 9;
    optional string region = 10;
    optional string branch_name = 11;
    optional int32  status = 12;
    optional BankAccountInfo last_bank_account = 13;
    optional int32  bankaccount_id = 14;
}

// Facebook account info
// the information is returned by facebook api
message FbAccountInfo
{
    optional string fbid = 1;
    optional string email = 2;
    optional string name = 3;
    optional string first_name = 4;
    optional string last_name = 5;
    optional string gender = 6;
    optional string link = 7;
    optional string locale = 8;
    optional float  timezone = 9;
    optional string updated_time = 10;
    optional bool   verified = 11;
    optional int32  friends_count = 12;
    optional int64  id = 13;
}

message LineAccountInfo
{
    optional int64  id = 1;
    optional string lineid = 2;
    optional int32  userid = 3;
    optional bool   verified = 4;
    optional int32  ctime = 5;
    optional int32  mtime = 6;
}

message BeetalkAccountInfo
{
    optional int32 beetalk_userid = 1;
    optional string uniqueid = 2;
    optional string mobile = 3;
    optional string nickname = 4;
    optional int64 icon = 5;
    optional int32 gender = 6;
    optional int32 birthday = 7;
    optional int32 buddy_count = 8;
    optional int32 info_sync_time = 9;
    optional int32 buddy_sync_time = 10;
}

message GoogleAccountInfo
{
    optional int64  id = 1;
    optional string googleid = 2;
    optional string email = 3;
    optional bool   email_verified = 4;
    optional string name = 5;
    optional string picture = 6;
    optional string given_name = 7;
    optional string family_name = 8;
    optional string locale = 9;
    optional int64  create_time = 10;
    optional int64  update_time = 11;
}

message OrderDetailedStatus
{
    optional int32 type         = 1; // ref: PBData.beeshop.db.OrderDetailedStatusType
    optional string description = 2; // if empty, use default
    optional int32 ctime        = 3;
}

message OrderLogisticsExtInfo {
    optional int32 pickup_cutoff_time = 1; // after this time can not change pickup info
    optional string pickup_fail_reason = 2; // for app to display
    optional string deliver_fail_reason = 3; // for app to display
    optional string delivery_status_desc = 4; // for app to display
    optional int32 delivery_status_mtime = 5; // for app to display
    optional int64 logistics_flag = 6; // ref: PBData.beeshop.dbLogisticsOrderFlag
    optional int32 pickup_attempts = 7; // number of pickup attempts (including the first)
    optional string pickup_date_description = 8; // seller arranged pickup datetime info (may be string range)
    optional int32 channel_id = 9;
    optional bytes extra_data = 10; // json defined by xianyou
    optional string shipping_proof = 11; // image of shipping proof
    optional int32 shipping_proof_status = 12; // 0: unreviewd, 1: approved, 2: rejected
    optional int32 logistics_channel_promotion_rule_id = 13; // channel-based promotion rule that determins shipping fee rebate
    optional int64 rebate_shipping_fee = 14; // actual amount of rebate provided by shopee for logistics cost (may or may not be escrowed to seller depending on how the channel charges shipping fee)
    optional int32 expected_receive_time = 15; // Min(original_expected_receive_time, release_time)
    optional int32 original_expected_receive_time = 16; // Unix timestamp of expected receive time
    optional int32 pouch_size_id = 17; // Pouch Size id for channel that use size selection
    optional int64 thirdparty_pickup_time = 18; // This is the time that the 3rd party logistics channel received the parcel (pickup / dropoff) from the seller.
    optional int32 logistics_channel_discount_promotion_rule_id = 19; // channel-based promotion rule that determins shipping fee discount
    optional int32 logistics_channel_discount_promotion_rule_snapshot_id = 20; // snapshot id of channel-based promotion rule that determins shipping fee discount
    optional int32 days_to_ship = 21; // max(order.items.extinfo.EstimatedDays)
    optional BuyerAddress buyer_address = 22;
    optional BuyerAddress seller_address = 23;
    optional int32 arrange_pickup_by_date = 24; // == ship_by_date
    optional int32 ship_by_date = 25; // get from P&L api
    optional int64 discount_shipping_fee = 26; // shipping fee after discount
    optional int64 origin_shipping_fee = 27; // shipping fee before discount & revise
    optional int64 carrier_shipping_fee = 28; // (extinfo.contractual_carrier_shipping_fee has value) ? (the front-facing shipping fees used for rebate, discount calculation and others) : (the shipping fee carrier will receive)
    optional int32 days_toship_extended = 29;
    optional int64 escrow_shipping_fee = 30; // Set by P&L. This is the shipping delta (positive or negative) to modify the escrow amount based on the actual shipping fee, buyer paid shipping fee and possible promotion rules in place.
    optional int64 shipping_fee_max_discount = 31; // P&L set
    optional bytes trans_detail_shipping_fee = 32; // Serialized field containing FeeInfoList
    optional int32 days_can_extend = 33; // config[country].DaysCanExtend
    optional int64 contractual_carrier_shipping_fee = 34; // the shipping fee carrier will receive
    optional OrderDropshippingInfo dropshipping_info = 35;
    optional int32 auto_cancel_arrange_ship_date = 36; // Cancellation Layer 1 for time-left-to-ship, epoch time
    optional int32 auto_cancel_3pl_ack_date = 37; // Cancellation Layer 2 for time-left-to-ship, epoch time
    optional int64 group_shipping_id = 38; // Service by Shopee usage
    optional LogisticsVoucherInfo free_shipping_voucher = 39; // set this field if and only if a SHOPEE logistics promotion rule is successfully applied to the order. ref: PBData.beeshop.db.LogisticsVoucherInfo
    optional int32 last_change_address_time = 40;
    optional uint64 buyer_user_location_snapshot_id = 41;
    optional uint64 seller_user_location_snapshot_id = 42;
    optional int64 actual_buyer_paid_shipping_fee = 43; // original BPSF - returned parcel's BPSF.
    optional int32 preferred_delivery_option = 44; // ref: PreferredDeliveryOption
    optional int32 cdt_with_ext = 45; // days to delivery taking consider of cdt. Set by order processing team
    optional int32 fe_receive_time = 46; // deprecreated
    optional uint32 shipment_model_id = 47; // ref: ShipmentModelId
    optional uint32 integration_type = 48; // ref: IntegrationType
    optional uint64 pick_up_date = 49; // Standard Reverse Logistics: the date that the return is picked up from buyer
    optional int32 pick_up_timeslot_id = 50; // Standard Reverse Logistics: ID of the timeslot that the return is picked up from buyer
    optional string pick_up_timeslot_display = 51; // Standard Reverse Logistics: Display of the timeslot that the return is picked up from buyer
    optional int32 fulfilment_channel_id = 52; // Initially set to Checkout channel id
    optional int32 fulfilment_shipping_method = 53; // Initially set to Checkout shipping method
    optional int64 shipping_fee_seller_group_id = 54; //  Shipping fee group id for masking channel
    optional int64 channel_priority_seller_group_id = 55; // Priority group id for masking channel
    optional int32  warehouse_address_id = 56; // OSS uses it to get warehouse address first, if this field is none, use old logic to get shop's address and save it as seller's address.
    optional string fulfilment_source_snapshot = 57; // Saved in DB for callers to use in future.
    optional bool managed_by_sbs_snapshot = 58; // Saved in DB for callers to use in future.
    optional int64 fulfilment_channel_logistics_flag = 59; // ref: PBData.beeshop.dbLogisticsOrderFlag
    optional string return_booking_number = 60; // Standard Reverse Logistics: The proof that buyer can drop off the parcel for free
    optional int32 delivery_timeslot_id = 61; // defined by SLS to specify the actual timeslot, and set by clients invoking CMD_CREATE_MULTI_ORDER
    optional int32 fulfilment_channel_buyer_change_time = 62; // Time when buyer changed fulfilment channel. Default to 0 when Order is created.
    optional int32 auto_cancel_arrive_tws_date = 63; // Cancellation Layer 3, epoch time
}

// IMPORTANT: Must redeploy OrderInfo when this proto is changed, even if OrderInfo has no code changes.
// Take note when reading from GDS - field 2 & 3 are flipped in the DB.
message OrderLogistics {
    optional int64 order_id = 1;
    optional int32 user_id = 2; // buyer userid
    optional int32 shop_id = 3; // seller shopid
    optional uint64 logistics_id = 4;
    optional int32 shipping_method = 5; // 0 - not supported, > 0 - supported
    optional string shipping_name = 6;
    optional string shipping_phone = 7;
    optional string shipping_address = 8;
    optional int64 shipping_fee = 9; // actual shipping fee, Will be refunded if whole order is returned
    optional string shipping_carrier = 10; // Kerry/Heimao (supported) or SingPost
    optional string shipping_traceno = 11; // consignment number
    optional string actual_carrier = 12;
    optional int32 revise_time = 13; // set by P&L
    optional int32 delivery_time = 14; // history reason, this is the shipping started time
    optional int32 logistics_status = 15; // ref: PBData.beeshop.db.Status_LOGISTICS
    optional int32 pickup_time = 16; // seller arranged pickup_time, set by P&L SetOrderLogisticsProcessor
    optional int32 shipping_confirm_time = 17; // COD->confirmed time, NONCOD->paid_time
    optional int32 receive_time = 18; // this is the shipping end time
    optional int32 cb_option = 19; // true: is_cross_border_user == item.data.CbOption == acc.data.CbOption
    optional bytes extinfo = 20;
    optional string region = 21;
    optional int32 update_time = 22;
    optional int32 create_time = 23;
    optional int32 db_version = 24;
}


// IMPORTANT: Adding to OrderLogisticsInfo we need to change OrderLogisticsExtInfo
message OrderLogisticsInfo
{
    optional uint64 logisticsid          = 1;
    optional int32 pickup_cutoff_time   = 2; // after this time can not change pickup info
    optional string pickup_fail_reason  = 3; // for app to display
    optional string deliver_fail_reason = 4; // for app to display
    optional string delivery_status_desc = 5; // for app to display
    optional int32 delivery_status_mtime = 6; // for app to display
    optional int64 logistics_flag       = 7; // ref: PBData.beeshop.dbLogisticsOrderFlag
    optional int32 pickup_attempts      = 8; // number of pickup attempts (including the first)
    optional string pickup_date_description = 9; // seller arranged pickup datetime info (may be string range)
    optional int32 channelid        = 10;
    optional bytes extra_data       = 11; // json defined by xianyou
    optional string shipping_proof  = 12; // image of shipping proof
    optional int32  shipping_proof_status               = 13; // 0: unreviewd, 1: approved, 2: rejected
    optional int32 logistics_channel_promotion_rule_id  = 14; // channel-based promotion rule that determins shipping fee rebate
    optional int64 rebate_shipping_fee                  = 15; // actual amount of rebate provided by shopee for logistics cost (may or may not be escrowed to seller depending on how the channel charges shipping fee)
    optional int32 expected_receive_time                = 16; // Min(original_expected_receive_time, release_time)
    optional int32 original_expected_receive_time       = 17; // Unix timestamp of expected receive time
    optional int32 pouch_size_id                        = 18; // Pouch Size id for channel that use size selection
    optional int64 thirdparty_pickup_time = 19; // This is the time that the 3rd party logistics channel received the parcel (pickup / dropoff) from the seller.
    optional int32 logistics_channel_discount_promotion_rule_id  = 20; // channel-based promotion rule that determins shipping fee discount
    optional int32 logistics_channel_discount_promotion_rule_snapshot_id = 21; // snapshot id of channel-based promotion rule that determins shipping fee discount
    optional int32 preferred_delivery_option = 22; // ref: PreferredDeliveryOption
    optional uint32 shipment_model_id = 23; // ref: ShipmentModelId
    optional uint32 integration_type = 24; // ref: IntegrationType
    optional uint64 pick_up_date = 25; // Standard Reverse Logistics: the date that the return is picked up from buyer
    optional int32 pick_up_timeslot_id = 26; // Standard Reverse Logistics: ID of the timeslot that the return is picked up from buyer
    optional string pick_up_timeslot_display = 27; // Standard Reverse Logistics: Display of the timeslot that the return is picked up from buyer
    optional int32 fulfilment_channel_id = 28; // Initially set to Checkout channel id
    optional int32 fulfilment_shipping_method = 29; // Initially set to Checkout shipping method
    optional int64 shipping_fee_seller_group_id = 30; //  Shipping fee group id for masking channel
    optional int64 channel_priority_seller_group_id = 31; // Priority group id for masking channel
    optional int32 warehouse_address_id = 32; // OSS uses it to get warehouse address first, if this field is none, use old logic to get shop's address and save it as seller's address.
    optional string fulfilment_source_snapshot = 33; // Saved in DB for callers to use in future.
    optional bool managed_by_sbs_snapshot = 34; // Saved in DB for callers to use in future.
    optional int64 fulfilment_channel_logistics_flag = 35; // ref: PBData.beeshop.dbLogisticsOrderFlag
    optional string return_booking_number = 36; // Standard Reverse Logistics: The proof that buyer can drop off the parcel for free
    optional int32 delivery_timeslot_id = 37; // defined by SLS to specify the actual timeslot, and set by clients invoking CMD_CREATE_MULTI_ORDER
    optional int32 fulfilment_channel_buyer_change_time = 38; // Time when buyer changed fulfilment channel. Default to 0 when Order is created.
}

message CustomTaxInfo {
    optional uint64 declaration_amount = 1;
    optional uint64 tax_amount = 2; // If there is tax_exemption, then tax_amount is after exemption
    optional uint32 tax_rate = 3;
    optional CBTaxDetail cb_tax_detail = 4;
    optional LocalTaxDetail local_tax_detail = 5;
    optional int64 tax_exemption = 6; // Tax exemption
    optional uint32 declaration_rate = 7;
}

message CBTaxDetail{
     optional uint64 cif_value = 1; // common price roudning (request final price / tax rate)
}

message LocalTaxDetail{
     optional uint64 import_tax_value =1; // request rounding (total_tax *import_tax_rate / sum(import rate,income rate, gst_Rate))
     optional uint64 income_tax_value = 2; //request rounding (total_tax *import_tax_rate / sum(import rate,income rate, gst_Rate))
     optional uint64 gst_value = 3; // request total_tax - import_tax_value - income_tax_value
}

message GSTInfo {
    optional uint32 gst_type = 1;       // GSTType
    optional uint32 gst_rate = 2;       // 1% = 100000 * 0.01
    optional uint64 gst_amount = 3;
}

message OrderTaxInfo
{
    optional int64 tax_amount = 1; // Tax for TW cross border.
    optional int64 updated_tax_amount = 2; // This field always reflects the most updated remaining amount of cb_tax for this order.
    repeated GSTInfo shipping_fee_gst_infos = 3; // Store shipping_fee gst info
    repeated GSTInfo buyer_txn_fee_gst_infos = 4; // Store buyer_txn_fee gst info
    optional uint32 tax_evaluation_status = 5; //refer to PBData.beeshop.cmd.TaxEvaluationStatus
}

message OrderExtInfo
{
    repeated string item_image  = 1; // List of all items' images in this order.
    optional string item_name   = 2; // Name of the first item in the order.
    optional int64  item_price  = 3; // Original price of the first item in the order.
    optional int64  order_price = 4; // Order price of the first item in the order (with promotions applied).
    optional int32  buy_count   = 5; // Amount/quantity bought for the first item in the order.
    optional int32  item_count  = 6; // Total number of distinct items in this order.
    optional int32  total_count = 7; // Sum of all order items' amount/quantity.
    optional int32  seller_userid       = 8; // Unique identifier of the seller's account.
    optional int32  days_to_ship        = 9; // Number of days to ship all items. It is the maximum of all items' estimated_days (item.item_extinfo.estimated_days).
    optional int32  days_to_delivery    = 10; // Number of days for all items to be delivered. Set by P&L according to a country-specific config.
    optional int32  days_to_confirm     = 11; // Set by P&L according to a country-specific config.
    optional int32  days_extended       = 12; // Number of days to extend the escrow release time. Set by P&L.
    optional int32  days_to_pay         = 13; // Number of days to pay. Set by P&L according to a country-specific config.
    optional int32  cancel_reason       = 14; // The reason that indicates why the order is cancelled. ref: PBData.beeshop.db.OrderCancelReason
    optional int64  first_itemid        = 16; // Item ID of the first item in the order.
    optional bool   first_item_return   = 17; // Whether the first item in the order is returned.
    optional int32  release_time        = 18; // Time when the order should already be completed and escrow started.
    optional int32  cancel_userid       = 19; // Unique identifier of the account that cancelled this order. Can be either the seller's or the buyer's user ID.
    optional int32  batchid             = 20; // Set by P&L. Used for escrow.
    optional string payment_channel_name = 21; // Name of the payment channel used. Equal to the checkout's payment channel name (CheckoutInfo.PaymentChannelName).
    optional int32  seller_due_date     = 22; // Deadline for seller to respond to a return request for this order. When return is created, set to config[country].DaysSellerResponse*3600*24 + now (1 day from now).
    optional int32  payby_date          = 23; // Deadline for the buyer to pay the order. Set by checkout || now + 3600*24*region.DaysToPay || when ReceiptReject = now + 3600*24*2 (2 days from now)
    optional int64  price_before_discount   = 24; // Total price before applying buyer promotion, after subtracting returned items, and after adding shipping fee. If there is no promotion, this value is equal to total_price.
    optional int32  discount_percentage     = 25; // If the promotion is discount, then contains the discount percentage, otherwise this field is not set.
    optional PromotionInfo promotion_info   = 26; // Information related to promotion.
    optional int64  item_price_before_discount = 27; // Price of the first item before seller promotion. Equal to item.extinfo.price_before_discount.
    optional BuyerAddress buyer_address     = 28; // Address of the buyer.
    optional BuyerAddress seller_address    = 29; // Address of the seller.
    optional OrderLogisticsInfo logistics_info = 30; // Logistics information. Set by P&L.
    optional int32 arrange_pickup_by_date   = 31; // Equal to shipby_date.
    optional int32 ship_by_date             = 32; // Deadline of the order to be shipped. Get from P&L API.
    optional int64 discount_shipping_fee    = 33; // Shipping fee after discount. See https://confluence.shopee.io/x/RfQLAw
    optional int64 origin_shipping_fee      = 34; // Original shipping fee before discount and revise. See https://confluence.shopee.io/x/RfQLAw
    optional int64 carrier_shipping_fee     = 35; // If extinfo.contractual_carrier_shipping_fee has value, then this will be set to the front-facing shipping fees used for rebate, discount calculation and others. Otherwise, this will be set to the shipping fee carrier will receive. See https://confluence.shopee.io/x/RfQLAw
    optional int32 cod_process_by_date      = 36; // Cash on delivery (COD) confirmation deadline.
    optional int32 days_toship_extended     = 37; // Number of days to extend the arranged shipping time.
    optional int32 is_from_webcheckout      = 38; // Whether this order is created from the web checkout (0: not from web checkout, > 0: from web checkout).
    optional bool  buyer_confirm_order      = 39; // Set by P&L.
    optional CartPriceInfo cart_price_info  = 40;
    optional Shop  shop_snapshot            = 41; // A copy of the complete state of the selling shop when the order is placed. Deprecated and replaced by shop_snapshot_id since shop snapshots are now stored in a separate service under Account Shared Service.
    optional int64 payment_flag             = 42; // Copied from Checkout.checkout_info.checkout_payment_info.payment_flag when Checkout set to PAYMENT_PAID.
    optional string model_name          = 43; // Model name of the first item in the order.
    optional int64 escrow_shipping_fee  = 44; // Set by P&L. This is the shipping delta (positive or negative) to modify the escrow amount based on the actual shipping fee, buyer paid shipping fee and possible promotion rules in place. See https://confluence.shopee.io/x/RfQLAw
    optional int64  modelid             = 45; // Model ID of the first item in the order.
    optional int64  snapshotid          = 46; // Item snapshot ID of the first item in the order.
    optional int64 rebate_shipping_fee  = 47; // used by legacy logistics promotion system, please check logistics_info.rebate_shipping_fee for actual amount of rebate provided to seller. See https://confluence.shopee.io/x/RfQLAw
    optional int64 shipping_fee_max_discount = 48; // Set by P&L. See https://confluence.shopee.io/x/RfQLAw
    optional bytes trans_detail_shipping_fee = 49; // Serialized field containing FeeInfoList. See https://confluence.shopee.io/x/RfQLAw
    optional int32 days_can_extend  = 50; // config[country].DaysCanExtend
    optional bytes seller_note      = 51; // seller_center used for order notes, ref: PBData.beeshop.db.SellerOrderNote
    optional int64 shop_snapshot_id = 52; // Unique identifier of shop snapshots, containing a copy of the complete state of the shop when the order is placed.
    optional int32  buyer_is_rated  = 53; // Whether the buyer is not rated (0), rated by the seller (1), or rated by the system (2).
    optional int32 rate_by_date     = 54; // rateable deadline: order.GetCompleteTime() + config[country].DaysToRate
    optional int32  seller_is_rated = 55; // Whether the seller is not rated (0), rated by the buyer (1), or rated by the system (2).
    optional int64  buyer_rate_cmtid    = 56; // The cmtid of this order's BuyerRating.
    optional int64  seller_rate_cmtid   = 57; // The cmtid of this order's OrderRating.
    optional int64 detail_flag          = 58; // Set by P&L. ref: PBData.beeshop.db.OrderDetailFlag
    repeated OrderDetailedStatus detailed_statuses  = 59; // Set by P&L.
    optional int64 escrow_to_seller     = 60; // Set by P&L. Set when this order status is updated to escrow verified.
    optional int64 contractual_carrier_shipping_fee = 61; // The shipping fee carrier will receive. See https://confluence.shopee.io/x/RfQLAw
    optional CoinInfo coin_info         = 62; // Information about the coins used or earned in this order.
    optional bool  not_stats            = 63; // Do not count in stats if set to true.
    optional string payer_veri_fail_reason  = 64; // Set when NOTI_BUYER_PAYER_VERI_FAIL is triggered.
    repeated FraudInfo fraud_info           = 65; // Contains fraud-related information.
    optional bool first_item_is_wholesale   = 66; // Whether the first item in the order is wholesale.
    optional bool is_ad_order               = 67; // Whether this order is for an ad.
    optional CardPromotionOrderInfo card_promotion_info = 68; // Information related to card promotion.
    optional int64 comm_fee                 = 69; // commission_fee is updated when there is return. See https://confluence.shopee.io/x/RfQLAw
    optional CardTxnFeeOrderInfo card_txn_fee_info      = 70; // Information related to the calculated seller transaction fee for this order. See https://confluence.shopee.io/x/RfQLAw
    optional int64 estimated_escrow         = 71; // Computed by coreserver. Same value as computed seller income in trans history. See https://confluence.shopee.io/x/RfQLAw
    optional int64 rebate_price = 72; // Updated when there is return. See https://confluence.shopee.io/x/RfQLAw
    optional int64 wallet_transaction_id    = 73; // set it when order is paid_by_wallet (checkout.GetPaymentType() == int32(db.PaymentMethod_PAY_SHOPEE_WALLET))
    optional int32 buyercancel_pending_time = 74; // Time when the buyer requests a cancellation to a To Ship order.
    optional bool  is_buyercancel_toship    = 75; // Whether this order was canceled by the buyer when it is in the To Ship list.
    optional bool official_shop = 76; // Whether the seller is an official shop when the order is placed. This field is copied from shop snapshot extinfo.admininfo.official_shop on order creation. This field does not indicate whether the seller is currently an official shop.
    optional int32 buyer_cancel_reason      = 77; // Used by mall, set when the buyer cancels this order when it is in the To Ship list.
    optional bool  instant_buyercancel_toship = 78; // Whether this order is instantly cancelled by the buyer when it is in the To Ship list.
    optional int32 first_item_free_return_day   = 79; // The free return period of the first item in the order.
    optional int32 ratecancel_by_date       = 80; // ordercancel rateable deadline: order.GetCancelTime() + 15days
    optional OrderTaxInfo tax_info = 81; // Information related to taxes. See https://confluence.shopee.io/x/RfQLAw
    optional OrderDropshippingInfo dropshipping_info = 82;
    optional bool first_item_is_bundle = 83; // Whether the first item in the order is part of a bundle.
    optional int32 auto_cancel_arrange_ship_date = 84; // Deadline for the seller to arrange shipment for this order. If shipment is not arranged after his deadline, this order will be automatically cancelled. This field is used by auto_cancel_orders cron job for Cancellation Layer 1.
    optional int32 auto_cancel_3pl_ack_date = 85; // Deadline for the order to be picked up by 3rd party logistics (3PL). If this deadline passes and 3PL fails to pick up, the order will be automatically cancelled. This field is used by auto_cancel_orders cron job for Cancellation Layer 2.
    optional int32 db_version = 86; // Version of the record in DB. For coreserver use only.
    optional CardTxnFeeOrderInfo buyer_txn_fee_info = 87; // Order-level buyer transaction fee (v1). In v2, only its card_txn_fee value will be set. See https://confluence.shopee.io/x/RfQLAw
    optional int64 order_ext_data_ref = 88; // Reference ID to shopee_backend_common_db.order_ext_data_ref_auto_increment_tab.
    optional int64 group_buy_groupid = 89; // Unique identifier of the group buy this order belongs to.
    optional int64 group_shipping_id = 90;  // Service by Shopee usage
    optional int32 group_buy_payment_end_time = 91; // display at FE, this value won't be changed after order created.
    optional int64 bank_transfer_time = 92; // Time of payment using bank transfer.
    optional bool  is_group_buy_ongoing = 93; // (Deprecated)
    optional LogisticsVoucherInfo free_shipping_voucher = 94; // set this field if and only if a SHOPEE logistics promotion rule is successfully applied to the order. ref: PBData.beeshop.db.LogisticsVoucherInfo
    optional int32 last_change_address_time = 95;
    optional CardTxnFeeOrderInfo admin_fee = 96; // Information related to the calculated admin fee. See https://confluence.shopee.io/x/RfQLAw
    optional int64 channel_payee_account_id = 97; // stores payee account used for cb escrow
    optional uint64 buyer_user_location_snapshot_id = 98;
    optional uint64 seller_user_location_snapshot_id = 99;
    optional bool is_slash_price = 100; // Whether the order is for slash price item.
    optional bool is_ship_from_overseas = 101; // Whether the order items are shipped from overseas.
    optional int64 actual_buyer_paid_shipping_fee       = 102; // The original buyer paid shipping fee subtracted by returned parcels' buyer paid shipping fee.
    optional JkoAccountSeller jko_seller_info = 103; // Jko seller account info.
    optional bool post_return_completed_steps_not_executed = 104; // Whether steps after return completed for this order have been executed or not, like giving the user his coins earned, voucher etc.
    optional bool using_wallet = 105; // Whether this order is transacted using wallet. Copied into ref: PBData.beeshop.db.TransHisExtInfo by script.
    optional ServiceFeeOrderInfo service_fee_info = 106; // Information related to the calculated service fee. See https://confluence.shopee.io/x/RfQLAw
    optional int32 cdt_with_ext = 107; // Courier delivery time (CDT) with extension. Number of days for the courier to deliver the order, with holidays extension already taken into account. Set by order processing team.
    optional int32 fe_receive_time = 108; // (Deprecated)
    optional string tax_number = 109; // A number assigned by the Brazilian revenue agency for citizens subject to taxes (taxpayer ID).
    repeated VoucherInfo vouchers = 110; // Voucher(s) applied for this order, including Shopee platform vouchers and seller vouchers.(include FSV)
    repeated OrderKyc kycs = 111; // KYCs of Order. ref: PBData.beeshop.db.OrderKyc
    optional BuyerAddress bbc_address = 112; // BBC address.
    optional uint64 noti_channel_id = 113; // id of noti channel enum. See https://confluence.shopee.io/x/OqS1C
    optional uint32 platform = 114; // id of platform enum. See https://confluence.shopee.io/x/OqS1C
    repeated OrderKycSnapshot kyc_snapshots = 115; // KYC snapshots from latest[0] to oldest[N-1]
    optional int64 total_tax_exemption = 116; // Sum of all OrderItem tax_exemption
    optional int32 auto_cancel_arrive_tws_date = 117; // Cancellation Layer 3, epoch time
    optional bool is_shop_taxable = 118; // indicate whether shop is taxable
}

message LogisticsVoucherInfo {
    optional int64  promotionid  = 1; // P&L team set this field to indicate a free-shipping-voucher is successfully applied in the order
    optional string voucher_code = 2; // coreserver will auto fill in this field when needed
}

message OrderExtAdInfoMappings
{
    optional int32 itemid = 1;
    repeated OrderExtAdInfo ext_ad_infos = 2; // ref: PBData.beeshop.db.OrderExtAdInfo
}

message OrderExtAdInfo
{
    optional int32 ext_ad_id = 1; // the identifier for the ad channel
    optional int32 ad_tracking_time = 2; // the ad tracking time
    optional string ext_ad_data = 3; // the relevant data needed for processing
}

message OrderDropshippingInfo
{
    optional int32 enabled = 1;
    optional string name = 2;
    optional string phone_number = 3;
}

message OrderBrief
{
  optional int64  orderid         = 1; // auto increase
  required int32  shopid          = 2; // seller shopid
  optional int32  userid          = 3; // buyer userid
  optional int32  status          = 4; // ref: PBData.beeshop.db.Status_ORDER
  optional int32  status_ext      = 5; // ref: PBData.beeshop.db.Status_ORDER_EXT_
  optional int32  create_time     = 6; // order created time, will not change
  optional int32  pay_time        = 7; // when StatusExt was changed to OR_PAID
  optional int32  delivery_time   = 8; // history reason, this is the shipping started time
  optional int32  complete_time   = 9; // set to Now after OrderComplete || ReturnAccepted || ReturnCompleted
  optional int32  cancel_time     = 10; // set to Now when order.StatusExt was changed to OR_INVALID
  optional int32  shipping_confirm_time = 11; // COD->confirmed time, NONCOD->paid_time
  optional int64  total_price     = 12; // total price after buyer promotion, including returned items, including shipping fee
  optional int64  actual_price    = 13; // actual price after return & refund
  optional int32  shipping_method = 14; // 0 - not supported, > 0 - supported
  optional int32  payment_method  = 15; // ref: PBData.beeshop.db.PaymentMethod: set by P&L
  optional int32  order_type      = 16; // ref: PBData.beeshop.db.OrderType: if payment_method ==
  optional int32  archive         = 17; // true: is arcived by user // deprecated all 0
  optional int32  list_type       = 18; // ref: PBData.beeshop.cmd.OrderListType
  optional int32  logistics_status = 19; // ref: PBData.beeshop.db.Status_LOGISTICS
  optional int64  checkoutid      = 20; // the checkout this order belongs to
  optional string currency        = 21;
  optional int32  pickup_time     = 22; // seller arranged pickup_time, set by P&L SetOrderLogisticsProcessor
}

message OrderBriefV2
{
    optional int64  orderid         = 1; // auto increase
    required int32  shopid          = 2; // seller shopid
    optional int32  userid          = 3; // buyer userid
    optional int32  status          = 4; // ref: PBData.beeshop.db.Status_ORDER
    optional int32  status_ext      = 5; // ref: PBData.beeshop.db.Status_ORDER_EXT_
    optional int32  create_time     = 6; // order created time, will not change
    optional int32  pay_time        = 7; // when StatusExt was changed to OR_PAID
    optional int32  delivery_time   = 8; // history reason, this is the shipping started time
    optional int32  complete_time   = 9; // set to Now after OrderComplete || ReturnAccepted || ReturnCompleted
    optional int32  cancel_time     = 10; // set to Now when order.StatusExt was changed to OR_INVALID
    optional int32  shipping_confirm_time = 11; // COD->confirmed time, NONCOD->paid_time
    optional int64  total_price     = 12; // total price after buyer promotion, including returned items, including shipping fee
    optional int64  actual_price    = 13; // actual price after return & refund
    optional int32  shipping_method = 14; // 0 - not supported, > 0 - supported
    optional int32  payment_method  = 15; // ref: PBData.beeshop.db.PaymentMethod: set by P&L
    optional int32  order_type      = 16; // ref: PBData.beeshop.db.OrderType: if payment_method ==
    optional int32  archive         = 17; // true: is arcived by user // deprecated all 0
    optional int32  list_type       = 18; // ref: PBData.beeshop.cmd.OrderListType
    optional int32  logistics_status = 19; // ref: PBData.beeshop.db.Status_LOGISTICS
    optional int64  checkoutid      = 20; // the checkout this order belongs to
    optional string currency        = 21;
    optional int32  pickup_time     = 22; // seller arranged pickup_time, set by P&L SetOrderLogisticsProcessor
    optional int32  mtime           = 23;
}

// Order is a transaction between one buyer and one seller. It contains order items/products from one seller only.
message Order
{
    optional int64  orderid         = 1; // Unique identifier of the order for internal systems. This field should not be exposed to external clients. See https://confluence.shopee.io/x/ybV6BQ
    required int32  shopid          = 2; // Unique identifier of the seller's shop.
    optional int32  userid          = 3; // Unique identifier of the buyer's account.
    optional string ordersn         = 4; // Unique identifier of the order for external clients. See https://confluence.shopee.io/x/ybV6BQ
    optional int64  total_price     = 5; // Total price that buyer should pay when placing the order (MerchandiseSubtotal + Shipping - OrderPromotion + BuyerFee). See https://confluence.shopee.io/x/RfQLAw
    optional int64  actual_price    = 6; // Actual price that the buyer paid for this order. Initially equal to total_price, but can be deducted by return and refund. See https://confluence.shopee.io/x/RfQLAw
    optional int64  paid_amount     = 7; // Actual amount that has been paid by the buyer. Initially 0, after order paid this will be set to actual_amount. See https://confluence.shopee.io/x/RfQLAw
    optional string currency        = 8; // The currency this order is paid in.
    optional int32  shipping_method = 9; // Type of logistics channel that the buyer has selected when placing the order (0: not 3PL supported, > 0: 3PL supported). Used to group similar logistics channel together.
    optional string shipping_name   = 10; // Name of the person that will receive the shipped order parcel(s). Does not have to be the buyer's name.
    optional string shipping_phone  = 11; // Phone number of the person that will receive the shipped order parcel(s). Does not have to be the buyer's phone number.
    optional string shipping_address = 12; // The address to which the order's parcel(s) should be shipped.
    optional int64  shipping_fee    = 13; // Actual shipping fee. Will be refunded if the whole order is returned. See https://confluence.shopee.io/x/RfQLAw
    optional string shipping_carrier = 14; // The name of the logistics channel that is chosen to handle delivery.
    optional string shipping_traceno = 15; // Shipping consignment number/tracking code of the logistics channel.
    optional string actual_carrier  = 16; // The name of the logistics channel actually doing the delivery.
    optional int32  order_type      = 17; // (Deprecated) Type of this order (0: unknown, 1: simple (payment_method is offline payment), 2: escrow (payment_method is other payment method)). ref: PBData.beeshop.db.OrderType
    optional int32  payment_method  = 18; // Type of payment channel selected by the buyer (0: none, 1: cybersource, 2: bank transfer, 3: offline payment, 4: ipay88, 5: free, 6: cash on delivery, 7: esun, 8: bill payment, 13-42: other 3rd party payment channels). Used to group similar payment channels together. Set by P&L. ref: PBData.beeshop.db.PaymentMethod
    optional int64  escrow_fee      = 19; // (Deprecated) Set by P&L. See https://confluence.shopee.io/x/RfQLAw
    optional string remark          = 20; // Set by P&L.
    optional int32  status          = 21; // (Deprecated, use status_ext instead) Current status of the order. ref: PBData.beeshop.db.Status_ORDER_
    optional int32  create_time     = 22; // Order creation time. Will not change after order is created.
    optional int32  pay_time        = 23; // Time when the order is paid.
    optional int32  revise_time     = 24; // Set by P&L.
    optional int32  delivery_time   = 25; // Time when shipment of the order started.
    optional int32  complete_time   = 26; // Time when order status is changed to completed/return accepted/return completed.
    optional int32  cancel_time     = 27; // Time when order status (status_ext) is set to invalid.
    optional int64  checkoutid      = 28; // Unique identifier of the checkout this order belongs to.
    optional bytes  extinfo         = 29; // Extra information for this order in serialized protobuf format. ref: PBData.beeshop.db.OrderExtInfo
    optional bool   is_rated        = 30; // Whether this order has been rated.
    optional int32  status_ext      = 31; // Current status of the order (0: deleted, 1: unpaid, 2: paid, 3: shipped, 4: completed, 6: invalid, 7: cancel processing, 8: cancel completed, 9: return processing, 10: return completed, 11: escrow paid, 12: escrow created, 13: escrow pending, 14: escrow verified, 15: escrow payout, 16: cancel pending). ref: PBData.beeshop.db.Status_ORDER_EXT_
    optional int32  archive         = 32; // (Deprecated) Whether this order has been archived by the user.
    optional int32  mtime           = 33; // Order last modified time.
    optional int32  bankaccount_id  = 34; // Unique identifier of the buyer's bank account. Set by P&L with UpdateBankaccountIdProcess.
    optional int32  logistics_status = 35; // Current logistics status of the order (0: not started, 1: request created, 2: pickup done, 3: pickup retry, 4: pickup failed, 5: delivery done, 6: delivery failed, 7: request cancelled, 8: cash on delivery rejected, 9: ready, 10: invalid, 11: lost, 12: pending arrange). ref: PBData.beeshop.db.Status_LOGISTICS_
    optional int32  pickup_time     = 36; // Time when the order is picked up from the seller's store. Set by P&L SetOrderLogisticsProcessor.
    optional int32  shipping_confirm_time = 37; // For cash on delivery (COD) order: time when the order is placed. For non-COD order: time when the order is paid.
    optional int32  list_type       = 39; // The list that the order belongs to in the frontend order list page (1: paid, 2: unpaid, 3: completed, 4: cancelled, 5: shipped, 6: all, 7: to ship, 8: to receive, 9: to pay, 10: to ship (unprocessed), 11: to ship (processed), 12: return, 13: group buy - all, 14: group buy - ongoing, 15: group buy - completed, 16: group buy - invalid). For frontend use. ref: PBData.beeshop.cmd.OrderListType
    optional int32  receive_time    = 40; // Time when shipping ends (parcel(s) are received).
    optional int32  cb_option       = 41; // Whether this order is sold by cross border seller (0: sold by local seller, 1: sold by cross border seller).
}

// OrderItem is a representation of the item model/group purchased in the order. It is uniquely identified by a combination of order_id, item_id, model_id and group_id.
message OrderItem
{
    optional int32  userid      = 1; // Unique identifier of the buyer's account.
    optional int64  orderid     = 2; // Unique identifier of this order item's order.
    optional int32  shopid      = 3; // Unique identifier of the seller's shop.
    required int64  itemid      = 4; // ID of the item.
    optional int64  modelid     = 5; // ID of the item's model.
    optional int32  amount      = 6; // Quantity/number of the product bought/sold for this specific item and model in this order.
    optional int64  item_price  = 7; // Price of the item. Equal to item.price or item.wholesale_price.
    optional int64  order_price = 8; // If this order item has an offer, this is equal to the offer price, otherwise this is equal to the item_price. If this order item has a bundle, this is equal to the bundled price. If this order item has an addon subitem, this is equal to AddOnPrice.
    optional string currency    = 9; // Currency of the item price.
    optional int32  status      = 10; // Status of the order item (0: deleted, 1: unrated, 2: rated, 3: returned, 4: returned and rated, 5: cancelled). ref: PBData.beeshop.db.Status_OITEM_
    optional int64  chatid      = 11; // Used to find offer. Will be deprecated in the future, use offerid instead.
    optional int64  snapshotid  = 12; // Unique identifier of this item's snapshot, which is the complete state of the item when the order is placed.
    optional int64  offerid     = 13; // Unique identifier of the responding offer.
    optional bytes  extinfo     = 14; // Extra information for this order item. ref: PBData.beeshop.db.OrderItemExtInfo
    optional int64  groupid     = 15; // A unique group ID to distinguish groups of items in Cart, and Order. (e.g. AddOnDeal)
}

/*
The values in the orderitemextinfo (eg. discount, comm_fee) are already multiplied by quantity(amount field in OrderItem)
If there is any returns, these amounts do not change. Please check OrderItem.status instead.
*/
message OrderItemExtInfo
{
    optional int64 discount     = 1; // Voucher discount (already multiplied by quantity)
    optional int64 coin_offset  = 2; // Cash value of coins spent (already multiplied by quantity)
    optional int64 coin_used    = 3; // Coins spent (already multiplied by quantity)
    optional int32 coin_earn_rule_id = 4;
    optional int64 coin_earn_by_rule = 5; // Coins that can be earned according to the ruleid but could be > coin_earn because of user earning limits.
    optional int64 coin_earn    = 6; // Actual coins earned (already multiplied by quantity) ignoring return.
    optional bool  is_wholesale = 7;
    optional int32 wholesale_amount  = 8;
    optional int64 card_promotion_discount_bank = 9;
    optional int64 card_promotion_discount_shopee = 10;
    optional int32 comm_rule_id = 11; // Unique identifier of commission fee rule used by this order item.
    optional int64 comm_fee     = 12; // Commission fee charged to the seller (already multiplied by quantity).
    optional int64 card_txn_fee = 13; // Seller transaction fee (already multiplied by quantity).
    optional int64 comm_base_amount = 14; // Actual amount (after deduct voucher, coin etc) that is used by comm_fee calculation (already multiplied by quantity)
    optional int64 rebate_price = 15; // Bundle or promotion rebate that shopee will give to seller. Copied from item on order creation. (already multiplied by quantity)
    optional int32 free_return_day = 16;
    optional BundleOrderItem bundle_order_item = 17; // ref: PBData.beeshop.db.BundleOrderItem
    optional int32 coin_type = 18; // ref: PBData.beeshop.db.OrderCoinType
    optional int64 slash_price_activity_id = 19; // The related slash price activity id to the order item
    optional uint64 add_on_deal_id = 20; // Which AddOnDeal the item was purchased as part of
    optional bool  is_add_on_sub_item = 21; // true if item is purchased as an add_on_sub_item
    optional int64  order_item_id   = 22; // new field added for the order item data in TiDB for 1$ game shop
    optional bool is_one_dollar_game_order = 23; // new field added for the order item data in TiDB for 1$ game shop
    optional int32  create_time     = 24; // new field added for the order item data in TiDB for 1$ game shop
    optional ServiceFeeOrderItemInfo service_fee_info     = 25; // new field added for the active rules the order item extinfo is qualified
    optional uint32 product_promotion_type = 26; // save item/model promotion_type when placing order
    optional uint64 product_promotion_id = 27; // save item/model promotion_id when placing order
    optional int64 buyer_txn_fee = 28; // OrderItem-level buyer transaction fee (already multiplied by quantity). In v1, this value is distributed from the order. In v2, this value is either calculated directly or is the sum of bundle order items' buyer transaction fees.
    optional int64 admin_fee = 29; // admin fee divided at order item level, for Multi-parcel feature.
    repeated VoucherInfo vouchers = 30; // new logic, 1 OrderItem can enjoy multiple vouchers reward, including Shopee platform vouchers and seller vouchers.(NOT include FSV)
    optional bool is_virtual_sku = 31; // to mark if order item is virtual sku
    optional int64 input_price = 32; // the item's input price. If it's empty, it is the same with order_item.item_price
    optional int64 item_tax = 33; // tax component of the item's price, after item's price has been inflated. If it is empty, then item_tax will be 0
    optional int32 product_allocated_stock =34; // save item/model allocated stock when placing order
    repeated OrderSkuDBInfo component_skus = 35; // if item is virtual this list will be list of all the item which are parent of this item.
    optional int32 comm_rule_id_v2 = 36; // Unique identifier of commission fee rule used by this order item. comm_rule_id and comm_rule_id_v2 come from old and new DBs, respectively, and they are exclusive, only one of them has valid data at a time.
    optional OrderItemTaxInfo order_item_tax_info = 37;
    optional uint64 product_promotion_group_id = 38; // save item/model product_promotion_group_id when placing order
    repeated OrderItemStock order_item_stocks = 39; // save stock info when order is created, it is used to deduct and return item stock.
    optional string hs_code = 40; //hs code
    optional int32 buyer_txn_fee_rule_id = 41; // OrderItem-level buyer transaction fee rule ID (v2). If this OrderItem contains bundle order items, the rule ID will be set in each bundle order item instead.
    optional int64 tax_exemption = 42; // Tax exemption calculated for each Order Item. If having bundle deal, it includes total exemption of bundle deal items.
    optional OverallPurchaseLimitInfo overall_purchase_limit = 43; //store user level Overall purchase limit info for order item, for bundle deal, user level Overall purchase limit is stored in BundldOrderItemDetailInfo for each items in the bundle
}

message OrderItemStock {
    optional int32 product_stock = 1;
    optional string product_location_id = 2;
    optional uint32 product_fulfilment_type = 3;
    optional int32 product_amount = 4;
    optional int32 product_allocated_stock = 5;
}

message OrderSkuDBInfo {
    optional int64 item_id = 1;
    optional int64 model_id = 2;
    optional int64 snapshot_id = 3;
    optional int64 cost_price = 4;
    optional int64 item_price = 5;
    optional int32 quantity = 6;
    repeated OrderSkuDBInfo component_skus = 7; // if item is virtual this list will be list of all the item which are parent of this item.
    optional int64 order_sku_id = 8;
    optional int32 total_quantity = 9; // total number of items
}

message OrderItemTaxInfo {
    optional CustomTaxInfo custom_tax_info = 1;
    repeated GSTInfo gst_infos = 2; // Will only have order_item gst
}

message CommentTagInfo
{
   optional int32 offset   = 1;
   optional int32 length   = 2;
   optional int64 infoid   = 3;
   optional int32 type     = 4; // 0:User
}

message CommentTagInfoList
{
   repeated CommentTagInfo tags = 1;
}

message ItemModelId
{
    optional int64  itemid = 1;
    optional int64  modelid = 2;
    optional int64  snapshotid = 3;
}

message CmtExtInfo
{
    repeated string image = 1;
    optional bool   anonymous = 2;
    optional int32  editable_date = 3;
    repeated ItemModelId items = 4;
    optional string delete_reason = 5;
    optional string delete_operator     = 6;
    optional ItemCmt ItemRatingReply    = 7;
    optional UserCmt UserRatingReply    = 8;
    optional string tagids = 9;
    optional int32 like_count = 10;
    optional bool is_hidden = 11;
    optional InitialItemCmt first_submission_content = 12;
    optional string country = 13;
    repeated CmtExtVideo videos = 14;
    optional bool sync_to_social = 15;
    optional CmtExtDetailedRating detailed_rating = 16;
    repeated string template_tags = 17;
    optional bool has_template_tag = 18;
}

message CmtExtVideo
{
    optional string id = 1;
    optional string url = 2;
    optional string url_transcoded = 3;
    optional uint32 duration = 4;
    optional string cover = 5;
}

message CmtExtDetailedRating {
    optional uint32 product_quality = 1;
    optional uint32 seller_service = 2;
    optional uint32 delivery_service = 3;
}

message ItemCmt
{
    optional int64  cmtid       = 1;
    optional int32  userid      = 2;
    optional int64  orderid     = 3;
    optional int32  shopid      = 4;
    optional int64  itemid      = 5;
    optional int64  modelid     = 6;
    optional int32  rating      = 7;
    optional string comment     = 8;
    optional int32  ctime       = 9;
    optional int32  status      = 10;  // 0:CMT_DELETE 1:CMT_NORMAL 2:CMT_VALID
    optional bytes  mentioned   = 11;  // ref: PBData.beeshop.db.CommentTagInfoList
    optional int32  rating_star = 12;
    optional int32  editable    = 13;  // 0: expired, 1: can edit, 2: have edit once
    optional bytes  extinfo     = 14;  // ref: PBData.beeshop.db.CmtExtInfo
    optional int32  mtime       = 15;
    optional int32  opt         = 16;  // bitmap, see enum ref: PBData.beeshop.db.CmtOpt
    optional int32  filter      = 17;  // bitmap, see enum CmtFilter
}

message InitialItemCmt
{
    optional string comment = 1;
    repeated string images = 2;
    optional int64 coins = 3;
    repeated CmtExtVideo videos = 4;
}

enum CoinStatusType
{
    NOT_RETRIEVED = 0;
    RETRIEVED = 1;
}

message ItemCmtCoin
{
    optional int64 cmtid = 1;
    optional int32 userid = 2;
    optional int32 shopid = 3;
    optional int64 itemid = 4;
    optional string comment = 5;
    optional string images = 6;
    optional int64 coins = 7;
    optional int32 coin_status = 8; // see enum ref: PBData.beeshop.db.CoinStatusType
    optional int32 ctime = 9;
    optional int64 orderid = 10;
    optional string country = 11;
}

message UserCmt
{
    optional int64  cmtid       = 1;
    optional int32  userid      = 2;
    optional int32  shopid      = 3; // shopid of order
    optional int64  orderid     = 4;
    optional int32  rating      = 5;
    optional string comment     = 6;
    optional int32  ctime       = 7;
    optional int32  mtime       = 8;
    optional int32  status      = 9;  // 0:CMT_DELETE 1:CMT_NORMAL 2:CMT_VALID
    optional bytes  mentioned   = 10; // ref: PBData.beeshop.db.CommentTagInfoList
    optional int32  rating_star = 11; // [1,5]
    optional int32  editable    = 12; // 0: expired, 1: can edit, 2: have edit once
    optional bytes  extinfo     = 13; // ref: PBData.beeshop.db.CmtExtInfo
    optional int32  opt         = 14; // bitmap, see enum ref: PBData.beeshop.db.CmtOpt
}

message NotiMsg
{
    optional int64  msgid = 1;
    optional int32  shopid = 2;
    optional int32  cmd = 3;
    optional bytes  msg = 4;
    optional int32  ctime = 5;
    optional int32  status = 6;
}

message ShopStats
{
    optional int32  shopid = 1;
    optional int32  day = 2;  // in format yyyymmdd
    optional int32  stype = 3; // StatsType
    optional int64  value = 4;
}

message Feedback
{
    optional int32  userid = 1;
    optional int32  shopid = 2;
    optional string name = 3;
    optional string email = 4;
    optional string content = 5;
    optional int32  ctime = 6;
    optional int32  id = 7;
}

message ItemAudit
{
    optional int32  auditid = 1;
    optional int32  shopid = 2;
    optional int64  itemid = 3;
    optional int32  audit_type = 4;
    optional int32  status = 5;
    optional string data = 6;
    optional int32  ctime = 7;
    optional int32  mtime = 8;
    optional string country = 9;
}

message UserAudit
{
    optional int32  auditid = 1;
    optional int32  userid = 2;
    optional int32  audit_type = 3;
    optional int32  status = 4;
    optional string data = 5;
    optional int32  ctime = 6;
    optional int32  mtime = 7;
    optional string country = 8;
    optional int32  reason_type = 9;
}

message AccountAudit
{
    optional int32  auditid = 1;
    optional int32  userid = 2;
    optional int32  audit_type = 3;
    optional int32  status = 4;
    optional string data = 5;
    optional int32  ctime = 6;
    optional int32  mtime = 7;
    optional string country = 8;
}

message ShopAudit
{
    optional uint64 auditid = 1;
    optional int32  shopid = 2;
    optional int32  audit_type = 3;
    optional int32  status = 4;
    optional string data = 5;
    optional int32  ctime = 6;
    optional int32  mtime = 7;
    optional string country = 8;
}

message OrderAudit
{
    optional int32  auditid = 1;
    optional int32  shopid = 2;
    optional int64  orderid = 3;
    optional int32  opuserid = 4;
    optional int32  old_status = 5;
    optional int32  new_status = 6;
    optional string data = 7;
    optional int32  ctime = 8;
    optional int32  mtime = 9;
    optional string country = 10;
    optional int32  estimate_time = 11;  //  for next status
    optional bytes  extinfo = 12;  // ref: PBData.beeshop.db.TransHisExtInfo
}

message LogisticsAudit
{
    optional int32  auditid = 1; // Unique identifier of the logistics audit.
    optional int32  shopid = 2; // Unique identifier of the seller's shop.
    optional int64  orderid = 3; // Unique identifier of the order.
    optional int32  opuserid = 4; // Unique identifier of the user account doing the audited operation.
    optional int32  old_status = 5; // Previous logistics status of the order.
    optional int32  new_status = 6; // New logistics status of the order.
    optional string data = 7; // JSON string containing fields that is changed as the result of the audited operation and other related information.
    optional int32  ctime = 8; // Creation time of the logistics audit.
    optional int32  mtime = 9; // Modification time of the logistics audit.
    optional string country = 10; // Country of the order, determined from the order's currency.
}

message ReturnLogisticsAudit
{
    optional int32  auditid = 1; // Unique identifier of the return logistics audit.
    optional int32  shopid = 2; // Unique identifier of the seller's shop.
    optional int64  return_id = 3; // Unique identifier of the return.
    optional int32  opuserid = 4; // Unique identifier of the user account doing the audited operation.
    optional int32  old_status = 5; // Previous logistics status of the return.
    optional int32  new_status = 6; // New logistics status of the return.
    optional string data = 7; // JSON string containing fields that is changed as the result of the audited operation and other related information.
    optional int32  ctime = 8; // Creation time of the return logistics audit.
    optional int32  mtime = 9; // Modification time of the return logistics audit.
    optional string country = 10; // Country of the returned order.
}

message ReturnCompensationAudit
{
  optional int32  audit_id = 1; // Unique identifier of the return logistics audit.
  optional int32  shop_id = 2; // Unique identifier of the seller's shop.
  optional int64  return_id = 3; // Unique identifier of the return.
  optional int32  op_user_id = 4; // Unique identifier of the user account doing the audited operation.
  optional int32  old_status = 5; // Previous logistics status of the return.
  optional int32  new_status = 6; // New logistics status of the return.
  optional string data = 7; // JSON string containing fields that is changed as the result of the audited operation and other related information.
  optional int32  create_time = 8; // Creation time of the return logistics audit.
  optional int32  update_time = 9; // Modification time of the return logistics audit.
  optional string region = 10; // Region of the returned order.
}

message ReturnNegotiationAudit
{
  optional int32  audit_id = 1; // Unique identifier of the return logistics audit.
  optional int32  shop_id = 2; // Unique identifier of the seller's shop.
  optional int64  return_id = 3; // Unique identifier of the return.
  optional int32  op_user_id = 4; // Unique identifier of the user account doing the audited operation.
  optional int32  old_status = 5; // Previous logistics status of the return.
  optional int32  new_status = 6; // New logistics status of the return.
  optional string data = 7; // JSON string containing fields that is changed as the result of the audited operation and other related information.
  optional int32  create_time = 8; // Creation time of the return logistics audit.
  optional int32  update_time = 9; // Modification time of the return logistics audit.
  optional string region = 10; // Region of the returned order.
}

message ReturnAudit
{
    optional int32  auditid = 1;
    optional int32  shopid = 2;
    optional int64  return_id = 3;
    optional int32  opuserid = 4;
    optional int32  old_status = 5;
    optional int32  new_status = 6;
    optional string data = 7;
    optional int32  ctime = 8;
    optional int32  mtime = 9;
    optional string country = 10;
}

message RefundAudit
{
    optional int32  auditid = 1;
    optional int32  userid = 2;
    optional int64  refund_id = 3;
    optional int32  opuserid = 4;
    optional int32  old_status = 5;
    optional int32  new_status = 6;
    optional string data = 7;
    optional int32  ctime = 8;
    optional int32  mtime = 9;
    optional string country = 10;
}

message CheckoutAudit
{
    optional int32  auditid = 1;
    optional int32  userid = 2;
    optional int64  checkoutid = 3;
    optional int32  opuserid = 4;
    optional int32  old_status = 5;
    optional int32  new_status = 6;
    optional int64  amount = 7;
    optional string currency = 8;
    optional string data = 9;
    optional int32  ctime = 10;
    optional int32  mtime = 11;
    optional string country = 12;
    optional int32  audit_type = 13;
}

message ShopFollow
{
    required int32  userid = 1;
    optional int32  shopid = 2;
    optional int32  status = 3; // ref: FOLLOW_DELETE =0, FOLLOW_NORMAL =1
    optional int32  ctime = 4;
    optional int64  id = 5;
}

message ItemLiked
{
    required int32  userid = 1;
    optional int32  shopid = 2;
    optional int64  itemid = 3;
    optional int32  status = 4;
    optional int32  ctime = 5;
}

message WarehouseAddressMap
{
    optional uint32 id = 1;
    optional string warehouse_code = 2;
    optional int32  address_id = 3;
    optional string region = 4;
    optional int32  create_time = 5;
    optional int32  update_time = 6;
}

message DeliveryInstruction {
    optional bool authorization_to_leave_parcel = 1;
    optional string direction_message = 2;
    optional string foody_address_instruction = 3; // deprecated
}

message BuyerAddressExtInfo
{
    optional string geoinfo = 1; // json for Go-Jek geo location
    optional uint64 user_location_id = 2;
    optional int32 preferred_delivery_option = 3; // ref: PreferredDeliveryOption
    optional DeliveryInstruction delivery_instruction = 4;
    optional string label = 5;
    optional string address_instruction = 6;
    optional int32 delivery_timeslot_id = 7; // id defined by SLS to specify the actual timeslot
    optional string place_id = 8;
    optional int32 client_id = 9; // ref: AddressClientInfoType
}

message BuyerAddress
{
    optional int32  id = 1;
    optional int32  userid = 2;
    optional string name = 3;
    optional string phone = 4;
    optional string country = 5;
    optional string state = 6;
    optional string city = 7;
    optional string address = 8;
    optional int32  status = 9;
    optional int32  ctime = 10;
    optional int32  mtime = 11;
    optional string zipcode = 12;
    optional int32  deftime = 13;
    optional string full_address = 14;
    optional string district = 15; // newly added fields used for different level address
    optional string town = 16;
    optional int32  logistics_status = 17; // last recent logistics success status, 0-default, 1-success, 2-failed
    optional string icno = 18;
    optional bytes  extinfo = 19;  // ref: PBData.beeshop.db.BuyerAddressExtInfo
}

message TransHisExtInfo
{
    optional int32  mintime = 1; // min estimated escrow release time
    optional int32  maxtime = 2; // max estimated escrow release time
    optional bool   using_wallet = 3;  // seller also using wallet when this trans EscrowVerified
    optional int64  comm_fee = 4; //Same value as in orderExtInfo. Adjusted for return
    optional int64  seller_absorbed_discount = 5; //Discount absorbed by seller due to seller paid voucher. Adjusted for return
    optional int64  card_txn_fee = 6; //Adjusted for return
    optional int64  tax_amount = 7;
    optional int64  buyer_txn_fee = 8;
    optional int64  admin_fee = 9;
    optional int64 payment_channel_id = 10; // Payment channel
    optional JkoAccountSeller jko_seller_info = 11; // Jko seller account info
    optional int64  seller_absorbed_coin_cashback_voucher = 12; // coin-cashback voucher absorbed by seller, already adjusted for return
    optional int64  total_compensate_fee = 13;
    optional int64  service_fee = 14;
    optional int64  total_item_tax_amount = 15;
}

message TransHistory
{
    optional int32  transid = 1;
    optional int32  userid = 2;
    optional int32  shopid = 3;
    optional int64  orderid = 4;
    optional string image = 5; // order.item.image
    optional string custom_name = 6; // buyer name
    optional string withdraw_name = 7; // deprecated
    optional string withdraw_bankacc = 8; // deprecated
    optional int64  amount = 9; // deprecated
    optional string currency = 10;
    optional int64  withdraw_transfee = 11;   // deprecated
    optional int32  trans_type = 12; // ref: PBData.beeshop.db.TransType
    optional int32  status = 13; // ref: PBData.beeshop.db.Status_TRANS_
    optional int32  ctime = 14;
    optional int32  mtime = 15;
    optional int32  release_time = 16; // escrow release time (the money be transfered to seller)
    optional int64  price_before_discount = 17;  // order amount + buyerpromottion + shipping fee
    optional int64  rebate_price = 18; // Cost of Good Sold, for seller promotion
    optional bytes  extinfo = 19;  // ref: PBData.beeshop.db.TransHisExtInfo
}

message TransHistoryV2
{
    optional int32  transid = 1;
    optional int32  userid = 2;
    optional int32  shopid = 3;
    optional int64  orderid = 4;
    optional string image = 5; // order.item.image
    optional string custom_name = 6; // buyer name
    optional string withdraw_name = 7; // deprecated
    optional string withdraw_bankacc = 8; // deprecated
    optional int64  amount = 9; // deprecated
    optional string currency = 10;
    optional int64  withdraw_transfee = 11;   // deprecated
    optional int32  trans_type = 12; // ref: PBData.beeshop.db.TransType
    optional int32  status = 13; // ref: PBData.beeshop.db.Status_TRANS_
    optional int32  ctime = 14;
    optional int32  mtime = 15;
    optional int32  release_time = 16; // escrow release time (the money be transfered to seller)
    optional int64  price_before_discount = 17;  // order amount + buyerpromottion + shipping fee
    optional int64  rebate_price = 18; // Cost of Good Sold, for seller promotion
    optional bytes  extinfo = 19;  // ref: PBData.beeshop.db.TransHisExtInfo
    optional int64  estimated_escrow_amount = 20; // ref: PBData.beeshop.db.Order.OrderExtInfo
    optional int32  trans_payment_type = 21; // ref: PBData.beeshop.db.Order.PaymentMethod
}

message UserLoginInfo
{
    optional int32  userid = 1;
    optional string server_addr = 2;
    optional int32  timestamp = 3;
    optional uint64 clikey = 4;
    optional int32  shopid = 5;
    optional bool   push_ready = 6;
    optional string country = 7;
    optional bytes  deviceid = 8;
    optional int32  app_version = 9; // deprecated, will be removed recently
    optional string source = 10; // eg: ios-app_mall, can use SessDM.AnalysisNewSource to analysis
    optional string new_source = 11; // deprecated, will be removed recently
}

message UserLoginClikey
{
    optional int32  userid = 1;
    repeated uint64 clikey = 2;
}

message UserLocationSnapshot {
  optional uint64 id = 1;
  optional uint32 user_id = 2;
  optional uint64 original_user_location_id = 3;  // points back to the original address object

  // Protobuf serialized string storing detailed snapshot information. See below for definition.
  optional bytes user_location_snapshot_data = 4;

  // create_time refers to the create time of this snapshot object (not the original address)
  optional uint32 create_time = 5;

  // Indicate user_location_snapshot_data_type for BuyerAddress and future UserLocation
  optional uint32 user_location_snapshot_data_type = 6;
}

message ChatTextTranslated
{
    optional string translated_text = 1;
    optional string translated_language = 2;
    optional string source = 3;  // 1: google
}

message ChatGeneralText
{
    optional string text = 1;
    repeated ChatTextTranslated translated_text = 2;
}

message ChatTextInfo
{
    required string text = 1;
    optional ChatTextTranslated translated_text = 2;
}

enum ChatFileServerId {
    // original default file server
    CHAT_FILE_SERVER_DEFAULT = 0;
    // special file server setup for chat/chatbot feature.
    CHAT_FILE_SERVER_CHATBOT = 1;
}

message ChatImageInfo
{
    required string imageUrl = 1;
    optional string thumbUrl = 2;
    optional int32 thumbWidth = 3;
    optional int32 thumbHeight = 4;
    // added for chatbot feature (SPSS-7335 https://confluence.shopee.io/x/g4bOCw).
    // now there are multiple file servers.
    // which file server the image_url/thumb_url (image id) locates is specified by file_server_id.
    // mapping of file_server_id to actual file server address is maintained on both client side and server side.
    optional int32 file_server_id = 5; // ref: ChatFileServerId
}

/// Copied to beeshop_chat_generic_msg.proto.ChatProductInfo to avoid dependency on beeshop_db.proto.
/// Remember to update them together.
message ChatProductInfo
{
    optional int64 itemid = 1;
    optional int32 shopid = 2;
    optional string name = 3;
    optional string thumbUrl = 4;
    optional string price = 5;
    optional int32 quantity = 6;
    optional int64 snapshopid = 7;
    optional string modelname = 8;
    optional string price_before_discount = 9;
}

message ChatOfferInfo
{
    required int32 offerStatus = 1;
    required int64 price = 2;   //offer price
    required string currency = 3;
    required int32 quantity = 4;
    optional int64 modelid = 5;
    optional int64 offerid = 6;
    optional int64 itemid = 7;
    optional string item_name = 8;
    optional string model_name = 9;
    optional int64 price_before_discount = 10;
    optional int64 original_price = 11;
    optional string imageUrl = 12;
    optional int32 shopid = 13;
    optional bool tax_applicable = 14;
    optional int64 tax_value = 15;
    optional uint32 tax_rate = 16;
    optional int64 price_before_tax = 17;
}

message ChatOrderInfo
{
    optional int32 shopid = 1;
    optional int64 orderid = 2;
    optional int64 checkoutid = 3;
    optional string ordersn = 4;
    optional int64 total_price = 5;
    optional string currency = 6;
    optional string order_status = 7;
    repeated string item_image = 8;
    optional int32 list_type = 9;
    optional bool has_request_refund = 10;
}

message ChatChangeOrderAddressInfo
{
    optional ChatOrderInfo order_info = 1;
    optional BuyerAddress buyer_address = 2;
}

message ChatStickerInfo
{
    optional string stickerid = 1;
    optional string packid = 2;
    optional string format = 3; // png/jpg/gif ...
}

message ChatWebViewInfo {
   optional string url = 1;
}

message ChatNotificationInfo {
   optional string notification_for_sender = 1;
   optional string notification_for_receiver = 2;
   optional ChatNotificationType notification_type = 3;
   //_formatted text is added for SPSS-8494
   //to support bold text
   //the format is defined same as the one used by Noti AR
   //currently we only support `<b>...</b>` without escaping rule.
   //_formatted text has higher precedence for client that can render the format
   //if _formatted text is non empty, we should display _formatted text.
   optional string notification_for_sender_formatted = 4;
   optional string notification_for_receiver_formatted = 5;
}

enum ChatNotificationType
{
    NOTI_TYPE_UNKNOWN    = 0;
    NOTI_TYPE_CLOSE      = 1;
    NOTI_TYPE_JOIN       = 2;
    NOTI_TYPE_MOVE       = 3;
    NOTI_TYPE_AUTO_CLOSE = 4;
    NOTI_TYPE_ANGBAO_CLAIMED = 5; // for chat angbao claim
    NOTI_TYPE_ANGBAO_EXPIRED = 6; // for chat angbao expire
    NOTI_TYPE_CHATBOT_ENDED = 7; // inform user the chatbot session has ended
    NOTI_TYPE_COINS_TRANSFER_CLAIMED = 8; // inform sender&recipient that coin has been claimed
}

message ChatMsgFaqEntry {
    optional uint64 question_id = 1;
    optional string text = 2;
}

message ChatMsgFaqCategory {
    optional string title = 1;
    repeated ChatMsgFaqEntry questions = 2;
}

// beeshop.cmd.ChatMessageType.MSG_TYPE_FAQ
message ChatMsgFaq {
    optional uint64 userid = 1;
    optional uint64 shopid = 2;
    optional uint64 faq_id = 3;
    optional string opening = 4;
    repeated ChatMsgFaqCategory categories = 5;
}

// beeshop.cmd.ChatMessageType.MSG_TYPE_FAQ_QUESTION
message ChatMsgFaqQuestion {
    optional uint64 userid = 1;
    optional uint64 shopid = 2;
    optional uint64 faq_id = 3;
    optional uint64 question_id = 4;
    optional string text = 5; // original question text shown to the user
}

// beeshop.cmd.ChatMessageType.MSG_TYPE_FAQ_FEEDBACK_PROMPT
message ChatMsgFaqFeedbackPrompt {
    optional bool feedback_sent = 1; // whether the user has clicked on the "Yes/No"
    optional uint64 userid = 2;
    optional uint64 shopid = 3;
    optional uint64 faq_id = 4;
    optional uint64 question_id = 5;
}

// beeshop.cmd.ChatMessageType.MSG_TYPE_FAQ_FEEDBACK
message ChatMsgFaqFeedback {
    optional uint64 feedback_msg_id = 1; // on which Feedback Prompt the user chooses "Yes/No"
    optional bool helpful = 2;
    optional string text = 3; // original text on the button seen by the user
    optional uint64 userid = 4;
    optional uint64 shopid = 5;
    optional uint64 faq_id = 6;
    optional uint64 question_id = 7;
}

// beeshop.cmd.ChatMessageType.MSG_TYPE_FAQ_UNSUPPORTED_INPUT
message ChatMsgFaqUnsupportedInput {
    // empty
}

// beeshop.cmd.ChatMessageType.MSG_TYPE_LIVE_AGENT_PROMPT
message ChatMsgLiveAgentPrompt {
    // empty
}

// beeshop.cmd.ChatMessageType.MSG_TYPE_LIVE_AGENT
message ChatMsgLiveAgent {
    optional string text = 1; // original text on the button seen by the user
}

// beeshop.cmd.ChatMessageType.MSG_TYPE_BUNDLE_MESSAGE
message ChatBundleMessage
{
    repeated uint64 message_ids = 1;
    optional uint64 type = 2; //beeshop.cmd.BundleMsgType
}

// beeshop.cmd.ChatMessageType.MSG_TYPE_VIDEO
message ChatVideoInfo {
    optional string video_url = 1;
    optional string thumb_url = 2;
    optional int32 thumb_width = 3;
    optional int32 thumb_height = 4;
    optional int32 duration_seconds = 5;
}

// for crm template
message TemplateMsgValue
{
    // empty
}

// for crm template
message TemplateInfo
{
    optional string template_id = 1;
    optional TemplateMsgValue msg_value = 2;
}

message CategoryNameInfo
{
    optional string Lang = 1;
    optional string Name = 2;
}

message GST {
	optional uint32 gst_type = 1; // ref: GSTType
	optional uint32 gst_rate = 2; // 1% => 1000,
}

message IDCategoryTax {
    // this field is used in CB tax calculation in ID
    // ref: https://confluence.shopee.io/display/SLP/SPLT-403+Tax+Service+-+CB+Seller+Tax
    optional uint64 cif_offset_a = 1; // Inflated by 10^5
    optional uint64 cif_offset_b = 2; // Inflated by 10^5
    optional uint64 cif_floor = 3; // Inflated by 10^5

    optional uint32 income_tax_rate = 4;
    optional uint32 import_tax_rate = 5;
    optional uint32 vat_gst_tax_rate = 6;
}

message CategoryExtInfo
{
    repeated CategoryNameInfo names = 1;
    repeated int64 default_attrid = 2;
    optional int32 return_type = 3; //ref: PBData.beeshop.db.CategoryReturnType
    optional int32 free_return_day = 4;
    repeated int32 return_reason_bl_before_frd = 5;
    repeated int32 return_reason_bl_after_frd = 6;
    optional bool block_cb = 7;
    optional bool placeholder = 8;//if you want to clear a repeated value, but all other fields are nil, the extinfo will be nil and ignored by coreserver. you can set this filed to avoid this issue
    optional bool is_3c = 9;  // computer, communications, consumer electronics. flag used for affiliate ads classification
    optional bool enable_size_chart = 10; // [SPCS-1418]
    optional int32 low_stock_value = 11; // low stock threshold of category
    repeated int32 block_buyer_platform = 12;  // contains a list of beeshop_ads.TrackingPlatformType integers, specifying each platform the category should be hidden from.
    optional int32 tax_rate = 13; // tax rate is a cb tax rate, multiplied by 100000 (same with item price)
    optional int32 local_tax_rate = 14; // local tax rate is a tax rate for local seller, multiplied by 100000 (same with item price)
    repeated GST gsts  = 15; // a list of gsts of different types
    optional int32 declaration_tax_rate = 16; // declaration tax rate
    optional string hs_code = 17; // category level hs code
    optional IDCategoryTax id_tax = 19; // tax fields used for ID tax
}

message Category
{
    optional int32  catid = 1;
    optional string name = 2;
    optional string image = 3;
    optional string country = 4;
    optional int32  status = 5;  // ref: CAT_NORMAL
    optional int32  sort_weight = 6;  // for homepage
    optional string display_name = 7;
    optional int32  usage_sort_weight = 8;  // for add product
    optional int32  parent_category = 9; // parent catid, 0 for top level category
    optional int32  is_default_subcat = 10; // this category is default subcategory in parent cat, for old version app
    optional int32  is_adult = 11; // this category is adult category if value is 1
    optional int32  max_estimated_days = 12;
    optional int32  has_active_childs = 13; // 1 means this categrory has non-default childs, 0 don't have
    optional bytes  extinfo = 14; // ref: PBData.beeshop.db.CategoryExtInfo
}

message Pchat
{
    optional int64  pchatid = 1;
    optional int32  buyer_userid = 2;  // buyer_userid is the smaller userid of two users
    optional int32  seller_userid = 3;
    optional int32  buyer_clear_time = 4;
    optional int32  seller_clear_time = 5;
    optional int64  buyer_last_read = 6;  // msgid of buyer last read
    optional int64  seller_last_read = 7;  // msgid of seller last read
    optional int64  last_msgid = 8;
    optional int32  last_msgtime = 9;
    optional int32  ctime = 10;
    optional int32  mtime = 11;
    optional int32  buyer_unread = 12; // # of msgs buyer didn't read
    optional int32  seller_unread = 13;
}

message Conversation
{
    optional int64  chatid = 1;
    optional int32  buyer_userid = 2;  // for new chat, buyer_userid will be the smaller userid of two users
    optional int32  seller_userid = 3;
    optional int32  shopid = 4;
    optional int64  itemid = 5;
    optional int32  buy_count = 6;
    optional int64  offer_price = 7;
    optional int64  accept_price = 8;
    optional int32  offer_status = 9;
    optional int32  ctime = 10;
    optional int32  mtime = 11; // last msg time
    optional int64  orderid = 12;
    optional int32  archive = 13;
    optional int64  modelid = 14;
    optional int64  pchatid = 15;  // primary chatid
    optional int32  accept_buy_count = 16;
    optional int32  buyer_clear_time = 17;
    optional int32  seller_clear_time = 18;
    optional int64  buyer_last_read = 19;  // msgid of buyer last read
    optional int64  seller_last_read = 20;  // msgid of seller last read
    optional int64  last_msgid = 21;
}

message Offer
{
    optional int64  offerid = 1;
    optional int64  pchatid = 2;
    optional int64  chatid = 3;
    optional int32  buyer_userid = 4;
    optional int32  seller_userid = 5;
    optional int32  shopid = 6;
    optional int64  itemid = 7;
    optional int64  modelid = 8;
    optional int32  buy_count = 9;
    optional int64  offer_price = 10;
    optional int32  offer_status = 11; // ref: OFFER_ACCEPT
    optional int32  ctime = 12;
    optional int32  mtime = 13;
    optional int64  snapshotid = 14;
    optional bool tax_applicable = 15;
    optional int64 tax_value = 16;
    optional uint32 tax_rate = 17;
    optional int64  offer_price_before_tax = 18;
}

message ConversationMsg
{
    optional int64  chatid = 1;
    optional int32  userid = 2;  // from userid
    optional int64  msgid = 3;
    optional int32  status = 4;
    optional int32  ctime = 5;
    optional int32  opt = 6; // 1: autoreply
}

message ConvMsg
{
    optional int64  pchatid = 1;  // primary chatid
    optional int64  chatid = 2;
    optional int32  userid = 3;  // from userid
    optional int64  msgid = 4;
    optional int32  status = 5;
    optional int32  ctime = 6;
    optional int32  opt = 7; // 1: autoreply
}

message ChatQuickReply
{
    optional int32 id = 1;
    optional int32 userid = 2;
    optional bytes content = 3;  // content contains serialized form of ref: PBData.beeshop.db.ChatQuickReplyContent
    optional bytes extinfo = 4;
    optional bool  is_on = 5;
    optional int32 mtime = 6;
}

message ChatQuickReplyContent
{
    repeated string value = 1;
}

message Cart
{
    optional int32  id          = 1;
    optional int32  userid      = 2;
    optional int32  shopid      = 3;
    optional int32  mtime       = 4;
    optional string cartproto   = 5; // ref: PBData.beeshop.cmd.ShopOrder
    optional int32  item_count  = 6; // number of items in cartproto
}

// used in cart shared service
message CartV2
{
    optional int64  id           = 1;
    optional uint64 user_id      = 2;
    optional uint64 shop_id      = 3;
    optional uint32 update_time  = 4;
    optional string cart_proto   = 5; // ref: ShopOrderV2
    optional int32  item_count   = 6; // number of items in shop_order_proto
}

// used in cart shared service
message ShopOrderV2
{
    optional uint64 shop_id = 1;
    repeated CartItemV2 cart_items = 2; // ref: CartItemV2
}

// used in cart shared service
message CartItemV2 {
    optional uint64 item_id = 1;
    optional int32 quantity = 2;
    optional int64 price = 3; // core will update price on site
    optional string name = 4;
    optional string image = 5;
    optional uint64 model_id = 6;
    optional string currency = 7;
    optional string model_name = 8;
    optional bool checkout = 9;
    optional uint64 chat_id = 10; // deprecated, use offer_id
    optional int32 status = 12; // server set CART_ITEM_DELETE or CART_ITEM_NORMAL or CART_ITEM_UPDATED
    optional uint32 update_time = 13;
    optional uint32 create_time = 14;
    optional bool donot_add_quantity = 15; // true:just save iteminfo.quantity;   false:add iteminfo.quantity to old one;
    optional int32 stock = 16; // item stock, core will update it on site
    optional uint64 offer_id = 17; // will set if this cartitem belongs to an offer
    optional int64 origin_price = 18; // price when buyer add this cartitem in
    optional int32 origin_condition = 19; // condition when buyer add this cartitem in
    optional int32 condition = 20;
    optional int32 origin_quantity = 21; // quantity when buyer add this cartitem in
    optional int64 item_discount = 22;
    optional string source = 23; // for tracking source of bought, json string
    optional int64 wholesale_price = 24;
    optional int64 origin_wholesale_price = 25; // wholesale_price when buyer add this cartitem in
    optional bool is_flash_sale = 26; // whether item is having flash sale when item is added into cart
    optional uint64 bundle_deal_id = 27;
    optional uint64 add_on_deal_id = 28;
    optional bool is_add_on_sub_item = 30; // true if item is purchased as an add_on_sub_item
    optional int64 item_group_id_v2 = 31; // A unique group ID to distinguish groups of items in Cart, and Order. (eg. AddOnDeal)
    optional uint32 promotion_type = 32; // used for addOnDeal and pwg. See cart share service: CartItem proto for more detail
}

message Activity
{
    optional int32  activity_id     = 1;
    optional int32  userid          = 2;
    optional int32  type            = 3; // ref: PBData.beeshop.db.ActivityType
    optional int32  createtime      = 4;
    optional string activity_proto  = 5; // ref: PBData.beeshop.cmd.ActivityInfo
    optional int32  status          = 6; // ref: PBData.beeshop.db.Status_ACTIVITY_
}

message SimpleAction
{
    optional int64  action_id       = 1;
    optional int32  createtime      = 2; // version
    optional int32  status          = 3; // ref: PBData.beeshop.db.Status_ACTION_
    optional int64  groupid         = 4; // not uniq guaranted, use action_id
    optional int32  grouped_count   = 5; // number of ARs who have the same groupid
    optional int32  userid          = 6; // for orm
}

message Action
{
    optional int64  action_id       = 1;
    optional int32  userid          = 2;
    optional int32  type            = 3; // ref: PBData.beeshop.cmd.NotiCode
    optional int32  createtime      = 4;
    optional string action_proto  = 5; // ref: PBData.beeshop.cmd.ActionInfoDB
    optional int32  status          = 6; // ref: PBData.beeshop.db.Status_ACTION_
    optional int32  action_cate     = 7; // ref: PBData.beeshop.db.ActionCategory
    optional int64  groupid         = 8;
    optional int32  grouped_count   = 9; // number of ARs who have the same groupid
}

message ActionIndex
{
    optional int64  action_id       = 1;
    optional int64  userid          = 2;
    optional int32  type            = 3; // ref: PBData.beeshop.cmd.NotiCode
    optional int32  taskid          = 4;
    optional int32  createtime      = 5;
    optional int32  status          = 6; // ref: PBData.beeshop.db.Status_ACTION_
    optional int32  action_cate     = 7; // ref: PBData.beeshop.db.ActionCategory
    optional int64  groupid         = 8;
    optional int32  grouped_count   = 9; // number of ARs who have the same groupid
    optional int32  app_type        = 10;
    optional int32  platform_type   = 11;
}

message ActionContent
{
    optional int64  action_id       = 1;
    optional int64  userid          = 2;
    optional string action_proto    = 3; // ref: PBData.beeshop.cmd.ActionInfoDB
}

// used to send events to seller center
enum AR_EVENT_TYPE {
    CREATE_ACTION = 1;
    DELETE_ACTION = 2;
    UPDATE_ACTION = 3;
}

message NotiAREvent {
    optional int32 event_type = 1; // ref: PBData.beeshop.db.AR_EVENT_TYPE
    optional string event_id = 2; // uniquely identify this event
    optional int64 action_id = 3;
    optional int32 userid = 4;
    optional int32 shopid = 5;
    optional int32 noticode = 6; // ref: PBData.beeshop.cmd.NotiCode
    optional int32 createtime = 7;
    optional int32 action_cate = 8; // ref: PBData.beeshop.db.ActionCategory
    optional int32 taskid = 9; //control system taskid
    optional int64 groupid = 10;
    optional int32 grouped_count = 11;
}

message ProductListWeight
{
    optional int32  id              = 1; // autoincrement primary key
    optional int32  catid          = 2; // -1 for default
    optional int32  weight_type     = 3; // ProductListWeightType
    optional double weight_value    = 4;
    optional int32  mtime           = 5;
    optional string country         = 6;
}

message RecUserConfig
{
    optional int32 id               = 1; // autoincrement primary key
    optional string fname           = 2; // config name
    optional string algo_name       = 3; // algorithm name
    optional string country         = 4;
    optional int32 fvalue           = 5;
}

message RecUserStats
{
    optional int32 id               = 1; // autoincrement primary key
    optional int32 shopid           = 2;
    optional int32 userid           = 3;
    optional string country         = 4;
    optional string category        = 5; // stat type i.e."search"
    optional int32 ctime            = 6;
    optional int32 value            = 7;
}

message RecUserBoost
{
    optional int32 id               = 1;
    optional int32 userid           = 2;
    optional double boost_value     = 3;
    optional string country         = 4;
}

message ProductListBoost
{
    optional int32  id              = 1;
    optional int32  shopid          = 2;
    optional int64  itemid          = 3;
    optional double boost_value     = 4;
    optional string country         = 5;
    optional int32  mtime           = 6;
    optional int32  status          = 7;
}


message Collection
{
    optional int32  collection_id   = 1;
    optional string name            = 2;
    optional string description     = 3;
    optional string image           = 4;
    optional string country         = 5;
    optional int32  status          = 6;
    optional int32  ctime           = 7;
    optional int32  mtime           = 8;
    optional string data            = 9; // ref: PBData.beeshop.cmd.ShopItemId
    optional bytes  long_tail_condition = 10; // long tail filter SearchCondition
    optional string product_overlay_image = 11; // SPCS-1533 Collection Item Customization
    optional string cluster_id = 12; // C57 Core Automated Collection
    optional bool   recommend_toggle = 13; // C57 Core Automated Collection
    optional int32  collection_type  = 14; // ref: PBData.beeshop.db.CollectionType
    optional int32  sold             = 15; // sold count, set by DEEP team
    optional string page_template_image = 16;// SPDL-1912,the top collection image displaying
    optional string page_template_subtitle = 17;// SPDL-1912,the collection description displaying
    optional bool personalised_item_display = 18;
}

message SecondLegShipment
{
    optional string shipping_carrier = 1;
    optional string tracking_number = 2;
    optional int64 shipping_fee = 3;
    optional int32 shipping_date = 4;
}

message CompensationEntity
{
  optional int32 return_seller_compensation_due_date    = 1;
  optional int64 compensation_amount  = 2;
  optional int64 maximum_compensation_amount  = 3;
  optional int32 wallet_payment_status = 4; //Ref: WalletPaymentStatus
}

/*
If returning item with itemid 100 and modelid 200.
Modelid will be set with [200] while itemid will be empty.
*/
message ReturnInfo
{
    repeated int64  itemid = 1; //if item has model, only modelid should be set.
    optional string buyer_email = 2;
    optional string seller_email = 3;
    repeated OrderItem item = 4;
    optional int32  requested_time = 5;
    optional int32  accepted_time = 6;
    optional int32  cancelled_time = 7;
    optional int32  judging_time = 8; //When return goes into either RT4 (judging) or RT8 (seller_dispute)
    optional int32  refundpaid_time = 9;
    optional int32  closed_time = 10;
    optional string text_reason = 11;
    optional int32  seller_due_date = 12;
    optional int32  dispute_reason = 13;
    optional string dispute_text_reason = 14;
    optional int32  seller_reply_due_date = 15; // seller provide evidence due date
    optional int64  amount_before_discount = 16; // amount of money to refund before voucher
    optional int32  discount_percentage = 17;
    optional PromotionInfo promotion_info = 18;
    optional int64  shipping_fee = 19; // returns the shipping fee part of total return
    repeated int64  modelid = 20;
    optional BankAccountInfo bank_account = 21;
    optional string images = 22; //comma separated string of images uploaded by seller as proof
    optional int64 coin_spend_refund = 23;
    optional int64 coin_earn_refund = 24;
    optional int64 seller_refund_amount = 25; //amount of money to deduct from seller
    optional CardPromotionOrderInfo card_promotion_info = 26;
    optional string buyer_images = 27; //comma-separated string of images uploaded by buyer to show problem
    optional BuyerAddress return_pickup_address = 28;
    optional string tracking_number = 29; // https://jira.garenanow.com/browse/SPCS-231
    optional bool needs_logistics = 30; //items to be sent back to seller. Can be either integrated/non-integrated
    optional OrderLogisticsInfo logistics_info = 31; //ref: PBData.beeshop.db.OrderLogisticsInfo
    optional BuyerAddress return_delivery_address = 32; //seller address that returned item should be sent to
    optional int32 shipping_method = 33; // Non-Integrated:0, Integrated > 0
    optional int64 carrier_shipping_fee = 34;
    optional int64 origin_shipping_fee = 35;
    optional int32 actual_deliver_time = 36;
    optional int32 logistics_error_code = 37;
    optional string judge_remarks = 38;
    optional int32 seller_dispute_time = 39; //Not used.
    optional string shipping_carrier = 40;
    optional string logistics_remarks = 41;
    optional int32 processing_time = 42; //when return goes into RT7. Field is only added after Jan 2018.
    optional int32 return_ship_due_date = 43; //Date when buyer must ship. processing_time + B days (B from BE Settings)
    optional int32 return_seller_due_date = 44; //Date when seller must respond after buyer has shipped. Buyer clicks ship + C days (C from BE Settings). Set by P&L team.
    optional SecondLegShipment second_leg_shipment = 45;
    optional int64 buyer_txn_fee = 46;
    optional int64 coin_earn_by_voucher_refund = 47;
    optional bool shopee_handle = 48;
    optional int64 admin_fee = 49;
    optional string buyer_real_name = 50; // 7-11 requires the buyers' real name for logistics requests
    optional string buyer_phone_number = 51; // To store buyer's phone because it can be different phone in the address
    optional bool is_ship_from_overseas = 52; // Indicating if the order items are shipped from local or overseas
    optional string sls_tracking_number = 53; // SLS tracking number, which is different from third party tracking number
    optional bool with_resolution_center = 54; // Indicating if the return is following the new dispute resolution center flow
    optional int32 return_solution = 55; // ref: PBData.beeshop.db.ReturnSolution
    optional int64 max_refundable_amount = 56; // maximum amount which the buyer can get refunded
    optional int64 refund_amount_from_seller = 57; // amount of money to refund from seller's perspective
    optional bool refund_amount_adjustable = 58; // Set by Coreserver. To indicate whether the return refund amount is adjustable or not.
    optional int64 service_fee_amount = 59; // sum of service_fee for returned items.
    optional bool physical_return_allowed = 60; // Configurable Return Flow
    optional bool auto_approve_return_processing = 61; // Configurable Return Flow
    optional bool warehouse_validation = 62; // Configurable Return Flow
    optional int64 tax_amount = 63;
    optional int64 refund_destination = 64; // SPM channel ID used to pay the refund
    optional int32 db_version = 65; // Db version for optimization and validation
    optional int32 return_flag = 66; // ref: enum ReturnFlag
    optional string dispute_resolved_by = 67; // Email of the Shopee Staff who resolved the dispute
    optional bool auto_return_accepted_eligible = 68; // Flag for moving return directly to RT1
    optional bool eligible_for_accelerated_refund_after_return_pickup_done = 69; //Flag for Accelerated Refund After Buyer Ship
    optional int32 eligible_for_accelerated_refund_after_return_pickup_done_due_time = 70; //Time for Accelerated Refund After Buyer Ship
    optional CompensationEntity seller_compensation = 71; //Ref: CompensationEntity
    optional int32 seller_compensation_status = 72; //Ref:SellerCompensationStatus
    optional int32 negotiation_status = 73; //Ref: NegotitationStatus
}

message Return
{
    optional int64  return_id       = 1;
    optional int64  orderid         = 2;
    optional int32  shopid          = 3;
    optional int32  userid          = 4;
    optional int64  refund_amount   = 5; // actual refund amount of money
    optional string currency        = 6;
    optional int32  reason          = 7;
    optional int32  status          = 8; // ref: RETURN_REQUESTED
    optional int32  ctime           = 9;
    optional int32  mtime           = 10;
    optional bytes  return_info     = 11; // serialized format data of ref: PBData.beeshop.db.ReturnInfo
    optional string country         = 12;
    optional string return_sn       = 13;
    optional int32  archive         = 14;
    optional int32  cb_option       = 15;  // true: is_cross_border_user
    optional int32 logistics_status = 16;
}

message Contact
{
    optional int32  userid          = 1;
    optional string account         = 2;
    optional int32  acctype         = 3;
    optional int32  acc_userid      = 4;
    optional int32  acc_shopid      = 5;
    optional int32  status          = 6;
    optional string name            = 7;
    optional bytes  deviceid        = 8;
}

message ContactDetail
{
    optional int64  id              = 1;
    optional int32  userid          = 2;
    optional string account         = 3; //Can be phone, email or fbId
    optional int32  acctype         = 4; //ref: PBData.beeshop.db.ContactAccType
    optional int32  acc_userid      = 5; //The userid of this contact if user has bound this account (eg. phone) to shopee
    optional int32  acc_shopid      = 6;
    optional int32  status          = 7; //0: Deleted. 1: Normal 2: Pushed as Feed (Just treat as normal)
    optional string name            = 8; //Contact name. Max length = 128
    optional bytes  deviceid        = 9;
    optional bytes hashed_fingerprint = 10;
    optional int32 report_time        = 11; //createtime set by server, not by client
    optional bytes extinfo            = 12; //not defined yet. Reserved first
    optional string itemid            = 13; //Used by datapoint service. max length=128
    optional string data_hash_id      = 14; //Used by datapoint service. max length=128
    optional int32 platform_type      = 15; //none=0, android = 1, ios = 2,
    optional int32 update_time        = 16; //The time when acc_userid update
}

message RefundExtInfo
{
    optional BankAccountInfo    bank_account = 1;
    optional int32  batchid = 2;
    optional string checkout_sn = 3;
    optional int32 created_time = 4;    // record each status create time (created, verified ...)
    optional int32 verified_time = 5;
    optional int32 pending_time = 6;
    optional int32 paid_time = 7;
    optional int32 days_shopee = 8; // config[country].RefundDaysShopee
    optional int32 days_bank = 9; // config[country].RefundDaysBank
    optional int64 amount_before_discount = 10; // amount of money before voucher
    optional int32 discount_percentage = 11; // percentage of discount if the voucher is this type
    optional PromotionInfo promotion_info = 12;
    optional int32 refund_days_min= 13;
    optional int32 refund_days_max = 14;
    optional int32 bin_type = 15;   // https://www.pivotaltracker.com/projects/1248076/stories/*********
    optional bool  refund_to_seller = 16; // true: this refund is to seller
    optional bool  is_group_buy     = 17; // group buy order
    optional int64 spm_transaction_id = 18; // created to handle shopee wallet v2 refund flow
    optional int64 parcelid = 19;
    optional int64 shipping_fee = 20;
    optional int64 buyer_txn_fee = 21;
    optional int64 admin_fee = 22;
    optional int64 refund_destination = 23; // SPM channel ID used to pay the refund
    optional int32 last_audit_clock = 24; // Counter that is incremented every time Refund is saved or a RefundAudit is inserted by itself. Used to set RefundAudit audit_clock.
}

message Refund
{
    optional int64  refund_id = 1;
    optional int32  userid = 2;
    optional int32  status = 3; // ref: PBData.beeshop.db.Status_REFUND_
    optional int64  amount = 4;
    optional string currency = 5;
    optional string reason = 6;
    optional int32  ctime = 7;
    optional int32  mtime = 8;
    optional int64  orderid = 9;
    optional int32  shopid = 10;
    optional int64  return_id = 11;
    optional string country = 12;
    optional string refund_sn = 13;
    optional int64  checkoutid = 14;
    optional int32  payment_method = 15; // ref: PBData.beeshop.db.PaymentMethod
    optional bytes  extinfo = 16;  // ref: PBData.beeshop.db.RefundExtInfo
    optional int32  bankaccount_id = 17;
    optional int32  cb_option = 18;  // true: is_cross_border_user
}

message CheckoutOrder
{
    optional int32  shopid = 1; // Unique identifier of the seller's shop.
    optional int64  orderid = 2; // Unique identifier of the order.
    optional int32  item_count = 3; // Sum of all order items' amount/quantity in this order.
    repeated string images = 4; // List of all items' images in the order.
    optional int32  status = 5; // Status of the order, from order.status (not status_ext).
    optional int64  price = 6; // Actual price that the buyer paid for this order. Initially equal to total_price, but can be deducted by return and refund.
    optional string currency = 7; // Currency used for this order.
    optional int32  days_to_pay = 8; // Number of days to pay.
    optional string ordersn = 9; // Unique identifier of the order for external clients.
    repeated OrderKyc kycs = 10; // List of all kycs in the order
    optional int32 cb_option = 11; // 1: is_cross_border_user
    optional bool is_ship_from_overseas = 12; // Whether the order items are shipped from overseas.
}

message CheckoutPaymentInfo
{
    optional int32 bank_transfer_option = 1; // How the bank transfer payment was made (1: normal, 2: ATM payment, 3: Internet banking, 4: ATM mobile, 5: ATM internet). ref: PBData.beeshop.db.BankTransferOption
    optional string bank_transfer_bank  = 2; // The bank used for payment when the payment method is bank transfer.
    optional int32 channelid = 3; // Payment channel ID
    optional int64 payment_flag = 4; // Bit map containing information related to payment. This value is the sum of active flags (1: refund by API, 2: need bank account, 4: is credit card, 8: is free shipping, 16: is installment, 32: manual escrow, 64: is safe integrated, 128: support partial refund). ref: PBData.beeshop.db.CheckoutPaymentFlag
    optional string option_info = 5; // To identify which option user selected (get from SPM payment option).
    optional int64 transaction_id = 6; // SPM transaction_id.
    optional int64 spm_channel_id = 7; // SPM channel_id in 7 digit format.
    optional string channel_item_option_info = 8; // JSON string of channel_item_option_info.
    optional int32 voucher_payment_type = 9; // Used for voucher validation (1: credit card no installment, 2: credit card installment). Payment team is responsible for mapping spm_channel_id to the corresponding voucher_payment_type. ref: PBData.beeshop.db.VoucherPaymentType
}

message CheckoutInfo
{
    repeated CheckoutOrder order = 1; // List of orders contained in this checkout. Each checkout order contains several important/frequently accessed order fields.
    optional string payment_channel_name = 2; // Name of the payment channel used.
    optional int64 unique_code = 3; // Unique code for bank transfer.
    optional int32 payment_card = 4;
    optional int32 payby_date = 5; // Deadline for paying the checkout.
    optional int32 receipt_upload_time = 6; // Time when the bank transfer receipt is uploaded.
    optional int32 receipt_processby_date = 7; // Deadline for processing the bank transfer receipt.
    optional int32 receipt_reject_time = 8; // Time when the uploaded bank transfer receipt is rejected.
    optional int32 receipt_process_work_days = 9; // Number of days for processing bank transfer receipt.
    optional int32 checkout_order_type = 10; // (Deprecated) Type of this order (0: unknown, 1: simple (payment_method is offline payment), 2: escrow (payment_method is other payment method)). ref: PBData.beeshop.db.OrderType
    optional int64 price_before_discount = 11; // Total price before voucher discounts are applied. Includes shipping fee.
    optional int32 discount_percentage = 12; // If the promotion is discount then this is the discount percentage, otherwise this is unset.
    optional PromotionInfo promotion_info = 13; // Information related to promotion.
    optional int64 total_shipping_fee = 14; // Sum of all orders' shipping fees.
    optional CheckoutPaymentInfo checkout_payment_info = 15; // Information related to payment.
    optional CartPriceInfo cart_price_info = 16;
    optional string payer_veri_fail_reason = 17; // set when trigger NOTI_BUYER_PAYER_VERI_FAIL
    repeated int64 checkout_split_ids = 18; // When a checkout's payment method is set to cash on delivery, it will be split into one new checkout per order. The new split checkouts' checkout_split_ids field stores all of their checkout IDs.
    optional int32 card_promotion_id = 19; // Unique identifier of the card promotion.
    optional TxnFeeCheckoutInfo buyer_txn_fee_info = 20; // Checkout-level buyer transaction fee (v1). In v2, only its txn_fee value will be set.
    optional int64 group_buy_groupid = 21; // Unique identifier of the group buy this order belongs to.
    optional TxnFeeCheckoutInfo admin_fee = 22; // Admin fee information at checkout level.
    optional int32 db_version = 23; // Version of the record in DB. For coreserver use only.
    optional int32 fraud_check_skip_reason = 24; // Reason why fraud check was skipped (0: not skipped, 1: toggle off, 2: network error).
    repeated VoucherInfo vouchers = 25; // Voucher(s) applied for this order, including Shopee platform vouchers and seller vouchers.(include FSV)
    optional string purchase_reason = 26; // buyer purchase reason to be stored with checkout. used for govtech approval
    optional int64 checkout_log_checkout_id = 27; // original checkout log for cod/split checkouts. used to query checkout log
}

// Checkout price calculation
// voucher = price_before_discount - total_price
// total price without shipping = price_before_discount - total_shipping_fee

// Checkout is a purchase transaction made by a buyer. A checkout can contain one or more orders.
message Checkout
{
    optional int64  checkoutid = 1; // Unique identifier of the checkout for internal systems.
    optional int32  userid = 2; // Unique identifier of the buyer's account.
    optional int32  status = 3; // Current status of the checkout. ref: Status_PAYMENT_
    optional int64  total_price = 4; // Sum of all orders' total price, including shipping fee.
    optional int64  paid_amount = 5; // Actual amount that has been paid by the buyer.
    optional string currency = 6; // The currency used by this checkout.
    optional int32  ctime = 7; // Checkout creation time.
    optional int32  mtime = 8; // Checkout last modified time.
    optional bytes  checkout_info = 9; // Extra information for this checkout in serialized protobuf format. ref: PBData.beeshop.db.CheckoutInfo
    optional string country = 10; // Country of the checkout.
    optional int32  payment_status = 11; // The current status of the checkout's payment (0: none, 1: success, 100: bank transfer init, 101: bank transfer verifying, 102: bank transfer reinit, 103: bank transfer pending, 200: offline payment).
    optional string checkout_sn = 12; // Unique identifier of the checkout for external clients. See https://confluence.shopee.io/x/ybV6BQ
    optional int64  actual_price = 13; // Sum of the total price of all unpaid orders.
    optional int32  payment_type = 14; // Type of payment channel selected by the buyer (0: none, 1: cybersource (credit/debit card), 2: bank transfer, 3: offline payment, 4: ipay88, 5: free, 6: cash on delivery, 7: esun, 8: bill payment, 13-42: other 3rd party payment channels). Used to group similar payment channels together. Set by P&L. ref: PBData.beeshop.db.PaymentMethod
    optional int32  logistics_status = 15; // Logistics status of the checkout. ref: PBData.beeshop.db.Status_LOGISTICS_
    optional int32  list_type = 16;
}

message DeviceExtinfo
{
    optional string gcm_subscription = 1; // used when machine_code = "gcm_web"
    // https://developers.google.com/web/updates/2016/03/web-push-encryption?hl=en
}

message Device
{
    optional bytes deviceid = 1;
    optional bytes pn_token = 2;
    optional int32  ctime = 3;          // server fill, client ignore
    optional int32  mtime = 4;          // server fill, client ignore
    optional int32  device_hash = 5;    // index field, client ignore
    optional int32  appversion = 6;
    optional string country = 7;
    optional string machine_code = 8;
    optional bytes  extinfo = 9;        // for future extension  ref: PBData.beeshop.db.DeviceExtinfo
    optional int32  userid = 10;
    optional int64  id = 11;
}

message DeviceV2
{
    optional bytes deviceid = 1;
    optional bytes pn_token = 2;
    optional int32  ctime = 3;          // server fill, client ignore
    optional int32  mtime = 4;          // server fill, client ignore
    optional int32  device_hash = 5;    // index field, client ignore
    optional int32  appversion = 6;
    optional string country = 7;
    optional string machine_code = 8;
    optional bytes  extinfo = 9;        // for future extension  ref: PBData.beeshop.db.DeviceExtinfo
    optional int32  userid = 10;
    optional int64  id = 11;
    optional uint32 appid = 12;
    optional uint32 os = 13;
    optional uint32 platform = 14;
}

// Appid identification is based on best-effort*
enum Appid
{
    APPID_SHOPEE             = 1;
    APPID_SHOPEEPAY_MERCHANT = 2;

    APPID_SHOPEE_LITE = 3;
    APPID_SHOPEE_WEB  = 4;
    APPID_SHOPEE_SC   = 5;
    APPID_SHOPEE_APP  = 6;
}


enum OS
{
    OS_UNKNOWN = 0;
    OS_IOS     = 1;
    OS_ANDROID = 2;
    OS_WINDOWS = 3;
    OS_MACOS   = 4;
    OS_LINUX   = 5;
    OS_HMS     = 6;
}

message DeviceUserIndex
{
    optional int64 id = 1;
    optional int32 userid = 2;
    optional bytes deviceid = 3;
    optional int32 create_time = 4;
    optional int32 update_time = 5;
}

message MarketPushnoti
{
    optional int32 task_id          = 1;
    optional string operator_email  = 2;
    optional string description     = 3;
    optional string banner          = 4;
    optional string url             = 5;
    optional string title           = 6;
    optional string content         = 7;
    optional string placeholder_string  = 8;
    optional string country         = 9;
    optional string status          = 10;
    optional string error_msg       = 11;
    optional int32 ctime            = 12;
    optional int32 mtime            = 13;
    //optional string stats_info      = 14;
    optional string name            = 15;
    optional int32 send_option      = 16;  // ref: PBData.beeshop.db.MarketPushSendOption (0 to send all)
    optional bytes  extinfo         = 17;   // serilized ref: PBData.beeshop.cmd.TriggerCustomizedNoti
    optional string action_content   = 18;
    optional int32 schedule_time    = 19;
}

message MarketPushnotiTasklist
{
    optional int32 id               = 1;
    optional int32 task_id          = 2;
    optional int32 user_id          = 3;
    optional string value_string    = 4;
    optional string push_content    = 5;
    optional string status          = 6;
    optional string error_msg       = 7;
    optional int32 ctime            = 8;
    optional int32 mtime            = 9;
    optional string url             = 10;
    optional string banner          = 11;
    optional string title           = 12;
    optional int32 task_type        = 13;  // ref: PBData.beeshop.db.MarketPushTaskType
    optional int32 send_option      = 14;  // ref: PBData.beeshop.db.MarketPushSendOption (0 to send all)
    optional bytes  extinfo         = 15;   // serilized ref: PBData.beeshop.cmd.TriggerCustomizedNoti
    optional string pure_action_content  = 16;
}

message ItemCategory
{
    optional int32 categoryid = 1;
    optional int64 itemid = 2;
    optional int32 shopid = 3;
    optional int32 mtime = 4;
    optional int32 score = 5;
    optional int32 is_invisible = 6;
}

message LoginRecord
{
    optional int32 userid = 1;
    optional int32 timestamp = 2;
}

message LoginRecords
{
    repeated LoginRecord records = 1;
}

message CheckoutRecord
{
    optional int64 checkoutid = 1;
    optional int32 timestamp = 2;
    optional int64 amount = 3;
    optional int32 userid = 4;
}

message CheckoutRecords
{
    repeated CheckoutRecord records = 1;
}

message DeviceRecord
{
    optional int64 id = 1;
    optional bytes deviceid = 2;
    optional string country = 3;
    optional int32 mtime = 4;
    optional int32 status = 5;
    optional bytes login_record = 6; // ref: PBData.beeshop.db.LoginRecords
    optional bytes checkout_record = 7; // ref: PBData.beeshop.db.CheckoutRecords
    optional uint32 appid = 8;
}

message DeviceBlacklist
{
    optional int64 id = 1;
    optional bytes deviceid = 2;
    optional string country = 3;
    optional int32 mtime = 4;
    optional int32 status = 5;
    optional uint32 appid = 6;
}

message DeviceCheckRecord
{
    optional int64 id = 1;
    optional bytes deviceid = 2;
    optional int32 userid = 3;
    optional string country = 4;
    optional int32 type = 5;
    optional int32 mtime = 6;
}

message BehaviorCheckRecord
{
    optional int32 id = 1;
    optional int32 buyerid = 2;
    optional int32 sellerid = 3;
    optional string country = 4;
    optional int32 ctime = 5;
    optional int32 type = 6;
    optional string voucher_code = 7;
    optional bytes extinfo = 8; // ref: PBData.beeshop.cmd.BehaviorCheck  Currently storing the BehaviorCheck request directly in extinfo
    optional bytes  buyer_deviceid = 9;
    optional bytes  seller_deviceid = 10;
    optional int32 status = 11;
    optional int32 method = 12; // Fraud method
}

message GroupBuyFraudRecord
{
    optional int64 id = 1; // auto increment
    optional int64 groupid = 2;
    optional int32 userid = 3; // the user who's caught as fraud, not the group owner
    optional int32 ctime = 4;
    optional int32 mtime = 5;
    optional string country = 6;
    optional bytes extinfo = 7; // ref: PBData.beeshop.db.GroupBuyFraudRecordExtInfo
}


message GroupBuyFraudRecordExtInfo
{
    repeated GroupBuyFraudLinkedUser linked_users = 1;
}

message GroupBuyFraudLinkedUser
{
    optional int32 userid = 1;
    repeated int32 reasons = 2; // ref: PBData.beeshop.cmd.BehaviorFraudReason
}

message ShopBoostSlot
{
    optional int64 itemid = 1;
    optional int32 touch_time = 2;
}

message ShopBoostInfo
{
    repeated ShopBoostSlot slots = 1;
}

message ShopBoost
{
    optional int32 shopid = 1;
    optional int32 userid = 2;
    optional int32 slot_limit = 3;
    optional int32 cooldown_second = 4;
    optional bytes info = 5; // ref: PBData.beeshop.db.ShopBoostInfo
}

enum ChatBlockOpt
{
    CHAT_BLOCK_OPT_NONE  = 0;
    CHAT_BLOCK_OPT_ADS   = 1;
}

message ChatBlackList
{
    optional int32 id = 1;
    optional int32 userid = 2;
    optional int32 bl_userid = 3;
    optional int32 status = 4;
    optional int32 ctime = 5;
    optional int32 mtime = 6;
    optional int64 block_opt = 7; // for ads msgs, bit mask, 0: unblock, 1: block ads, ref: ChatBlockOpt
}

message SellerDiscount
{
    optional int64 promotionid = 1;
    optional int32 userid = 2;
    optional int32 shopid = 3;
    optional string country = 4;
    optional int32 status = 5;
    optional string title = 6;
    optional int32 start_time = 7;
    optional int32 end_time = 8;
    optional int32 ctime = 9;
    optional int32 mtime = 10;
    optional int32 source = 11;  // ref: PBData.beeshop.db.SellerDiscountSource
    optional bytes extinfo = 12;
}

message SellerPromotionItem
{
    optional int64 id = 1;
    optional int64 promotionid = 2;
    optional int32 shopid = 3;
    optional int64 itemid = 4;
    optional int64 modelid = 5;
    optional int64 price_before_discount = 6;  // Not used anymore
    optional int64 promotion_price = 7;  // The promotion price that the buyer sees. This is price_before_discount - rebate_price - (seller-absorbed price)
    optional int64 rebate_price = 8;  // The portion of the difference (price_before_discount - promotion_price) that Shopee will rebate.
    optional int32 status = 9;  // see the Status.SELLER_PROMOTION_ITEM_*
    optional int32 user_item_limit = 10;  // how many item(including the item model) can purchase with the promotion.
    optional int32 user_model_limit = 11;  // Deprecated!!! check SPSS-822 for more detail. for itemmodel only, if set this value, and user_item_limit set 0, will use this value as limitation.
    optional int32 total_item_limit = 12;  // no useful, keep it 0
    optional int32 total_model_limit = 13;  // no useful, keep it 0
    optional int32 start_time = 14;
    optional int32 end_time = 15;
    optional int32 mtime = 16;
    optional int32 promo_source = 17;  //see defind SellerPromotionSource
    optional int32 stock = 18;
    optional int32 flash_sale_type = 19; // ref: PBData.beeshop.db.FlashSaleType
    optional int32 ctime = 20;
}

message SellerPromotionItemArchive
{
    optional int64 id = 1;
    optional int64 promotionid = 2;
    optional int32 shopid = 3;
    optional int64 itemid = 4;
    optional int64 modelid = 5;
    optional int64 price_before_discount = 6;  // Not used anymore
    optional int64 promotion_price = 7;  // The promotion price that the buyer sees. This is price_before_discount - rebate_price - (seller-absorbed price)
    optional int64 rebate_price = 8;  // The portion of the difference (price_before_discount - promotion_price) that Shopee will rebate.
    optional int32 status = 9;  // see the Status.SELLER_PROMOTION_ITEM_*
    optional int32 user_item_limit = 10;  // how many item(including the item model) can purchase with the promotion.
    optional int32 user_model_limit = 11;  // Deprecated!!! check SPSS-822 for more detail. for itemmodel only, if set this value, and user_item_limit set 0, will use this value as limitation.
    optional int32 total_item_limit = 12;  // no useful, keep it 0
    optional int32 total_model_limit = 13;  // no useful, keep it 0
    optional int32 start_time = 14;
    optional int32 end_time = 15;
    optional int32 mtime = 16;
    optional int32 promo_source = 17;  //see defind SellerPromotionSource
    optional int32 stock = 18;
    optional int32 flash_sale_type = 19; // ref: PBData.beeshop.db.FlashSaleType
    optional int32 ctime = 20;
}

message SellerPromotionRefresh
{
    optional int64 id = 1;
    optional int32 shopid = 2;
    optional int64 itemid = 3;
    optional int64 modelid = 4;
    optional int32 refresh_time = 5;
}

message SellerPromotionUsage
{
    optional int64 id = 1;
    optional int64 promotionid = 2;
    optional int32 shopid = 3;
    optional int64 itemid = 4;
    optional int64 modelid = 5;
    optional int32 userid = 6;
    optional int32 amount = 7;
}

message ChatItemLink
{
    optional int32  id = 1;
    optional int64  pchatid = 2;
    optional int32  shopid = 3;
    optional int64  itemid = 4;
    optional int32  userid = 5;
    optional int32  status = 6;
    optional int32  ctime = 7;
}

message ShopCreditScore
{
    optional int32 shopid = 1;
    optional double rate_score = 2;  // shop rating score
    optional double refund_score = 3; // orders returned or refunded
    optional double chat_score = 4; // chat response rate
    optional double banned_score = 5; // new listings banned
    optional double listing_score = 6; // new likes / new listings
    optional double order_score = 7; // number of orders
    optional double cumulative_order_score = 8; // cumulative number of orders
    optional double boosting = 9; // manual boosting, same as boosting in ShopExtInfo.ShopAdminInfo
    optional double chat_response_score = 10; // chat response speed
    optional double total = 11; // total score calculated
    optional double shipping = 12; // order shipping speed
    optional int32  banned = 13; // this user is banned
    optional string country = 14; // country of the user
    optional double cancel_rate = 15; // seller cancel rate
}

message SellerOrderNote
{
   optional string note = 1;
   optional int32 mtime = 2;
}

message LastLogin
{
    optional int32  id = 1;
    optional int32  userid = 2;
    optional string login_from = 3;
    optional int32  last_login = 4;
    optional string ip = 5;
}

message ShopCollectionData
{
    repeated int64  itemid = 1; // including all itemids at this level, the itemids at sublevel will store in sub_collections. If this level is not the end level, this field is nil.
    optional int32  nouse = 2;  // just make sure the whole data won't be empty
    repeated SubShopCollection sub_collections = 3; // including all sub collections, the order of sub collections is the same as the element order in array. If this level is end level, this field is nil
}
message SubShopCollection
{
    optional string name               = 1;
    optional int32  catid              = 2;
    optional ShopCollectionData  data  = 3;
}

message ShopCategoryIds
{
    repeated int64  shop_collectionid = 1;
}

message ShopCollection
{
    optional int64  shop_collection_id = 1;
    optional int32  shopid             = 2;
    optional string name               = 3;
    optional int32  collection_type    = 4;
    optional int32  catid              = 5;
    optional string hashtag            = 6;
    optional int32  sort_weight        = 7;
    optional int32  status             = 8;
    optional int32  ctime              = 9;
    optional int32  mtime              = 10;
    optional bytes  data               = 11; // ref: PBData.beeshop.db.ShopCollectionData
    optional bytes  rule               = 12; // shop collection filtering rule setting
}

// Note: V2 only used as db proto, this structure will not be used in the request
// Copy from API Namespace: shop_category
message ShopCollectionV2
{
    optional int64  id                 = 1;
    optional int64  shop_collection_id = 2;
    optional int32  shopid             = 3;
    optional string collection_name    = 4;
    optional int32  collection_type    = 5;
    optional int32  catid              = 6;
    optional string hashtag            = 7;
    optional int32  sort_weight        = 8;
    optional int32  collection_status  = 9;
    optional int32  ctime              = 10;
    optional int32  mtime              = 11;
    optional bytes  collection_data    = 12; // ref: ShopCollectionData
    optional bytes  collection_rule    = 13; // shop collection filtering rule setting
}


message FraudConfig
{
   optional int32 device_max_login_count = 1;
   optional int32 device_max_checkout_count = 2;
   optional int64 device_max_checkout_amount = 3;
}

message Wallet
{
    optional int32 userid = 1;
    optional int32 shopid = 2;
    optional string country = 3;
    optional int64 available = 4;
    optional int64 outgoing = 5;
    optional int32 mtime = 6;
    optional int32 status = 7;
}

/* Used in standalone wallet server (i.e. ID wallet)*/
message BuyerWallet
{
    optional int32 wallet_id = 1;
    optional int32 userid = 2;
    optional int64 available = 4;
    optional int64 outgoing = 5;
    optional int32 mtime = 6;
    optional int32 cashback = 7;
    optional int32 deposit = 8;
    optional int32 refund = 9;
}

/* Used in standalone wallet server (i.e. ID wallet)*/
message SellerWallet
{
    optional int32 wallet_id = 1;
    optional int32 userid = 2;
    optional int64 available = 4;
    optional int64 outgoing = 5;
    optional int32 mtime = 6;
}

/**
 * `PocketBreakdown` is the structure that holds the amount breakdown of a transaction.
 * NOTE: this is used in standalone wallet server.
 */
message PocketBreakdown
{
    optional int64 old_cashback = 1;
    optional int64 new_cashback = 2;
    optional int64 old_deposit = 3;
    optional int64 new_deposit = 4;
    optional int64 old_refund = 5;
    optional int64 new_refund = 6;
}

message WithdrawalExtInfo
{
    repeated int64 sub_id = 1;
    optional int32 batchid = 2;
    optional int32 source = 3;
    optional int32 due_time = 4;
    optional int32 verify_time = 5;
    optional int32 payout_time = 6;
    optional int32 complete_time = 7;
    optional int32 cancel_time = 8;
    optional string cancel_reason = 9;
    optional string payment_ref1 = 10;
    optional int64 transaction_id = 11; //Create withdrawal transaction id.
    optional int32 wallet_type = 12; //Wallet type, buyer or seller wallet
    optional int32 target_type = 13; //target type, Bank Account or ShopeePay
}

message Withdrawal
{
    optional int64 withdrawal_id = 1;
    optional int32 userid = 2;
    optional int64 root_id = 3;
    optional int64 total_amount = 4;
    optional int64 trans_fee = 5;
    optional int32 bankaccount_id = 6;
    optional int64 type = 7;
    optional int32 status = 8;
    optional int32 ctime = 9;
    optional int32 mtime = 10;
    optional string country = 11;
    optional bytes extinfo = 12; // ref: PBData.beeshop.db.WithdrawalExtInfo
}

message WithdrawalAudit
{
    optional int64  auditid = 1;
    optional int32  userid = 2;
    optional int64  withdrawal_id = 3;
    optional int32  opuserid = 4;
    optional int32  old_status = 5;
    optional int32  new_status = 6;
    optional int32  ctime = 7;
    optional bytes  data = 8;
    optional string country = 9;
}

message WalletTransactionExtinfo
{
    optional string reason = 1;
    optional string comment = 2;
}

message WalletTransaction
{
    optional int64 transaction_id = 1;
    optional int64 trace_id = 2;
    optional int32 userid = 3;
    optional int64 target_id = 4;
    optional int64 old_available = 5;
    optional int64 new_available = 6;
    optional int64 old_outgoing = 7;
    optional int64 new_outgoing = 8;
    optional int32 type = 9;    // ref: PBData.beeshop.db.TransactionType
    optional int32 ctime = 10;
    optional bytes extinfo = 12; // ref: PBData.beeshop.db.WalletTransactionExtinfo
}

message WalletTopupExtinfo
{
    optional int64 admin_fee_id         = 1;
    optional int64 admin_fee            = 2;
    optional string remark              = 3;
    optional int64 payment_channel_id   = 4;
    optional int64 payment_transaction_id   = 5;
    optional string payment_option_info = 6; // Deprecated, use payment_channel_item_option_info
    optional string payment_channel_item_option_info = 7;
}

// PRD: https://docs.google.com/document/d/1Bgy2dFwqMcFK4TGb79bZ6esF8BqXu0F-krbLFILFDVY/edit#heading=h.wipx1t2mc4r6
message WalletTopup
{
    optional int64 topup_id         = 1;
    optional int32 userid           = 2;
    optional int32 status           = 3; //ref: PBData.beeshop.db.Status_TOPUP
    optional int32 ctime            = 4;
    optional int32 mtime            = 5;
    optional int64 init_amount      = 6;
    optional int64 paid_amount      = 7; // realAmount(amount add into mywallet) : PaidAmount - extinfo.AdminFee
    optional string country         = 8;
    optional bytes extinfo          = 9; // ref: PBData.beeshop.db.WalletTopupExtinfo
}

// used for serialize, to calculate soldcount for search only
message SearchItemSoldScore
{
    optional int64 score = 1;
    optional int64 itemid = 2;
    optional double soldscore_shortterm = 3;
    optional double soldscore_longterm = 4;
    optional int32 shopid = 5;
}

//used to calculate soldcount for search only
message SearchItemBuyerSoldCount
{
   optional int64 soldcount_shortterm =1;
   optional int64 soldcount_longterm =2;
}

message BILogData
{
    optional int32  cmd = 1;
    optional string cmdname = 2;
    optional int32  userid = 3;
    optional int32  shopid = 4;
    optional string country = 5;
    optional string ip = 6;
    optional int32  event_time = 7;
    optional Account acc = 8;
    optional AccountExtInfo accext = 9;
    optional Order  order = 10;
    optional OrderExtInfo orderext = 11;
    repeated OrderItem order_items = 12;
    optional Return return = 13;
    optional ReturnInfo returnext = 14;
    optional bool   is_web = 15;
    repeated Snapshot snapshot = 16;
    optional Item   item = 17;
    optional ItemExtInfo itemext =18;
    repeated ItemModel model = 19;
    repeated ItemModelExtInfo modelext = 20;
    optional bool   is_new_account = 21;
    optional Refund refund = 22;
    optional RefundExtInfo refundext = 23;
    optional int64  event_time_i64 = 24;
    optional Checkout checkout = 25;
    optional CheckoutInfo checkoutext = 26;
    optional bool is_shadow = 27;
}

message UserLoginRecord
{
    optional int64  id = 1;
    optional int32  userid = 2;
    optional string country = 3;
    optional string ip = 4;
    optional string login_from = 5;
    optional bytes  deviceid = 6;
    optional int32  last_login = 7;
    optional bool is_emulator = 8;
    optional string source = 9;
}

message UserLoginRecordV2
{
    optional int64  id = 1;
    optional int32  userid = 2;
    optional string country = 3;
    optional string ip = 4;
    optional string login_from = 5;
    optional bytes  deviceid = 6;
    optional int32  last_login = 7;
    optional bool is_emulator = 8;
    optional string source = 9;
}

message Coin
{
    optional int32 userid = 1;
    optional int64 available_amount = 2;
    optional int32 mtime = 3;
    optional bytes expiry_info = 4; //it's the marshal data of ref: PBData.beeshop.db.CoinExpiryInfo
    optional string country = 5;
}

message CoinExpiryInfo
{
    repeated CoinMonthSummary summary = 1;
}

message CoinMonthSummary
{
    optional int32 month = 1; //calendar month when coin is CREATED, not expired
    optional int32 year = 2; //yyyy format
    optional int64 coin_amount = 3;
}

message CoinTransactionInfo
{
   optional string reason = 1;
   optional string title = 2; //Customized title
   optional string icon = 3; //Customized icon
   optional int64 rejected_amount = 4; // the amount of coins that's rejected because of fraud
   optional int64 parcelid = 5; // for coin refund of cancelled parcel related to multi-parcel feature
}

message CoinTransaction
{
    optional int64 id = 1;
    optional int32 userid = 2;
    optional int64 orderid = 3;
    optional int64 coin_amount = 4; //the amount changed (can be positive and negative value), and the value = new_amount-old_amount
    optional int64 old_amount = 5;  //before the changed, the coin amount
    optional int64 new_amount = 6;  //after the changed, the coin amount
    optional int32 type = 7;  //see the defind CoinTransactionType
    optional int32 ctime = 8;
    optional bytes info = 9; // it's the marshal data of ref: PBData.beeshop.db.CoinTransactionInfo
}

message CoinRuleInfo
{
    repeated int32 collection_ids = 1;
    repeated int32 cat_ids = 2;
    optional bool display_label = 3; // if false then do not display coins cashback label for this rule
}

message CoinEarnRule
{
    optional int32 rule_id = 1;
    optional string country = 2;
    optional int32 begin_time = 3;
    optional int32 end_time = 4;
    optional int64 cash_unit = 5;
    optional int64 coin_unit = 6;
    optional int64 rounding_unit = 7;
    optional bytes rule_info = 8; // ref: PBData.beeshop.db.CoinRuleInfo
    optional int32 mtime = 9;
    optional int32 status = 10;
    optional int32 ctime = 11;
    optional string name = 12;
}

message CoinGlobalExtInfo
{
    repeated int32 rule_rank_ids = 1;
}

message CoinGlobalRule
{
    optional int32 rule_id = 1;
    optional string country = 2;
    optional int64 earn_cash_unit = 3;
    optional int64 earn_coin_unit = 4;
    optional int64 earn_rounding_unit = 5;
    optional int64 earn_max_daily = 6;
    optional int64 earn_max_weekly = 7;
    optional int64 earn_max_order_fix = 8;
    optional int64 spend_cash_unit = 9;
    optional int64 spend_max_daily = 10;
    optional int64 spend_max_weekly = 11;
    optional int32 spend_max_order_percent = 12;
    optional bytes extinfo = 13; // ref: PBData.beeshop.db.CoinGlobalRule
    optional int32 ctime = 14;
    optional int32 mtime = 15;
    optional int32 status = 16;
    optional int32 disable_check_shop = 17; //ref:CoinCheckShopFlag
    optional int32 expiry_months = 18;
}

message CoinInfo  // for order
{
    optional int64 coin_used = 1; //Coins spent for this order. This value is not changed when order is cancelled or returned.
    optional int64 coin_offset = 2; //Cash Value of coin used. This value is not changed when order is cancelled or returned.
    optional int32 coin_spend_rule_id = 3;
    optional int64 coin_earn = 4; //Actualy coins earned after including returns, both by coin cashback voucher and by coin rule.
    optional int64 coin_earn_before_return = 5; //Coins earned for this order, both by coin cashback voucher and by coin rule. Ignores return.
    optional int64 coin_earn_by_voucher = 6; //Actual coins earned by coin cashback voucher after including returns
    optional int64 coin_earn_cash_by_voucher = 7; // convert coin_earn_by_voucher to equivalent cash amount
}

message DeviceExtExtinfo
{
    optional string os_version = 1;
    optional bool   is_web = 2;
}

message DeviceExt
{
    optional int64  id = 1;
    optional int32  userid = 2;
    optional bytes  deviceid = 3;
    optional bytes  device_fingerprint = 4;
    optional string user_agent = 5;
    optional int32  ctime = 6;
    optional int32  mtime = 7;
    optional bytes  hashed_fingerprint = 8;
    optional bool   is_rooted = 9;
    optional bool   is_fingerprint_tempered = 10;
    optional bytes  fingerprint_before_temper = 11;
    optional bytes  hashed_df_before_temper = 12;
    optional bytes  extinfo = 13; // ref: DeviceExtExtinfo
    optional uint32 appid = 14;
}

message UserLoginDeviceFingerprint
{
    optional int64 id                 = 1;
    optional int32 userid             = 2;
    optional int32 df_hash            = 3;
    optional int32 mtime              = 4;
    optional bytes extinfo            = 5;
    optional bytes hashed_fingerprint = 6;
    optional uint32 appid             = 7;
}

message OrderIndex
{
    optional int64  orderid = 1;
    optional int32  shopid = 2;
    optional int32  userid = 3;
    optional string ordersn = 4;
}

enum ItemAttributeType
{
    INT_TYPE                        = 1;
    STRING_TYPE                     = 2;
    ENUM_TYPE                       = 3;
    FLOAT_TYPE                      = 4;
    DATE_TYPE                       = 5;
    TIMESTAMP_TYPE                  = 6;
}

enum ReturnSolution
{
    RETURN_AND_REFUND  = 0;
    REFUND_ONLY        = 1;
}

enum ItemAttributeInputType
{
    DROP_DOWN                       = 1;
    TEXT_FILED                      = 2;
    COMBO_BOX                       = 3;
}

message AttributeMultiLang
{
    required string lang = 1;
    optional string display_name = 2;
    optional string placeholder = 3;
    optional string tooltip = 4;
    repeated string value = 5; // must correspond to ItemAttributeData.value
}

message ItemAttributeData
{
    repeated string value           = 1;
    optional string description     = 2;
    optional int32 brand_option     = 3;
    optional string tooltip         = 4; // descripton for user, EK
    optional string placeholder     = 5; // example in input, EK
    optional int32 is_filter        = 6; // flag, 1-can be used as filter field, 0-not, EK
    repeated AttributeMultiLang multi_lang = 7;
    optional int32 mandatory_mall = 8;
}

message ItemAttribute
{
    optional int32 attr_id          = 1;
    optional string name            = 2;
    optional int32 input_type       = 3; // ItemAttributeInputType
    optional int32 attr_type        = 4; // ItemAttributeType
    optional int32 status           = 5;
    optional int32 validate_type    = 6; // ItemAttributeType
    optional bytes extinfo          = 7; // ref: PBData.beeshop.db.ItemAttributeData
    optional int32 mandatory        = 8;
    optional string country         = 9;
    optional int32 ctime            = 10;
    optional int32 mtime            = 11;
    optional string display_name    = 12;
}

message AttributeModelData
{
    repeated int32 id_attributes    = 1;
    repeated int32 value_attributes = 2;
}

message AttributeModel  // item_attr_model_tab
{
    optional int32 model_id         = 1;
    optional string name            = 2;
    optional int32 catid            = 3;    //category id related to this model
    optional bytes extinfo          = 4;  // ref: PBData.beeshop.db.AttributeModelData
    optional int32 is_standard      = 5;
    optional string country         = 6;
    optional int32 ctime            = 7;
    optional int32 mtime            = 8;
    optional int32 status           = 9;
}

message AttributeInstance
{
    optional int32 attr_id          = 1;
    repeated string values          = 2;    //possible values to this attribute
}

message AttributeModelInstanceData
{
    repeated AttributeInstance id_attrs     = 1;
    repeated AttributeInstance value_attrs  = 2;
}

message AttributeModelInstance  // item_attr_modelinstance_tab
{
    optional int32 instance_id      = 1;
    optional int32 model_id         = 2;
    optional bytes extinfo          = 3;  // ref: PBData.beeshop.db.AttributeModelInstanceData
    optional string country         = 4;
    optional string name            = 5;
    optional int32 ctime            = 6;
    optional int32 mtime            = 7;
    optional int32 status           = 8;
}

message AttributeValue
{
    optional int32 attr_id          = 1;
    optional string value           = 2;
    optional int32 status           = 3;
}

message GlobalAttributeValue
{
    optional uint32 attr_id         = 1;
    optional uint32 attr_val_id     = 2;
    optional string formatted_value = 3;
    optional int32 status           = 4;
}

message AttributeSnapshot    // in ItemExtinfo
{
    repeated AttributeValue values  = 1;
    optional int32 modelid          = 2;
}

message GlobalAttributeSnapshot    // in ItemExtinfo
{
    repeated AttributeValue values               = 1; // depreacted, use global_values instead
    repeated GlobalAttributeValue global_values  = 2;
}

enum AttributeAuditType
{
    AUDIT_ATTRIBUTE                 = 1;
    AUDIT_MODEL                     = 2;
    AUDIT_INSTANCE                  = 3;
}

message AttributeAudit
{
    optional int64 id               = 1;
    optional int32 object_id        = 2;
    optional int32 timestamp        = 3;
    optional string operator        = 4;
    optional string data            = 5;
    optional int32 audit_type       = 6;
    optional int32 status           = 7;
    optional int32 object_type      = 8;  // AttributeAuditType
    optional string country         = 9;
}

message CheckoutLog
{
    optional int64 checkoutid = 1;
    optional int32 userid = 2;
    optional bytes hashed_device = 3;
    optional int32 ctime = 4;
    optional int32 status = 5;
    optional int32 flag = 6;  //1 free_shipping
    optional int64 promotionid = 7;
}

message CheckoutLogV2
{
    optional int64 checkoutid = 1;
    optional int64 userid = 2;
    optional bytes hashed_device = 3;
    optional int32 ctime = 4;
    optional int32 status = 5;
    optional int32 flag = 6;  //1 free_shipping
    optional int64 promotionid = 7;
}

message FulfillmentRateExtinfo // fulfillment_rate_tab.extinfo
{
    optional int32 out_of_stock       = 1;
    optional int32 auto_cancellation  = 2;
    optional int32 return             = 3;
    optional int32 paid_order         = 4;
    optional int32 cod_verified       = 5;
    optional int32 cannot_support_cod = 6;
    optional int32 undeliverable_area = 7;
    optional int32 denominator        = 8;
    optional int32 flag               = 9;
    optional int32 cancellation_rate  = 10;
    optional int32 return_refund_rate = 11;
    optional int32 period             = 12;
}

message ShopMetricExtinfo // shop_metric_tab.extinfo, merge fulfillment_rate extinfo
{
    optional int32 out_of_stock       = 1;  // nfr
    optional int32 auto_cancellation  = 2;  // nfr
    optional int32 return             = 3;  // nfr
    optional int32 paid_order         = 4;  // nfr
    optional int32 cod_verified       = 5;  // nfr
    optional int32 cannot_support_cod = 6;  // nfr
    optional int32 undeliverable_area = 7;  // nfr
    optional int32 denominator        = 8;
    optional int32 flag               = 9;  // nfr
    optional int32 cancellation_rate  = 10; // nfr
    optional int32 return_refund_rate = 11; // nfr
    optional int32 period             = 12; // nfr
    optional int32 numerator          = 13;
    optional bytes text               = 14; //plain text
    repeated int64 orderid            = 15; //store orderids for debug purpose. // nfr lsr
    optional int32 extra_flag         = 16; // ref: PBData.beeshop.db.ShopMetricExtraFlag
    optional int32 buyer_cancellation = 17; // nfr
}

message ItemWithScore
{
    optional int64 itemid = 1;
    optional int32 shopid = 2;
    optional int64 score = 3;
    optional double fscore = 4;
    optional int64 adsid = 5;
    optional int64 timestamp = 6;
    repeated int64 scores = 7;
}

enum FraudType
{
    FRAUD_TYPE_OTHER                     = 1;
    FRAUD_TYPE_LOGISTICS                 = 2;
    FRAUD_TYPE_PAYMENT                   = 3;
    FRAUD_TYPE_ITEM                      = 4;
}

enum FraudLogisticsReason
{
    OTHER_REASON                        = 1;
    FREE_SHIPPING_DAILY_QUOTA_EXCEED    = 2;
    FREE_SHIPPING_WEEKLY_QUOTA_EXCEED   = 3;
    FREE_SHIPPING_MONTHLY_QUOTA_EXCEED  = 4;
    DUPLICATED_CONSIGNMENT_NO           = 5;
}

enum FraudLevel
{
    FRAUD_LEVEL_OTHER       = 1;
    FRAUD_LEVEL_USER        = 2;
    FRAUD_LEVEL_DEVICE      = 3;
    FRAUD_LEVEL_WEB         = 4;
}

message FraudInfo
{
    optional int32 fraud_type       = 1; // Type of fraud (1: other, 2: logistics, 3: payment, 4: item). ref: PBData.beeshop.db.FraudType
    optional int32 fraud_level      = 2; // Level of fraud (1: other, 2: user, 3: device, 4: web). ref: PBData.beeshop.db.FraudLevel
    optional int32 fraud_reason     = 3; // Reason of fraud, depends on fraud_type (e.g. if fraud_type is logistis, then values can be 1: other, 2: daily free shipping quota exceeded, 3: weekly free shipping quote exceeded, 4: monthly free shipping quota exceeded, 5: duplicate consignment number).
    optional string fraud_message   = 4;
}

message CardPromotionUsage
{
    optional int64 checkoutid       = 1;
    optional int32 card_promotion_id    = 2;
    optional int32 userid       = 3;
    optional int32 ctime        = 4;
    optional string card_id     = 5;
}

message CardPromotion
{
    optional int32 card_promotion_id    = 1;
    optional int32 start_time       = 2;
    optional int32 end_time         = 3;
    optional int32 ctime        = 4;
    optional int32 mtime        = 5;
    optional int32 status       = 6;
    optional string country         = 7;
    optional string name        = 8;
    optional bytes  extinfo     = 9; // ref: PBData.beeshop.db.CardPromotionExtInfo
    optional bytes  uiinfo      = 10; // ref: PBData.beeshop.db.CardPromotionUIInfo
}

message CardPromotionPriceTier
{
    optional int64 min_price        = 1; //including
    optional int64 max_price        = 2; //not including
    optional int64 discount_fix_value   = 3;
    optional int32 discount_percentage  = 4;
    optional int64 discount_max_value   = 5;
    optional int64 discount_shopee_value= 6;
}

message CardPromotionUIInfo
{
    optional string description     = 1;  // Input detailed description of the promotion - should mention the credit card name
    optional string url             = 3;  // Target URL when user tap on the banner.
    optional string title           = 4;  // Input title text part 1 - e.g. “Up to”, “Get”, etc.
    optional int64 discount_value   = 5;  // Input number amount of the discount - this should be the maximum value in the discount tiering table
    optional int64 discount_type    = 6;  // Select the type of the discount: (1) Dollar amount off: $x Off (2) Percentage off: x% Off
    optional int64 bank_id          = 7;  // Select which bank’s promotion it is (so that FE can generate the right background)
}

message CardPromotionExtInfo
{
    repeated int32 categories               = 1;
    repeated int32 shopids                  = 2;
    repeated PromotionItem items            = 3;
    repeated int32 disable_categories       = 4;
    repeated int32 disable_shopids          = 5;
    repeated PromotionItem disable_items    = 6;
    repeated CardPromotionPriceTier price_tiers = 7;
    optional bool can_use_buyer_promotion   = 8;
    repeated string card_bin_numbers        = 9;
    optional int32 total_usage_limit        = 10;  //monthly
    optional int32 user_usage_limit         = 11;  //monthly
    optional int32 total_usage_daily_limit  = 12;
    optional int32 user_usage_daily_limit   = 13;
    optional int32 total_usage_weekly_limit = 14;
    optional int32 user_usage_weekly_limit  = 15;
    optional int32 total_usage_cycle_limit  = 16;  //maximum number of times the promotion can be used in the entire promotion period
    optional int32 user_usage_cycle_limit   = 17;  //maximum number of times an user can use the promotion in the entire promotion period
    optional int32 credit_card_usage_daily_limit   = 18;  //maximum number of times a credit-card can be used in a day
    optional int32 credit_card_usage_weekly_limit  = 19;  //maximum number of times a credit-card can be used in a week
    optional int32 credit_card_usage_monthly_limit = 20;  //maximum number of times a credit-card can be used in a month
    optional int32 credit_card_usage_cycle_limit   = 21;  //maximum number of times a credit-card can be used in the entire promotion period
    optional bool limit_on_credit_card             = 22;  //false to set limit on user, true to set limit on credit card
    repeated uint64 fe_categories = 23;
    repeated uint64 disabled_fe_categories = 24;
}

message CardPromotionOrderInfo  // for order,return
{
    optional int32 card_promotion_id    = 1;
    optional int64 discount_bank    = 2;
    optional int64 discount_shopee  = 3;
}

message CardTxnFeeOrderInfo // For order
{
    optional int32 rule_id = 1; // ID of the transaction fee rule used to calculate the transaction fee.
    optional int64 card_txn_fee = 2; // The calculated amount of the transaction fee. Will be updated if there is return.
    optional int64 card_txn_fee_shipping_fee = 3; //the shipping fee part, if fully return, will return the shipping_fee part.
    optional int64 initial_txn_fee = 4; // The original transaction fee, not affected by returns. Initially equal to card_txn_fee. Only used for buyer transaction fee.
    optional int32 display_rule_id = 5; // Rule ID used to look up display-related info from external services (set by buyer transaction fee v2, from MPP fee service).
}

message TxnFeeCheckoutInfo // For checkout
{
    optional int32 rule_id = 1; // ID of the transaction fee rule used to calculate the transaction fee.
    optional int64 txn_fee = 2; // The calculated amount of the transaction fee. This value is only changed when changing the payment method of the checkout.
}

message CommRule
{
    optional int32 rule_id = 1; // Unique identifier of this rule.
    optional int32 cat_id = 2;
    optional int32 group_id = 3;
    optional int32 status = 4; // Status of this rule (0: normal, 1: deleted).
    optional string country = 5; // Country for which this rule applies.
    optional int64 cap = 6; // Maximum commission fee to apply, for fee calculation.
    optional int64 rate = 7; // Percentage of the base amount to be used as the commission fee, for fee calculation.
    optional int32 ctime = 8; // Creation time of this rule.
}

message AdminFeeRule
{
    optional int64 cap = 1; // Maximum transaction fee to apply, for admin fee calculation.
    optional int64 min_fee = 2; // Minimum transaction fee to apply, for admin fee calculation.
    optional int64 rate = 3; // Percentage of the base amount to be used as the admin fee, for transaction fee calculation.
    optional int64 fixed = 4; // Fixed amount to be added to the transaction fee, for admin fee calculation.
}

message TxnFeeRuleExtinfo  // Defined by P&L
{
    optional string option_info = 1; // SPM option_info data.
    optional string sublevel = 2; // Second level setting for each channel, for CC it represents bank ID.
    optional string learn_more_url = 3; // Helper URL to the microsite page explaining this rule.
    optional int32 logistics_channel_id = 4; // To support COD with different logistics channel.
    optional AdminFeeRule admin_fee_rule = 5; // The admin fee rule embedded in this rule (for buyer transaction fee rule).
}

message CardTxnFeeRule
{
    optional int32 rule_id = 1; // Unique identifier of this transaction fee rule.
    optional int32 channel_id = 2; // ID of the channel for which this rule applies.
    optional int32 start_time = 3; // Time on which this rule will start to apply.
    optional int32 status = 4; // Status of this rule (0: normal, 1: deleted).
    optional string country = 5; // Country for which this rule applies.
    optional int64 cap = 6; // Maximum transaction fee to apply, for transaction fee calculation.
    optional int64 rate = 7; // Percentage of the base amount to be used as the transaction fee, for transaction fee calculation.
    optional int64 fixed = 8; // Fixed amount to be added to the transaction fee, for transaction fee calculation.
    optional int32 rounding_method = 9; // Rounding method to use if the calculated transaction fee falls between the smallest currency unit (1: round nearest, 2: round up, 3: round down).
    optional int32 ctime = 10; // Creation time of this rule.
    optional int32 mtime = 11; // Modification time of this rule.
    optional string desc = 12; // Description of this rule.
    optional int64 min_fee = 13; // Minimum transaction fee to apply, for transaction fee calculation.
    optional int32 type = 14; // Type of transaction fee which calculation uses this rule (0: seller transaction fee, 1: buyer transaction fee).
    optional bytes extinfo = 15; // Extra information for this rule.
    optional int32 user_group = 16; // Type of users affected by this rule (0: default user, 1: cross border sellers, 2: special group).
}

message AttributeValuePending
{
    optional int32 id = 1;
    optional int32 model_id = 2;
    optional int32 attr_id = 3;
    optional string value = 4;
    optional int32 count = 6;
    optional int32 mtime = 7;
    optional int32 status = 8;
    optional string country = 9;
}

message AttributeQC
{
    optional int32 attr_id = 1;
    optional int32 mtime = 2;
    optional int32 status = 3;
    optional string country = 4;
}

message AttributeValueFailed
{
    optional int32 id = 1;
    optional int32 attr_id = 2;
    optional string value = 3;
    optional int32 mtime = 4;
    optional int32 status = 5;
    optional string country = 6;
}

message AttributeValueStatus
{
    optional int32 id = 1;
    optional int64  item_id = 2;
    optional int32 shop_id = 3;
    optional int32 model_id = 4;
    optional int32 attr_id = 5;
    optional string value = 6;
    optional int32 status = 7;
    optional int32 mtime = 8;
    optional string country = 9;
}

message FlashSale
{
    optional int64 promotionid = 1;
    optional int32 start_time = 2;
    optional int32 end_time = 3;
    optional int32 ctime = 4;
    optional int32 mtime = 5;
    optional int32 status = 6; //see FLASH_SALE_STATUS_***
    optional string country = 7;
    optional bytes extinfo = 8;// ref: PBData.beeshop.db.FlashSaleExtInfo
    optional bytes logicinfo = 9; // ref: PBData.beeshop.db.FlashSaleLogicInfo  only coreserver can update this data
    optional int32 flash_sale_type = 10; // ref: PBData.beeshop.db.FlashSaleType
    optional int64 timeslot_id = 11; // [In-shop flash sale] A promotionId of platform flash sale which has the same timeslot
    optional int32 shop_id = 12; // [In-shop flash sale] has shop_id
}

enum FlashSaleType
{
    FLASH_SALE_NORMAL = 0;
    FLASH_SALE_BRAND = 1;
    FLASH_SALE_SELLER = 2;
}

message FlashSaleOutOfStockItem
{
    optional int32 shopid = 1;
    optional int64 itemid = 2;
    optional int64 modelid = 3;
    optional int32 stock = 4;
    optional int32 end_time = 5;
    optional int64 price = 6;
    optional int64 price_before_discount = 7;
}

message BrandSaleOutOfStockGroup
{
    optional int32 brandid = 1;
    optional int32 shopid = 2;
}

message FlashSaleLogicInfo
{
    repeated FlashSaleOutOfStockItem items = 1; //when out of stock, will set this info.
    repeated BrandSaleOutOfStockGroup groups = 2; // when all item in a brand sale group out of stock, will set this info
}

message FlashSaleItem
{
    optional int32 shopid = 1;
    optional int64 itemid = 2;
    repeated string promo_images = 3;
    optional string promo_name = 4;
    optional int32 flash_catid = 5; //flash sale category id, different from system categories
    optional int32 sort_position = 6; // -1 means in recommendation pool, otherwise means in manual pool
    optional bool  sold_out_auto_move = 7;
    optional string promo_overlay_image = 8; // Overlay image for FS
    optional bool has_ed_model = 9; // true if item has extra discount model
    optional uint64 lowest_price_model_id = 10;
}

message CarouselBanner
{
    repeated BannerDetail flash_banner =  1;
    repeated BannerDetail mega_banner  = 2;
}

message BannerDetail
{
    optional string banner = 1; // the filename of the app banner
    optional string banner_url = 2; // the landing page when user clicks on the banner
    optional string pc_banner = 3; // the filename of the pc banner
    optional string pc_banner_url = 4; // the landing page when user clicks on the pc banner, it is redundant.
}

message ItemSeq
{
    optional uint64 item_id = 1;
    optional int32  display_seq = 2;
}

message BrandSaleGroup
{
    optional int32 brandid = 1;
    optional int32 shopid = 2;
    repeated MultiLangTxt slogan = 3;
    optional int64 entrance_picture_src_itemid = 4; // Source of entrance picture (reference itemid), set to 0 if want to use custom entrance picture
    optional string custom_entrance_picture = 5;
    repeated FlashSaleItem items = 6;
}

message FlashSaleExtInfo
{
    repeated FlashSaleItem items = 1; // the sequence of the items in the promotion
    optional string name = 2; // the name of the promotion for internal use only
    optional string description = 3; // the description of the promotion for internal use only
    optional string banner = 4; // the filename of the banner
    optional int64 min_price = 5; // minimum price to be accepted into promotion
    optional int64 max_price = 6; // maximum price to be accpeted into promotion
    optional int32 min_percent = 7; // minimum discount to be accepted into promotion
    optional int32 max_percent = 8; // maximum discount to be accepted into promotion
    optional int32 min_stock = 9; // minimum stock required to be accepted into promotion
    optional string banner_url = 10; // the landing page when user clicks on the banner
    optional string pc_banner = 11; // the filename of the pc banner
    optional bool hide_price = 12; // whether price should be hidden/masked on FE
    optional int32 max_DTS = 13; // maximum DTS for items within the flash sale
    repeated BrandSaleGroup groups = 14; // list of participating shop/brand, with their correspondent brand sale item
    optional bool auto_reject_disabled = 15;
    repeated ItemSeq display_seq = 16;
    optional CarouselBanner carousel_banner = 17;
    repeated uint64 extra_discount_ids = 18; // list of extra discount id which has at least one approved item
}

message BrandSaleMeta
{
    optional int32 id = 1;
    optional int32 brandid = 2;
    optional int32 shopid = 3;
    optional string country = 4;
    optional string custom_name = 5;
    optional string custom_logo = 6;
    optional int32 ctime = 7;
    optional int32 mtime = 8;
}

message ShopMetric
{
    optional int32 shopid   = 1;
    optional int32 type     = 2;  // ref: ShopMetricType
    optional int32 value    = 3;
    optional int32 ctime    = 4;
    optional bytes extinfo  = 5;  // ref: PBData.beeshop.db.ShopMetricExtinfo
    optional string country = 6;
    optional int32 id       = 7;
}

message AddressTax
{
    optional int64 id = 1;
    optional string hashed_addr = 2;
    optional string normalized_addr = 3;
    optional int32 tax_flag = 4;
    optional int32 order_count = 5;
}

enum AddressTaxMetricType
{
    AMOUNTS     = 1;
    COUNTS   = 2;
}

message AddressTaxV2
{
    optional int64 id = 1;
    optional int32 metric_type = 2;
    optional string hashed_addr = 3;
    optional string normalized_addr = 4;
    optional int64 amount = 5;
    optional uint32 effective_time = 6;
}

message AddressOrder
{
    optional int64 orderid = 1;
    optional string hashed_addr = 2;
    optional int32 ctime = 3;
    optional int32 status = 4;
    optional int32 userid = 5;
}

message ShopPenaltyHistoricalLog
{
    optional int64 logid         = 1;
    optional int32 shopid        = 2;
    optional int32 offence_type  = 3; // ref: PBData.beeshop.db.ShopPenaltyType
    optional int32 source_type   = 4; // ref: PBData.beeshop.db.ShopPenaltySource
    optional int32 ctime         = 5;
    optional int32 penalty_point = 6;
    optional string country      = 7;
    optional int32 status        = 8; // ref: PBData.beeshop.db.Status
    optional bytes extinfo       = 9;
}

message ShopPenaltyScoring
{
    optional int64 id                 = 1;
    optional int32 shopid             = 2;
    optional int32 shop_type          = 3;
    optional int32 penalty_score      = 4;
    optional int32 last_penalty_score = 5;
    optional int32 tier               = 6;
    optional int32 last_tier          = 7;
    optional int32 ctime              = 8;
    optional string country           = 9;
    optional bytes extinfo           = 10;
}

message PenaltyReason
{
    optional int32 reason_id    = 1;
    optional string reason_type = 2;
    optional string metric      = 3;
    optional int32 status       = 4;
    optional int32 ctime        = 5;
    optional int32 mtime        = 6;
}


message ShopMetricOrder
{
    optional int64 orderid = 1;
    optional int32 order_pickup_time = 2;
    optional int32 order_ship_by_time = 3;
    optional int32 type = 4; //  ref: PBData.beeshop.db.ShopMetricOrderType
    optional int32 ctime = 5; // ctime of the record
}

message BundleDeal
{
    optional int64 bundle_deal_id   = 1;
    optional int32 shopid           = 2;
    optional int32 start_time       = 3;
    optional int32 end_time         = 4;
    optional int32 ctime            = 5;
    optional int32 mtime            = 6;
    optional int32 status           = 7; //ref: PBData.bbeeshop.db.Status
    optional int64 rebate_amount    = 8;
    optional int64 comm_fee         = 9;
    optional int32 usage_limit      = 10;
    optional int64 flag             = 11; //ref: PBData.beeshop.db.BundleDealFlagType
    optional string country         = 12;
    optional bytes bundle_deal_rule = 13; // ref: PBData.beeshop.db.BundleDealRule
    optional bytes extinfo          = 14; // ref: PBData.beeshop.db.BundleDealExtInfo
}

message BundleDealRule
{
    optional int32 rule_type            = 1;// ref: PBData.beeshop.db.BundleDealRuleType
    optional int32 min_amount           = 2;
    optional int64 fix_price            = 3;
    optional int64 discount_percentage  = 4;//Note the multiplier: 1% = 100000
    optional int64 discount_value       = 5;
}

message MultiLangTxt
{
    optional string language    = 1;
    optional string value       = 2;
}

message BundleDealExtInfo
{
    optional string name            = 1;
    repeated MultiLangTxt labels    = 2;
    repeated int64 itemid_list      = 3;
    repeated int32 shopid_list      = 4; // will set it for multi shop bundle deal
}

message BundleDealItem
{
    optional int64 id               = 1;
    optional int32 shopid           = 2;
    optional int64 itemid           = 3;
    optional int64 bundle_deal_id   = 4;
    optional int32 start_time       = 5;
    optional int32 end_time         = 6;
    optional int32 status           = 7;
}

message BundleDealUsage
{
    optional int64 id               = 1;
    optional int64 bundle_deal_id   = 2;
    optional int64 orderid          = 3;
    optional int32 userid           = 4;
    optional int32 ctime            = 5;
    optional int32 status           = 6;
    optional int32 count            = 7;
}

message BundleOrderItem
{
    optional int64 bundle_deal_id       = 1;
    optional int64 price_before_bundle  = 2; // sum of all items' price in this bundle
    repeated BundleOrderItemDetailInfo item_list = 3;
}

message BundleOrderItemDetailInfo
{
    optional int64 itemid                  = 1;
    optional int64 modelid                 = 2;
    optional int32 amount                  = 3;
    optional int64 item_price              = 4;
    optional int64 snapshotid              = 5;
    optional uint32 product_promotion_type = 6; // save used item/model promotion_type when placing order
    optional uint64 product_promotion_id   = 7; // save used item/model promotion_id when placing order
    optional bool is_virtual_sku = 8; // to mark if order item is virtual sku
    optional int32 product_allocated_stock = 9; // save item/model allocated stock when placing order
    optional int64 bundle_item_price       = 10; // discount price after tax for single item in a bundle deal
    optional int64 item_rebate             = 11; // rebate for single item in a bundle deal
    optional int64 bundle_item_tax         = 12; // tax amount by bundle item level, if there is tax_exemption, then this is after exemption
    optional int64 bundle_item_input_price = 13; // discount price before tax by bundle item level, but after discount
    repeated OrderSkuDBInfo component_skus = 14; // if item is virtual this list will be list of all the item which are parent of this item.
    optional int64 order_sku_id            = 15; // order sku id
    repeated VoucherInfo vouchers          = 16; // Voucher(s) applied for this BundleOrderItem, including Shopee platform vouchers and seller vouchers.(NOT include FSV)
    optional OrderItemTaxInfo bundle_item_tax_info = 17; // Tax information in bundle order detail
    optional uint64 product_promotion_group_id = 18; // save used item/model product_promotion_group_id when placing order
    repeated OrderItemStock order_item_stocks = 19; // save stock info when order is created, it is used to deduct and return item stock.
    optional string hs_code = 20; //hs code
    optional ServiceFeeOrderItemInfo service_fee_info   = 21; // new field, the service fee information for a single item in a bundle deal
    optional CommissionFeeOrderItemInfo comm_fee_info   = 22; // new field, to store the commission fee info of a single item in a bundle deal
    optional BuyerTxnFeeOrderItemInfo buyer_txn_fee_info = 23; // BundleOrderItem level buyer transaction fee (v2)
    optional int64 coin_offset = 24; // Cash value of coins spent (already multiplied by quantity)
    optional int64 card_promotion_discount_bank = 25; // Cash value of payment promotion that is paid by 3rd party (already multiplied by quantity)
    optional int64 card_promotion_discount_shopee = 26; // Cash value of payment promotion that is paid by Shopee (already multiplied by quantity)
    optional int64 tax_exemption = 27; // Tax exemption calculated for each bundle deal item
    optional OverallPurchaseLimitInfo overall_purchase_limit = 28; //store user level Overall purchase limit info for bundle order items
}

message CommissionFeeOrderItemInfo
{
    optional int64 fee_amount     = 1;   // Commission fee amount
    optional int32 rule_id       = 2;   // Commission fee rule id
    optional int64 base_amount    = 3;  // commission fee base amount to calculate the fee
}

message BuyerTxnFeeOrderItemInfo
{
    optional int32 rule_id = 1;
    optional int64 fee_amount = 2;
}

message OverallPurchaseLimitInfo
{
    optional uint64 rule_id   = 1; //mapping with promotion_id
    optional uint32 rule_type = 2; //mapping with item.price_stock.promotion_type
}

message WebPush
{
    optional string title        = 1;
    optional string content      = 2;
    optional string image        = 3;
    optional string url          = 4;
    optional string action_title = 5;
}

message Referral
{
    optional int32 referral_id = 1;
    optional int32 referral_userid = 2;  //Userid of user who registered using a referral link
    optional int32 referrer_userid = 3;  //Userid of user who is the owneer of the referral link
    optional string phone = 4;
    optional int32 status = 5;
    optional int64 coin_earn = 6; // Coin earn for referrer
    optional int32 ctime = 7;
    optional int32 mtime = 8;
    optional string country = 9;
    optional bytes extinfo = 10;
}

message ReferralAudit
{
    optional int32  auditid = 1;
    optional int32  referral_id = 2;
    optional int32  audit_type = 3;
    optional int32  status = 4;
    optional string data = 5;
    optional int32  ctime = 6;
    optional int32  mtime = 7;
    optional string country = 8;
}

message ReferralRule
{
   optional int32 rule_id = 1;
   optional int64 promotionid = 2;
   optional int64 coin_earn = 3;
   optional int32 max_referral_user = 4; // max number of user can be awarded coins
   optional string country = 5;
   optional bool send_sms = 6;
   optional int32 ctime = 7;
   optional int32 mtime = 8;
   optional int32 referral_voucher_usage = 9; // number of total usage for referral public voucher
   optional string image_square = 10; // Square image template (640x640) for sharing referral, value in hashcode
   optional string image_fb = 11; // Image template (1080x567) for sharing referral by facebook, value in hashcode
}

message ReferralCodeCounter
{
    optional int64 id = 1;
    optional string prefix = 2;  //Referral code prefix which is the prefix of username
    optional int32 counter = 3;
    optional string country = 4;
    optional int32 ctime = 5;
    optional int32 mtime = 6;
    optional int64 promotionid = 7;
}

message ReferralCode
{
    optional int64 id = 1;
    optional int32 userid = 2;
    optional string voucher_code = 3; // Will store the referral public promotion code
    optional string country = 4;
    optional int32 ctime = 5;
    optional int64 promotionid = 6;
}

message RateControl
{
    optional int32 cmd            = 1; // ref: RateControlType. cmd = RateControlType + cmd (0: coreserver, 1000: new_backend_server)
    optional int32 threshold_warn = 2; // trigger an alarm email if QPS exceeds this threshold.
    optional int32 threshold_drop = 3; // drop requests if QPS exceeds this threshold.
    optional int32 mtime          = 4;
    optional int32 service_id     = 5; // ref: cmd.ServerID
    optional int32 cmd_number     = 6;
}

message StockChangeTask
{
    optional int64 id       = 1;
    optional int64 orderid  = 2;
    optional int64 itemid   = 3;
    optional int64 modelid  = 4;
    optional int64 snapshotid   = 5;
    optional int32 shopid   = 6;
    optional int32 stock_amount = 7;
    optional int32 ctime    = 8;
}

// in item.extinfo, itemmodel.extinfo
message GroupBuyInfo
{
    optional int64 campaignid       = 1;
    optional int64 group_buy_price  = 2;
    optional int64 rebate_amount    = 3;
    optional int32 start_time       = 4;
    optional int32 end_time         = 5;
    optional int32 group_size       = 6;
    optional int32 purchase_limit   = 7;
    optional int32 payment_duration = 8; // unit: second
}

// display at FE
message GroupBuyCampaign
{
    optional int64 campaignid       = 1; // PK
    optional int32 start_time       = 2;
    optional int32 end_time         = 3;
    optional int32 payment_duration = 4; // unit: second
    optional int32 ctime            = 5;
    optional int32 mtime            = 6;
    optional string country         = 7;
    optional bytes extinfo          = 8; // ref: GroupBuyCampaignExtinfo
    optional int32 group_duration   = 9; // unit: second
    optional int32 unpaid_group_num = 10;
}

message GroupBuyCampaignExtinfo
{
    repeated GroupBuyShopItemInfo shop_item_info = 1; // contains item infos to be displayed at FE
}

message GroupBuyShopItemInfo
{
    optional int32 shopid            = 1;
    optional int64 itemid            = 2;
    optional int32 group_buy_catid   = 3;
    optional string custom_name      = 4;
    repeated string custom_images    = 5;
    optional int32 users_paid        = 6; // count of unique users which already had paid for the item.
}

message GroupBuyCampaignItem
{
    optional int64 campaignid        = 1;
    optional int32 shopid            = 2;
    optional int64 itemid            = 3;
    optional int64 modelid           = 4;
    optional int64 group_buy_price   = 5;
    optional int64 rebate_amount     = 6;
    optional int32 group_size        = 7;
    optional int32 purchase_limit    = 8;
    optional int32 payment_duration  = 9;
    optional int32 start_time        = 10;
    optional int32 end_time          = 11;
    optional int32 status            = 12; // 1: normal 0: rejected
    optional int64 id                = 13;
}

message GroupBuyPurchaseLimit
{
    optional int64 orderid           = 1; // PK
    optional int64 campaignid        = 2;
    optional int64 groupid           = 3;
    optional int32 userid            = 4;
    optional int64 itemid            = 5; // item level, no modelid
    optional int32 amount            = 6;
    optional int32 order_status      = 7;
    optional int32 payment_end_time  = 8; // cronjob use only, if order is paid, payment_end_time=0
}

message GroupBuyGroup
{
    optional int64 groupid           = 1;
    optional int64 campaignid        = 2;
    optional int64 itemid            = 3; // group is on item level, no modelid
    optional int32 shopid            = 4; // for debugging use
    optional int32 purchase_limit    = 5;
    optional int32 group_size        = 6;
    optional int32 userid            = 7; // group owner
    optional int32 status            = 8; // ref: GB_GROUP_STARTED
    optional int32 start_time        = 9;
    optional int32 end_time          = 10;
    optional string group_code       = 11;
    optional string country          = 12;
    optional bytes extinfo           = 13;  // ref: GroupBuyGroupExtinfo
}

message GroupBuyGroupExtinfo
{
    repeated GroupOrderBrief orders  = 1;
}

message GroupOrderBrief
{
    optional int32 userid            = 1;
    optional int64 orderid           = 2;
    optional int32 order_status      = 3;
}

message VoucherWallet
{
    optional int32  userid           = 1; // table is sharded by userid % 1000
    optional int32  ctime            = 2; // the time when voucher is inserted into user's voucher wallet
    optional int64  promotionid      = 3;
    optional string voucher_code     = 4;
    optional int32  use_type         = 5; // ref: PBData.beeshop.db.VoucherUseType
    optional int32  reward_type      = 6; // ref: PBData.beeshop.db.PromotionType
    optional int32  start_time       = 7; // voucher start_time specific to this user, 0 means follow promotion_tab.start_time
    optional int32  end_time         = 8; // voucher end_time specific to this user, 0 means follow promotion_tab.end_time
    optional int32  total_count      = 9;  // how many times can a user use this voucher
    optional int32  used_count       = 10; // how many times the user already used this voucher
    optional int32  status           = 11; // Status_VW_VOUCHER_x: DELETED, NORMAL, EXPIRED
    optional bytes  extinfo          = 12;
    optional int32  voucher_market_type = 13; // ref: VoucherMarketType
    optional int32  shopid = 14;
    optional string reference_id = 15; // keep track of where the voucher is purchased, eg: DP order_id
}

// product attribute v2

message RootAttribute
{
    optional int64 attrid           = 1;
    optional string name            = 2;
    optional bytes alias            = 3; // AttributeAlias
    optional string remarks         = 4;
    optional int32 status           = 5; // db.Status_ATTR_NORMAL/db.Status_ATTR_DELETED
}

message AttributeAlias
{
    repeated AttributeAliasEntry entry = 1;
}

message AttributeAliasEntry
{
    optional string language        = 1;
    optional string value           = 2;
}

message Attribute
{
    optional int64 attrid           = 1;
    optional int64 root_attrid      = 2;
    optional int64 parent_attr_id   = 4;
    optional int64 parent_vid       = 5;
    optional int32 catid            = 6;
    optional int64 attr_type_flag   = 7; // AttrTypeFlag
    optional int64 sorting          = 8;
    optional int32 status           = 9; // db.Status_ATTR_NORMAL/db.Status_ATTR_DELETED
    optional bytes extinfo          = 10; // AttributeExtInfo
    optional string country         = 11;
}

message AttributeExtInfo
{
    optional int64 is_mandatory     = 1; // AttrMandatoryType
    optional int32 input_type       = 2; // AttrInputType
    optional int32 input_validator  = 3; // AttrInputValidateType
}

message AttributeValueV2
{
    optional int64 valueid          = 1;
    optional string value           = 2;
    optional bytes alias            = 3; // AttributeAlias
    optional int32 approval_status  = 4; // db.Status_ATTR_VALUE_XXX
    optional int32 status           = 5; // db.Status_ATTR_NORMAL/db.Status_ATTR_DELETED
}

message AttributeValueRelation
{
    optional int64 id               = 1;
    optional int64 root_attrid      = 2;  //root and attrid must not both be zeros or non-zeros
    optional int64 attrid           = 3;
    optional int64 valueid          = 4;
    optional int32 status           = 5;
}

// standard product
message StandardProduct
{
    optional int64 spuid            = 1;
    optional string spu_name        = 2;
    optional string spu_cover_img   = 3;
    optional int64 release_date     = 4;
    optional int32 status           = 5;
    optional bytes extinfo          = 6; // StandardProductExtInfo
    optional bytes alt_name         = 7; // AttributeAlias, language to alternative names
    optional bytes query_name       = 8; // StandardProductExtInfo
}

message CountryCat
{
    optional string country     = 1;
    repeated int32 category     = 2;
}

message SPUSynonym
{
    optional string country     = 1;
    optional int64  spuid   = 2;
    optional string spu_name    = 3;
}

message StandardProductExtInfo
{
    optional int64 reference_spu_id                 = 1;
    optional AttributeAlias alias                   = 2; // AttributeAlias
    optional string selling_point                   = 3;
    optional AttributeAlias selling_point_alias     = 4; // AttributeAlias
    repeated CountryCat cats                        = 5;
    repeated SPUSynonym synonyms            = 6;
}

message StandardProductAttribute
{
    optional int64 id               = 1;
    optional int64 spu_id           = 2;
    optional int64 attr_id          = 3;
    optional int64 valueid          = 4;
    optional int32 status           = 5;
}

message StandardProductSearchData
{
    optional int64 id               = 1;
    optional int64 spu_id           = 2;
    optional int64 average_price    = 3; // manually uploaded value
    optional int64 sold             = 4; // manually uploaded value
    optional int32 spu_label        = 5; // SpuLabel
    optional int64 num_of_seller    = 6; // manually uploaded value
    optional string country         = 7;
    optional int32 status           = 8; // manual upload value status
    optional int64 calc_average_price   = 9;  // calculated value
    optional int64 calc_sold_count      = 10; // calculated value
    optional int64 calc_seller_count    = 11; // calculated value
}

message StandardProductSearchTerm
{
    optional int64 id               = 1;
    optional int64 spu_id           = 2; // unique key = spu_id + search_team + country
    optional string search_term     = 3;
    optional string country         = 4;
    optional bytes cats             = 5; // SearchTermCategory
    optional int32 status           = 6;
}

message SearchTermCategory
{
    repeated int32 category = 1;
}

message ItemPreviewSetting
{
    optional int64 previewid  = 1; // the item preview setting id
    optional int64 itemid     = 2; // the item id
    optional int32 shopid     = 3; // the shop id
    optional int32 start_time = 4; //when the flag turn to be preview
    optional int32 end_time   = 5; // when the preview status is terminated
    optional int32 status     = 6; // status of the item preview setting, i.e. enum STATUS_ITEM_PREVIEW_xxxx
    optional int32 mtime      = 7;
    optional int32 ctime      = 8;
    optional string country   = 9;
    optional bool open_price_mask = 10; // price mask is opened with preview or not
}

//WELCOME_PACKAGE_TYPE is used to indicate the item type in the welcome package
enum WELCOME_PACKAGE_TYPE{
    NON_WELCOME_PACKAGE                     = 0; //NON_WELCOME_PACKAGE indicate the item does not belong to welcomepackage
    FREE_GIFT                               = 1; //FREE_GIFT indicate the item is the free gifts and discounted price could only be 0 [SPCS-1355]
    EXCLUSIVE_ITEMS                         = 2; //EXCLUSIVE_ITEMS indicate the item belong to the exclusive items and price > 0 [SPCS-1355]
}

//[SPCS-1355]
message WelcomePackage{
    optional int32 id                       = 1;
    optional int32 type                     = 2; // ref: WELCOME_PACKAGE_TYPE
    optional bytes data                     = 3; // data is ref: WelcomePackageExtInfo
    optional string country                 = 4;
    optional int32 mtime                    = 5;
    optional int32 ctime                    = 6;
}

message WelcomePackageExtInfo{
    repeated WelcomePackageItem items       = 1; // items is the array of all items for specific type(ref:WelcomePackageItem) in certain country, ref:WelcomePackageItem
}

// WelcomePackageItem is used to illustrate the welcome item properties in the welcome landing page  [SPCS-1355]
message WelcomePackageItem{
    optional int64 itemid                   = 1;
    optional int32 shopid                   = 2;
    optional string custom_image            = 3;
    optional string custom_name             = 4;

    // Store item welcome package price.
    // This field is ignored if the `models` list is not empty.
    // Both fields `price` and `models` should not be nil at the same time.
    optional int64 price                     = 6;

    // Store welcome package prices of all models under the item.
    // The modelid should be positive, otherwise, data will be considered
    // invalid. If this fiels is empty or nil, `price` field will be used.
    // Both fields `price` and `models` should not be nil at the same time.
    repeated WelcomePackageModelPrice models = 7;

    optional bool need_update                = 8; // need_update indicate existing WP item should be updated . Only SetWelcomePackage use this value
    optional bool new_user_only              = 9; // deprecated

    // Similar with `price`
    //`rebate` and `purchase_limit` will be ignored if models list not empty
    optional int64 rebate = 10;
    optional int32 purchase_limit = 11;
}


message WelcomePackageModelPrice{
    optional int64 price    = 1; // the welcome price
    optional int64 modelid  = 2;
    optional int64 rebate = 3;
    optional int32 purchase_limit = 4;
}

message GlobalVoucher
{
    optional int64  promotionid      = 1; // promotion_tab.promotionid
    optional string country          = 2;
    optional int32  ctime            = 3;
    optional string voucher_code     = 4;
    optional int32  use_type         = 5; // ref: PBData.beeshop.db.VoucherUseType
    optional int32  reward_type      = 6; // ref: PBData.beeshop.db.PromotionType
}

message CollectionItemData
{
    optional int64 id = 1;
    optional int32 collection_id = 2;
    optional int32  shopid = 3;
    optional int64 itemid = 4;
    optional string display_name = 5; // SPCS-1533 Collection Item Customization
    optional int32 campaign_stock = 6; // SPCS-1533 Collection Item Customization
    optional bytes  extinfo = 7; // ref: PBData.beeshop.db.CollectionItemExtInfo
}

message CollectionItemExtInfo
{
    repeated ClusterRecord item_cluster_info = 1; // All clusters that the item belongs to
}

message CollectionCluster // C57 Core Automated Collection
{
    optional int32 collection_id = 1;
    optional string cluster_id = 2;
    optional int32 status = 3;
    optional int32 mtime = 4;
    optional int32 ctime = 5;
    optional bytes extinfo = 6;  // ref: PBData.beeshop.db.CollectionClusterExtInfo
    optional int32 source = 7; // ref: PBData.beeshop.db.ClusterSource
}

message CollectionClusterExtInfo
{
    repeated ClusterRecord cluster_info = 1; // C57 Core Automated Collection
}

message ClusterRecord
{
    optional string id = 1;
    optional string name = 2;
}

enum OrderCntUpdateTaskType
{
    CHECKOUT_CNT = 1;
    ORDER_CNT = 2;
    RETURN_CNT = 3;
}

message OrderCntUpdateTask
{
    optional int64 id = 1;
    optional int32 userid = 2;
    optional int32 shopid = 3;
    optional int32 type = 4; // ref: OrderCntUpdateTaskType
    optional int32 old_val = 5;
    optional int32 new_val = 6; // old_val and new_val will be either OrderListType or return status, depends on task type
    optional int32 ctime = 7;
    optional int64 object_id = 8; // orderid/checkoutid/return_id
}

enum CoinRejectActionFlag
{
    BLOCK_ORDER_COINS = 1;
    BLOCK_CASHBACKVOUCHER_COINS = 2;
    BLOCK_REFERRAL_COINS = 4;
}

message CoinReject
{
    optional int64 orderid = 1;
    optional int32 buyerid = 2;
    optional int32 sellerid = 3;
    optional string country = 4;
    optional int32 action = 5; //ref: CoinRejectActionFlag, bitwise flags
    optional int32 status = 6; //ref: Status_COIN_REJECT_
    optional int32 mtime = 7;
    optional string operator = 8; // Last person that changes the status
}

message SlashPriceItem
{
    optional int32 id = 1;
    optional int32 shopid = 2;
    optional int64 itemid = 3;
    optional int64 lowest_price = 4;
    optional string country = 5;
    optional int32 status = 6; // ref: PBData.beeshop.db.Status_SLASH_PRICE_ITEM_
    optional int32 ctime = 7;
    optional int32 mtime = 8;
    optional string operator = 9;
    optional int32 new_player_max_slash_off = 10; // If set, this will override global rule, store percentage after multiplied by 100000 (e.g. 12.3% -> 1230000)
    optional int32 new_player_min_slash_off = 11; // If set, this will override global rule, store percentage after multiplied by 100000 (e.g. 12.3% -> 1230000)
    optional int32 existing_player_max_slash_off = 12; // If set, this will override global rule, store percentage after multiplied by 100000 (e.g. 12.3% -> 1230000)
    optional int32 existing_player_min_slash_off = 13; // If set, this will override global rule, store percentage after multiplied by 100000 (e.g. 12.3% -> 1230000)
    optional bytes extinfo = 14; // ref: PBData.beeshop.db.SlashPriceItemExtInfo
}

message SlashPriceItemExtInfo
{
    optional int32 self_max_slash_off = 1; // If set, this will override global rule, store percentage after multiplied by 100000 (e.g. 12.3% -> 1230000)
    optional int32 self_min_slash_off = 2; // If set, this will override global rule, store percentage after multiplied by 100000 (e.g. 12.3% -> 1230000)
    optional int32 duplicate_player_max_slash_off = 3; // If set, this will override global rule, store percentage after multiplied by 100000 (e.g. 12.3% -> 1230000)
    optional int32 duplicate_player_min_slash_off = 4; // If set, this will override global rule, store percentage after multiplied by 100000 (e.g. 12.3% -> 1230000)
    optional bool enable_duplicate_player = 5;
}

message SlashPriceItemAudit
{
    optional int64 auditid = 1;
    optional int64 itemid = 2;
    optional int32 audit_type = 3;
    optional int32 status = 4;
    optional string data = 5;
    optional int32 ctime = 6;
    optional int32 mtime = 7;
}

message SlashPriceActivity
{
    optional int64 activity_id = 1;
    optional int32 shopid = 2;
    optional int64 itemid = 3;
    optional int64 modelid = 4;
    optional int32 sender_userid = 5;
    optional int64 orderid = 6;
    optional int64 original_price = 7;
    optional int64 current_price = 8;
    optional int64 lowest_price = 9;
    optional int32 status = 10; // ref: PBData.beeshop.db.Status_SLASH_PRICE_ACTIVITY_
    optional string country = 11;
    optional int32 start_time = 12;
    optional int32 end_time = 13;
    optional int32 expiry_time = 14;
    optional bool notification_sent = 15;
    optional bool stock_returned = 16; // DEPRECATED: Not used anymore (stock deduction and restoration will be done as pernormal)
    optional bytes extinfo = 17; // ref: PBData.beeshop.db.SlashPriceActivityInfo
    optional int32 ctime = 18;
    optional int32 mtime = 19;
    optional bytes hashed_fingerprint = 20;
}

message SlashPriceActivityInfo
{
    optional string name = 1;
    repeated string images = 2;
    optional int32 original_expiry_time = 3;
    optional string model_name = 4;
}

message SlashPriceHistory
{
    optional int64 history_id = 1;
    optional int64 activity_id = 2;
    optional int32 receiver_userid = 3;
    optional string receiver_phone = 4;
    optional int64 before_slashed_price = 5;
    optional int64 after_slashed_price = 6;
    optional int64 coin_reward = 7;
    optional int32 ctime = 8;
    optional int32 mtime = 9;
    optional bool is_first_slash = 10;
    optional bytes extinfo = 11;  // ref: PBData.beeshop.db.SlashPriceHistoryExtInfo
    optional int32 sender_userid = 12;
}

message SlashPriceHistoryExtInfo
{
    optional string nickname = 1;
    optional string client_ip = 2;
    optional bytes hashed_fingerprint = 3;
}

message SlashPriceRule
{
    optional int32 rule_id = 1;
    optional string country = 2;
    optional int32 max_slash_price_activity = 3;
    optional int32 max_slash_for_other = 4;
    optional int32 new_player_max_slash_off = 5; // will store percentage after multiplied by 100000 (e.g. 12.3% -> 1230000)
    optional int32 new_player_min_slash_off = 6; // will store percentage after multiplied by 100000 (e.g. 12.3% -> 1230000)
    optional int32 existing_player_max_slash_off = 7; // will store percentage after multiplied by 100000 (e.g. 12.3% -> 1230000)
    optional int32 existing_player_min_slash_off = 8; // will store percentage after multiplied by 100000 (e.g. 12.3% -> 1230000)
    optional int64 new_player_coin_reward = 9;
    optional int64 existing_player_max_coin_reward = 10;
    optional int64 existing_player_min_coin_reward = 11;
    optional int32 countdown_timer_in_hour = 12;
    optional int32 purchase_validity_timer_in_hour = 13;
    optional bytes extinfo = 14; // ref: PBData.beeshop.db.SlashPriceRuleExtInfo
    optional int32 ctime = 15;
    optional int32 mtime = 16;
}

message SlashPriceRuleExtInfo
{
    repeated int64 itemids = 1;
    optional int32 max_start_activity_by_device = 2;
    optional int32 self_max_slash_off = 3; // will store percentage after multiplied by 100000 (e.g. 12.3% -> 1230000)
    optional int32 self_min_slash_off = 4; // will store percentage after multiplied by 100000 (e.g. 12.3% -> 1230000)
    optional int32 duplicate_player_max_slash_off = 5; // will store percentage after multiplied by 100000 (e.g. 12.3% -> 1230000)
    optional int32 duplicate_player_min_slash_off = 6; // will store percentage after multiplied by 100000 (e.g. 12.3% -> 1230000)
    optional int32 duplicate_period_in_day = 7;
}

message SlashPriceRuleAudit
{
    optional int64 auditid = 1;
    optional int32 rule_id = 2;
    optional int32 audit_type = 3;
    optional int32 status = 4;
    optional string data = 5;
    optional int32 ctime = 6;
    optional int32 mtime = 7;
}

message SlashPriceUser
{
    optional int32 id = 1;
    optional int32 userid = 2;
    optional string phone = 3;
    optional int32 slash_count = 4;
    optional int32 slash_limit_expiry_date = 5;
    optional int32 ctime = 6;
    optional int32 mtime = 7;
}

message SlashPriceCoin
{
    optional int32 id = 1;
    optional int64 activity_id = 2;
    optional string phone = 3;
    optional int64 coin_reward = 4;
    optional int32 status = 5; // ref: PBData.beeshop.db.Status_SLASH_PRICE_COIN_
    optional int32 ctime = 6;
    optional int32 mtime = 7;
}

message SlashPriceReject
{
    optional int32 id = 1;
    optional int64 activity_id = 2;
    optional int32 sender_user_id = 3;
    optional int32 receiver_user_id = 4;
    optional string receiver_phone = 5;
    optional int32 activity_start_time = 6;
    optional int32 slash_start_time = 7;
    optional int32 type = 8; // ref: PBData.beeshop.db.SlashPriceRejectType_
    optional bytes extinfo = 9; // ref: PBData.beeshop.db.SlashPriceRejectExtInfo
    optional int32 ctime = 10;
}

enum SlashPriceRejectType
{
    SENDER_DEVICE_LIMIT = 1;
    SENDER_ACCOUNT_STATUS = 2;
    SENDER_PHONE_BLACKLIST = 3;
    RECEIVER_ACCOUNT_STATUS = 4;
    RECEIVER_PHONE_BLACKLIST = 5;
    SENDER_RECEIVER_SAME_DF = 6;
    SENDER_RECEIVER_SAME_ADDR = 7;
    SENDER_RECEIVER_SAME_PHONE = 8;
}

message SlashPriceRejectExtInfo
{
    optional string client_ip = 1;
    optional bytes hashed_fingerprint = 2;
}

message SpuProductionItem
{
     optional int64 id = 1;
     optional int64 modelid = 2;
     optional int64 itemid = 3;
     optional int64 spuid = 4;
     optional int32 shopid = 5;
     optional int32 ctime = 6;
     optional int32 mtime = 7;
     optional int32 status = 8;
     optional bytes extinfo = 9; // not used (for future usage)
}

message SearchSpuProductionItem
{
    optional int64 id = 1;
    optional int64 modelid = 2;
    optional int64 itemid = 3;
    optional int64 spuid = 4;
    optional int32 shopid = 5;
    optional int32 ctime = 6;
    optional int32 mtime = 7;
    optional int32 status = 8;
    optional bytes extinfo = 9; // not used (for future usage)
}

message CheckoutTongdun
{
    optional int64 id = 1;
    optional int32 userid = 2;
    optional int32 behavior_check_record_id = 3;
    optional int64 checkoutid = 4;
    optional string tongdun_df = 5;
    optional string country = 6;
    optional int32  ctime = 7;
    optional string voucher_code = 8;
    optional int64 promotionid = 9;
    optional int64  total_price = 10;
    optional string platform = 11;
    optional int32 final_score = 12;
    optional string final_decision = 13;
    optional int32 decode_status = 14;
    optional string tongdun_info = 15;
    optional bytes extinfo = 16; // ref: PBData.beeshop.db.CheckoutTongdunExtInfo
}

message CheckoutTongdunExtInfo
{
    optional bool success = 1; // Copied from Tongdun API's Response
    repeated int32 decode_reason = 2; // refer TongdunDecodeReason
}

message AccountIntegrationExtraInfo {
}

enum AccountIntegrationStatus {
    VALID = 1;
    INVALID = 2;
}

enum AccountIntegrationThirdPartyType {
    FOODY  = 3;
    AIRPAY = 4;
    APPLE  = 5;
    GOOGLE = 6;
    LKPP   = 7;
}

message AccountIntegration {
    optional uint64 id = 1;
    optional uint64 user_id = 2;
    optional string third_party_identity = 3;
    optional uint32 third_party_type = 4; //Ref: AccountIntegrationThirdPartyType
    optional uint32 status = 5; //Ref: AccountIntegrationStatus
    optional bytes extra_info = 6;
    optional uint64 create_time = 7;
    optional uint64 update_time = 8;
    optional string region = 9;
}

message OrderItemV2
{
    optional int64  order_item_id       = 1;
    optional int32  user_id             = 2; // buyer userid
    optional int64  order_id            = 3;
    optional int32  shop_id             = 4; // seller shopid
    required int64  item_id             = 5;
    optional int64  model_id            = 6;
    optional int32  amount              = 7; // how many products(itemid,modelid) you bought\seller
    optional int32  actual_amount       = 8; // actual price of the order item after return/refund
    optional int64  item_price          = 9; // same as Item.Price || Item.WholesalePrice
    optional int64  order_price         = 10; // if has offer, equals to offer price else equals item_price. If has bundle, it is the bundled price. If add on sub item, it is AddOnPrice.
    optional string region              = 11; // same as currency
    optional int32  order_item_status   = 12; // ref: PBData.beeshop.db.Status_OITEM
    optional int64  chat_id             = 13; // use chatid to find offer, will deprecated in future, use offerid instread
    optional int64  snapshot_id         = 14; // ItemSnapshot->snapshopid
    optional int64  offer_id            = 15; // the responding offer
    optional int64  group_id            = 16;  // A unique group ID to distiguish groups of items in Cart, and Order. (eg. AddOnDeal)
    optional int32  create_time         = 17;
    optional bytes  extinfo             = 18; // ref: PBData.beeshop.db.OrderItemExtInfo
}

message SingleServiceFeeInfo
{
    optional int32 rule_id = 1;
    optional int64 fee_amount = 2;
}

message ServiceFeeOrderItemInfo
{
    repeated SingleServiceFeeInfo info_list = 1;
}

message ServiceFeeOrderInfo
{
    optional int64 fee_amount = 1; // to be used to display and escrow calculation
    optional int64 initial_fee_amount = 2;
}

message AccountBindAudit
{
    optional int64 id = 1;
    optional string accid = 2;
    optional int32 acctype = 3;
    optional int32 userid = 4;
    optional int32 action = 5;
    optional int32 origin = 6;
    optional string operator = 7;
    optional string reason = 8;
    optional int32 create_time = 9;
}

message DeviceInfo
{
    optional string id                        = 1;
    optional string machine_code              = 2;  // beeshop_db.Device.machine_code, type of device, e.g. android, apple
    optional int32  appversion                = 3;  // beeshop_db.Device.appversion
    optional bytes  pn_token                  = 4;  // beeshop_db.Device.pn_token
    optional string user_agent                = 5;  // beeshop_db.DeviceExt.user_agent
    optional bool   is_rooted                 = 6;  // beeshop_db.DeviceExt.is_rooted
    optional bytes  device_fingerprint        = 7;  // beeshop_db.DeviceExt.device_fingerprint
    optional bool   is_fingerprint_tempered   = 8;  // beeshop_db.DeviceExt.is_fingerprint_tempered
    optional bytes  fingerprint_before_temper = 9;  // beeshop_db.DeviceExt.fingerprint_before_temper
    optional string gcm_subscription          = 10; // beeshop_db.DeviceExtinfo.gcm_subscription, used when machine_code = "gcm_web"
    optional float  latitude                  = 11; // GPS
    optional float  longitude                 = 12; // GPS
    optional int32  location_setting          = 13; // 0:no permission from user;1:have permission but GPS off;2:have permission and GPS on.
    optional int32  platform                  = 14; // ref beeshop.ads.TrackingPlatformType
    optional string source                    = 15; // same value as CMD_LOGIN
    optional string system_version            = 16; // android 9.0/io 11.3
    optional string client_ip                 = 17; // client address
    optional string language                  = 18; // device language
    optional string app_version_name          = 19;
    optional string device_model              = 20;
    optional string device_brand              = 21;
    optional string network_type              = 22;
    optional string cellular_type             = 23;
    optional int32  rn_version                = 24;
    optional string client_type               = 25;
    optional string tongdun_blackbos_data     = 26;
    optional int64  create_time               = 27;
    optional string app_package_name          = 28; // refer to the Reference below for official Shopee App package name
    optional bool   is_using_vpn              = 29; // this is to detect whether the user is using vpn
    optional string carrier                   = 30; // this is the mobile carrier's name, such as Singtel and Starhub
    optional int64  update_time               = 31;
    optional bytes  deviceid                  = 32;
}

message VoucherInfo
{
    optional int64  promotionid = 1; // (promotionid + voucher_code) is voucher's unique identifier
    optional string voucher_code = 2;
    optional int32  shopid = 3; // to distinguish Shopee voucher and Seller voucher, (Shopee: shopid=0, Seller: shopid>0)
    optional int32  reward_type = 4; // discount, coin-cashback or FSV? ref: PBData.beeshop.db.PromotionType
    optional int64  reward_discount = 5; // discount voucher reward, deduct how much money, static value not affected by return/refund
    optional int64  reward_coin = 6; // coin-cashback voucher reward, earn how many coins(DB coin), static value not affected by return/refund
    optional int64  reward_coin_equivalent_cash = 7; // equivalent cash value of the coins earned, static value not affected by return/refund
    optional bool   is_seller_absorbed = 8; // true: voucher cost is borne by seller, false: voucher cost is borne by shopee
}

message OtpRecord
{
    optional int64  id = 1;
    optional int32  record_type = 2;
    optional string phone = 3;
    optional int32  operation_type = 4;
    optional int32  switch_reason = 5;
    optional int32  otp_type = 6;
    optional string country = 7;
    optional int32  platform = 8;
    optional string ip = 9;
    optional bytes  hashed_fingerprint = 10;
    optional int32  userid = 11;
    optional int32  verify_status = 12;
    optional int32  failure_reason = 13;
    optional string otp_code = 14;
    optional string attempt_code = 15;
    optional int32  create_time = 16;
    optional string tracking_id = 17;
    optional string tracing = 18;
    optional string task_name = 19;
    optional string clientid = 20;
}

message WelcomePackageV2Item {
    optional uint64 id           = 1;
    optional uint64 shop_id      = 2;
    optional uint64 item_id      = 3;
    optional uint32 item_type    = 4;   // Ref: WelcomePackageV2ItemType.*
    optional uint32 item_status  = 5;   // Ref: WelcomePackageV2ItemStatus.*
    optional uint32 sequence     = 6;
    optional string custom_image = 7;
    optional string custom_name  = 8;
    optional string region       = 9;
    optional uint32 create_time  = 10;
    optional uint32 update_time  = 51;
  }

  message WelcomePackageV2ItemModel {
    optional uint64 id                = 1;
    optional uint64 shop_id           = 2;
    optional uint64 item_id           = 3;
    optional uint32 item_type         = 4;   // Ref: WelcomePackageV2ItemType.*
    optional uint32 item_sequence     = 5;
    optional string item_custom_name  = 6;
    optional uint64 model_id          = 7;
    optional uint32 item_model_status = 8;   // Ref: WelcomePackageV2ItemModelStatus.*
    optional uint64 price             = 9;
    optional uint64 rebate            = 10;
    optional uint32 purchase_limit    = 11;
    optional string region            = 12;
    optional uint32 create_time       = 13;
    optional uint32 update_time       = 14;
  }

// Should only be used by account service
message AccountUserID
{
    optional int32 userid            = 1;
}

// Should only be used by account service
message AccountShopID
{
    optional int32 shopid            = 1;
}

// Should only be used by account service
message AccountMapping
{
    optional string acct_id          = 1;
    optional string acct_type        = 2;
    optional string acct_scope       = 3;
    optional string region           = 4;
    optional int32  userid           = 5;
    optional int32  create_time      = 6;
}

// Should only be used by promotion service
message UpdateDefaultModel {
    optional uint64  promotion_id   = 1;
    optional uint32  promotion_type = 2;
    optional string  region         = 3;
    optional uint32  shop_id        = 4;
    optional uint64  item_id        = 5;
    optional uint64  old_model_id   = 6;    // This will always be 0, then we can ignore the field.
    optional uint64  new_model_id   = 7;
    optional bytes   set_item_promotion_request    = 8;    // ips.SetItemPromotionRequest
    optional int32   retry          = 9;    // IPS might not need to fill this. This is for error processing
}

// Should only be used by promotion service
message MessageRetry {
    optional int64   id              = 1;
    optional uint64  promotion_id    = 2;
    optional uint32  promotion_type  = 3;
    optional string  region          = 4;
    optional uint32  shop_id         = 5;
    optional uint64  item_id         = 6;
    optional uint64  old_model_id    = 7;
    optional uint64  new_model_id    = 8;
    optional bytes   set_item_promotion_request     = 9;    // ips.SetItemPromotionRequest
    optional string  topic           = 10;
    optional int32   retry           = 11;
    optional string  reason          = 12;
}

// Should only be used by promotion service
message DeadMessage {
    optional int64  id              = 1;
    optional bytes  message         = 2;
    optional string topic           = 3;
    optional string reason          = 4;
}

// Should only be used by promotion service
message PromotionFlashSaleItem {
    optional int64  id                      = 1;
    optional int64  promotionid             = 2;
    optional int64  shopid                  = 3;
    optional int64  itemid                  = 4;
    optional int64  modelid                 = 5;
    optional int64  price_before_discount   = 6;
    optional int64  promotion_price         = 7;
    optional int64  rebate_price            = 8;
    optional int32  status                  = 9;
    optional int32  user_item_limit         = 10;
    optional int32  user_model_limit        = 11;
    optional int64  start_time              = 12;
    optional int64  end_time                = 13;
    optional int64  mtime                   = 14;
    optional int64  stock                   = 15;
    optional int64  sort_weight             = 16;
    optional string reject_reason           = 17;
    optional int32  source                  = 18;
    optional int32  fs_cat                  = 19;
    optional string operator                = 20;
    optional string fail_approve_reason     = 21;
    optional string extra_data              = 22;
    optional int32  sort_position           = 23;
    optional int32  sold_out_auto_move      = 24;
    optional int64  approve_time            = 25;
    optional int64  move_to_manual_time     = 26;
}

// Should only be used by promotion service
message PromotionBrandSaleItem {
    optional int64  id                      = 1;
    optional int64  promotionid             = 2;
    optional int64  shopid                  = 3;
    optional int64  itemid                  = 4;
    optional int64  modelid                 = 5;
    optional int64  price_before_discount   = 6;
    optional int64  promotion_price         = 7;
    optional int64  rebate_price            = 8;
    optional int32  status                  = 9;
    optional int32  user_item_limit         = 10;
    optional int32  user_model_limit        = 11;
    optional int64  ctime                   = 12;
    optional int64  mtime                   = 13;
    optional int64  stock                   = 14;
    optional int64  item_sort_weight        = 15;
    optional string reject_reason           = 16;
    optional int32  source                  = 17;
    optional string operator                = 18;
    optional string fail_approve_reason     = 19;
    optional bytes  extra_data              = 20;
}

enum ProductPromotionBackendItemStatus {
    PP_BACKEND_STATUS_NOMINATED = 0;
    PP_BACKEND_STATUS_CONFIRMED = 1;
    PP_BACKEND_STATUS_REJECTED = 2;
    PP_BACKEND_STATUS_SELLER_DELETED = 3; // removed by seller in nomination stage
}

// Should only be used by promotion service
message ProductPromotionDetail {
    optional int64  id                      = 1;
    optional int64  promotionid             = 2;
    optional int64  shopid                  = 3;
    optional int64  itemid                  = 4;
    optional int64  modelid                 = 5;
    optional int64  price_before_discount   = 6;
    optional int64  promotion_start_time    = 7;
    optional int64  promotion_end_time      = 8;
    optional int64  promotion_price         = 9;
    optional int64  rebate_price            = 10;
    optional int64  item_quantity           = 11;
    optional int64  model_quantity          = 12;
    optional int32  status                  = 13;
    optional int32  type                    = 14;
    optional bytes  extra_data              = 15;
    optional string operator                = 16;
    optional int64  vtime                   = 17;
    optional int64  mtime                   = 18;
    optional int64  ctime                   = 19;
}

// Should only be used by promotion service
message PromotionGroupBuyCampaignItem {
    optional int64  id                      = 1;
    optional int64  campaignid              = 2;
    optional int64  shopid                  = 3;
    optional int64  itemid                  = 4;
    optional int64  modelid                 = 5;
    optional int64  group_buy_price         = 6;
    optional int64  rebate_amount           = 7;
    optional int64  group_size              = 8;
    optional int64  purchase_limit          = 9;
    optional int64  payment_duration        = 10;
    optional int64  start_time              = 11;
    optional int64  end_time                = 12;
    optional int32  status                  = 13;
    optional int32  backend_status          = 14;
    optional int64  sort_weight             = 15;
    optional int64  l1_catid                = 16;
    optional int64  l2_catid                = 17;
    optional int64  l3_catid                = 18;
    optional string custom_name             = 19;
    optional string custom_image            = 20;
    optional string reject_reason           = 21;
    optional int32  source                  = 22;
    optional int32  group_buy_catid         = 23;
    optional int32  sync_status             = 24;
    optional string operator                = 25;
    optional int64  ctime                   = 26;
    optional int64  mtime                   = 27;
    optional bytes  extra_data              = 28;
}

message PurchaseCount
{
    optional uint64 id             = 1;
    optional string region         = 2;
    optional uint64 promotion_id   = 3;
    optional uint32 promotion_type = 4;
    optional uint64 shop_id        = 5;
    optional uint64 item_id        = 6;
    optional uint64 user_id        = 7;
    optional int32  purchase_count = 8;
    optional uint64 modified_time  = 9;
}

message CampaignPurchaseCount
{
    optional uint64 id             = 1;
    optional string region         = 2;
    optional uint64 promotion_id   = 3;
    optional uint32 promotion_type = 4;
    optional uint64 user_id        = 5;
    optional int32  purchase_count = 6;
    optional uint64 update_time    = 7;
}

message ItemLikedV2 {
    optional uint64 id         = 1;
    optional uint64 userid     = 2;
    optional uint64 shopid     = 3;
    optional uint64 itemid     = 4;
    optional uint32 status     = 5; // Enum: Status.LIKED_DELETE, Status.LIKED_NORMAL
    optional uint32 ctime      = 6;
    optional uint32 mtime      = 7;
    optional uint64 shopuserid = 8;
    optional string region     = 9;
}

message OSReserveKeywordsInfo {
    optional int64 id = 1;
    optional int64 userid = 2;
    optional int64 shopid = 3;
    optional string country = 4;
    optional string operator = 5;
    optional int64 ctime = 6;
    optional int64 mtime = 7;
    // Reserve keywords are for user search and Ads side, only need care about this field when consume GDS message
    optional bytes reserve_keywords = 8; // KeywordList
    // Pending keywords are for QC only
    optional bytes pending_keywords = 9; // KeywordList
    optional uint32 qc_status = 10; // Constant.OS_RESERVE_KEYWORDS_STATUS
    optional bytes extinfo = 11;
}

message KeywordList {
    repeated string keywords = 1;
}

message AccountAdminConfig {
    optional int64   configid = 1;
    optional string  config_type = 2;
    optional string  region = 3;
    optional int32   config_status = 4;
    optional string  config_data = 5;
    optional string  data_hash = 6;
    optional int32   create_time = 7;
    optional int32   update_time = 8;
}

// Maintain by promotion, should not use it outside promotion
message PromotionModel {
    optional uint64 id = 1;
    optional uint64 promotion_id = 2;
    optional uint64 shop_id = 3;
    optional uint64 item_id = 4;
    optional uint64 model_id = 5;
    optional string region = 6;
    optional int64 price_before_discount = 7;
    optional int64 promotion_price = 8;
    optional int64 rebate_price = 9;
    optional int32 promotion_stock = 10;
    optional uint32 model_status = 11; // refer PromotionModelStatus
    optional int32 user_item_limit = 12;
    optional uint32 start_time = 13;
    optional uint32 end_time = 14;
    optional uint32 promotion_type = 15; // refer PromotionType
    optional uint32 promotion_sub_type = 16; // refer PromotionSubType
    optional uint32 create_time = 17;
    optional uint32 update_time = 18;
    optional bytes ext_info = 19;
}

// Maintain by promotion, should not use it outside promotion
message ShopPromotionModel {
    optional uint64 id = 1;
    optional uint64 promotion_id = 2;
    optional uint64 shop_id = 3;
    optional uint64 item_id = 4;
    optional uint64 model_id = 5;
    optional string region = 6;
    optional uint32 model_status = 7; // refer PromotionModelStatus
    optional uint32 start_time = 8;
    optional uint32 end_time = 9;
    optional uint32 promotion_type = 10; // refer PromotionType
    optional uint32 promotion_sub_type = 11; // refer PromotionSubType
    optional uint32 create_time = 12;
    optional uint32 update_time = 13;
}