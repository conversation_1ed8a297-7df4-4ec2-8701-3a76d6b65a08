/** Status enum. */
export enum Status {
  ITEM_DELETE = 0,
  ITEM_NORMAL = 1,
  ITEM_REVIEWING = 2,
  ITEM_BANNED = 3,
  ITEM_INVALID = 4,
  ITEM_INVALID_HIDE = 5,
  ITEM_OFFENSIVE_HIDE = 6,
  ITEM_AUDITING = 7,
  ITEM_NORMAL_UNLIST = 8,
  SNAPSHOT_DELETE = 0,
  SNAPSHOT_NORMAL = 1,
  ORDER_DELETE = 0,
  ORDER_UNPAID = 1,
  ORDER_PAID = 2,
  ORDER_SHIPPED = 3,
  ORDER_COMPLETED = 4,
  ORDER_CANCEL = 5,
  ORDER_EXT_DELETE = 0,
  ORDER_EXT_UNPAID = 1,
  ORDER_EXT_PAID = 2,
  ORDER_EXT_SHIPPED = 3,
  ORDER_EXT_COMPLETED = 4,
  ORDER_EXT_INVALID = 6,
  ORDER_EXT_CANCEL_PROCESSING = 7,
  ORDER_EXT_CANCEL_COMPLETED = 8,
  ORDER_EXT_RETURN_PROCESSING = 9,
  ORDER_EXT_RETURN_COMPLETED = 10,
  ORDER_EXT_ESCROW_PAID = 11,
  ORDER_EXT_ESCROW_CREATED = 12,
  ORDER_EXT_ESCROW_PENDING = 13,
  ORDER_EXT_ESCROW_VERIFIED = 14,
  ORDER_EXT_ESCROW_PAYOUT = 15,
  ORDER_EXT_CANCEL_PENDING = 16,
  LOGISTICS_NOT_STARTED = 0,
  LOGISTICS_REQUEST_CREATED = 1,
  LOGISTICS_PICKUP_DONE = 2,
  LOGISTICS_PICKUP_RETRY = 3,
  LOGISTICS_PICKUP_FAILED = 4,
  LOGISTICS_DELIVERY_DONE = 5,
  LOGISTICS_DELIVERY_FAILED = 6,
  LOGISTICS_REQUEST_CANCELED = 7,
  LOGISTICS_COD_REJECTED = 8,
  LOGISTICS_READY = 9,
  LOGISTICS_INVALID = 10,
  LOGISTICS_LOST = 11,
  LOGISTICS_PENDING_ARRANGE = 12,
  OITEM_DELETE = 0,
  OITEM_UNRATED = 1,
  OITEM_RATED = 2,
  OITEM_RETURNED = 3,
  OITEM_RETURN_RATED = 4,
  OITEM_CANCEL = 5,
  PROMOTION_DISABLED = 0,
  PROMOTION_ENABLED = 1,
  PROMOTION_DELETED = 2,
  RETURN_DELETE = 0,
  RETURN_REQUESTED = 1,
  RETURN_ACCEPTED = 2,
  RETURN_CANCELLED = 3,
  RETURN_JUDGING = 4,
  RETURN_REFUND_PAID = 5,
  RETURN_CLOSED = 6,
  RETURN_PROCESSING = 7,
  RETURN_SELLER_DISPUTE = 8,
  REFUND_DELETE = 0,
  REFUND_CREATED = 1,
  REFUND_VERIFIED = 2,
  REFUND_PENDING = 3,
  REFUND_PAID = 4,
  REFUND_PAYOUT = 5,
  PAYMENT_DELETE = 0,
  PAYMENT_MATCHING = 1,
  PAYMENT_NO_MATCH = 2,
  PAYMENT_UNDERPAID = 3,
  PAYMENT_PAID = 4,
  PAYMENT_OVERPAID = 5,
  PAYMENT_WRONGPAID = 6,
  PAYMENT_FAILED = 7,
  PAYMENT_CHECKOUT_SPLITED = 8,
  PAYMENT_FRAUD = 9,
  SHOP_DELETE = 0,
  SHOP_NORMAL = 1,
  SHOP_BANNED = 2,
  SHOP_ENABLE_MAKE_OFFER = 0,
  SHOP_DISABLE_MAKE_OFFER = 1,
  ACCOUNT_DELETE = 0,
  ACCOUNT_NORMAL = 1,
  ACCOUNT_BANNED = 2,
  ACCOUNT_FROZEN = 3,
  MODEL_DELETE = 0,
  MODEL_NORMAL = 1,
  MODEL_UNAVAILABLE = 2,
  FOLLOW_DELETE = 0,
  FOLLOW_NORMAL = 1,
  LIKED_DELETE = 0,
  LIKED_NORMAL = 1,
  ADDRESS_DELETE = 0,
  ADDRESS_NORMAL = 1,
  ADDRESS_DEFAULT = 2,
  CARD_NORMAL = 0,
  CARD_DELETE = 1,
  CARD_DEFAULT = 2,
  SLIP_NOT_UPLOAD = 0,
  SLIP_UPLOADED = 1,
  SLIP_APPROVED = 2,
  OFFER_NONE = 0,
  OFFER_NEW = 1,
  OFFER_ACCEPT = 2,
  OFFER_REJECT = 3,
  OFFER_CANCEL = 4,
  MSG_DELETE = 0,
  MSG_NOT_ACKED = 1,
  MSG_ACKED = 2,
  CART_SHOP_HAS_ITEMS = 0,
  CART_SHOP_HAS_NO_ITEMS = 1,
  CART_ITEM_DELETED = 0,
  CART_ITEM_NORMAL = 1,
  CART_ITEM_UPDATED = 2,
  CART_ITEM_SELLER_INACTIVE = 3,
  CART_ITEM_SELLER_FROZEN = 4,
  CART_ITEM_MISSING_MODEL = 5,
  CART_ITEM_DELETED_MODEL = 6,
  CART_ITEM_NO_MODEL = 7,
  CART_ITEM_BLOCKED = 8,
  CART_ITEM_FLASH_SALE = 9,
  CART_ITEM_ADD_ON_DEAL_INVALID = 10,
  CART_ITEM_ADD_ON_DEAL_EXPIRED = 11,
  CMT_DELETE = 0,
  CMT_NORMAL = 1,
  CMT_VALID = 2,
  CMT_HIDDEN = 3,
  ACTIVITY_DELETE = 0,
  ACTIVITY_NORMAL = 1,
  ACTION_DELETE = 0,
  ACTION_NORMAL = 1,
  ACTION_GROUPED = 2,
  AUDIT_DELETE = 0,
  AUDIT_WAITING = 1,
  CAT_DELETE = 0,
  CAT_NORMAL = 1,
  CAT_DISABLE = 2,
  COLLECTION_DELETE = 0,
  COLLECTION_NORMAL = 1,
  COLLECTION_INACTIVE = 2,
  COLLECTION_ITEM_DELETE = 0,
  COLLECTION_ITEM_NORMAL = 1,
  COLLECTION_CLUSTER_DELETE = 0,
  COLLECTION_CLUSTER_NORMAL = 1,
  SEARCH_ITEM_INVISIABLE = 0,
  SEARCH_ITEM_VISIABLE = 1,
  SEARCH_ITEM_OFFENSIVEHIDE = 2,
  CONTACT_DELETE = 0,
  CONTACT_NORMAL = 1,
  CONTACT_PUSHED = 2,
  TRANS_HISTORY_NEW = 0,
  TRANS_HISTORY_FINISH = 1,
  TRANS_HISTORY_CANCEL = 2,
  TRANS_HISTORY_VERIFY = 3,
  TRANS_HISTORY_CREATED = 4,
  TRANS_HISTORY_PAYOUT = 5,
  VOUCHER_DELETED = 0,
  VOUCHER_NORMAL = 1,
  CBL_DELETE = 0,
  CBL_NORMAL = 1,
  SELLER_PROMOTION_NORMAL = 1,
  SELLER_PROMOTION_DELETED = 2,
  SELLER_PROMOTION_ITEM_NORMAL = 1,
  SELLER_PROMOTION_ITEM_DELETED = 2,
  SELLER_PROMOTION_ITEM_END = 3,
  DEVICE_NORMAL = 1,
  DEVICE_BAN = 2,
  DEVICE_CHECK_LIMIT = 1,
  DEVICE_CHECK_BAN = 2,
  DEVICE_CHECKOUT_LIMIT_COUNT = 3,
  DEVICE_CHECKOUT_LIMIT_AMOUNT = 4,
  WITHDRAWAL_STATUS_CREATED = 1,
  WITHDRAWAL_STATUS_VERIFIED = 2,
  WITHDRAWAL_STATUS_PAYOUT = 3,
  WITHDRAWAL_STATUS_COMPLETED = 4,
  WITHDRAWAL_STATUS_PENDING = 5,
  WITHDRAWAL_STATUS_CANCELLED = 6,
  TOPUP_UNPAID = 1,
  TOPUP_PAID = 2,
  TOPUP_INVALID = 3,
  TOPUP_ERROR = 4,
  COINRULE_DELETED = 0,
  COINRULE_NORMAL = 1,
  BA_CHECK_STATUS_NONE = 0,
  BA_CHECK_STATUS_SUBMITTED = 1,
  BA_CHECK_STATUS_SUCCEEDED = 2,
  BA_CHECK_STATUS_FAILED = 3,
  BA_CHECK_STATUS_RESUBMITTED = 4,
  CARD_PROMOTION_DELETED = 0,
  CARD_PROMOTION_NORMAL = 1,
  COMM_RULE_STATUS_DELETED = 0,
  COMM_RULE_STATUS_NORMAL = 1,
  CARD_TXN_FEE_RULE_STATUS_DELETED = 0,
  CARD_TXN_FEE_RULE_STATUS_NORMAL = 1,
  FLASH_SALE_STATUS_NORMAL = 1,
  FLASH_SALE_STATUS_DELETED = 0,
  FLASH_SALE_STATUS_SYSTEM_REJECT = 3,
  FLASH_SALE_ITEM_STATUS_DISABLED = 0,
  FLASH_SALE_ITEM_STATUS_NORMAL = 1,
  FLASH_SALE_ITEM_STATUS_DELETE = 2,
  FLASH_SALE_ITEM_STATUS_SYSTEM_REJECTED = 4,
  FLASH_SALE_ITEM_STATUS_MANUAL_REJECTED = 5,
  QC_STATUS_NORMAL = 1,
  QC_STATUS_DELETED = 0,
  ATTR_DELETED = 0,
  ATTR_NORMAL = 1,
  ATTR_VALUE_DELETED = -1,
  ATTR_VALUE_FAILED = 0,
  ATTR_VALUE_PASSED = 1,
  ATTR_VALUE_NORMAL = 2,
  ATTR_VALUE_PENDING = 3,
  ATTR_VALUE_NA = 4,
  SPU_DELETE = 0,
  SPU_NORMAL = 1,
  CATEGORY_FAILED = 0,
  CATEGORY_PASSED = 1,
  CATEGORY_UPDATED = 2,
  CATEGORY_NA = 3,
  ADDR_TAX_ORDER_DELETED = 0,
  ADDR_TAX_ORDER_NORMAL = 1,
  BUNDLE_DEAL_DELETED = 0,
  BUNDLE_DEAL_NORMAL = 1,
  PENALTY_HISTORY_HIDE = 0,
  PENALTY_HISTORY_NORMAL = 1,
  PENALTY_REASON_DELETE = 0,
  PENALTY_REASON_NORMAL = 1,
  SMID_STATUS_NOT_VERIFIED = 0,
  SMID_STATUS_VERIFYING = 1,
  SMID_STATUS_VERIFICATION_FAILED = 2,
  SMID_STATUS_VERIFIED = 3,
  BUNDLE_DEAL_USAGE_DELETED = 0,
  BUNDLE_DEAL_USAGE_NORMAL = 1,
  REFERRAL_RECORDED = 1,
  REFERRAL_SIGNED_UP = 2,
  REFERRAL_COMPLETED = 3,
  REFERRAL_FRAUD = 4,
  REFERRAL_PAID = 5,
  REFERRAL_COIN_REJECTED = 6,
  GB_GROUP_STARTED = 1,
  GB_GROUP_PENDING_PAYMENT = 4,
  GB_GROUP_DISMISSED = 5,
  GB_GROUP_FORMED = 7,
  VW_VOUCHER_DELETED = 0,
  VW_VOUCHER_NORMAL = 1,
  VW_VOUCHER_EXPIRED = 2,
  ITEM_PREVIEW_DELETED = 0,
  ITEM_PREVIEW_NORMAL = 1,
  ITEM_PREVIEW_FAILED = 2,
  COIN_REJECT_SUCCESS = 1,
  COIN_REJECT_FAILED = 2,
  COIN_REJECT_PENDING = 3,
  COIN_REJECT_ALLOW_RELEASE = 4,
  SLASH_PRICE_ITEM_DELETE = 0,
  SLASH_PRICE_ITEM_NORMAL = 1,
  SLASH_PRICE_ITEM_HIDDEN = 2,
  SLASH_PRICE_ACTIVITY_DELETE = 0,
  SLASH_PRICE_ACTIVITY_NORMAL = 1,
  SLASH_PRICE_COIN_NORMAL = 1,
  SLASH_PRICE_COIN_REWARDED = 2,
  OS_RESERVE_KEYWORDS_STATUS_DELETED = 0,
  OS_RESERVE_KEYWORDS_STATUS_NORMAL = 1,
}

/** GSTType enum. */
export enum GSTType {
  IGST = 1,
  CGST = 2,
  SGST = 3,
}

/** ItemType enum. */
export enum ItemType {
  SHOPEE_ITEM = 0,
  DIGITAL_PURCHASE_ITEM = 1,
}

/** CardTxnFeeRoundingMethod enum. */
export enum CardTxnFeeRoundingMethod {
  ROUNDING_OFF = 1,
  ROUNDING_UP = 2,
  ROUNDING_DOWN = 3,
}

/** TxnFeeType enum. */
export enum TxnFeeType {
  SELLER_TXN_FEE = 0,
  BUYER_TXN_FEE = 1,
}

/** TxnFeeRuleUserGroup enum. */
export enum TxnFeeRuleUserGroup {
  TXN_FEE_RULE_USER_DEFAULT = 0,
  TXN_FEE_RULE_CB_SELLER = 1,
  TXN_FEE_RULE_SPECIAL_GROUP = 2,
}

/** AccountWalletSetting enum. */
export enum AccountWalletSetting {
  DEFAULT_WALLET = 0,
  ENABLE_WALLET = 1,
  DISABLE_WALLET = 2,
  ENABLE_CB_WALLET = 3,
}

/** SellerPromotionSource enum. */
export enum SellerPromotionSource {
  SHOPEE = 0,
  SELLER = 1,
  FLASH_SALE = 2,
  SELLING_PRICE = 3,
}

/** SellerDiscountSource enum. */
export enum SellerDiscountSource {
  SELLER_DISCOUNT_SC = 0,
  SELLER_DISCOUNT_BE = 1,
}

/** CoinTransactionType enum. */
export enum CoinTransactionType {
  EARN_COMPLETE_ORDER = 1,
  EARN_CANCEL_ESCROW = 2,
  SPEND_CREATE_ORDER = 3,
  SPEND_INVALID_ORDER = 4,
  SPEND_CANCEL_ORDER = 5,
  SPEND_RETURN_ORDER = 6,
  EXPIRE = 7,
  ADMIN_UPDATE = 8,
  REWARD = 9,
  TOKEN = 10,
  REFERRAL_COIN = 11,
  REDEEM_VOUCHER = 12,
  DP_CREATE_ORDER = 13,
  DP_CANEL_ORDER = 14,
  DP_COMPLETED_ORDER = 15,
  RATE_ORDER_COIN = 16,
  SLASH_PRICE_COIN = 17,
  RATE_ORDER_COIN_RETRIEVED = 18,
  SPEND_CANCEL_PARCEL = 19,
  FOODY_COMPLETED_ORDER = 20,
  FOODY_REDEEM_VOUCHER = 21,
  FOODY_MANUAL_UPDATE = 22,
  AIRPAY_CREATE_TXN = 23,
  AIRPAY_CANCEL_TXN = 24,
  AIRPAY_REFUND_TXN = 25,
  AIRPAY_COMPLETED_TXN = 26,
  AIRPAY_REWARDS = 27,
  SELLER_COIN = 28,
}

/** CoinCheckShopFlag enum. */
export enum CoinCheckShopFlag {
  SPEND = 1,
  EARN = 2,
}

/** WithdrawalType enum. */
export enum WithdrawalType {
  NO_SPLIT = 1,
  SPLIT_ROOT = 2,
  SPLIT_SUB = 3,
}

/** WithdrawalSource enum. */
export enum WithdrawalSource {
  ONETIME_SELLER = 1,
  ONETIME_ADMIN = 2,
  RECURRING = 3,
}

/** WithdrawalTargetType enum. */
export enum WithdrawalTargetType {
  BANK_ACCOUNT = 1,
  SHOPEEPAY_ACCOUNT = 2,
}

/** TransactionType enum. */
export enum TransactionType {
  ESCROW_VERIFIED_ADD = 101,
  ESCROW_VERIFIED_MINUS = 102,
  OFFLINE_ESCROW_ADD = 103,
  WITHDRAWAL_CREATED = 201,
  WITHDRAWAL_COMPLETED = 202,
  WITHDRAWAL_CANCELLED = 203,
  REFUND_VERIFIED_ADD = 301,
  AUTO_REFUND_ADD = 302,
  FOODY_REFUND_ADD = 303,
  ADJUSTMENT_ADD = 401,
  ADJUSTMENT_MINUS = 402,
  SHOPEE_BUDDY_ADD = 403,
  PAID_ADS_CHARGE = 450,
  PAID_ADS_REFUND = 451,
  FAST_ESCROW_DISBURSE = 452,
  FAST_ESCROW_PRINCIPAL_DEDUCT = 453,
  FAST_ESCROW_INTEREST_DEDUCT = 454,
  AFFILIATE_ADS_SELLER_FEE = 455,
  AFFILIATE_ADS_SELLER_FEE_REFUND = 456,
  INFLUENCER_CREDIT = 457,
  FAST_ESCROW_DEDUCT = 458,
  SHOPEE_WALLET_PAY = 501,
  SPM_DEDUCT = 502,
  APM_DEDUCT = 503,
  SPM_REFUND_ADD = 504,
  APM_REFUND_ADD = 505,
  TOPUP_SUCCESS = 601,
  TOPUP_FAILD = 602,
  DP_REFUND_VERIFIED_ADD = 701,
  SPM_DEDUCT_DIRECT = 801,
  SPM_DISBURSE_ADD = 802,
}

/** TransactionListType enum. */
export enum TransactionListType {
  MONEY_INOUT = 1,
  MONEY_IN = 2,
  MONEY_OUT = 3,
}

/** LogisticsShopFlag enum. */
export enum LogisticsShopFlag {
  ENABLE_SHOPEE_SUPPORTED = 1,
  SHOP_ENABLE_SHOPEE_SUPPORTED = 2,
  SHOW_FREE_SHIPPING = 4,
}

/** PreferredDeliveryOption enum. */
export enum PreferredDeliveryOption {
  DELIVERY_ANYTIME = 0,
  DELIVERY_OFFICE_HOUR = 1,
}

/** ItemFlags enum. */
export enum ItemFlags {
  IS_FAKE_ITEM = 1,
  FREE_SHIPPING = 2,
  SEO_DESCRIPTION = 4,
  INTEGRATED_FREE_SHIPPING = 8,
  OFFLINE_DEAL = 16,
  NO_SEARCHABLE = 32,
  IS_HIDDEN = 64,
  IS_SYSTEM_UNLIST = 128,
  IS_USER_UNLIST = 256,
  IS_PREVIEW = 512,
  IS_PRICE_MASK = 1024,
  HAS_VIRTUAL_SKU = 2048,
  HAS_CHILD_SKU = 4096,
  VIRTUAL_GOODS = 8192,
  COD_FILTER = 65536,
  CREDIT_CARD_FILTER = 131072,
  NON_CC_INSTALLMENT_FILTER = 262144,
  CC_INSTALLMENT_FILTER = 524288,
  SERVICE_BY_SHOPEE_1 = 33554432,
  SERVICE_BY_SHOPEE_2 = 67108864,
  SERVICE_BY_SHOPEE_3 = 134217728,
  BADGE_TYPE_24H = 268435456,
  BADGE_TYPE_4H = 536870912,
}

/** RequestSource enum. */
export enum RequestSource {
  FROM_UNKNOWN = 1,
  FROM_APP = 2,
  FROM_API = 3,
  FROM_SELLER_CENTER_SINGLE = 4,
  FROM_SELLER_CENTER_MASS = 5,
  FROM_ADMIN = 6,
}

/** MyIncomeType enum. */
export enum MyIncomeType {
  ACTUAL_SHIPPING_COST = 1,
  SHIPPING_FEE_REBATE = 2,
  SHIPPING_FEE_PAIDBY_BUYER = 3,
  REBATE_FEE = 4,
  SHIPPING_FEE_SELLER_DISCOUNT = 5,
}

/** UserAgentType enum. */
export enum UserAgentType {
  IOS = 1,
  ANDROID = 2,
  WEB = 4,
}

/** LogisticsOrderFlag enum. */
export enum LogisticsOrderFlag {
  HAS_TRACKING = 2,
  ESCROW_RELEASE_INCLUDES_SHIPPING = 4,
  MANUAL_CONSIGNMENT = 1,
  AUTO_CONSIGNMENT = 8,
  ARRANGE_CONSIGNMENT = 16,
  IS_SELF_COLLECT = 8192,
  BUYER_PAID_FLAT_SHIPPING_FEE = 32,
  DISCOUNT_PERCENTAGE = 64,
  DISCOUNT_FIXED_DEDUCTION = 128,
  REBATE_SHIPPING_FEE = 256,
  REBATE_SHIPPING_FIXED_FEE = 512,
  DISABLE_SHIPPING_REBATE = 1024,
  SHOPEE_GENERATED_TRACKING_NUMBER = 2048,
  SELLER_COVER_SHIPPING_FEE = 4096,
  HOLD_ESCROW = 16384,
  IS_RAPID_SLA = 268435456,
  SUPPORT_CROSS_BORDER = 536870912,
  DISCOUNT_BUYER_LOCATION_CHECK = 1073741824,
}

/** OrderDetailFlag enum. */
export enum OrderDetailFlag {
  SELLER_SEEN_ORDER_DETAIL_AFTER_SHIPPING_READY = 1,
  SERVICE_BY_SHOPEE = 2,
  BLOCK_MANUAL_SHIPPING = 4,
  IS_WELCOME_PACKAGE = 8,
  FULFILLED_BY_SHOPEE = 16,
  FULFILLED_BY_CB_SELLER = 32,
  FULFILLED_BY_LOCAL_SELLER = 64,
  IS_SIP_CB = 128,
  IS_SIP_PRIMARY = 256,
  IS_SIP_AFFILIATED = 512,
  SHOP_SUPPORT_RECEIPT = 65536,
  FAST_ESCROW = 131072,
}

/** OrderDetailedStatusType enum. */
export enum OrderDetailedStatusType {
  ORDER_DETAIL_CREATED = 1,
  ORDER_DETAIL_SHIPPING_CONFIRMED = 2,
  ORDER_DETAIL_PROCESSING = 3,
  ORDER_DETAIL_SHIPPED = 4,
  ORDER_DETAIL_RECEIVED = 5,
  ORDER_DETAIL_CANCELED = 6,
  ORDER_DETAIL_NOTIFY_SHIP = 7,
}

/** CheckoutPaymentFlag enum. */
export enum CheckoutPaymentFlag {
  REFUND_BY_API = 1,
  NEED_BANK_ACCOUNT = 2,
  IS_CREDIT_CARD = 4,
  IS_FREE_SHIPPING = 8,
  IS_INSTALLMENT = 16,
  MANUAL_ESCROW = 32,
  IS_SAFE_INTEGRATED = 64,
  SUPPORT_PARTIAL_REFUND = 128,
  IS_SHOPEE_LITE = 256,
}

/** PaymentMethod enum. */
export enum PaymentMethod {
  PAY_NONE = 0,
  PAY_CYBERSOURCE = 1,
  PAY_BANK_TRANSFER = 2,
  PAY_OFFLINE_PAYMENT = 3,
  PAY_IPAY88 = 4,
  PAY_FREE = 5,
  PAY_COD = 6,
  PAY_ESUN = 7,
  PAY_BILL_PAYMENT = 8,
  PAY_INDOMARET = 13,
  PAY_KREDIVO = 14,
  PAY_NICEPAY_CC = 15,
  PAY_ESUN_CB = 16,
  PAY_IPAY88_CC = 17,
  PAY_AIRPAY_CC = 18,
  PAY_BCA_ONE_KLIK = 19,
  PAY_SHOPEE_WALLET = 20,
  PAY_AKULAKU = 21,
  PAY_STRIPE_CC = 22,
  PAY_AIRPAY_CC_INSTALLMENT = 23,
  PAY_SHOPEE_CREDIT = 24,
  PAY_ALFAMART = 25,
  PAY_CYBERSOURCE_INSTALLMENT = 26,
  PAY_SHOPEE_WALLET_V2 = 27,
  PAY_AIRPAY_WALLET_V2 = 28,
  PAY_VN_IBANKING = 29,
  PAY_AIRPAY_GIRO = 30,
  PAY_SIPP_VA = 31,
  PAY_WIRECARD_INSTALLMENT = 32,
  PAY_JKO_PAY = 33,
  PAY_MOLPAY_CASH = 34,
  PAY_JKO_COD = 35,
  PAY_JKO_BT = 36,
  PAY_MOLPAY_IBANKING = 37,
  PAY_EBANX_BOLETO = 38,
  PAY_EBANX_CC = 39,
  PAY_EBANX_IBANKING = 40,
  PAY_SIPP_MIXED_VA = 41,
  PAY_MPGS_CC = 42,
  PAY_WECHAT_PAY = 43,
  PAY_CF_UPI = 44,
  PAY_CF_CC = 45,
  PAY_CF_IBANKING = 46,
  PAY_PAY_AT_SHOP = 47,
  PAY_MAYBANK = 48,
  PAY_GOOGLE_PAY = 49,
  PAY_AIRPAY_IBANKING_V2 = 50,
  PAY_UOB_PAYNOW = 51,
  PAY_RPP_ONLINE_BANKING = 52,
  PAY_SIPP_DIRECT_DEBIT = 53,
  PAY_CORP_BILLING = 54,
  PAY_EBANX_SPEI = 55,
  PAY_OXXO = 56,
  PAY_SIPP_CC = 57,
  PAY_RPP_DIRECT_DEBIT = 58,
  PAY_SEABANK_DIRECT_DEBIT = 59,
  PAY_BALOTO = 60,
  PAY_EFECTY = 61,
  PAY_PSE = 62,
  PAY_SERVIPAG = 63,
  PAY_BPI_GIRO = 64,
  PAY_WEBPAY = 65,
}

/** VoucherPaymentType enum. */
export enum VoucherPaymentType {
  VPT_CREDIT_CARD_NO_INSTALLMENT = 1,
  VPT_CREDIT_CARD_INSTALLMENT = 2,
  VPT_COD = 3,
}

/** CompareMethod enum. */
export enum CompareMethod {
  EQUAL = 1,
  LE = 2,
  GE = 3,
  LESS = 4,
  GREATER = 5,
  IN = 6,
  NOTIN = 7,
}

/** PaymentStatus enum. */
export enum PaymentStatus {
  NONE = 0,
  SUCCESS = 1,
  BANK_TRANSFER_INIT = 100,
  BANK_TRANSFER_VERIFYING = 101,
  BANK_TRANSFER_REINIT = 102,
  BANK_TRANSFER_PENDING = 103,
  OFFLINE_PAYMENT = 200,
}

/** EscrowPaymentMethod enum. */
export enum EscrowPaymentMethod {
  BANK_TRANSFER = 1,
  SEVEN_ELEVEN = 2,
}

/** StatsType enum. */
export enum StatsType {
  ORDERS = 0,
  INCOME = 1,
  VISITS = 2,
  SEARCH = 3,
}

/** AuditType enum. */
export enum AuditType {
  ITEM_NEW = 1,
  ITEM_EDIT = 2,
  ITEM_REPORT = 3,
  ITEM_DEL = 4,
  USER_REPORT = 5,
  BANKACC = 6,
  CHECKOUT = 7,
  REFUND = 8,
  ESCROW_RELEASE = 9,
  PAYMENT_STATUS = 10,
  MANUAL = 11,
  ITEM_STATUS = 12,
  SHOP_UPDATE = 13,
  MODEL_EDIT = 14,
  MODEL_ADD = 15,
  MODEL_DEL = 16,
  VIDEO_ADD = 17,
  VIDEO_EDIT = 18,
  VIDEO_DELETE = 19,
  ACCOUNT_UPDATE = 20,
  REFERRAL_UPDATE = 21,
  ITEM_UNLIST = 22,
  SLASH_PRICE_RULE_UPDATE = 23,
  ITEM_LICENSE = 24,
  SLASH_PRICE_ITEM_CREATED = 25,
  SLASH_PRICE_ITEM_UPDATED = 26,
  SLASH_PRICE_ITEM_DELETED = 27,
  BRAND_NEW = 28,
  BRAND_EDIT = 29,
  BRAND_DELETE = 30,
  JKO_SELLER_UPDATE = 31,
  JKO_BUYER_UPDATE = 32,
  ITEM_FLAG_EDIT = 33,
}

/** ItemOption enum. */
export enum ItemOption {
  FIRSTITEM_DISMISSED = 1,
  INVALID_DISMISSED = 2,
}

/** OrderType enum. */
export enum OrderType {
  UNKNOWN = 0,
  SIMPLE = 1,
  ESCROW = 2,
}

/** BankaccVerified enum. */
export enum BankaccVerified {
  UNSUBMIT = 0,
  REQUESTED = 1,
  APPROVED = 2,
  REJECTED = 3,
  CHECKED = 4,
  BANNED = 5,
}

/** EscrowOption enum. */
export enum EscrowOption {
  ESCROW_OFF = 0,
  ESCROW_ON = 1,
}

/** TransType enum. */
export enum TransType {
  OTHER_ORDER = 0,
  ESCROW_ORDER = 1,
  WITHDRAW = 2,
}

/** ActivityType enum. */
export enum ActivityType {
  FOLLOW_YOU = 0,
  LIKE_YOUR_ITEM = 1,
  YOUR_LIKEITEM_UPDATED = 2,
  AT_YOU_IN_COMMENT = 3,
  COMMENT_YOUR_ITEM = 4,
  CONTACT_REGISTERED = 5,
  ITEM_RATED = 6,
  CUSTOMIZED_ACTIVITY = 7,
  AT_YOU_IN_FEED_CMT = 8,
  COMMENT_YOUR_FEED = 9,
}

/** ProductListWeightType enum. */
export enum ProductListWeightType {
  BOOST = 0,
  ARRIVAL = 1,
  SOCIAL = 2,
  SALE = 3,
  RANDOM = 4,
  SOCIAL_N = 5,
  SALE_M = 6,
  SHOP_SCORE = 7,
  ITEM_RATING = 8,
}

/** PredefinedLabelFlag enum. */
export enum PredefinedLabelFlag {
  UNDER_REVIEW = 1001,
  BLOCK_BUYER_PLATFORM_OTHERS = 1000000,
  BLOCK_BUYER_PLATFORM_IOS_WEB = 1000001,
  BLOCK_BUYER_PLATFORM_IOS_APP = 1000002,
  BLOCK_BUYER_PLATFORM_ANDROID_WEB = 1000003,
  BLOCK_BUYER_PLATFORM_ANDROID_APP = 1000004,
  BLOCK_BUYER_PLATFORM_PC_MALL = 1000005,
}

/** ActivityUpdateType enum. */
export enum ActivityUpdateType {
  ITEM_DELETED = 0,
  ITEM_OUTOF_STOCK = 1,
  ITEM_EDITED = 2,
}

/** ActionRedirctType enum. */
export enum ActionRedirctType {
  REDIRECT_NONE = 0,
  REDIRECT_ORDERS_DETAIL = 1,
  REDIRECT_MY_PRODUCTS = 2,
  REDIRECT_MY_INCOME = 3,
  REDIRECT_ORDERS_RETURNREFUND = 4,
  REDIRECT_ORDERS_CANCEL = 5,
  REDIRECT_OUTOF_STOCK = 6,
  REDIRECT_PURE_REFUND = 8,
  REDIRECT_NEW_WEB_PAGE = 10,
  REDIRECT_UPLOAD_RECEIPT_PAGE = 11,
  REDIRECT_SHOPING_CART = 12,
  REDIRECT_BUNCH_ORDERS_DETAIL = 13,
  REDIRECT_ORDER_CHAT_PAGE = 16,
  REDIRECT_RELATED_PRODUCT_PAGE = 18,
  REDIRECT_CREDIT_CARD_PAYMENT_PAGE = 19,
  REDIRECT_MY_WALLET = 20,
  REDIRECT_EDIT_SHOP_PROFILE = 21,
  REDIRECT_APP_PATH = 22,
  REDIRECT_MY_ACCOUNT = 23,
  REDIRECT_APP_ROUTE = 24,
  REDIRECT_REACTNATIVE_PATH = 25,
  REDIRECT_EXTERNAL_WEBPAGE = 26,
}

/** ItemCondition enum. */
export enum ItemCondition {
  NOT_SET = 0,
  NEW_WITH_TAGS = 1,
  NEW_WITHOUT_TAGS = 2,
  NEW_WITH_DEFECTS = 3,
  USED = 4,
  NEW_OTHERS = 5,
  USED_LIKE_NEW = 6,
  USED_GOOD = 7,
  USED_ACCEPTABLE = 8,
  USED_WITH_DEFECTS = 9,
}

/** NotiMethod enum. */
export enum NotiMethod {
  NM_ALL = 1,
  NM_AR = 2,
  NM_ACTIVITY = 4,
  NM_EMAIL = 8,
  NM_SMS = 16,
  NM_PUSH = 32,
  NM_WEB_PUSH = 64,
  NM_INAPP = 128,
}

/** NotiGroupType enum. */
export enum NotiGroupType {
  GROUPBY_CHECKOUT = 1,
  GROUPBY_ORDER = 2,
  GROUPBY_TRANS = 3,
  GROUPBY_TOPUP = 4,
  GROUPBY_DP_ORDER = 5,
  GROUPBY_RETURN_ID = 6,
}

/** PNOption enum. */
export enum PNOption {
  NOTI_ALL = 1,
  NOTI_ACTION_REQUIRED = 2,
  NOTI_ACTIVITY = 4,
  NOTI_CHATS = 8,
  NOTI_STOCK = 16,
  NOTI_GROUP_NOTI_OFF = 32,
  NOTI_SHOPEE_PROMOTION = 64,
  NOTI_RATING = 128,
  EMAIL_ALL = 256,
  EMAIL_ORDER_UPDATES = 512,
  EMAIL_LIST_UPDATES = 1024,
  EMAIL_NEWS_LETTER = 2048,
  EMAIL_PERSONALISED = 4096,
  NOTI_WALLET = 8192,
  NOTI_ADS = 16384,
  PN_FEED_COMMENTED = 32768,
  PN_FEED_LIKED = 65536,
  PN_FEED_MENTIONED = 131072,
  NOTI_SHOPEEFOOD = 262144,
  NOTI_SHOPEEPAY_TRANSACTIONAL_UPDATES = 524288,
  NOTI_FEED = 1048576,
  NOTI_LIVESTREAM = 2097152,
  NOTI_GAMES = 4194304,
  NOTI_SHOPEEPAY_LATER = 8388608,
  NOTI_SELLER_ORDER_UPDATES = 16777216,
  NOTI_SELLER_RETURN_REFUND = 33554432,
  NOTI_SELLER_WALLET = 67108864,
  NOTI_SELLER_MARKET_CENTER = 134217728,
  NOTI_SELLER_PERFORMANCE = 268435456,
}

/** ActionCategory enum. */
export enum ActionCategory {
  ACTION_PROMOTIONS = 1,
  ACTION_LISTING_UPDATES = 2,
  ACTION_ACTIVITY = 3,
  ACTION_ORDER_UPDATES = 4,
  ACTION_CHAT = 5,
  ACTION_IMPT_UPDATES = 6,
  ACTION_RATING = 7,
  ACTION_WALLET = 8,
  ACTION_ADS = 9,
  ACTION_FEED = 10,
  ACTION_NOW = 11,
  ACTION_DISCUSS_MESSAGES = 12,
  ACTION_SHOPEE_PAYLATER = 13,
  ACTION_NOMINATIONS = 14,
  ACTION_FBS_PRD_NOMINATION_UPDATES = 15,
  ACTION_FBS_INBOUND_UPDATES = 16,
  ACTION_SELLER_ORDER_UPDATES = 17,
  ACTION_SELLER_RATING = 18,
  ACTION_SELLER_RETURN_REFUND = 19,
  ACTION_SELLER_LISTING = 20,
  ACTION_SELLER_WALLET = 21,
  ACTION_SELLER_MARKETING_CENTRE = 22,
  ACTION_SELLER_PERFORMANCE = 23,
  ACTION_SELLER_SHOPEE_UPDATES = 24,
  ACTION_DISCUSS_MESSAGES_BUYER_MIGRATION = 25,
  ACTION_DISCUSS_MESSAGES_SELLER_MIGRATION = 26,
  ACTION_WALLET_SELLER_LISTING_MIGRATION = 27,
  ACTION_LISTING_SELLER_WALLET_MIGRATION = 28,
  ACTION_LISTING_PERF_MIGRATION = 29,
  ACTION_LISTING_SHOPEE_UPDATE_MIGRATION = 30,
  ACTION_WALLET_SHOPEE_UPDATE_MIGRATION = 31,
  ACTION_GAMES = 32,
  ACTION_SHOPEE_FOOD_UPDATES = 33,
  ACTION_SHOPEE_PAY_TRANSACTIONAL_UPDATES = 34,
  ACTION_SHOPEE_FOOD_BUYER_UPDATES = 35,
  ACTION_SHOPEE_FOOD_DRIVER_UPDATES = 36,
}

/** ContactAcctype enum. */
export enum ContactAcctype {
  PHONE = 1,
  FACEBOOK = 2,
  EMAIL = 3,
  PHONE_AND_EMAIL = 4,
  BEETALK = 5,
}

/** CheckoutBuyerCancelReason enum. */
export enum CheckoutBuyerCancelReason {
  BUYER_CANCEL_CHECKOUT_REASON_SELLER_UNRESPONSIVE = 1,
  BUYER_CANCEL_CHECKOUT_REASON_SELLER_REQUEST = 2,
  BUYER_CANCEL_CHECKOUT_REASON_MODIFY_ORDER = 3,
  BUYER_CANCEL_CHECKOUT_REASON_BAD_PRODUCT_REVIEWS = 4,
  BUYER_CANCEL_CHECKOUT_REASON_ORDER_TAKES_TOO_LONG_TO_SHIP = 5,
  BUYER_CANCEL_CHECKOUT_REASON_UNTRUSTWORTHY_SELLER = 6,
  BUYER_CANCEL_CHECKOUT_REASON_OTHERS = 7,
  BUYER_CANCEL_CHECKOUT_REASON_UPDATE_VOUCHER_CODE = 8,
  BUYER_CANCEL_CHECKOUT_REASON_CHANGE_OF_MIND = 9,
  BUYER_CANCEL_CHECKOUT_REASON_UPDATE_DELIVERY_ADDRESS = 10,
}

/** OrderBuyerCancelReason enum. */
export enum OrderBuyerCancelReason {
  BUYER_CANCEL_REASON_UPDATE_DELIVERY_ADDRESS = 501,
  BUYER_CANCEL_REASON_UPDATE_VOUCHER_CODE = 502,
  BUYER_CANCEL_REASON_MODIFY_ORDER = 503,
  BUYER_CANCEL_REASON_PAYMENT_PROCEDURE_TROUBLESOME = 504,
  BUYER_CANCEL_REASON_FOUND_CHEAPER_ALTERNATIVE = 505,
  BUYER_CANCEL_REASON_CHANGE_OF_MIND = 506,
  BUYER_CANCEL_REASON_OTHERS = 507,
  BUYER_CANCEL_REASON_APPROVAL_REJECTED = 508,
  BUYER_CANCEL_REASON_CANNOT_PLACE_ORDER = 509,
}

/** OrderCancelReason enum. */
export enum OrderCancelReason {
  CANCEL_REASON_NONE = 0,
  CANCEL_REASON_OUT_OF_STOCK = 1,
  CANCEL_REASON_CUSTOMER_REQUEST = 2,
  CANCEL_REASON_UNDELIVERABLE_AREA = 3,
  CANCEL_REASON_CANNOT_SUPPORT_COD = 4,
  CANCEL_REASON_LOST_PARCEL = 5,
  CANCEL_REASON_GAME_COMPLETED = 6,
  CANCEL_REASON_SYSTEM_UNPAID = 100,
  CANCEL_REASON_SYSTEM_UNDERPAID = 101,
  CANCEL_REASON_SYSTEM_PAYMENT_REJECTED = 102,
  CANCEL_REASON_LOGISTICS_REQUEST_CANCELED = 200,
  CANCEL_REASON_LOGISTICS_PICKUP_FAILED = 201,
  CANCEL_REASON_LOGISTICS_DELIVERY_FAILED = 202,
  CANCEL_REASON_LOGISTICS_COD_REJECTED = 203,
  CANCEL_REASON_BACKEND_LOGISTICS_NOT_STARTED = 204,
  CANCEL_REASON_TWS_CANCEL = 205,
  CANCEL_REASON_BACKEND_ESCROW_TERMINATED = 300,
  CANCEL_REASON_BACKEND_INACTIVE_SELLER = 301,
  CANCEL_REASON_BACKEND_SELLER_DID_NOT_SHIP = 302,
  CANCEL_REASON_ORDER_NOT_REACH_WAREHOUSE = 303,
  CANCEL_REASON_RULE_ENGINE_AUTO_CANCEL = 400,
  CANCEL_REASON_CUSTOM_KYC_EXPIRY = 701,
  CANCEL_REASON_CUSTOM_KYC_CANCEL = 702,
  CANCEL_REASON_LOGISTICS_ISSUE = 801,
  CANCEL_REASON_FRAUD_CASE = 901,
  CANCEL_REASON_CUSTOM_CORPBILLING_EXPIRE = 1001,
  CANCEL_REASON_CUSTOM_CORPBILLING_BLACKLIST = 1002,
}

/** ReturnReason enum. */
export enum ReturnReason {
  RETURN_REASON_NONE = 0,
  RETURN_REASON_NONRECEIPT = 1,
  RETURN_REASON_WRONG_ITEM = 2,
  RETURN_REASON_ITEM_DAMAGED = 3,
  RETURN_REASON_DIFF_DESC = 4,
  RETURN_REASON_MUTUAL_AGREE = 5,
  RETURN_REASON_OTHER = 6,
  RETURN_REASON_USED = 7,
  RETURN_REASON_ITEM_WRONGDAMAGED = 101,
  RETURN_REASON_CHANGE_MIND = 102,
  RETURN_REASON_ITEM_MISSING = 103,
  RETURN_REASON_EXPECTATION_FAILED = 104,
  RETURN_REASON_ITEM_FAKE = 105,
  RETURN_REASON_PHYSICAL_DMG = 106,
  RETURN_REASON_FUNCTIONAL_DMG = 107,
}

/** ReturnDisputeReason enum. */
export enum ReturnDisputeReason {
  REJECT_NONRECEIPT = 1,
  REJECT_OTHER = 2,
  NOT_RECEIVED = 3,
}

/** ReturnFlag enum. */
export enum ReturnFlag {
  RETURN_IS_OFFICIAL_SHOP = 1,
  RETURN_IS_AUTO_RETURN_JUDGING = 2,
  RETURN_IS_ELIGIBLE_FOR_SELLER_COMPENSATION_AFTER_RETURN_ACCEPTED = 4,
  RETURN_IS_ELIGIBLE_FOR_NEGOTIATION_DURING_RETURN_PROCESSING = 8,
}

/** SellerCompensationStatus enum. */
export enum SellerCompensationStatus {
  COMPENSATION_NOT_APPLICABLE = 0,
  COMPENSATION_PENDING_REQUEST = 1,
  COMPENSATION_NOT_REQUIRED = 2,
  COMPENSATION_REQUESTED = 3,
  COMPENSATION_APPROVED = 4,
  COMPENSATION_REJECTED = 5,
  COMPENSATION_CANCELLED = 6,
}

/** WalletPaymentStatus enum. */
export enum WalletPaymentStatus {
  WALLET_PAYMENT_SUCCESSFUL = 1,
  WALLET_PAYMENT_FAILURE = 2,
}

/** NegotitationStatus enum. */
export enum NegotitationStatus {
  NEGOTIATION_NOT_APPLICABLE = 0,
  NEGOTIATION_INITIAL_STAGE = 1,
  NEGOTIATION_PENDING = 2,
  NEGOTIATION_NOT_INITIATED = 3,
  NEGOTIATION_ONGOING = 4,
  NEGOTIATION_TERMINATED = 5,
}

/** ArchiveFlag enum. */
export enum ArchiveFlag {
  BUYER_ARCHIVE = 1,
  SELLER_ARCHIVE = 2,
}

/** BankTransferOption enum. */
export enum BankTransferOption {
  NORMAL = 1,
  ATM_PAYMENT = 2,
  INTERNET_BANKING = 3,
  ATM_MOBILE = 4,
  ATM_INTERNET = 5,
}

/** ChatMsgOpt enum. */
export enum ChatMsgOpt {
  MSG_OPT_NORMAL = 0,
  MSG_OPT_AUTO_REPLY = 1,
  MSG_OPT_BLOCKED = 2,
  MSG_OPT_USER_CHAT = 4,
  MSG_OPT_WEB_CHAT = 8,
  MSG_OPT_CENSORED_BLACKLIST = 16,
  MSG_OPT_CENSORED_WHITELIST = 32,
  MSG_OPT_IGNORE_UNREAD_FOR_RECEIVER = 64,
  MSG_OPT_IGNORE_UNREAD_FOR_SENDER = 128,
  MSG_OPT_INVISIBLE_FOR_RECEIVER = 256,
  MSG_OPT_INVISIBLE_FOR_SENDER = 512,
  MSG_OPT_ADS = 1024,
  MSG_OPT_FAQ_SESSION = 2048,
  MSG_OPT_TRIGGER_FAQ = 4096,
  MSG_OPT_SOCIAL_MESSAGE = 8192,
  MSG_OPT_OFFWORK_AUTOREPLY = 16384,
  MSG_OPT_CANCELORDER_WARNING_SENT = 32768,
  MSG_OPT_CANCELORDER_WARNING_RETRACT = 65536,
  MSG_OPT_CHATBOT_SESSION = 131072,
  MSG_OPT_TRIGGER_CHATBOT = 262144,
  MSG_OPT_PRIVATE_MESSAGE = 524288,
  MSG_OPT_SILENT_FOR_SENDER = 1048576,
}

/** ShopCollectionType enum. */
export enum ShopCollectionType {
  SCT_CUSTOMIZED = 1,
  SCT_CATEGORY = 2,
  SCT_HASHTAG = 3,
  SCT_NEW_ARRIVAL = 4,
  SCT_ON_SALE = 5,
}

/** CmtOpt enum. */
export enum CmtOpt {
  CMT_OPT_AUTO_RATE = 1,
  CMT_OPT_VALID_ITEM_RATING = 2,
}

/** CmtFilter enum. */
export enum CmtFilter {
  CMT_FILTER_CONTEXT = 1,
  CMT_FILTER_IMAGE = 2,
  CMT_FILTER_VIDEO = 4,
}

/** FulfillmentRateFlag enum. */
export enum FulfillmentRateFlag {
  FULFILLMENT_NORMAL = 1,
  FULFILLMENT_WARNING = 2,
  FULFILLMENT_MANUAL_DISABLE_WARNING = 3,
  FULFILLMENT_DISPLAY_WARN = 4,
  FULFILLMENT_DISPLAY_PUNISH = 5,
}

/** LateShipmentRateFlag enum. */
export enum LateShipmentRateFlag {
  LATE_SHIPMENT_NORMAL = 1,
  LATE_SHIPMENT_WARNING = 2,
  LATE_SHIPMENT_PUNISH = 3,
}

/** VoucherUseType enum. */
export enum VoucherUseType {
  PRIVATE = 0,
  PUBLIC = 1,
}

/** WebOption enum. */
export enum WebOption {
  NEVER_SHOW_FEEDS_BANNER = 1,
  NEVER_ALERT_PRIVACY_MSG = 2,
  NEVER_SHOW_BOOST_MSG = 4,
  NEVER_SHOW_WALLET_BANNER = 8,
  KYC_CONSENT = 16,
  KYC_SIMPLE = 32,
  KYC_FULL = 64,
}

/** VcodeActionType enum. */
export enum VcodeActionType {
  SEND_SMS_OTP = 1,
  SEND_VOICE_OTP = 2,
  SEND_WHATS_APP_OTP = 3,
  SEND_EMAIL_OTP = 4,
}

/** ShopMetricType enum. */
export enum ShopMetricType {
  LATE_SHIPMENT_RATE = 1,
  ONTIME_SHIPMENT_RATE = 2,
  NON_FULFILLMENT_RATE = 3,
  AVG_PREPARATION_TIME = 4,
  ITEM_BANNED_COUNT = 5,
  ITEM_SEVERE_VIOLATE = 9,
  ITEM_NORMAL_VIOLATE = 10,
  CHAT_RESPONSE_RATE = 11,
  PREORDER_RATE = 12,
}

/** ShopMetricExtraFlag enum. */
export enum ShopMetricExtraFlag {
  SHOP_METRIC_FIRST_THRESHOLD = 1,
  SHOP_METRIC_SECOND_THRESHOLD = 2,
  SHOP_METRIC_PUNISH_SECOND_THRESHOLD = 1024,
}

/** CategoryReturnType enum. */
export enum CategoryReturnType {
  RETURN_DEFAULT = 0,
  RETURN_NO_AUTO_PASS = 1,
}

/** AddressTaxFlag enum. */
export enum AddressTaxFlag {
  ADDR_FLAG_NONE = 0,
  ADDR_FLAG_TAXED = 1,
}

/** ShopPenaltyType enum. */
export enum ShopPenaltyType {
  PENALTY_TYPE_FLUSH = -1,
  PENALTY_TYPE_MANUAL = 0,
  PENALTY_TYPE_HIGH_NON_FULFILMENT = 1,
  PENALTY_TYPE_HIGH_LATE_SHIPMENT = 2,
  PENALTY_TYPE_PROHIBITED_LISTING = 3,
  PENALTY_TYPE_COUNTERFEIT = 4,
  PENALTY_TYPE_SPAM = 5,
  PENALTY_TYPE_OFFICIAL_SHOP_FRAUD = 6,
  PENALTY_TYPE_OFFICIAL_SHOP_FRAUD_VOUCHER = 7,
  PENALTY_TYPE_CHAT_RESPONSE_RATE_OFFICIAL = 8,
  PENALTY_TYPE_CHAT_RESPONSE_RATE_NORMAL = 9,
  PENALTY_TYPE_COPY_IMAGE = 10,
  PENALTY_TYPE_RUDE_CHAT = 11,
  PENALTY_TYPE_FAKE_RETURN_ADDRESS = 12,
}

/** ShopPenaltySource enum. */
export enum ShopPenaltySource {
  PENALTY_SOURCE_AUTOMATIC = 0,
  PENALTY_SOURCE_MANUAL = 1,
}

/** TierVariationType enum. */
export enum TierVariationType {
  LIPSTICK = 1,
}

/** ShopPenaltyFlag enum. */
export enum ShopPenaltyFlag {
  LISTING_NON_BROWSABLE = 1,
  LISTING_NON_SEARCHABLE = 2,
  DISABLE_CREATE_LISTING = 4,
  DISABLE_EDIT_LISTING = 8,
  PUNISHMENT_DISABLE_MARKETING = 16,
  PUNISHMENT_DISABLE_FREE_SHIP = 32,
  PUNISHMENT_FREEZE_SHOP = 64,
}

/** ShopMetricOrderType enum. */
export enum ShopMetricOrderType {
  SHOP_METRIC_ORDER_LATE_SHIPMENT = 1,
  SHOP_METRIC_ORDER_RETURN = 2,
  SHOP_METRIC_ORDER_CANCEL = 3,
}

/** BundleDealFlagType enum. */
export enum BundleDealFlagType {
  BUNDLE_DEAL_FLAG_SHOPEE = 0,
  BUNDLE_DEAL_FLAG_SHOPEE_MULTI_SHOP = 1,
  BUNDLE_DEAL_FLAG_SELLER = 2,
}

/** BundleDealRuleType enum. */
export enum BundleDealRuleType {
  BUNDLE_DEAL_RULE_FIX_PRICE = 1,
  BUNDLE_DEAL_RULE_DISCOUNT_PERCENTAGE = 2,
  BUNDLE_DEAL_RULE_DISCOUNT_VALUE = 3,
}

/** MarketPushSendOption enum. */
export enum MarketPushSendOption {
  MP_PUSH_ACTION = 0,
  MP_PUSH = 1,
  MP_AR = 2,
  MP_WEB_PUSH = 4,
}

/** MarketPushTaskType enum. */
export enum MarketPushTaskType {
  MP_REAL = 1,
  MP_TEST = 2,
  MP_FAIL = 3,
  MP_AUTOGEN = 100,
}

/** VoucherPurpose enum. */
export enum VoucherPurpose {
  VOUCHER_WELCOME = 1,
  VOUCHER_REFERRAL = 2,
  VOUCHER_SHOP_FOLLOW = 3,
  VOUCHER_PURPOSE_SHOP_GAME = 4,
}

/** PromotionFraudRule enum. */
export enum PromotionFraudRule {
  EXCLUDE_BLACKLISTED_PHONE_PREFIX = 1,
  EXCLUDE_EXTISTING_DEVICE_FINGERPRINT = 2,
}

/** AttrTypeFlag enum. */
export enum AttrTypeFlag {
  KEY_ATTR = 1,
  SALES_ATTR = 2,
}

/** AttrMandatoryType enum. */
export enum AttrMandatoryType {
  NORMAL_SELLER = 1,
  MALL_SELLER = 2,
}

/** AttrInputType enum. */
export enum AttrInputType {
  INPUT_ENUM = 1,
  INPUT_COMBOBOX = 2,
  INPUT_TEXT = 3,
}

/** AttrInputValidateType enum. */
export enum AttrInputValidateType {
  VALID_ENUM = 1,
  VALID_STRING = 2,
  VALID_INT = 3,
  VALID_DECIMAL = 4,
  VALID_DATE = 5,
}

/** SpuLabal enum. */
export enum SpuLabal {
  NEW = 1,
  HOT = 2,
}

/** CollectionType enum. */
export enum CollectionType {
  COLLECTION_ALL = 0,
  COLLECTION_MANUAL = 1,
  COLLECTION_AUTOMATED = 2,
}

/** ClusterSource enum. */
export enum ClusterSource {
  CLUSTER_SOURCE_UNIDENTIFIED = 0,
  CLUSTER_SOURCE_DEEP = 1,
  CLUSTER_SOURCE_DATA_SCIENCE = 2,
}

/** RateControlType enum. */
export enum RateControlType {
  RATE_CONTROL_BACKEND = 1000,
}

/** OTPHistory enum. */
export enum OTPHistory {
  OTP_NONE = 0,
  VOICE_OTP_ONCE = 1,
}

/** TongdunDecodeStatus enum. */
export enum TongdunDecodeStatus {
  DECODE_NOT_NEEDED = 0,
  DECODE_PENDING = 1,
  DECODE_SUCCESS = 2,
  DECODE_FAILED = 3,
  DECODE_MISSING_BLACKBOX = 4,
}

/** TongdunDecodeReason enum. */
export enum TongdunDecodeReason {
  DECODE_VOUCHER = 1,
  DECODE_WELCOME_PACKAGE = 2,
  DECODE_FREE_SHIPPING_VOUCHER = 3,
  DECODE_SHOPEE_REBATE = 4,
  DECODE_COINS_REBATE = 5,
}

/** FraudCheckSkipReason enum. */
export enum FraudCheckSkipReason {
  SKIP_REASON_NOT_SKIPPED = 0,
  SKIP_REASON_TOGGLE_OFF = 1,
  SKIP_REASON_NETWORK_ERROR = 2,
}

/** SortPosition enum. */
export enum SortPosition {
  NON_SPECIFIED_POSITION = 2147483647,
}

/** WelcomePackageV2ItemType enum. */
export enum WelcomePackageV2ItemType {
  ITEM_TYPE_NON_WELCOME_PACKAGE = 0,
  ITEM_TYPE_FREE_GIFT = 1,
  ITEM_TYPE_EXCLUSIVE_DEAL = 2,
}

/** WelcomePackageV2ItemStatus enum. */
export enum WelcomePackageV2ItemStatus {
  ITEM_STATUS_DELETED = 0,
  ITEM_STATUS_NORMAL = 1,
}

/** WelcomePackageV2ItemModelStatus enum. */
export enum WelcomePackageV2ItemModelStatus {
  ITEM_MODEL_STATUS_DELETED = 0,
  ITEM_MODEL_STATUS_NORMAL = 1,
}

/** ShipmentModelId enum. */
export enum ShipmentModelId {
  SELF_ARRANGE = 0,
  PICK_UP = 1,
  DROP_OFF = 2,
}

/** IntegrationType enum. */
export enum IntegrationType {
  NON_INTEGRATED = 0,
  INTEGRATED = 1,
}

/** GSTInfoType enum. */
export enum GSTInfoType {
  GST_ORDER_ITEM = 1,
  GST_SHIPPING_FEE = 2,
  GST_BUYER_TXN_FEE = 3,
}

/** CheckoutListType enum. */
export enum CheckoutListType {
  CHECKOUT_TOPAY = 1,
}

/** AddressClientInfoType enum. */
export enum AddressClientInfoType {
  WSA_MALL = 1,
  WSA_PC = 2,
  WSA_RN = 3,
  WSA_RWEB = 4,
  MALL_CENTRAL = 5,
  SC_PC = 6,
  SC_SFF = 7,
  GEO_ADDRESS_PIPELINE = 8,
  GEO_WH_SCRIPT = 9,
  GEO_ONETIME_SCRIPT = 10,
  AUTO_MALL_API = 11,
  AUTO_MALL_CENTRAL = 12,
  AUTO_SELLER_APP = 13,
  AUTO_SFF = 14,
  ADMIN_PORTAL = 15,
  SLS = 16,
  AUTO_SLS = 17,
}

/** AccountAdminConfigType enum. */
export enum AccountAdminConfigType {
  VOICE_OTP_BLACKLIST = 1,
  ANTI_FRAUD_CONFIG = 2,
  BOT_REG_IP_WHITELIST = 3,
}

/** ItemAttributeType enum. */
export enum ItemAttributeType {
  INT_TYPE = 1,
  STRING_TYPE = 2,
  ENUM_TYPE = 3,
  FLOAT_TYPE = 4,
  DATE_TYPE = 5,
  TIMESTAMP_TYPE = 6,
}

/** ReturnSolution enum. */
export enum ReturnSolution {
  RETURN_AND_REFUND = 0,
  REFUND_ONLY = 1,
}

/** ItemAttributeInputType enum. */
export enum ItemAttributeInputType {
  DROP_DOWN = 1,
  TEXT_FILED = 2,
  COMBO_BOX = 3,
}
