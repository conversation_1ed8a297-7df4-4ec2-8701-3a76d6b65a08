import { request } from 'src/api/helper';
import type { WithResponse } from 'src/api/helper/codeApiHandle';
import { apiHandler } from 'src/api/helper/codeApiHandle';
import type { uploadBffAdmin } from './uploadBffAdmin';

const URLPrefix = '/wsa/marketplace/listing/upload/upload_bff/admin';

function createRequestFunc<IRequest, IResponse>(url: string) {
  return function (
    requestBody: IRequest,
    config?: { returnFullResponse?: boolean; skipError?: boolean }
  ) {
    return apiHandler(
      request<WithResponse<IResponse>>({
        url: URLPrefix + url,
        data: requestBody,
      }),
      config
    );
  };
}

export const echo = createRequestFunc<
  uploadBffAdmin.IEchoRequest,
  uploadBffAdmin.IEchoResponse
>('/echo');

export const updateProduct = createRequestFunc<
  uploadBffAdmin.IUpdateProductRequest,
  uploadBffAdmin.IUpdateProductResponse
>('/update_product');

export const submitKitMigrateTask = createRequestFunc<
  uploadBffAdmin.ISubmitKitMigrateTaskRequest,
  uploadBffAdmin.ISubmitKitMigrateTaskResponse
>('/submit_kit_migrate_task');

export const getKitMigrateTaskInfo = createRequestFunc<
  uploadBffAdmin.IGetKitMigrateTaskInfoRequest,
  uploadBffAdmin.IGetKitMigrateTaskInfoResponse
>('/get_kit_migrate_task_info');

export const editKitMigrateTask = createRequestFunc<
  uploadBffAdmin.IEditKitMigrateTaskRequest,
  uploadBffAdmin.IEditKitMigrateTaskResponse
>('/edit_kit_migrate_task');

export const searchProductForKitComponent = createRequestFunc<
  uploadBffAdmin.ISearchProductForKitComponentRequest,
  uploadBffAdmin.ISearchProductForKitComponentResponse
>('/search_product_for_kit_component');
