export namespace uploadBffAdmin {
  // proto syntax: proto2  

  // proto package name: uploadBffAdmin  

  // proto import: sp_idl.proto  

  export interface IConstant {
  }

  export interface IEchoRequest {
    data?: string;
  }

  export interface IEchoResponse {
    data?: string;
  }

  export interface IUpdateProductRequest {
    data?: string;
  }

  export interface IUpdateProductResponse {
    code?: number;
    debugMessage?: string;
    userMessage?: string;
    transifyKey?: string;
    data?: IUpdateProductResponseData;
  }

  export interface IUpdateProductResponseData {
    brandId?: number;
    originalBrandName?: string;
  }

  export interface ISubmitKitMigrateTaskRequest {
    productId?: number;
    shopId?: number;
    vskuSetting?: IVirtualSKUInfoSetting;
    bffMeta?: IRequestMeta;
  }

  export interface IVirtualSKUInfoSetting {
    name?: string;
    images?: string[];
    longImages?: string[];
    videoList?: IVideo[];
    descriptionInfo?: IDescriptionInfo;
    unlisted?: boolean;
    parentSku?: string;
    weight?: IWeight;
    dimension?: IDimension;
    preOrderInfo?: IPreOrderInfo;
    modelList?: IVirtualSKUModelInfo[];
    stdTierVariationList?: IStdTierVariation[];
    videoTaskId?: string;
    syncSetting?: IVirtualSKUSyncSetting;
  }

  export interface IEditLogisticsChannel {
    channelid?: number;
    enabled?: boolean;
    price?: string;
    sizeid?: number;
    size?: number;
    coverShippingFee?: boolean;
  }

  export interface IVirtualSKUModelInfo {
    id?: number;
    sku?: string;
    price?: string;
    tierIndex?: number[];
    weight?: IWeight;
    dimension?: IDimension;
    componentSkuList?: IVskuComponentSku[];
  }

  export interface IVskuComponentSku {
    mpskuItemId?: number;
    mpskuModelId?: number;
    quantity?: number;
    mainSku?: boolean;
    name?: string;
    modelName?: string;
    modelSku?: string;
    imageId?: string;
    inputPrice?: string;
  }

  export interface IVirtualSkuStockSetting {
    locationId?: string;
    /** only for reserved stock mode */
    changedReservedStock?: number;
    /** only for weak stock mode */
    isEnabled?: boolean;
  }

  export interface IVideo {
    vid?: string;
    videoId?: string;
    thumbUrl?: string;
    duration?: number;
    version?: number;
  }

  export interface IDescriptionInfo {
    description?: string;
    descriptionType?: string;
  }

  export interface IWeight {
    value?: string;
    unit?: number;
  }

  export interface IDimension {
    width?: string;
    length?: string;
    height?: string;
  }

  export interface IPreOrderInfo {
    preOrder?: boolean;
    daysToShip?: number;
  }

  export interface IStdTierVariation {
    id?: number;
    index?: number;
    customValue?: string;
    groupId?: number;
    groupName?: string;
    valueList?: IStdTierVariationValue[];
  }

  export interface IStdTierVariationValue {
    id?: number;
    index?: number;
    customValue?: string;
    imageId?: string;
  }

  export interface IVirtualSKUSyncSetting {
    autoSyncDts?: boolean;
  }

  export interface IMigrateVirtualSKUByProductResponseData {
    taskId?: number;
  }

  export interface ISubmitKitMigrateTaskResponse {
    code?: number;
    msg?: string;
    userMessage?: string;
    transifyKey?: string;
    data?: IMigrateVirtualSKUByProductResponseData;
  }

  export interface IGetKitMigrateTaskInfoRequest {
    taskId?: number;
    shopId?: number;
    productId?: number;
  }

  export interface IGetKitMigrateTaskInfoResponse {
    code?: number;
    msg?: string;
    userMessage?: string;
    transifyKey?: string;
    data?: IGetKitMigrateTaskInfoResponseData;
  }

  export interface IGetKitMigrateTaskInfoResponseData {
    productId?: number;
    vskuSetting?: IVirtualSKUInfoSetting;
  }

  export interface IEditKitMigrateTaskRequest {
    taskId?: number;
    productId?: number;
    shopId?: number;
    vskuSetting?: IVirtualSKUInfoSetting;
    reason?: string;
    bffMeta?: IRequestMeta;
  }

  export interface IEditKitMigrateTaskResponse {
    code?: number;
    msg?: string;
    userMessage?: string;
    transifyKey?: string;
  }

  export interface IApproveKitMigrateTaskRequest {
    taskId?: number;
    reason?: string;
    shopId?: number;
  }

  export interface IApproveKitMigrateTaskResponse {
    code?: number;
    msg?: string;
    userMessage?: string;
    transifyKey?: string;
  }

  export interface IAbandonKitMigrateTaskRequest {
    taskId?: number;
    shopId?: number;
    reason?: string;
  }

  export interface IAbandonKitMigrateTaskResponse {
    code?: number;
    msg?: string;
    userMessage?: string;
    transifyKey?: string;
  }

  export interface ISearchProductForKitComponentRequest {
    cursor?: string;
    pageSize?: number;
    sortBy?: string;
    isAsc?: boolean;
    keyword?: string;
    categoryId?: number;
    shopId?: number;
    region?: string;
  }

  export interface ISearchProductForKitComponentResponse {
    code?: number;
    debugMessage?: string;
    userMessage?: string;
    transifyKey?: string;
    data?: IProductListSearchData;
  }

  export interface IProductListSearchData {
    pageInfo?: ICursorPagination;
    products?: IProduct[];
  }

  export interface ICursorPagination {
    cursor?: string;
    total?: number;
  }

  export interface IProduct {
    id?: number;
    name?: string;
    status?: number;
    coverImage?: string;
    parentSku?: string;
    aggregatedPrice?: IAggregatedPrice;
    aggregatedStock?: IAggregatedStock;
    modifyTime?: number;
    createTime?: number;
    modelList?: IModel[];
    enableKitMigration?: boolean;
  }

  export interface IModel {
    id?: number;
    name?: string;
    sku?: string;
    tierIndex?: number[];
    isDefault?: boolean;
    image?: string;
    stockDetail?: IModelStockDetail;
    priceDetail?: IModelPriceDetail;
  }

  export interface IModelStockDetail {
    advancedStock?: IAdvancedStockDetail;
    totalAvailableStock?: number;
    totalSellerStock?: number;
    totalShopeeStock?: number;
  }

  export interface IAdvancedStockDetail {
    sellableStock?: number;
    inTransitStock?: number;
  }

  export interface IModelPriceDetail {
    originPrice?: string;
    promotionPrice?: string;
  }

  export interface IAggregatedPrice {
    priceMin?: string;
    priceMax?: string;
  }

  export interface IAggregatedStock {
    totalAvailableStock?: number;
    totalSellerStock?: number;
  }

  export interface IRequestMeta {
    /** current user ID of the logged-in user */
    userid?: number;
    /** session_id */
    sessionId?: string;
    /** the real ip of the client who initiate this request */
    clientIp?: string;
    /** TrackingPlatformType, used by a few backend services */
    platform?: number;
    /** FE requested URL */
    url?: string;
    userAgent?: string;
    /** encrypted sso_token */
    ssoToken?: string;
    /** decrypted token used by backend service, such as coreserver */
    shopeeToken?: string;
    /** cookie key is REC_T_ID */
    trackingSessionId?: string;
    /** configured cid of this gateway instance, "SG", "ID", "TH", and etc. */
    country?: string;
    /** this flag informs BFF to downgrade */
    downgrade?: boolean;
    /** i18n language, "en", "sg", "zhHans", "ms_my", and etc. */
    language?: string;
    /** GeoIP country code */
    clientIpCountry?: string;
    /** fingerprint used for tracking and anti-fraud */
    deviceFingerprint?: string;
    /** app version, eg. "25001" */
    appVersion?: string;
    /** RN version, eg. "4015012" */
    rnVersion?: string;
    /** current shop ID of the logged-in user */
    shopid?: number;
    /** 1 for Shopee, and 2 for Shopee Pay Merchant */
    appType?: number;
    /** client_id for cookie 'SPC_CLIENTID' */
    clientId?: string;
    /** packing the platform information in to an integer flag */
    clientPlatform?: number;
    /** the flag indicates that whether the request is from xiapibuy */
    isFromXiapibuy?: boolean;
    /** Referer header value */
    referer?: string;
    /** list of ip obtained from X-Forwarded-For header */
    forwardedFor?: string[];
    /** X-Real-Ip header value */
    realIp?: string;
    /** SPC_DID cookie, 32-byte random string */
    deviceid?: string;
    /** business account userid */
    tobUserid?: number;
    /** business account token returned from account shared service */
    tobShopeeToken?: string;
    /** business account business_id */
    tobBusinessId?: number;
    /** SPC_B_SI cookie, business account session identifier */
    tobSessionId?: string;
    /** crawler flag to indicate if request comes from crawler */
    crawler?: boolean;
    /** rn_bundle_version is from cookie SPC_RNBV */
    rnBundleVersion?: string;
    /** admin_region is the region for which some admin service resource is required */
    adminRegion?: string;
    /** soup_email is the email of the soup authenticated user */
    soupEmail?: string;
    /** http/https request */
    isFromHttps?: boolean;
    /** request host */
    host?: string;
    /** this flag indicates whether or not the request comes from office IP */
    isFromOfficeIp?: boolean;
    /** anti-fraud verification */
    isSignatureVerificationFailed?: boolean;
    /** the SOUP Object Code this request was authorised against and is for */
    soupObjectCode?: string;
  }

}
