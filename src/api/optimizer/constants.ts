export enum ErrorCode {
  SUCCESS = 0,
  /** Error Code Range 515700000 - 515800000 */
  /** Common Error Range 515700000 - 515710000 */
  ERROR_DB_EXECUTE = 515700000,
  ERROR_UNEXPECTED_DATA = 515700001,
  /** task Error Range 515710001 - 515720000 */
  ERROR_STATUS_NOT_ALLOW_TRANSFER = 515710001,
  ERROR_STATUS_TASK_NOT_EXIST = 515710002,
  ERROR_TASK_NOT_ALLOW_EXECUTE = 515710003,
}

export enum CountType {
  COUNT_TYPE_WRONG_WEIGHT_WILL_BE_OPTIMIZED = 1,
  COUNT_TYPE_WRONG_WEIGHT_ALREADY_BE_OPTIMIZED = 2,
  COUNT_TYPE_UNCLEAR_VARIATION_WILL_BE_OPTIMIZED = 3,
  COUNT_TYPE_UNCLEAR_VARIATION_ALREADY_BE_OPTIMIZED = 4,
  COUNT_TYPE_BETTER_RATIO_IMAGE_WILL_BE_OPTIMIZED = 5,
  COUNT_TYPE_BETTER_RATIO_IMAGE_ALREADY_BE_OPTIMIZED = 6,
  COUNT_TYPE_WRONG_DIMENSION_WILL_BE_OPTIMIZED = 7,
  COUNT_TYPE_WRONG_DIMENSION_ALREADY_BE_OPTIMIZED = 8,
  COUNT_TYPE_UNCLEAR_SPECIFICATION_WILL_BE_OPTIMIZED = 9,
  COUNT_TYPE_UNCLEAR_SPECIFICATION_ALREADY_BE_OPTIMIZED = 10,
  COUNT_TYPE_MISSING_SIZE_CHART_WILL_BE_OPTIMIZED = 11,
  COUNT_TYPE_MISSING_SIZE_CHART_ALREADY_BE_OPTIMIZED = 12,
  COUNT_TYPE_MISSING_VEHICLE_COMPATIBILITY_WILL_BE_OPTIMIZED = 13,
  COUNT_TYPE_MISSING_VEHICLE_COMPATIBILITY_ALREADY_BE_OPTIMIZED = 14,
  COUNT_TYPE_KIT_MIGRATION_WILL_BE_OPTIMIZED = 15,
  COUNT_TYPE_KIT_MIGRATION_ALREADY_BE_OPTIMIZED = 16,
}

export enum OptimizerType {
  OPTIMIZER_TYPE_WRONG_WEIGHT = 1,
  OPTIMIZER_TYPE_UNCLEAR_VARIATION = 2,
  OPTIMIZER_TYPE_BETTER_RATIO_IMAGE = 3,
  OPTIMIZER_TYPE_WRONG_DIMENSION = 4,
  OPTIMIZER_TYPE_UNCLEAR_SPECIFICATION = 5,
  OPTIMIZER_TYPE_MISSING_SIZE_CHART = 6,
  OPTIMIZER_TYPE_MISSING_VEHICLE_COMPATIBILITY = 7,
  OPTIMIZER_TYPE_KIT_MIGRATION = 8,
}

export enum WeightUnitType {
  COUNT_WEIGHT_KG = 1,
  COUNT_WEIGHT_G = 2,
}

export enum SellerOperation {
  OPERATION_NONE = 0,
  OPERATION_CONFIRM = 1,
  OPERATION_REJECT = 2,
}

export enum CoverType {
  COVER_TYPE_AIGC_IMAGE = 1,
}

export enum ImageType {
  IMAGE_TYPE_AIGC_IMAGE = 1,
  IMAGE_TYPE_SELLER_UPLOADED_IMAGE = 2,
}

export enum FeedbackType {
  /** disable item feedback */
  FEEDBACK_TYPE_AIGC_IMAGE_ITEM = 1,
  /** AIGC side image feedback */
  FEEDBACK_TYPE_AIGC_IMAGE_ITEM_IMAGE = 2,
}

export enum FeedbackContent {
  FEEDBACK_CONTENT_SELECT_1 = 1,
  FEEDBACK_CONTENT_SELECT_2 = 2,
  FEEDBACK_CONTENT_SELECT_3 = 3,
  FEEDBACK_CONTENT_SELECT_4 = 4,
}

export enum SizeChartValueType {
  SIZE_CHART_IMAGE = 0,
  SIZE_CHART_TEMPLATE = 1,
}

export enum KitMigrationOperationType {
  KIT_MIGRATION_OPERATION_SELLER_ACCEPT = 1,
  KIT_MIGRATION_OPERATION_SELLER_EDIT = 2,
  KIT_MIGRATION_OPERATION_SELLER_CREATE = 3,
  KIT_MIGRATION_OPERATION_SELLER_REJECT = 4,
  KIT_MIGRATION_OPERATION_SELLER_CONFIRM = 5,
  KIT_MIGRATION_OPERATION_SELLER_ABANDON = 6,
  KIT_MIGRATION_OPERATION_OPS_EDIT = 7,
  KIT_MIGRATION_OPERATION_OPS_CREATE = 8,
  KIT_MIGRATION_OPERATION_OPS_APPROVE = 9,
  KIT_MIGRATION_OPERATION_SYSTEM_AUTO_CREATE = 10,
  KIT_MIGRATION_OPERATION_SYSTEM_AUTO_ACCEPT = 11,
  KIT_MIGRATION_OPERATION_SYSTEM_RETRY = 12,
  KIT_MIGRATION_OPERATION_SYSTEM_EXECUTE = 13,
  KIT_MIGRATION_OPERATION_OPS_ABANDON = 14,
}

export enum KitMigrationModifyType {
  KIT_MIGRATION_SELLER_HAS_NOT_MODIFIED_TASK_YET = 0,
  KIT_MIGRATION_SELLER_MODIFIED_TASK_AFTER_REVIEW = 1,
  KIT_MIGRATION_SELLER_DID_NOT_MODIFY_TASK_AFTER_REVIEW = 2,
}

export enum KitMigrationOptimizerSource {
  MASS_MIGRATION = 0,
  SINGLE_MIGRATION = 1,
  SELLER_MIGRATION = 2,
}

export enum KitMigrationPendingReviewStatus {
  KIT_MIGRATION_NOT_IN_PENDING_REVIEW = 0,
  KIT_MIGRATION_PENDING_SELLER = 1,
  KIT_MIGRATION_PENDING_OPS = 2,
  KIT_MIGRATION_FAILED = 3,
}

export enum KitMigrationCallbackType {
  KIT_MIGRATION_CALLBACK_TYPE_UNKNOWN = 0,
  KIT_MIGRATION_CALLBACK_TYPE_SYSTEM_ERROR = 1,
  KIT_MIGRATION_CALLBACK_TYPE_HAS_MT_RELATIONSHIP = 2,
  KIT_MIGRATION_CALLBACK_TYPE_STILL_IN_PROMOTION = 3,
  KIT_MIGRATION_CALLBACK_TYPE_FINISHED = 4,
}

export enum KitMigrationTaskStatus {
  KIT_MIGRATION_STATUS_DEFAULT = 0,
  KIT_MIGRATION_STATUS_WAITING_ACCEPT = 1,
  KIT_MIGRATION_STATUS_PENDING_REVIEW = 2,
  KIT_MIGRATION_STATUS_CONFIRMED = 3,
  KIT_MIGRATION_STATUS_ABANDONED = 4,
  KIT_MIGRATION_STATUS_FINISHED = 5,
  KIT_MIGRATION_STATUS_FAILED = 6,
  KIT_MIGRATION_STATUS_PENDING_MIGRATION = 7,
}

