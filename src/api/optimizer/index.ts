import { request } from 'src/api/helper';
import type { WithResponse } from 'src/api/helper/codeApiHandle';
import { apiHandler } from 'src/api/helper/codeApiHandle';
import type { optimizer } from './optimizer';

const URLPrefix = '/listing_info_optimization/admin';

function createRequestFunc<IRequest, IResponse>(url: string) {
  return function (
    requestBody: IRequest,
    config?: { returnFullResponse?: boolean; skipError?: boolean }
  ) {
    return apiHandler(
      request<WithResponse<IResponse>>({
        url: URLPrefix + url,
        data: requestBody,
      }),
      config
    );
  };
}

export const searchKitMigrationTaskList = createRequestFunc<
  optimizer.ISearchKitMigrationListRequest,
  optimizer.ISearchKitMigrationListResponse
>('/search_kit_migration_task_list');

export const batchUpsertKitMigrationTaskByOps = createRequestFunc<
  optimizer.IBatchUpsertKitMigrationByOpsTaskRequest,
  optimizer.IBatchUpsertKitMigrationByOpsTaskResponse
>('/batch_upsert_kit_migration_task');
