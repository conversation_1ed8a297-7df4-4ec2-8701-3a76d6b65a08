export namespace optimizer {
  // proto syntax: proto2  

  // proto package name: optimizer  

  // proto import: sp_idl.proto  

  export interface IConstant {
  }

  export interface ICheckEnabledKitMigrationListRequest {
    shopId?: number;
    itemIds?: number[];
  }

  export interface ICheckEnabledKitMigrationListResponse {
    debugMessage?: string;
    tasks?: IEnabledKitMigration[];
  }

  export interface IEnabledKitMigration {
    itemId?: number;
    shopId?: number;
    taskId?: number;
    enabled?: boolean;
  }

  export interface ICheckPendingReviewKitMigrationListRequest {
    shopId?: number;
    itemIds?: number[];
  }

  export interface ICheckPendingReviewKitMigrationListResponse {
    debugMessage?: string;
    products?: IPendingReviewKitMigration[];
  }

  export interface IPendingReviewKitMigration {
    itemId?: number;
    shopId?: number;
    taskId?: number;
    /** refer to KitMigrationPendingReviewStatus */
    pendingStatus?: number;
  }

  export interface IBatchUpsertKitMigrationByOpsTaskRequest {
    taskShopItemIds?: ITaskShopItemId[];
    /** only support batch approve and batch abandon */
    operationType?: number;
    operator?: string;
    /** reserve */
    reason?: string;
  }

  export interface ITaskShopItemId {
    itemId?: number;
    shopId?: number;
    taskId?: string;
  }

  export interface IBatchUpsertKitMigrationByOpsTaskResponse {
    debugMessage?: string;
    failedTasks?: IFailedUpsertKitMigration[];
  }

  export interface IFailedUpsertKitMigration {
    itemId?: number;
    shopId?: number;
    taskId?: number;
    errMsg?: number;
  }

  export interface IUpsertKitMigrationTaskByCallbackRequest {
    itemId?: number;
    shopId?: number;
    taskId?: number;
    /** see KitMigrationCallbackType */
    errType?: number;
    /** not required */
    errMsg?: string;
  }

  export interface IUpsertKitMigrationTaskByCallbackResponse {
    debugMessage?: string;
    taskId?: number;
  }

  export interface IUpsertKitMigrationTaskRequest {
    itemId?: number;
    shopId?: number;
    taskId?: number;
    jsonKit?: string;
    /** refer to KitMigrationOperationType */
    operationType?: number;
    operator?: string;
  }

  export interface IUpsertKitMigrationTaskResponse {
    debugMessage?: string;
    taskId?: number;
  }

  export interface IGetKitMigrationInfoRequest {
    itemId?: number;
    shopId?: number;
    taskId?: number;
  }

  export interface IGetKitMigrationInfoResponse {
    debugMessage?: string;
    taskInfo?: IRawKitMigrationTask;
  }

  export interface IRawKitMigrationTask {
    itemId?: number;
    shopId?: number;
    jsonKitMigration?: string;
    taskId?: number;
    taskStatus?: number;
    lastOperation?: number;
    optimizerType?: number;
    operator?: string;
    ctime?: number;
    mtime?: number;
  }

  export interface IKitMigrationExtInfo {
    retryCount?: number;
    failedReason?: string;
    uploadTaskId?: number;
  }

  export interface ISearchKitMigrationListRequest {
    itemId?: number;
    /** see TaskStatus */
    taskStatusList?: number[];
    operator?: string;
    startCtime?: number;
    endCtime?: number;
    cursor?: string;
    /** no more than 50 */
    limit?: number;
    /** see KitMigrationModifyType */
    modifyType?: number;
    /** see KitMigrationOptimizerSource */
    optimizerSource?: number;
    lastOperation?: number;
  }

  export interface ISearchKitMigrationListResponse {
    debugMessage?: string;
    pageInfo?: IPageInfo;
    tasks?: IKitMigrationTask[];
  }

  export interface IKitMigrationTask {
    itemId?: number;
    shopId?: number;
    kitMigration?: IKitMigration;
    taskId?: string;
    taskStatus?: number;
    lastOperation?: number;
    optimizerType?: number;
    operator?: string;
    ctime?: number;
    mtime?: number;
    /** see KitMigrationModifyType */
    modifyType?: number;
    /** see KitMigrationOptimizerSource */
    optimizerSource?: number;
  }

  export interface IKitMigration {
    itemId?: number;
    shopId?: number;
    coverImage?: string;
    catIds?: number[];
    name?: string;
    sku?: string;
    kitComponents?: IKitComponent[];
    stdTierVariationList?: IStdTierVariation[];
    isDefault?: boolean;
  }

  export interface IKitComponent {
    kitId?: number;
    kitName?: string;
    sku?: string;
    tierIndexList?: number[];
    price?: string;
    stock?: number;
    image?: string;
    quantity?: number;
    /** reserved */
    weight?: IWeightForDisplay;
    /** reserved */
    dimension?: IDimensionForDisplay;
    kitComponentSkuList?: IKitComponentSku[];
  }

  export interface IKitComponentSku {
    mpskuItemId?: number;
    mpskuModelId?: number;
    sku?: string;
    itemName?: string;
    modelName?: string;
    costPrice?: number;
    stock?: number;
    mainSku?: boolean;
    image?: string;
    quantity?: number;
  }

  export interface IWeightForDisplay {
    /** Keep 2 decimal places. so it will plus 100. */
    value?: string;
    /** such as "1 kg". if unit is g. value will be 100000. if unit is kg. value will be 100 */
    /** see WeightUnitType */
    unit?: number;
  }

  export interface IDimensionForDisplay {
    width?: string;
    length?: string;
    height?: string;
  }

  export interface IGetAIOptimizedProductCountResponse {
    debugMessage?: string;
    countInfos?: ICountInfo[];
  }

  export interface ICountInfo {
    countType?: number;
    count?: number;
  }

  export interface IGetAIOptimizedProductCountRequest {
    shopId?: number;
    countTypes?: number[];
  }

  export interface IGetWillBeOptimizedProductListRequest {
    shopId?: number;
    cursor?: string;
    /** no more than 50 */
    limit?: number;
    /** see OptimizerType */
    optimizerType?: number;
    listQueryParam?: IListQueryParam;
  }

  export interface IListQueryParam {
    /** will return limit + 1 products with this product as first */
    topProductId?: number;
  }

  export interface IPageInfo {
    total?: number;
    cursor?: string;
    hasNext?: boolean;
  }

  export interface IGetWillBeOptimizedProductListResponse {
    debugMessage?: string;
    data?: IWillBeOptimizedProductListData;
  }

  export interface IWillBeOptimizedProductListData {
    pageInfo?: IPageInfo;
    products?: IWillBeOptimizedProduct[];
  }

  export interface IWillBeOptimizedProduct {
    productId?: number;
    /** unix time */
    deadlineToFix?: number;
    currentValue?: IProductInfo;
    replaceValue?: IProductInfo;
    taskId?: number;
    optimizerType?: number;
    taskLastMtime?: number;
  }

  export interface IProductInfo {
    weight?: IWeight;
    modelList?: IModelDetail[];
    stdTierVariationList?: IStdTierVariation[];
    images?: string[];
    longImages?: IBetterRatioImage[];
    attributes?: IAttributeInfo[];
    brandInfo?: IBrandInfo;
    sizeChartInfo?: IProductSizeChartInfo;
    compatibilityInfo?: ICompatibilityInfo;
    kitMigrationInfo?: IKitMigration;
  }

  export interface ICompatibilityInfo {
    vehicleInfoList?: IVehicleSetting[];
  }

  export interface IVehicleSetting {
    brandId?: number;
    modelId?: number;
    yearId?: number;
    versionId?: number;
  }

  export interface IBetterRatioImage {
    outpaintingImageId?: string;
    /** such as [x, y, w, h] */
    location?: number[];
    croppedImageId?: string;
    absoluteLocation?: number[];
  }

  export interface IModelDetail {
    id?: number;
    weight?: IWeight;
    dimension?: IDimension;
  }

  export interface IDimension {
    width?: number;
    length?: number;
    height?: number;
  }

  export interface IAttributeInfo {
    attributeId?: number;
    attributeValues?: IAttributeValueInfo[];
  }

  export interface IAttributeValueInfo {
    valueId?: number;
    customValue?: ICustomValue;
  }

  export interface ICustomValue {
    rawValue?: string;
    unit?: string;
  }

  export interface IBrandInfo {
    brandId?: number;
  }

  export interface IProductSizeChartInfo {
    sizeChartImage?: string;
    sizeChartTemplate?: ISizeChartTemplate;
    /** see to SizeChartValueType */
    sizeChartValueType?: number;
  }

  export interface IGetAlreadyBeOptimizedProductListRequest {
    shopId?: number;
    cursor?: string;
    /** no more than 50 */
    limit?: number;
    /** see OptimizerType */
    optimizerType?: number;
  }

  export interface IGetAlreadyBeOptimizedProductListResponse {
    debugMessage?: string;
    data?: IAlreadyBeOptimizedProductListData;
  }

  export interface IAlreadyBeOptimizedProductListData {
    pageInfo?: IPageInfo;
    products?: IAlreadyBeOptimizedProduct[];
  }

  export interface IAlreadyBeOptimizedProduct {
    productId?: number;
    /** unix time to be auto-corrected */
    optimizedTime?: number;
    currentValue?: IProductInfo;
    replaceValue?: IProductInfo;
    optimizerType?: number;
    taskId?: number;
  }

  export interface IGetWillBeOptimizedInfoByProductIDsRequest {
    shopId?: number;
    productIds?: number[];
    optimizerTypes?: number[];
  }

  export interface IGetWillBeOptimizedInfoByProductIDsResponse {
    debugMessage?: string;
    data?: IWillBeOptimizedInfoByProductIDsData;
  }

  export interface IWillBeOptimizedInfoByProductIDsData {
    products?: IWillBeOptimizedProduct[];
  }

  export interface IGetAlreadyBeOptimizedInfoByProductIDsRequest {
    shopId?: number;
    /** no more than 10 */
    productIds?: number[];
    optimizerTypes?: number[];
  }

  export interface IGetAlreadyBeOptimizedInfoByProductIDsResponse {
    debugMessage?: string;
    data?: IAlreadyBeOptimizedInfoByProductIDsData;
  }

  export interface IAlreadyBeOptimizedInfoByProductIDsData {
    products?: IAlreadyBeOptimizedProduct[];
  }

  export interface IFailProduct {
    productId?: number;
    errorCode?: number;
    errorDetailList?: IErrorDetail[];
  }

  export interface IErrorDetail {
    key?: string;
    value?: string;
  }

  export interface IBatchConfirmAlreadyBeOptimizedProductRequest {
    shopId?: number;
    /** deprecated */
    productIds?: number[];
    /** deprecated */
    optimizerTypes?: number[];
    confirmList?: IConfirmAlreadyBeOptimizedProduct[];
    audit?: IAudit;
  }

  export interface IConfirmAlreadyBeOptimizedProduct {
    taskId?: number;
    productId?: number;
    optimizerType?: number;
  }

  export interface IAudit {
    source?: string;
    operator?: string;
    reason?: string;
  }

  export interface IBatchConfirmAlreadyBeOptimizedProductResult {
    failProducts?: IFailConfirmAlreadyBeOptimizedProduct[];
  }

  export interface IFailConfirmAlreadyBeOptimizedProduct {
    productId?: number;
    failDetail?: IFailDetail[];
  }

  export interface IFailDetail {
    optimizerType?: number;
    taskId?: number;
    errorCode?: number;
  }

  export interface IBatchConfirmAlreadyBeOptimizedProductResponse {
    debugMessage?: string;
    data?: IBatchConfirmAlreadyBeOptimizedProductResult;
  }

  export interface IBatchConfirmWillBeOptimizedProductRequest {
    shopId?: number;
    optimizerType?: number;
    products?: IConfirmProduct[];
    audit?: IAudit;
    /** quick operation: reject all/ accept all */
    /** see SellerOperation */
    operation?: number;
  }

  export interface IConfirmProduct {
    productId?: number;
    taskId?: number;
    productInfo?: IConfirmProductInfo;
    taskLastMtime?: number;
  }

  export interface IBatchConfirmWillBeOptimizedProductResponse {
    debugMessage?: string;
    data?: IBatchConfirmWillBeOptimizedProductResult;
  }

  export interface IBatchConfirmWillBeOptimizedProductResult {
    failProducts?: IFailProduct[];
  }

  export interface IBatchConfirmWillBeOptimizedProductResults {
    failProducts?: IFailProduct[];
  }

  export interface IConfirmAllAlreadyBeOptimizedProductRequest {
    shopId?: number;
    optimizerType?: number;
    audit?: IAudit;
    productIds?: number[];
  }

  export interface IConfirmAllAlreadyBeOptimizedProductResponse {
    debugMessage?: string;
  }

  export interface IBatchRejectWillBeOptimizedProductRequest {
    shopId?: number;
    optimizerType?: number;
    products?: IRejectProduct[];
    audit?: IAudit;
  }

  export interface IRejectProduct {
    productId?: number;
  }

  export interface IBatchRejectWillBeOptimizedProductResponse {
    debugMessage?: string;
    data?: IBatchRejectWillBeOptimizedProductResult;
  }

  export interface IBatchRejectWillBeOptimizedProductResult {
    failProducts?: IFailProduct[];
  }

  export interface IPartialConfirmWillBeOptimizedProductRequest {
    shopId?: number;
    optimizerType?: number;
    product?: IPartialConfirmProduct;
    audit?: IAudit;
  }

  export interface IPartialConfirmProduct {
    productId?: number;
    productInfo?: IConfirmProductInfo;
    taskId?: number;
    taskLastMtime?: number;
  }

  export interface IConfirmProductInfo {
    modelList?: IEditModel[];
    weight?: IWeight;
    stdTierVariationList?: IOptimizedStdTierVariation[];
    longImages?: ILongImages;
    specification?: ISpecificationOperation;
    sizeChartImage?: ISizeChartImageOperation;
    vehicleCompatibility?: IVehicleCompatibilityOperation;
    sizeChartTemplate?: ISizeChartTemplateOperation;
    kitMigration?: IKitMigrationOperation;
  }

  export interface IKitMigrationOperation {
    /** see SellerOperation */
    operation?: number;
  }

  export interface IVehicleCompatibilityOperation {
    vehicleSettingOperation?: IVehicleSettingOperation[];
  }

  export interface IVehicleSettingOperation {
    brandId?: number;
    modelId?: number;
    yearId?: number;
    versionId?: number;
    operation?: number;
  }

  export interface ISizeChartImageOperation {
    /** see SellerOperation */
    operation?: number;
    /** seller selected, if only reject in list page, it should be "" */
    selectedImage?: string;
  }

  export interface ISizeChartTemplateOperation {
    /** see SellerOperation */
    operation?: number;
  }

  export interface ILongImages {
    /** see SellerOperation */
    operation?: number;
  }

  export interface ISpecificationOperation {
    brandOperation?: IBrandOperation;
    attributesOperation?: IAttributeOperation[];
  }

  export interface IBrandOperation {
    /** see SellerOperation */
    operation?: number;
  }

  export interface IAttributeOperation {
    attributeId?: number;
    /** see SellerOperation */
    operation?: number;
  }

  export interface IPartialConfirmWillBeOptimizedProductResponse {
    debugMessage?: string;
    data?: IPartialConfirmWillBeOptimizedProduct;
  }

  export interface IPartialConfirmWillBeOptimizedProduct {
    failProduct?: IFailProduct;
  }

  export interface IEditModelList {
    models?: IEditModel[];
  }

  export interface IEditModel {
    id?: number;
    weight?: IWeight;
    dimension?: IModelDimension;
  }

  export interface IModelDimension {
    /** see SellerOperation */
    operation?: number;
  }

  export interface IWeight {
    /** Keep 2 decimal places. so it will plus 100. */
    value?: number;
    /** such as "1 kg". if unit is g. value will be 100000. if unit is kg. value will be 100 */
    /** see WeightUnitType */
    unit?: number;
  }

  export interface IStandardiseTierVariationValue {
    id?: number;
    index?: number;
    customValue?: string;
    imageId?: string;
  }

  export interface IStdTierVariation {
    id?: number;
    index?: number;
    groupId?: number;
    valueList?: IStandardiseTierVariationValue[];
    customValue?: string;
    nameCanOperation?: boolean;
    subTypeCanOperation?: boolean;
    displayName?: string;
  }

  export interface IReportOptimizedProductResultRequest {
    shopId?: number;
    productId?: number;
    reportData?: IReportOptimizedProduct[];
    audit?: IAudit;
  }

  export interface IReportOptimizedProduct {
    optimizerType?: number;
    taskId?: number;
    stdTierVariationList?: IReportStdTierVariation;
    longImages?: IReportLongImages;
    dimension?: IReportDimension;
    gtinSpecification?: IReportGtinSpecification;
    sizeChartImage?: IReportSizeChartImage;
    sizeChartTemplate?: IReportSizeChartTemplate;
    taskLastMtime?: number;
    jsonKit?: string;
  }

  export interface IReportDimension {
    modelList?: IReportOptimizedModel[];
  }

  export interface IReportOptimizedModel {
    id?: number;
    /** see SellerOperation */
    operation?: number;
    dimension?: IDimension;
  }

  export interface IReportStdTierVariation {
    stdTierVariationOperation?: IOptimizedStdTierVariation[];
    stdTierVariationList?: IStdTierVariation[];
  }

  export interface IReportLongImages {
    /** see SellerOperation */
    operation?: number;
    reportImage?: IReportLongImage;
    categoryIds?: number[];
  }

  export interface IReportLongImage {
    selectedLongImages?: string[];
  }

  export interface IReportGtinSpecification {
    operation?: ISpecificationOperation;
    gtin?: string;
    brandInfo?: IBrandInfo;
    attributes?: IAttributeInfo[];
  }

  export interface IReportSizeChartImage {
    /** see SellerOperation */
    operation?: number;
    selectedSizeChart?: string;
  }

  export interface IReportSizeChartTemplate {
    /** see SellerOperation */
    operation?: number;
    savedSizeChartTemplateId?: number;
  }

  export interface IReportOptimizedProductResultResponse {
    debugMessage?: string;
  }

  export interface IOptimizedStandardiseTierVariationValue {
    index?: number;
    /** see SellerOperation */
    operation?: number;
  }

  export interface IOptimizedStdTierVariation {
    index?: number;
    /** see SellerOperation */
    operation?: number;
    valueList?: IOptimizedStandardiseTierVariationValue[];
    subTypeValue?: ISubTypeValue;
  }

  export interface ISubTypeValue {
    groupId?: number;
    /** see SellerOperation */
    operation?: number;
  }

  export interface IGetLastAutoOptimizedDeadlineRequest {
    shopId?: number;
  }

  export interface IGetLastAutoOptimizedDeadlineResponse {
    debugMessage?: string;
    /** unix time */
    deadlineToFix?: number;
  }

  export interface IGetOptimizerSettingRequest {
    shopId?: number;
    /** repeated uint64 item_id_list = 2[(sp.idl.field) = { */
    /** repeated: { */
    /** unique: true */
    /** min_items: 1 */
    /** max_items: 50 */
    /** } */
    /** }];			// support item level in the future */
  }

  export interface IGetOptimizerSettingResponse {
    debugMessage?: string;
    data?: IGetOptimizerSettingData;
  }

  export interface IGetOptimizerSettingData {
    setting?: IShopSetting;
  }

  export interface IShopSetting {
    aigcCoverImage?: boolean;
  }

  export interface ISetOptimizerSettingRequest {
    shopId?: number;
    setting?: IShopSetting;
  }

  export interface ISetOptimizerSettingResponse {
    debugMessage?: string;
  }

  export interface IGetProductCoverListRequest {
    shopId?: number;
    /** CoverType */
    coverType?: number;
    pageNumber?: number;
    pageSize?: number;
  }

  export interface IGetProductCoverListResponse {
    debugMessage?: string;
    data?: IProductCoverListData;
  }

  export interface IProductCoverListData {
    pageInfo?: IPages;
    coverImageInfo?: IProductCoverImageInfo[];
  }

  export interface IProductCoverImageInfo {
    itemId?: number;
    itemName?: string;
    coverImage?: string;
    /** from AIGC side. */
    aigcImageList?: string[];
    /** from upload service. */
    uploadImageList?: string[];
    /** from upload disable item db, seller reject to show AIGC image */
    disabledTag?: boolean;
  }

  export interface IPages {
    pageNum?: number;
    pageSize?: number;
    total?: number;
  }

  export interface IGetProductCoverDetailRequest {
    shopId?: number;
    itemId?: number;
    /** CoverType */
    coverType?: number;
  }

  export interface IGetProductCoverDetailResponse {
    debugMessage?: string;
    data?: IProductCoverInfoResultData;
  }

  export interface IProductCoverInfoResultData {
    coverImageDetail?: IProductCoverImageDetail;
  }

  export interface IProductCoverImageDetail {
    itemId?: number;
    itemName?: string;
    coverImage?: string;
    /** from AIGC side. */
    aigcImageList?: string[];
    /** from upload service. */
    uploadImageList?: string[];
    /** from AIGC convert to second. */
    optimizedTime?: number;
  }

  export interface IDisableProductCoverRequest {
    shopId?: number;
    itemId?: number;
    disable?: boolean;
    /** CoverType such as AIGC_IMAGE */
    coverType?: number;
    feedback?: IFeedback;
  }

  export interface IFeedback {
    selectedValue?: number;
    customizedValue?: string;
  }

  export interface IDisableProductCoverResponse {
    debugMessage?: string;
  }

  export interface IDeleteProductCoverImageRequest {
    shopId?: number;
    itemId?: number;
    deletedCoverInfo?: IDeletedCoverInfo;
    feedback?: IFeedback;
  }

  export interface IDeletedCoverInfo {
    imageValue?: IDeleteImageValue;
  }

  export interface IDeleteImageValue {
    aigcImage?: string;
    sellerUploadImage?: string;
  }

  export interface IDeleteProductCoverImageResponse {
    debugMessage?: string;
  }

  export interface ISizeChartTemplate {
    virtualCategoryId?: number;
    table?: ISizeChartTable;
    name?: string;
    /** unused field. just define it to support to decode MPI input */
    linkedItemCount?: number;
  }

  export interface ISizeChartTable {
    /** columns of table */
    columns?: IColumn[];
    /** unit of the table */
    unit?: string;
  }

  export interface IColumn {
    /** column header */
    header?: IMeasurementHeader;
    /** column cells */
    cells?: ICell[];
  }

  export interface IMeasurementHeader {
    measurementId?: number;
    /** measurement name */
    name?: string;
    /** 1: normal, 2: size convention */
    type?: number;
    /** 1: dropdown, 2: single input number 3; range input number */
    inputType?: number;
    /** unit of column, if uint is null, use table unit */
    unit?: string;
    /** be nil for half-standardized size chart template */
    displayNames?: IMultiLangList;
    isKeyMeasurement?: boolean;
  }

  export interface ICell {
    dropDownValue?: IDropDownOption;
    rangeInputValue?: IRangeInputNumber;
    singleInputValue?: ISingleInputNumber;
  }

  export interface IDropDownOption {
    /** attribute value id if measurement is size covetion. be 0 for half-standardized size chart template */
    attrValueId?: number;
    value?: string;
    /** multiple language display. be nil for half-standardized size chart template */
    displayValues?: IMultiLangList;
  }

  export interface ISingleInputNumber {
    /** refer to Upload-bff: ProductInfo struct */
    /** product list cover info */
    /** single product cover info */
    /** size chart table value */
    /** column of size chart table */
    /** Each Cell will only have one value */
    /** true value multi 100000 */
    value?: number;
  }

  export interface IRangeInputNumber {
    /** true value multi 100000 */
    begin?: number;
    /** true value multi 100000 */
    end?: number;
  }

  export interface IMultiLangList {
    /** multiple language display */
    multiLangValues?: IValWithLang[];
  }

  export interface IValWithLang {
    /** UpCase code */
    language?: string;
    value?: string;
  }

  export interface IStsGatewayRequestMeta {
    bffMeta?: IBffMeta;
  }

  export interface IBffMeta {
    userEmail?: string;
    adminRegion?: string;
  }

  export interface ISingleInputNumberCheckPendingReviewKitMigrationListRequest {
    shopId?: number;
    itemIds?: number[];
  }

  export interface ISingleInputNumberCheckPendingReviewKitMigrationListResponse {
    debugMessage?: string;
    products?: IPendingReviewKitMigration[];
  }

  export interface ISingleInputNumberPendingReviewKitMigration {
    itemId?: number;
    shopId?: number;
    /** refer to KitMigrationPendingStatus */
    pendingStatus?: number;
  }

  export interface ISingleInputNumberBatchApproveKitMigrationTaskRequest {
    taskShopItemIds?: ITaskShopItemId[];
    operator?: string;
  }

  export interface ISingleInputNumberTaskShopItemId {
    itemId?: number;
    shopId?: number;
    taskId?: number;
  }

  export interface ISingleInputNumberBatchApproveKitMigrationTaskResponse {
    debugMessage?: string;
  }

  export interface ISingleInputNumberUpsertKitMigrationTaskRequest {
    itemId?: number;
    shopId?: number;
    taskId?: number;
    jsonKit?: string;
    /** refer to KitMigrationOperationType */
    operationType?: number;
    operator?: string;
  }

  export interface ISingleInputNumberUpsertKitMigrationTaskResponse {
    debugMessage?: string;
  }

  export interface ISingleInputNumberGetKitMigrationInfoRequest {
    itemId?: number;
    shopId?: number;
    taskId?: number;
  }

  export interface ISingleInputNumberGetKitMigrationInfoResponse {
    itemId?: number;
    shopId?: number;
    taskId?: number;
    /** see TaskStatus */
    taskStatus?: number;
    operator?: string;
    /** json string of informal kit */
    originJsonKit?: string;
    newJsonKit?: string;
    ctime?: number;
    mtime?: number;
  }

  export interface ISingleInputNumberGetAIOptimizedProductCountResponse {
    debugMessage?: string;
    countInfos?: ICountInfo[];
  }

  export interface ISingleInputNumberCountInfo {
    countType?: number;
    count?: number;
  }

  export interface ISingleInputNumberGetAIOptimizedProductCountRequest {
    shopId?: number;
    countTypes?: number[];
  }

  export interface ISingleInputNumberGetWillBeOptimizedProductListRequest {
    shopId?: number;
    cursor?: string;
    /** no more than 50 */
    limit?: number;
    /** see OptimizerType */
    optimizerType?: number;
    listQueryParam?: IListQueryParam;
  }

  export interface ISingleInputNumberListQueryParam {
    /** will return limit + 1 products with this product as first */
    topProductId?: number;
  }

  export interface ISingleInputNumberPageInfo {
    total?: number;
    cursor?: string;
    hasNext?: boolean;
  }

  export interface ISingleInputNumberGetWillBeOptimizedProductListResponse {
    debugMessage?: string;
    data?: IWillBeOptimizedProductListData;
  }

  export interface ISingleInputNumberWillBeOptimizedProductListData {
    pageInfo?: IPageInfo;
    products?: IWillBeOptimizedProduct[];
  }

  export interface ISingleInputNumberWillBeOptimizedProduct {
    productId?: number;
    /** unix time */
    deadlineToFix?: number;
    currentValue?: IProductInfo;
    replaceValue?: IProductInfo;
    taskId?: number;
    optimizerType?: number;
    taskLastMtime?: number;
  }

  export interface ISingleInputNumberProductInfo {
    weight?: IWeight;
    modelList?: IModelDetail[];
    stdTierVariationList?: IStdTierVariation[];
    images?: string[];
    longImages?: IBetterRatioImage[];
    attributes?: IAttributeInfo[];
    brandInfo?: IBrandInfo;
    sizeChartInfo?: IProductSizeChartInfo;
    compatibilityInfo?: ICompatibilityInfo;
    kitMigrationInfo?: IKitMigration;
  }

  export interface ISingleInputNumberCompatibilityInfo {
    vehicleInfoList?: IVehicleSetting[];
  }

  export interface ISingleInputNumberVehicleSetting {
    brandId?: number;
    modelId?: number;
    yearId?: number;
    versionId?: number;
  }

  export interface ISingleInputNumberBetterRatioImage {
    outpaintingImageId?: string;
    /** such as [x, y, w, h] */
    location?: number[];
    croppedImageId?: string;
    absoluteLocation?: number[];
  }

  export interface ISingleInputNumberModelDetail {
    id?: number;
    weight?: IWeight;
    dimension?: IDimension;
  }

  export interface ISingleInputNumberDimension {
    width?: number;
    length?: number;
    height?: number;
  }

  export interface ISingleInputNumberAttributeInfo {
    attributeId?: number;
    attributeValues?: IAttributeValueInfo[];
  }

  export interface ISingleInputNumberAttributeValueInfo {
    valueId?: number;
    customValue?: ICustomValue;
  }

  export interface ISingleInputNumberCustomValue {
    rawValue?: string;
    unit?: string;
  }

  export interface ISingleInputNumberBrandInfo {
    brandId?: number;
  }

  export interface ISingleInputNumberProductSizeChartInfo {
    sizeChartImage?: string;
    sizeChartTemplate?: ISizeChartTemplate;
    /** see to SizeChartValueType */
    sizeChartValueType?: number;
  }

  export interface ISingleInputNumberGetAlreadyBeOptimizedProductListRequest {
    shopId?: number;
    cursor?: string;
    /** no more than 50 */
    limit?: number;
    /** see OptimizerType */
    optimizerType?: number;
  }

  export interface ISingleInputNumberGetAlreadyBeOptimizedProductListResponse {
    debugMessage?: string;
    data?: IAlreadyBeOptimizedProductListData;
  }

  export interface ISingleInputNumberAlreadyBeOptimizedProductListData {
    pageInfo?: IPageInfo;
    products?: IAlreadyBeOptimizedProduct[];
  }

  export interface ISingleInputNumberAlreadyBeOptimizedProduct {
    productId?: number;
    /** unix time to be auto-corrected */
    optimizedTime?: number;
    currentValue?: IProductInfo;
    replaceValue?: IProductInfo;
    optimizerType?: number;
    taskId?: number;
  }

  export interface ISingleInputNumberGetWillBeOptimizedInfoByProductIDsRequest {
    shopId?: number;
    productIds?: number[];
    optimizerTypes?: number[];
  }

  export interface ISingleInputNumberGetWillBeOptimizedInfoByProductIDsResponse {
    debugMessage?: string;
    data?: IWillBeOptimizedInfoByProductIDsData;
  }

  export interface ISingleInputNumberWillBeOptimizedInfoByProductIDsData {
    products?: IWillBeOptimizedProduct[];
  }

  export interface ISingleInputNumberGetAlreadyBeOptimizedInfoByProductIDsRequest {
    shopId?: number;
    /** no more than 10 */
    productIds?: number[];
    optimizerTypes?: number[];
  }

  export interface ISingleInputNumberGetAlreadyBeOptimizedInfoByProductIDsResponse {
    debugMessage?: string;
    data?: IAlreadyBeOptimizedInfoByProductIDsData;
  }

  export interface ISingleInputNumberAlreadyBeOptimizedInfoByProductIDsData {
    products?: IAlreadyBeOptimizedProduct[];
  }

  export interface ISingleInputNumberFailProduct {
    productId?: number;
    errorCode?: number;
    errorDetailList?: IErrorDetail[];
  }

  export interface ISingleInputNumberErrorDetail {
    key?: string;
    value?: string;
  }

  export interface ISingleInputNumberBatchConfirmAlreadyBeOptimizedProductRequest {
    shopId?: number;
    /** deprecated */
    productIds?: number[];
    /** deprecated */
    optimizerTypes?: number[];
    confirmList?: IConfirmAlreadyBeOptimizedProduct[];
    audit?: IAudit;
  }

  export interface ISingleInputNumberConfirmAlreadyBeOptimizedProduct {
    taskId?: number;
    productId?: number;
    optimizerType?: number;
  }

  export interface ISingleInputNumberAudit {
    source?: string;
    operator?: string;
    reason?: string;
  }

  export interface ISingleInputNumberBatchConfirmAlreadyBeOptimizedProductResult {
    failProducts?: IFailConfirmAlreadyBeOptimizedProduct[];
  }

  export interface ISingleInputNumberFailConfirmAlreadyBeOptimizedProduct {
    productId?: number;
    failDetail?: IFailDetail[];
  }

  export interface ISingleInputNumberFailDetail {
    optimizerType?: number;
    taskId?: number;
    errorCode?: number;
  }

  export interface ISingleInputNumberBatchConfirmAlreadyBeOptimizedProductResponse {
    debugMessage?: string;
    data?: IBatchConfirmAlreadyBeOptimizedProductResult;
  }

  export interface ISingleInputNumberBatchConfirmWillBeOptimizedProductRequest {
    shopId?: number;
    optimizerType?: number;
    products?: IConfirmProduct[];
    audit?: IAudit;
    /** quick operation: reject all/ accept all */
    /** see SellerOperation */
    operation?: number;
  }

  export interface ISingleInputNumberConfirmProduct {
    productId?: number;
    taskId?: number;
    productInfo?: IConfirmProductInfo;
    taskLastMtime?: number;
  }

  export interface ISingleInputNumberBatchConfirmWillBeOptimizedProductResponse {
    debugMessage?: string;
    data?: IBatchConfirmWillBeOptimizedProductResult;
  }

  export interface ISingleInputNumberBatchConfirmWillBeOptimizedProductResult {
    failProducts?: IFailProduct[];
  }

  export interface ISingleInputNumberBatchConfirmWillBeOptimizedProductResults {
    failProducts?: IFailProduct[];
  }

  export interface ISingleInputNumberConfirmAllAlreadyBeOptimizedProductRequest {
    shopId?: number;
    optimizerType?: number;
    audit?: IAudit;
    productIds?: number[];
  }

  export interface ISingleInputNumberConfirmAllAlreadyBeOptimizedProductResponse {
    debugMessage?: string;
  }

  export interface ISingleInputNumberBatchRejectWillBeOptimizedProductRequest {
    shopId?: number;
    optimizerType?: number;
    products?: IRejectProduct[];
    audit?: IAudit;
  }

  export interface ISingleInputNumberRejectProduct {
    productId?: number;
  }

  export interface ISingleInputNumberBatchRejectWillBeOptimizedProductResponse {
    debugMessage?: string;
    data?: IBatchRejectWillBeOptimizedProductResult;
  }

  export interface ISingleInputNumberBatchRejectWillBeOptimizedProductResult {
    failProducts?: IFailProduct[];
  }

  export interface ISingleInputNumberPartialConfirmWillBeOptimizedProductRequest {
    shopId?: number;
    optimizerType?: number;
    product?: IPartialConfirmProduct;
    audit?: IAudit;
  }

  export interface ISingleInputNumberPartialConfirmProduct {
    productId?: number;
    productInfo?: IConfirmProductInfo;
    taskId?: number;
    taskLastMtime?: number;
  }

  export interface ISingleInputNumberConfirmProductInfo {
    modelList?: IEditModel[];
    weight?: IWeight;
    stdTierVariationList?: IOptimizedStdTierVariation[];
    longImages?: ILongImages;
    specification?: ISpecificationOperation;
    sizeChartImage?: ISizeChartImageOperation;
    vehicleCompatibility?: IVehicleCompatibilityOperation;
    sizeChartTemplate?: ISizeChartTemplateOperation;
  }

  export interface ISingleInputNumberVehicleCompatibilityOperation {
    vehicleSettingOperation?: IVehicleSettingOperation[];
  }

  export interface ISingleInputNumberVehicleSettingOperation {
    brandId?: number;
    modelId?: number;
    yearId?: number;
    versionId?: number;
    operation?: number;
  }

  export interface ISingleInputNumberSizeChartImageOperation {
    /** see SellerOperation */
    operation?: number;
    /** seller selected, if only reject in list page, it should be "" */
    selectedImage?: string;
  }

  export interface ISingleInputNumberSizeChartTemplateOperation {
    /** see SellerOperation */
    operation?: number;
  }

  export interface ISingleInputNumberLongImages {
    /** see SellerOperation */
    operation?: number;
  }

  export interface ISingleInputNumberSpecificationOperation {
    brandOperation?: IBrandOperation;
    attributesOperation?: IAttributeOperation[];
  }

  export interface ISingleInputNumberBrandOperation {
    /** see SellerOperation */
    operation?: number;
  }

  export interface ISingleInputNumberAttributeOperation {
    attributeId?: number;
    /** see SellerOperation */
    operation?: number;
  }

  export interface ISingleInputNumberPartialConfirmWillBeOptimizedProductResponse {
    debugMessage?: string;
    data?: IPartialConfirmWillBeOptimizedProduct;
  }

  export interface ISingleInputNumberPartialConfirmWillBeOptimizedProduct {
    failProduct?: IFailProduct;
  }

  export interface ISingleInputNumberEditModelList {
    models?: IEditModel[];
  }

  export interface ISingleInputNumberEditModel {
    id?: number;
    weight?: IWeight;
    dimension?: IModelDimension;
  }

  export interface ISingleInputNumberModelDimension {
    /** see SellerOperation */
    operation?: number;
  }

  export interface ISingleInputNumberWeight {
    /** Keep 2 decimal places. so it will plus 100. */
    value?: number;
    /** such as "1 kg". if unit is g. value will be 100000. if unit is kg. value will be 100 */
    /** see WeightUnitType */
    unit?: number;
  }

  export interface ISingleInputNumberStdTierVariation {
    id?: number;
    index?: number;
    groupId?: number;
    valueList?: IStandardiseTierVariationValue[];
    customValue?: string;
    nameCanOperation?: boolean;
    subTypeCanOperation?: boolean;
    displayName?: string;
  }

  export interface ISingleInputNumberReportOptimizedProductResultRequest {
    shopId?: number;
    productId?: number;
    reportData?: IReportOptimizedProduct[];
    audit?: IAudit;
  }

  export interface ISingleInputNumberReportOptimizedProduct {
    optimizerType?: number;
    taskId?: number;
    stdTierVariationList?: IReportStdTierVariation;
    longImages?: IReportLongImages;
    dimension?: IReportDimension;
    gtinSpecification?: IReportGtinSpecification;
    sizeChartImage?: IReportSizeChartImage;
    sizeChartTemplate?: IReportSizeChartTemplate;
    taskLastMtime?: number;
    jsonKit?: string;
  }

  export interface ISingleInputNumberReportDimension {
    modelList?: IReportOptimizedModel[];
  }

  export interface ISingleInputNumberReportOptimizedModel {
    id?: number;
    /** see SellerOperation */
    operation?: number;
    dimension?: IDimension;
  }

  export interface ISingleInputNumberReportStdTierVariation {
    stdTierVariationOperation?: IOptimizedStdTierVariation[];
    stdTierVariationList?: IStdTierVariation[];
  }

  export interface ISingleInputNumberReportLongImages {
    /** see SellerOperation */
    operation?: number;
    reportImage?: IReportLongImage;
    categoryIds?: number[];
  }

  export interface ISingleInputNumberReportLongImage {
    selectedLongImages?: string[];
  }

  export interface ISingleInputNumberReportGtinSpecification {
    operation?: ISpecificationOperation;
    gtin?: string;
    brandInfo?: IBrandInfo;
    attributes?: IAttributeInfo[];
  }

  export interface ISingleInputNumberReportSizeChartImage {
    /** see SellerOperation */
    operation?: number;
    selectedSizeChart?: string;
  }

  export interface ISingleInputNumberReportSizeChartTemplate {
    /** see SellerOperation */
    operation?: number;
    savedSizeChartTemplateId?: number;
  }

  export interface ISingleInputNumberReportOptimizedProductResultResponse {
    debugMessage?: string;
  }

  export interface ISingleInputNumberOptimizedStandardiseTierVariationValue {
    index?: number;
    /** see SellerOperation */
    operation?: number;
  }

  export interface ISingleInputNumberOptimizedStdTierVariation {
    index?: number;
    /** see SellerOperation */
    operation?: number;
    valueList?: IOptimizedStandardiseTierVariationValue[];
    subTypeValue?: ISubTypeValue;
  }

  export interface ISingleInputNumberSubTypeValue {
    groupId?: number;
    /** see SellerOperation */
    operation?: number;
  }

  export interface ISingleInputNumberGetLastAutoOptimizedDeadlineRequest {
    shopId?: number;
  }

  export interface ISingleInputNumberGetLastAutoOptimizedDeadlineResponse {
    debugMessage?: string;
    /** unix time */
    deadlineToFix?: number;
  }

  export interface ISingleInputNumberGetOptimizerSettingRequest {
    shopId?: number;
    /** repeated uint64 item_id_list = 2[(sp.idl.field) = { */
    /** repeated: { */
    /** unique: true */
    /** min_items: 1 */
    /** max_items: 50 */
    /** } */
    /** }];			// support item level in the future */
  }

  export interface ISingleInputNumberGetOptimizerSettingResponse {
    debugMessage?: string;
    data?: IGetOptimizerSettingData;
  }

  export interface ISingleInputNumberGetOptimizerSettingData {
    setting?: IShopSetting;
  }

  export interface ISingleInputNumberShopSetting {
    aigcCoverImage?: boolean;
  }

  export interface ISingleInputNumberSetOptimizerSettingRequest {
    shopId?: number;
    setting?: IShopSetting;
  }

  export interface ISingleInputNumberSetOptimizerSettingResponse {
    debugMessage?: string;
  }

  export interface ISingleInputNumberGetProductCoverListRequest {
    shopId?: number;
    /** CoverType */
    coverType?: number;
    pageNumber?: number;
    pageSize?: number;
  }

  export interface ISingleInputNumberGetProductCoverListResponse {
    debugMessage?: string;
    data?: IProductCoverListData;
  }

  export interface ISingleInputNumberProductCoverListData {
    pageInfo?: IPages;
    coverImageInfo?: IProductCoverImageInfo[];
  }

  export interface ISingleInputNumberProductCoverImageInfo {
    itemId?: number;
    itemName?: string;
    coverImage?: string;
    /** from AIGC side. */
    aigcImageList?: string[];
    /** from upload service. */
    uploadImageList?: string[];
    /** from upload disable item db, seller reject to show AIGC image */
    disabledTag?: boolean;
  }

  export interface ISingleInputNumberPages {
    pageNum?: number;
    pageSize?: number;
    total?: number;
  }

  export interface ISingleInputNumberGetProductCoverDetailRequest {
    shopId?: number;
    itemId?: number;
    /** CoverType */
    coverType?: number;
  }

  export interface ISingleInputNumberGetProductCoverDetailResponse {
    debugMessage?: string;
    data?: IProductCoverInfoResultData;
  }

  export interface ISingleInputNumberProductCoverInfoResultData {
    coverImageDetail?: IProductCoverImageDetail;
  }

  export interface ISingleInputNumberProductCoverImageDetail {
    itemId?: number;
    itemName?: string;
    coverImage?: string;
    /** from AIGC side. */
    aigcImageList?: string[];
    /** from upload service. */
    uploadImageList?: string[];
    /** from AIGC convert to second. */
    optimizedTime?: number;
  }

  export interface ISingleInputNumberDisableProductCoverRequest {
    shopId?: number;
    itemId?: number;
    disable?: boolean;
    /** CoverType such as AIGC_IMAGE */
    coverType?: number;
    feedback?: IFeedback;
  }

  export interface ISingleInputNumberFeedback {
    selectedValue?: number;
    customizedValue?: string;
  }

  export interface ISingleInputNumberDisableProductCoverResponse {
    debugMessage?: string;
  }

  export interface ISingleInputNumberDeleteProductCoverImageRequest {
    shopId?: number;
    itemId?: number;
    deletedCoverInfo?: IDeletedCoverInfo;
    feedback?: IFeedback;
  }

  export interface ISingleInputNumberDeletedCoverInfo {
    imageValue?: IDeleteImageValue;
  }

  export interface ISingleInputNumberDeleteImageValue {
    aigcImage?: string;
    sellerUploadImage?: string;
  }

  export interface ISingleInputNumberDeleteProductCoverImageResponse {
    debugMessage?: string;
  }

  export interface ISingleInputNumberSizeChartTemplate {
    virtualCategoryId?: number;
    table?: ISizeChartTable;
    name?: string;
    /** unused field. just define it to support to decode MPI input */
    linkedItemCount?: number;
  }

  export interface ISingleInputNumberSizeChartTable {
    /** columns of table */
    columns?: IColumn[];
    /** unit of the table */
    unit?: string;
  }

  export interface ISingleInputNumberColumn {
    /** column header */
    header?: IMeasurementHeader;
    /** column cells */
    cells?: ICell[];
  }

  export interface ISingleInputNumberMeasurementHeader {
    measurementId?: number;
    /** measurement name */
    name?: string;
    /** 1: normal, 2: size convention */
    type?: number;
    /** 1: dropdown, 2: single input number 3; range input number */
    inputType?: number;
    /** unit of column, if uint is null, use table unit */
    unit?: string;
    /** be nil for half-standardized size chart template */
    displayNames?: IMultiLangList;
    isKeyMeasurement?: boolean;
  }

  export interface ISingleInputNumberCell {
    dropDownValue?: IDropDownOption;
    rangeInputValue?: IRangeInputNumber;
    singleInputValue?: ISingleInputNumber;
  }

  export interface ISingleInputNumberDropDownOption {
    /** attribute value id if measurement is size covetion. be 0 for half-standardized size chart template */
    attrValueId?: number;
    value?: string;
    /** multiple language display. be nil for half-standardized size chart template */
    displayValues?: IMultiLangList;
  }

  export interface ISingleInputNumberSingleInputNumber {
    /** true value multi 100000 */
    value?: number;
  }

  export interface ISingleInputNumberRangeInputNumber {
    /** true value multi 100000 */
    begin?: number;
    /** true value multi 100000 */
    end?: number;
  }

  export interface ISingleInputNumberMultiLangList {
    /** multiple language display */
    multiLangValues?: IValWithLang[];
  }

  export interface ISingleInputNumberValWithLang {
    /** UpCase code */
    language?: string;
    value?: string;
  }

}
