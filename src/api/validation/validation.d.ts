export namespace validation {
  // proto syntax: proto2  

  // proto package name: validation  

  export interface IConstant {
  }

  export interface ICreateGroupRequest {
    groupType?: number;
    region?: string;
    groupName?: string;
    groupDesc?: string;
    operator?: string;
  }

  export interface ICreateGroupResponse {
    debugMsg?: string;
    groupId?: number;
    spErrorCode?: number;
    spDebugMsg?: string;
  }

  export interface IUpdateGroupRequest {
    groupId?: number;
    groupName?: string;
    groupDesc?: string;
    operator?: string;
  }

  export interface IUpdateGroupResponse {
    debugMsg?: string;
    spErrorCode?: number;
    spDebugMsg?: string;
  }

  export interface IGetGroupDetailRequest {
    groupId?: number;
  }

  export interface IGetGroupDetailResponse {
    debugMsg?: string;
    group?: IGroup;
    spErrorCode?: number;
    spDebugMsg?: string;
  }

  export interface ISearchGroupRequest {
    filterType?: number;
    groupType?: number;
    region?: string;
    groupId?: number;
    groupName?: string;
    groupDesc?: string;
    element?: number;
    pageSize?: number;
    currentPage?: number;
  }

  export interface ISearchGroupResponse {
    debugMsg?: string;
    count?: number;
    groups?: IGroup[];
    spErrorCode?: number;
    spDebugMsg?: string;
  }

  export interface IDeleteGroupRequest {
    groupId?: number;
    operator?: string;
  }

  export interface IDeleteGroupResponse {
    debugMsg?: string;
    spErrorCode?: number;
    spDebugMsg?: string;
  }

  export interface IDeleteGroupRuleRequest {
    groupId?: number;
    operator?: string;
  }

  export interface IDeleteGroupRuleResponse {
    debugMsg?: string;
    spErrorCode?: number;
    spDebugMsg?: string;
  }

  export interface IAddElementListRequest {
    groupId?: number;
    elementList?: number[];
    operator?: string;
  }

  export interface IAddElementListResponse {
    debugMsg?: string;
    spErrorCode?: number;
    spDebugMsg?: string;
  }

  export interface IDeleteElementRequest {
    elementId?: number;
    operator?: string;
  }

  export interface IDeleteElementResponse {
    debugMsg?: string;
    spErrorCode?: number;
    spDebugMsg?: string;
  }

  export interface IGetElementListRequest {
    groupId?: number;
    element?: number;
    pageSize?: number;
    currentPage?: number;
  }

  export interface IGetElementListResponse {
    debugMsg?: string;
    elements?: IElement[];
    count?: number;
    spErrorCode?: number;
    spDebugMsg?: string;
  }

  export interface IUploadElementListRequest {
    fileKey?: string;
    groupId?: number;
    operator?: string;
  }

  export interface IUploadElementListResponse {
    debugMsg?: string;
    spErrorCode?: number;
    spDebugMsg?: string;
  }

  export interface IDownloadElementListRequest {
    groupId?: number;
  }

  export interface IDownloadElementListResponse {
    debugMsg?: string;
    fileKey?: string;
    fileName?: string;
    spErrorCode?: number;
    spDebugMsg?: string;
  }

  export interface IGroup {
    id?: number;
    groupType?: number;
    region?: string;
    groupName?: string;
    groupDesc?: string;
    combineRuleId?: number;
    status?: number;
    ctime?: number;
    mtime?: number;
    createdBy?: string;
    modifiedBy?: string;
    groupRuleMtime?: number;
    groupRuleModifiedBy?: string;
  }

  export interface IElement {
    id?: number;
    groupType?: number;
    region?: string;
    element?: number;
    groupId?: number;
    status?: number;
    ctime?: number;
    mtime?: number;
    createdBy?: string;
    modifiedBy?: string;
  }

  export interface IGetRuleSubCatListRequest {
    /** int value of enum ProductType */
    productType?: number;
    sellerType?: number;
    sellerCategory?: number;
    region?: string;
    filterType?: number;
    combineRuleId?: number;
  }

  export interface IGetRuleSubCatListResponse {
    debugMsg?: string;
    ruleSubCat?: number[];
    subCategoryList?: string[];
    spErrorCode?: number;
    spDebugMsg?: string;
  }

  export interface ICheckBasicRuleRequest {
    groupId?: number;
    /** deprecated */
    basicRuleType?: number;
    /** deprecated */
    basicRuleStrType?: string;
    ruleName?: string;
  }

  export interface ICheckBasicRuleResponse {
    debugMsg?: string;
    canUpdate?: boolean;
    spErrorCode?: number;
    spDebugMsg?: string;
  }

  export interface ICreateProductRuleDataRequest {
    /** int value of enum ProductType */
    productType?: number;
    region?: string;
    sellerType?: number;
    sellerCategory?: number;
    operator?: string;
  }

  export interface ICreateProductRuleDataResponse {
    debugMsg?: string;
    combineRuleId?: number;
    spErrorCode?: number;
    spDebugMsg?: string;
  }

  export interface IDeleteCombineRuleRequest {
    combineRuleId?: number;
  }

  export interface IDeleteCombineRuleResponse {
    debugMsg?: string;
    spErrorCode?: number;
    spDebugMsg?: string;
  }

  export interface IGetProhibitedCharRecordRequest {
    basicRuleId?: number;
    pageSize?: number;
    currentPage?: number;
    combineRuleId?: number;
    /** could be "name", "description", "tier_variation", "tier_option"; default is "name" */
    fieldName?: string;
    isWhitelist?: boolean;
  }

  export interface IGetProhibitedCharRecordResponse {
    debugMsg?: string;
    count?: number;
    prohibitCharFileRecords?: IProhibitCharFileRecord[];
    spErrorCode?: number;
    spDebugMsg?: string;
  }

  export interface IProhibitCharFileRecord {
    id?: number;
    combineRuleId?: number;
    fileKey?: string;
    fileName?: string;
    createdBy?: string;
    ctime?: number;
    /** could be "name", "description" or "tier" */
    fieldName?: string;
    isWhitelist?: boolean;
  }

  export interface IGetDimensionCategoryConfigRequest {
    /** deprecated */
    basicRuleId?: number;
    combineRuleId?: number;
    /** 1.Normal 2.with Dimension config */
    categoryTreeType?: number;
  }

  export interface IGetDimensionCategoryConfigResponse {
    debugMsg?: string;
    basicRules?: IDimensionBasicRule[];
    spErrorCode?: number;
    spDebugMsg?: string;
  }

  export interface IDimensionBasicRule {
    dimensionCatConfig?: IDimensionCatConfig[];
    combineRuleDetail?: ICombineRuleDetail;
  }

  export interface IDimensionCatConfig {
    catId?: string;
    numberCatId?: number;
    catName?: string;
    isDimensionMandatory?: boolean;
    sourceCatStatus?: number;
    children?: IDimensionCatConfig[];
  }

  export interface IAdminProduct {
    name?: IName;
    description?: IDescription;
    sku?: ISku;
    media?: IMedia;
    text?: IText;
    price?: IAdminPrice;
    stock?: IStock;
    tier?: ITierVariation;
    purchase?: IPurchase;
    weight?: IAdminWeight;
    dimension?: IAdminDimension;
    dts?: IDts;
    sizeChart?: ISizeChart;
    gtin?: IGTIN;
  }

  export interface ISizeChart {
    mandatoryCatIdList?: number[];
  }

  export interface IName {
    nameMandatory?: boolean;
    nameImageDuplicated?: boolean;
    nameLengthMin?: number;
    nameLengthMax?: number;
    nameProhibitChar?: string[];
    nameProhibitCharFileKey?: string;
    /** the whitelist for name characters. */
    /** name_prohibit_char_file_key and name_allow_char_file_key cannot both exist */
    nameAllowCharFileKey?: string;
  }

  export interface IDescription {
    descriptionMandatory?: boolean;
    descriptionLengthMin?: number;
    descriptionLengthMax?: number;
    descriptionRichtextLengthMin?: number;
    descriptionRichtextLengthMax?: number;
    descriptionRichtextImageQuantityMin?: number;
    descriptionRichtextImageQuantityMax?: number;
    descriptionRichtextImageHeightMin?: number;
    descriptionRichtextImageHeightMax?: number;
    descriptionRichtextImageWidthMin?: number;
    descriptionRichtextImageWidthMax?: number;
    descriptionImageAspectRatioMax?: number;
    descriptionImageAspectRatioMin?: number;
    descriptionProhibitCharFileKey?: string;
    descriptionAllowCharFileKey?: string;
  }

  export interface ISku {
    skuParentLengthMin?: number;
    skuParentLengthMax?: number;
    skuLengthMin?: number;
    skuLengthMax?: number;
  }

  export interface IMedia {
    mediaImageMandatory?: boolean;
    mediaImageExist?: boolean;
    mediaImageSizeLimit?: number;
    mediaImageQuantityMin?: number;
    mediaImageQuantityMax?: number;
    mediaImageLengthLimit?: number;
    mediaImageHeightLimit?: number;
    mediaImageFormat?: string;
    mediaVideoExist?: boolean;
    mediaVideoSizeLimit?: number;
    mediaVideoDurationMin?: number;
    mediaVideoDurationMax?: number;
    mediaVideoFormat?: string;
  }

  export interface IText {
    textMultiplier?: number;
  }

  export interface IAdminPrice {
    priceMandatory?: boolean;
    priceMin?: number;
    priceMax?: number;
    priceGapLimit?: number;
    priceWholesaleThresholdMin?: number;
    priceWholesaleThresholdMax?: number;
    priceWholesaleLimit?: boolean;
    priceUsMin?: number;
    priceUsMax?: number;
    priceHkMin?: number;
    priceHkMax?: number;
    /** The maximum max_count of the wholesale. */
    /** This field will be ignored even if sent in the request of edit_combine_rule. */
    priceWholesaleCountMax?: number;
  }

  export interface IStock {
    stockMandatory?: boolean;
    stockMin?: number;
    stockMax?: number;
  }

  export interface ITierVariation {
    tierVariationNumLimit?: number;
    tierNameLengthMin?: number;
    tierNameLengthMax?: number;
    tierOptionNameLengthMin?: number;
    tierOptionNameLengthMax?: number;
    tierImageExist?: boolean;
    tierImageOptionEqual?: boolean;
    tierNameDuplicated?: boolean;
    tierOptionDuplicated?: boolean;
    tierModelQuantityLimit?: number;
    tierModelOptionEqual?: boolean;
    oneTierOptionQuantityLimit?: number;
    totalOptionQuantityLimit?: number;
    tierVariationProhibitCharFileKey?: string;
    tierVariationAllowCharFileKey?: string;
    tierOptionProhibitCharFileKey?: string;
    tierOptionAllowCharFileKey?: string;
  }

  export interface IPurchase {
    purchaseMinpqMin?: number;
    purchaseMinpqMax?: number;
    purchaseMinpqMinwhosaleLe?: boolean;
    purchaseMinpqMaxpqLe?: boolean;
    purchaseMaxpqExclusion?: boolean;
    purchasePerorderMaxpqMin?: number;
    purchasePerorderMaxpqMax?: number;
    purchasePeriodMaxpqStarttimeMandatory?: boolean;
    purchasePeriodMaxpqStarttimeRangeLimit?: boolean;
    purchasePeriodMaxpqMin?: number;
    purchasePeriodMaxpqMax?: number;
    purchasePeriodMaxpqCaculationDaysMin?: number;
    purchasePeriodMaxpqCaculationDaysMax?: number;
    purchasePeriodMaxpqModelMandatory?: boolean;
    purchasePeriodMaxpqRepeatTimeMin?: number;
    purchasePeriodMaxpqRepeatTimeMax?: number;
  }

  export interface IAdminWeight {
    weightMandatory?: boolean;
    weightMin?: number;
    weightMax?: number;
  }

  export interface IAdminDimension {
    dimensionLengthMin?: number;
    dimensionLengthMax?: number;
    dimensionWidthMin?: number;
    dimensionWidthMax?: number;
    dimensionHeightMin?: number;
    dimensionHeightMax?: number;
    dimensionMandatoryCatList?: number[];
  }

  export interface IDts {
    /** deprecated */
    dtsNonpreorderLimit?: number;
    dtsPreorder?: IPreOrderDts[];
    dtsNonpreorderMin?: number;
    dtsNonpreorderMax?: number;
  }

  export interface IPreOrderDts {
    catIdList?: number[];
    min?: number;
    max?: number;
    description?: string;
    isEnabled?: boolean;
    modifiedBy?: string;
    mtime?: number;
    needUpdate?: boolean;
  }

  export interface IGetCombineRuleRequest {
    /** int value of enum ProductType */
    productType?: number;
    sellerType?: number;
    sellerCategory?: number;
    region?: string;
    filterType?: number;
    combineRuleId?: number;
  }

  export interface IGetCombineRuleResponse {
    combineRuleDetails?: ICombineRuleDetail[];
    product?: IAdminProduct;
    combineRuleId?: number;
    spErrorCode?: number;
    spDebugMsg?: string;
  }

  export interface ICombineRuleDetail {
    basicRuleName?: string;
    ruleStatus?: number;
    description?: string;
    ctime?: number;
    mtime?: number;
    createdBy?: string;
    modifiedBy?: string;
  }

  export interface IEditCombineRuleRequest {
    combineRuleId?: number;
    groupId?: number;
    combineRuleDetails?: ICombineRuleDetail[];
    deleteSubCategory?: string[];
    product?: IAdminProduct;
    operator?: string;
    /** int value of enum EditType */
    editType?: number;
    region?: string;
    /** deprecated: use product.name.name_prohibit_char_file_key / name_allow_char_file_key instead. */
    /** if name_prohibit_char_file_key or name_allow_char_file_key is set, this field would be ignored. */
    prohibitFileKey?: string;
  }

  export interface IEditCombineRuleResponse {
    combineRuleId?: number;
    debugMsg?: string;
    spErrorCode?: number;
    spDebugMsg?: string;
    failedRule?: string;
  }

  export interface ISearchRuleEditLogRequest {
    regionList?: string[];
    /** int value of enum ProductType */
    productType?: number;
    /** int value of enum RuleType */
    ruleType?: number;
    ruleName?: string[];
    groupId?: number;
    modifiedBy?: string;
    startUpdateTime?: number;
    endUpdateTime?: number;
    pageSize?: number;
    currentPage?: number;
  }

  export interface ISearchRuleEditLogResponse {
    ruleEditLogs?: IRuleEditLog[];
    totalCount?: number;
    debugMsg?: string;
  }

  export interface IRuleEditLog {
    region?: string;
    productType?: number;
    oldValue?: string;
    newValue?: string;
    operator?: string;
    updateTime?: number;
    sellerType?: number;
    sellerCategory?: number;
    groupId?: number;
    ruleType?: number;
  }

  export interface ISearchGroupEditLogRequest {
    regionList?: string[];
    groupId?: number;
    /** 1.Normal 2.Delete */
    operateType?: number;
    modifiedBy?: string;
    startUpdateTime?: number;
    endUpdateTime?: number;
    pageSize?: number;
    currentPage?: number;
  }

  export interface ISearchGroupEditLogResponse {
    groupEditLogs?: IGroupEditLog[];
    totalCount?: number;
    debugMsg?: string;
  }

  export interface IGroupEditLog {
    region?: string;
    groupId?: number;
    operateType?: number;
    oldValue?: string;
    newValue?: string;
    operator?: string;
    updateTime?: number;
  }

  export interface IGetCategoryTreeWithRuleRequest {
    region?: string;
    combineRuleId?: number;
  }

  export interface IGetCategoryTreeWithRuleResponse {
    debugMsg?: string;
    spErrorCode?: number;
    spDebugMsg?: string;
    basicRules?: ICategoryWithRule[];
  }

  export interface ICategoryWithRule {
    categoryId?: number;
    name?: string;
    children?: ICategoryWithRule[];
    isLinkedVirtualCategory?: boolean;
    isSizeChartMandatory?: boolean;
    isGtinMandatory?: boolean;
    isGtinFlexible?: boolean;
  }

  export interface IGenerateAnatelAttributeValidationResultRequest {
    massProcessingMeta?: IMassProcessingMeta;
    paging?: IPaging;
    startDate?: string;
    endDate?: string;
    region?: string;
  }

  export interface IMassProcessingMeta {
    operatorEmail?: string;
  }

  export interface IPaging {
    cursor?: string;
    offset?: number;
  }

  export interface IGenerateAnatelAttributeValidationResultResponse {
    error?: number;
    errorMsg?: string;
    paging?: IPaging;
    data?: IAnatelAttributeValidationData[];
  }

  export interface IAnatelAttributeValidationData {
    validationTime?: string;
    itemId?: string;
    shopId?: string;
    isSellerNormal?: string;
    isSellerMall?: string;
    isSellerPs?: string;
    isSellerManaged?: string;
    isSellerFbs?: string;
    isSellerLocal?: string;
    isSellerCb?: string;
    categoryId?: string;
    categoryName?: string;
    toggleStatus?: string;
    validationResult?: string;
  }

  export interface IGTIN {
    mandatoryCatList?: number[];
    flexibleCatList?: number[];
  }

  export interface IGetCategoryWithRuleRequest {
    region?: string;
    combineRuleId?: number;
    categoryId?: number;
  }

  export interface IGetCategoryWithRuleResponse {
    debugMsg?: string;
    spErrorCode?: number;
    spDebugMsg?: string;
    category?: ICategoryWithRuleValue;
  }

  export interface ICategoryWithRuleValue {
    categoryId?: number;
    name?: string;
    mtime?: number;
    operator?: string;
    isGtinMandatory?: boolean;
    isGtinFlexible?: boolean;
  }

  export interface IGenerateGTINValidationResultRequest {
    massProcessingMeta?: IMassProcessingMeta;
    paging?: IPaging;
    startDate?: string;
    endDate?: string;
    region?: string;
    shopId?: number;
    categoryId?: number;
    ruleNames?: number[];
  }

  export interface IGenerateGTINValidationResultResponse {
    error?: number;
    errorMsg?: string;
    paging?: IPaging;
    data?: IGTINValidationData[];
  }

  export interface IGTINValidationData {
    gtin?: string;
    itemId?: string;
    shopId?: string;
    isSellerNormal?: string;
    isSellerMall?: string;
    isSellerPs?: string;
    isSellerManaged?: string;
    isSellerFbs?: string;
    isSellerLocal?: string;
    isSellerCb?: string;
    categoryId?: string;
    categoryName?: string;
    gtinValidationTime?: string;
    isGtinValidation?: string;
    gtinValidationRule?: string;
    isGs1Validation?: string;
    gs1ValidationTime?: string;
    gs1ValidationResult?: string;
    modelId?: string;
  }

}
