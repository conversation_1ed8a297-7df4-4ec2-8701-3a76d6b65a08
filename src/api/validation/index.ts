import { request } from 'src/api/helper';
import type { WithResponse } from 'src/api/helper/apiHandler';
import { apiHandler } from 'src/api/helper/apiHandler';
import type { validation } from './validation';

const URLPrefix = '/wsa/marketplace/listing/upload/upload_validation';

function createRequestFunc<IRequest, IResponse>(url: string) {
  return function (
    requestBody: IRequest,
    config?: { returnFullResponse?: boolean; skipError?: boolean }
  ) {
    return apiHandler(
      request<WithResponse<IResponse>>({
        url: URLPrefix + url,
        data: requestBody,
      }),
      config
    );
  };
}

export const createGroup = createRequestFunc<
  validation.ICreateGroupRequest,
  validation.ICreateGroupResponse
>('/create_group');

export const updateGroup = createRequestFunc<
  validation.IUpdateGroupRequest,
  validation.IUpdateGroupResponse
>('/update_group');

export const getGroupDetail = createRequestFunc<
  validation.IGetGroupDetailRequest,
  validation.IGetGroupDetailResponse
>('/get_group_detail');

export const searchGroup = createRequestFunc<
  validation.ISearchGroupRequest,
  validation.ISearchGroupResponse
>('/search_group');

export const deleteGroup = createRequestFunc<
  validation.IDeleteGroupRequest,
  validation.IDeleteGroupResponse
>('/delete_group');

export const deleteGroupRule = createRequestFunc<
  validation.IDeleteGroupRuleRequest,
  validation.IDeleteGroupRuleResponse
>('/delete_group_rule');

export const addElementList = createRequestFunc<
  validation.IAddElementListRequest,
  validation.IAddElementListResponse
>('/add_element_list');

export const deleteElement = createRequestFunc<
  validation.IDeleteElementRequest,
  validation.IDeleteElementResponse
>('/delete_element');

export const getElementList = createRequestFunc<
  validation.IGetElementListRequest,
  validation.IGetElementListResponse
>('/get_element_list');

export const uploadElementList = createRequestFunc<
  validation.IUploadElementListRequest,
  validation.IUploadElementListResponse
>('/upload_element_list');

export const downloadElementList = createRequestFunc<
  validation.IDownloadElementListRequest,
  validation.IDownloadElementListResponse
>('/download_element_list');

export const getRuleSubCatList = createRequestFunc<
  validation.IGetRuleSubCatListRequest,
  validation.IGetRuleSubCatListResponse
>('/get_rule_sub_cat_list');

export const checkBasicRule = createRequestFunc<
  validation.ICheckBasicRuleRequest,
  validation.ICheckBasicRuleResponse
>('/check_basic_rule');

export const createProductRuleData = createRequestFunc<
  validation.ICreateProductRuleDataRequest,
  validation.ICreateProductRuleDataResponse
>('/create_product_rule_data');

export const deleteCombineRule = createRequestFunc<
  validation.IDeleteCombineRuleRequest,
  validation.IDeleteCombineRuleResponse
>('/delete_combine_rule');

export const getProhibitedCharRecord = createRequestFunc<
  validation.IGetProhibitedCharRecordRequest,
  validation.IGetProhibitedCharRecordResponse
>('/get_prohibited_char_record');

export const getDimensionCategoryConfig = createRequestFunc<
  validation.IGetDimensionCategoryConfigRequest,
  validation.IGetDimensionCategoryConfigResponse
>('/get_dimension_category_config');

export const getCombineRule = createRequestFunc<
  validation.IGetCombineRuleRequest,
  validation.IGetCombineRuleResponse
>('/get_combine_rule');

export const editCombineRule = createRequestFunc<
  validation.IEditCombineRuleRequest,
  validation.IEditCombineRuleResponse
>('/edit_combine_rule');

export const searchRuleEditLog = createRequestFunc<
  validation.ISearchRuleEditLogRequest,
  validation.ISearchRuleEditLogResponse
>('/search_rule_edit_log');

export const searchGroupEditLog = createRequestFunc<
  validation.ISearchGroupEditLogRequest,
  validation.ISearchGroupEditLogResponse
>('/search_group_edit_log');

export const getCategoryTreeWithRule = createRequestFunc<
  validation.IGetCategoryTreeWithRuleRequest,
  validation.IGetCategoryTreeWithRuleResponse
>('/get_category_tree_with_rule');

export const generateAnatelAttributeValidationResult = createRequestFunc<
  validation.IGenerateAnatelAttributeValidationResultRequest,
  validation.IGenerateAnatelAttributeValidationResultResponse
>('/generate_anatel_attribute_validation_result');

export const getCategoryWithRule = createRequestFunc<
  validation.IGetCategoryWithRuleRequest,
  validation.IGetCategoryWithRuleResponse
>('/get_category_with_rule');

export const generateGtinValidationResult = createRequestFunc<
  validation.IGenerateGTINValidationResultRequest,
  validation.IGenerateGTINValidationResultResponse
>('/generate_gtin_validation_result');
