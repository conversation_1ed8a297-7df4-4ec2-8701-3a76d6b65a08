export enum ErrorCode {
  SUCCESS = 0,
  /** Error code range is 451400000 - 451500000 */
  /** group management error (451401001 - 451401999) */
  ERROR_SHOP_EXIST_MUTUAL = 451401001,
  ERROR_GROUP_NOT_EXIST = 451401002,
  ERROR_GROUP_DUPLICATED = 451401003,
  ERROR_GROUP_TYPE_INVALID = 451401004,
  ERROR_GROUP_NAME_LENGTH_EXCEED = 451401005,
  ERROR_GROUP_DESC_LENGTH_EXCEED = 451401006,
  ERROR_SHOP_ID_REPEATED = 451401007,
  ERROR_SHOP_LIST_NIL = 451401008,
  ERROR_GROUP_NAME_NIL = 451401009,
  ERROR_ELEMENT_NOT_EXIST = 451401010,
  ERROR_SHOP_LIST_EXCEED = 451401011,
  ERROR_SEARCH_FILTER_TYPE_INVALID = 451401012,
  ERROR_GROUP_ID_AND_COMBINED_RULE_ID_NOT_MATCH = 451401013,
  ERROR_ELEMENT_LIST_EXCEL_HEADER_INVALID = 451401014,
  /** rule management error (451402001 - 451402999) */
  ERROR_FILTER_TYPE_INVALID = 451402001,
  ERROR_REGION_INVALID = 451402002,
  ERROR_PRODUCT_TYPE_INVALID = 451402003,
  ERROR_SELLER_TYPE_INVALID = 451402004,
  ERROR_SELLER_CATEGORY_INVALID = 451402005,
  ERROR_COMBINE_RULE_ID_NIL = 451402006,
  ERROR_SUB_CAT_TYPE_NOT_CORRECT = 451402007,
  ERROR_CONFIG_VALUE_QUANTITY = 451402008,
  ERROR_CONFIG_VALUE_TYPE_INVALID = 451402009,
  ERROR_CONFIG_VALUE_TYPE_NIL = 451402010,
  ERROR_CONFIG_VALUE_INVALID = 451402011,
  ERROR_CONFIG_VALUE_NIL = 451402012,
  ERROR_CONFIG_VALUE_ID_NIL = 451402013,
  ERROR_CONFIG_FIELD_TYPE_INVALID = 451402014,
  ERROR_LEFT_GREATER_THAN_RIGHT = 451402015,
  ERROR_VALUE_NOT_IN_RANGE = 451402016,
  ERROR_NOT_SUPPORT = 451402017,
  ERROR_NO_SUCH_RULE = 451402018,
  ERROR_STATUS_INVALID = 451402019,
  ERROR_BASIC_RULE_TYPE_NIL = 451402020,
  ERROR_BASIC_RULE_TYPE_INVALID = 451402021,
  ERROR_SUB_CAT_TYPE_NIL = 451402022,
  ERROR_BASIC_RULE_STATUS_NIL = 451402023,
  ERROR_SUB_CAT_TYPE_INVALID = 451402024,
  ERROR_BASIC_RULE_NIL = 451402025,
  ERROR_BASIC_RULE_ID_NIL = 451402026,
  ERROR_BASIC_RULE_ID_NOT_CORRECT_WITH_CONFIG_VALUE = 451402027,
  ERROR_VALUE_NOT_EQUALS_YES = 451402028,
  ERROR_VALUE_NOT_EQUALS_2 = 451402029,
  ERROR_FLOAT_NOT_WITH_TWO_DIGITS = 451402030,
  ERROR_CAN_NOT_DELETE_ALL_RULES = 451402031,
  ERROR_PROHIBIT_FILE_EXCEED_MAX_COLUMN = 451402032,
  ERROR_PROHIBIT_FILE_EXCEED_MAX_LINE = 451402033,
  ERROR_IMAGE_FORMAT = 451402034,
  ERROR_VIDEO_FORMAT = 451402035,
  ERROR_DOWNLOAD_FILE = 451402036,
  ERROR_PARSE_FILE = 451402037,
  ERROR_RULE_LIST_NIL = 451402038,
  ERROR_PRODUCT_DATA_INVALID = 451402039,
  ERROR_GTIN_CATEGORY_CONFLICT = 451402040,
}

export enum ProductType {
  MPSKU = 1,
  MTSKU = 2,
}

export enum RuleType {
  LOCAL_NORMAL = 1,
  LOCAL_MALL = 2,
  LOCAL_PREFERRED = 3,
  GROUP = 7,
}

export enum GTINValidationStatus {
  MANDATORY = 1,
  FLEXIBLE = 2,
  OPTIONAL = 3,
}

