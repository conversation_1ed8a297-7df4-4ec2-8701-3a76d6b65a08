import { getCountry } from '@classification/admin-solution';
import Axios from 'axios';
import { camelCase, snakeCase } from 'lodash';
import { v4 } from 'uuid';

let COMMIT_HASH = '';
try {
  //@ts-expect-error
  COMMIT_HASH = __COMMIT_HASH__;
} catch (e) {}

const createTransform = (
  transformKey: (string?: string | undefined) => string
) => {
  // @ts-expect-error
  const transformObject = (value: T, depth = -1): T => {
    if (
      depth === 0 ||
      value == null ||
      typeof value !== 'object' ||
      value instanceof FormData
    ) {
      return value;
    }

    if (Array.isArray(value)) {
      return value.map((item) => transformObject(item, depth - 1));
    }

    // @ts-expect-error
    const objectRes = {} as T;
    for (const key in value) {
      if (value.hasOwnProperty(key)) {
        const convertKey = transformKey(key);
        objectRes[convertKey] = transformObject(value[key], depth - 1);
      }
    }
    return objectRes;
  };
  return transformObject;
};

export const deepCamel = createTransform(camelCase);

export const deepSnake = createTransform(snakeCase);

const axios = Axios.create({
  method: 'post',
  headers: {
    'Service-Name': 'item.shopee_admin',
  },
});
export default axios;
export const request = axios.request.bind(axios);

axios.interceptors.request.use(
  (config) => {
    return {
      ...config,
      data: deepSnake(config.data),
      headers: {
        ...config.headers,
        'Request-Id': v4(),
        Region: getCountry() === 'WW' ? 'ALL' : getCountry(),
        ...(getCountry() === 'WW' ? { 'shopee-baggage': 'CID=global' } : {}),
        Version: COMMIT_HASH,
      },
    };
  },
  (error) => {
    return Promise.reject(error);
  }
);

axios.interceptors.response.use(
  (response) => {
    const camelData = deepCamel(response.data);
    response.data = camelData;
    return response;
  },
  (error) => {
    const { response = {} } = error;
    if (response.data) {
      response.data = deepCamel(response.data);
    }
    return Promise.reject(error);
  }
);
