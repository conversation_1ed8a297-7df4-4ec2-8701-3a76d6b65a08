import { printErrorMessageWithCopyReqID } from 'admin-upload-common';
import type { AxiosError,AxiosPromise } from 'axios';

type ErrorDetails = Array<{
  field?: string;
  value?: string;
}>;

const stringifyErrorDetails = (errorDetails?: ErrorDetails) => {
  if (errorDetails && errorDetails.length) {
    return errorDetails.reduce((s, next) => {
      if (Object.prototype.toString.call(next) === '[object Object]') {
        const { field = '', value = '' } = next;
        return `${s}${field}(${value})\n`;
      } else {
        return `${s}(${next})`;
      }
    }, '\n');
  }
  return '';
};

export const SOUPERRORCODE = '-1000001';
export type WithResponse<T> = T & {
  spErrorCode?: number;
  spDebugMsg?: string | null;
  errDetails?: ErrorDetails;
};
export interface ApiConfig {
  returnFullResponse?: boolean;
  skipError?: boolean;
}
/**
 * @param {boolean} returnFullResponse - If set to true, will return WithResponse<T>; otherwise, will return T
 * @param {boolean} skipError - If set to true, will message.toast response error; otherwise, will keep silent
 */
export const apiHandler = async <T>(
  request: AxiosPromise<WithResponse<T>>,
  config: ApiConfig = { returnFullResponse: false, skipError: false }
): Promise<T | WithResponse<T> | undefined> => {
  const { returnFullResponse = false, skipError = false } = config;
  const result = await request.catch((err: AxiosError) => {
    // Error Handling
    if (err.response && err.response.status === 302) {
      if (err.response) {
        window.location.href = err.response?.data?.redirectUrl;
      }
    } else if (
      err.response &&
      (err.response.status === 500 || err.response.status === 403)
    ) {
      // Gateway Error
      const errorData = err.response.data;
      if (errorData.success === false && typeof errorData.error === 'object') {
        const msg =
          err.response.status === 403 && errorData.error?.message
            ? errorData.error.message
            : 'Permission require for command.';
        return {
          ...err.response,
          data: {
            spErrorCode: SOUPERRORCODE,
            spDebugMsg: `${msg  } Please apply soup permission to continue.`,
            errDetails: [],
          } as unknown as WithResponse<T>,
        };
      } else if (typeof errorData.error === 'number') {
        return {
          ...err.response,
          data: {
            spErrorCode: errorData.error,
            spDebugMsg: 'Request fail.',
            errDetails: [],
          } as unknown as WithResponse<T>,
        };
      }
    }
  });
  if (result) {
    const resp = result.data;
    if (resp.spErrorCode) {
      // Deal with error
      let errMsg = '';
      errMsg += resp.spDebugMsg ?? '';
      errMsg += stringifyErrorDetails(resp.errDetails);
      if (!errMsg) {
        errMsg = 'Request fail.';
      }
      errMsg += resp.spErrorCode ? `(SP Error Code:${resp.spErrorCode})` : '';
      !skipError &&
        printErrorMessageWithCopyReqID(
          errMsg,
          result.config.headers['Request-Id']
        );
      return resp;
    } else {
      if (returnFullResponse) {
        return resp;
      }
      delete resp.spErrorCode;
      delete resp.spDebugMsg;
      delete resp.errDetails;
      return resp as T;
    }
  }
};
