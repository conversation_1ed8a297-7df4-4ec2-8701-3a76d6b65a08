import { printErrorMessageWithCopyReqID } from 'admin-upload-common';
import type {
  AxiosError,
  AxiosPromise,
} from 'axios';

export const SOUPERRORCODE = '-1000001';
export const API_ERROR = -111;
export type WithResponse<T> = T & {
  code?: number;
  error?: number;
  msg?: string;
  debugMessage?: string | null;
  userMessage?: string | null;
  errorMsg?: string | null;
};

export interface ApiConfig {
  returnFullResponse?: boolean;
  skipError?: boolean;
}

/**
 * @param request
 * @param config
 */
export const apiHandler = async <T>(
  request: AxiosPromise<WithResponse<T>>,
  config: ApiConfig = { skipError: false },
): Promise<WithResponse<T>> => {
  const { skipError = false } = config;
  const result = await request.catch((err: AxiosError) => {
    // Error Handling
    if (err.response && err.response.status === 302) {
      if (err.response) {
        window.location.href = err.response?.data?.redirectUrl;
      }
    } else if (
      err.response &&
      (err.response.status === 500 || err.response.status === 403)
    ) {
      // Gateway Error
      const errorData = err.response.data;
      if (errorData.success === false && typeof errorData.error === 'object') {
        const msg =
          err.response.status === 403 && errorData.error?.message
            ? errorData.error.message
            : 'Permission require for command.';
        return {
          ...err.response,
          data: {
            code: SOUPERRORCODE,
            debugMsg: `${ msg } Please apply soup permission to continue.`,
          } as unknown as WithResponse<T>,
        };
      }
    }
    return {
      ...err,
      data: {
        code: API_ERROR,
        debugMsg: `API ERROR.`,
      } as unknown as WithResponse<T>,
    };
  });
  if (result) {
    const resp = result.data;
    if (resp.code || resp.error) {
      // Deal with error
      let errMsg = resp.debugMessage || resp.msg || resp.userMessage || resp.errorMsg || '';
      if (!errMsg) {
        errMsg = 'Request fail.';
      }
      errMsg += resp.code ? `(Error Code:${ resp.code || resp.error })` : '';
      !skipError &&
      printErrorMessageWithCopyReqID(
        errMsg,
        result?.config?.headers['Request-Id'],
      );
      return resp;
    } else {
      return resp;
    }
  }
  return {
    code: API_ERROR,
  } as unknown as WithResponse<T>;
};
