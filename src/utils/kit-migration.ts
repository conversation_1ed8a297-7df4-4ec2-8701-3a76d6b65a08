import { getCountry } from '@classification/admin-solution';
import type { UploadFile } from 'antd';

import type { uploadAdmin } from 'src/api/uploadAdmin/uploadAdmin';
import type {
  uploadBffAdmin} from 'src/api/uploadBffAdmin/uploadBffAdmin';
import { imageUrlPrefix } from 'src/constants';
import { getV0videoCdn,getV1v2videoCdn,  } from 'src/pages/DetailPage/functions/setFormAndState';

export const formatItemDetail = (detail: uploadAdmin.IGetItemDetailResponse) => {
  return {
    name: detail.productName,
    itemId: detail.itemId,
    shopId: detail.shopId,
    description: detail.description,
    descriptionType: detail.descriptionType,
    images: detail.productImages?.images,
    productLongImages: detail.productImages?.productLongImages,
    videos: detail.videoInfoList?.map((video: uploadAdmin.IVideoInfo) => {
      return {
        uid: video.videoId!,
        name: video.url!,
        status: 'done',
        url:
          video.version === 1 || video.version === 2
            ? `${getV1v2videoCdn(getCountry())}${video.url}`
            : `${getV0videoCdn(getCountry())}${video.url}`,
        thumbUrl: `${imageUrlPrefix}${video.thumbUrl}_tn`,
        size: 600,
        type: 'video/mp4',
      };
    }) as UploadFile[],
  }
};

export const isDefaultModel = (product: uploadBffAdmin.IProduct) => {
  const { modelList = [] } = product;
  return modelList.length === 1 && modelList[0].isDefault;
};
