import { getCountry } from '@classification/admin-solution';
import { getCurrencyConfig } from '@shopee_common/currency';
import * as Currency from '@shopee_common/currency';

export interface FormatNumberOptions {
  /** 小数位数 */
  precision?: number;

  maxPrecision?: number;

  /** 整数部分分隔符。如果为true，会采用国家设置。如果为字符，则采用该字符。其他情况则为空 */
  separator?: boolean | string;

  /** 数字小数点符号 */
  decimalPoint?: string;

  /** 是否将数字转化为简化的贴近自然语言的字符串 */
  compactNumber?: boolean;

  /**
   * 简化数字的条件，要大于该数字。
   * Only works when compactNumber is truly.
   */
  compactStartFrom?: number;

  /**
   * 简化后数字保留的小数位数。
   * Only works when compactNumber is truly.
   */
  compactPrecision?: number | false;
}

export const formatNumber = (
  value: number | string | null | undefined,
  options: FormatNumberOptions = {},
  region: string = getCountry(),
) => {
  if (value === null || value === undefined) {
    return value;
  }

  const parsedValue = parseFloat(`${ value }`);
  if (isNaN(parsedValue)) {
    return `${ value }`;
  }

  options = Object.assign(
    {
      compactStartFrom: 1000,
    },
    options,
  );

  const { compactNumber, compactStartFrom } = options;
  let { precision, maxPrecision, separator, decimalPoint, compactPrecision } = options;

  // eslint-disable-next-line @typescript-eslint/no-magic-numbers
  precision = precision !== undefined && precision >= 0 ? precision : 2;
  maxPrecision = maxPrecision !== undefined && maxPrecision >= 0 ? maxPrecision : precision;

  const currencyConfig = getCurrencyConfig(region.toUpperCase() as any);
  separator =
    typeof separator === 'string' ? separator : separator === true ? (currencyConfig.separator as string) : '';

  decimalPoint = decimalPoint ?? currencyConfig.decimalPoint;

  // eslint-disable-next-line @typescript-eslint/no-non-null-assertion
  if (compactNumber && parsedValue > compactStartFrom!) {
    // eslint-disable-next-line @typescript-eslint/no-non-null-assertion
    compactPrecision = compactPrecision === false || compactPrecision! >= 0 ? compactPrecision : 1;
    return Currency.compactNumber(parsedValue, compactPrecision as number);
  }

  return `${ Currency.formatNumber(parsedValue, { precision, maxPrecision, separator, decimalPoint }) }`;
}
