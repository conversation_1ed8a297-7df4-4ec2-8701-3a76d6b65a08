import { getCountry } from '@classification/admin-solution';
import {
  getCurrencyConfig as getConfig,
  SYMBOL_POSITION
} from '@shopee_common/currency3';
import type { FormatCurrencyOptions } from 'ls-upload-cmpt/utils/currency';

import { formatNumber } from 'src/utils/number-format';

export const getCurrencyConfig = (region: string = getCountry()) => {
  return getConfig(region as any);
}

export const getCurrencySymbol = (region: string = getCountry()) => {
  const { symbol, symbolPosition } = getCurrencyConfig(region);

  const symbols = Array.isArray(symbol) ? symbol : ([symbol, symbol] as string[]);
  const symbolMap = {} as {
    prefix?: string;
    suffix?: string;
  };

  if (symbolPosition === SYMBOL_POSITION?.FRONT_AND_BACK) {
    symbolMap.prefix = symbols[0];
    symbolMap.suffix = symbols[1];
  } else if (symbolPosition === SYMBOL_POSITION?.FRONT) {
    symbolMap.prefix = symbols[0];
  } else if (symbolPosition === SYMBOL_POSITION?.BACK) {
    symbolMap.suffix = symbols[1];
  }

  return symbolMap;
}

export const formatCurrency =  (value: number | string, options: FormatCurrencyOptions, region: string = getCountry(),) => {
  let parsedValue = parseFloat(`${value}`);
  if (value === undefined) {
    return '';
  }
  if (isNaN(parsedValue)) {
    return `${value}`;
  }
  options = Object.assign(
    {
      negative: parsedValue < 0 && Math.abs(parsedValue) > Number.EPSILON,
      serverSidePrice: false,
    },
    options,
  );
  const { negative, serverSidePrice, hasCurrencySymbol, html } = options;
  /*
       * format number相关参数
       * 若传入的 region 或者 currencyId 则利用对应配置补充相关参数
       */
  const { maxPrecision, separator, decimalPoint } = options;
  let { precision } = options;

  if (serverSidePrice) {
    // eslint-disable-next-line @typescript-eslint/no-magic-numbers
    parsedValue /= 100000;
  }

  let prefix = '';
  if (negative && Math.abs(parsedValue) >= Number.EPSILON) {
    prefix = html ? '<span class="currency-prefix">-</span>' : '-';
    parsedValue = Math.abs(parsedValue);
  }

  const currencyConfig = getCurrencyConfig(region)
  precision = typeof precision === 'number' && precision >= 0 ? precision : (currencyConfig.precision as number);

  let params: { [p: string]: any } = {
    precision,
    maxPrecision,
    decimalPoint,
    separator,
  };
  params = Object.keys(params)
  .filter((key) => params[key] !== undefined)
  .reduce((acc, key) => ({ ...acc, [key]: params[key] }), {});

  let currency = formatNumber(parsedValue, params, region);
  if (hasCurrencySymbol) {
    let { symbol } = currencyConfig;
    if (html) {
      if (typeof symbol === 'string') {
        symbol = `<span class="currency-symbol">${symbol}</span>`;
      } else if (Array.isArray(symbol)) {
        symbol = symbol.map((item) => `<span class="currency-symbol">${item}</span>`) as [string, string];
      }
    }

    const position = currencyConfig.symbolPosition || SYMBOL_POSITION.FRONT;
    if (position === SYMBOL_POSITION.BACK) {
      // eslint-disable-next-line @typescript-eslint/no-non-null-assertion
      currency = `${currency!}${symbol as string}`;
    } else if (position === SYMBOL_POSITION.FRONT_AND_BACK) {
      /*
       * FRONT_AND_BACK 为新增类型, 此种类型下
       * symbol为数组, eg ['$', 'MXN']
       * 增加 spaceBetweenSymbol 字段， 返回值 eg [false, true]
       * 来作为拼接规范
       */
      const spaceBetweenSymbol = (currencyConfig.spaceBetweenSymbol ?? []) as [boolean, boolean];
      currency = `${symbol[0]}${spaceBetweenSymbol[0] ? ' ' : ''}${currency!}${spaceBetweenSymbol[1] ? ' ' : ''}${symbol[1]}`;
    } else {
      // eslint-disable-next-line @typescript-eslint/no-non-null-assertion
      currency = `${symbol as string}${currency!}`;
    }
  }

  // eslint-disable-next-line @typescript-eslint/no-non-null-assertion
  return `${prefix}${currency!}`;
}
