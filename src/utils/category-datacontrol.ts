import type { ICategory } from '@classification/category-selector/types';
import type { SearchType } from '@classification/category-selector/types/constants';
import { getCountry } from 'admin-upload-common';

import { getGlobalCategoryList } from 'src/api/uploadAdmin';
import type { uploadAdmin } from 'src/api/uploadAdmin/uploadAdmin';

import '@classification/table/dist/style/index.css';

const RootCatId = 0;
const CategoryIdLength = 6;

export function isNumber(val: string | number) {
  if (val === '' || val === null) {
    return false;
  }
  return !isNaN(Number(val));
}

export const formatCategoryList = (categoryList: uploadAdmin.ICategory[], {
  level = 1,
  maxLevel = 5,
  parentCatId = 0,
}: {
  level: number,
  maxLevel?: number,
  parentCatId?: number,
}) => {
  return categoryList.map(category => {
    const cat: ICategory = {
      catId: category.catId,
      name: category.catName,
      parentCatId,
    }
    if (level < maxLevel && category.subCategories && category.subCategories?.length > 0) {
      cat.children = formatCategoryList(category.subCategories, {
        maxLevel,
        parentCatId: cat.catId,
        level: level + 1,
      });
      cat.hasChild = true;
    } else {
      cat.children = [];
      cat.hasChild = false;
    }
    return cat;
  })
}
export default class DataControl {
  // 已经请求过的parent category map，防止重复请求，可以通过loadedParentCategoryMap.get(id)来获取。
  loadedParentCategoryMap: Map<
    number,
    Promise<{
      categoryList: ICategory[];
    }>
  > = new Map();
  // category 列表 包括父子category
  categoryList: ICategory[] = [];
  // category 列表 包括父子category
  categoryTree: ICategory[] = [];
  // category map 方便通过id取对应category值。categoryMap.get(id)
  categoryMap: Map<number, ICategory> = new Map();
  // category tree map 具有父子层级关系的category map
  categoryTreeMap: Map<number, ICategory[]> = new Map();
  loading = false;

  constructor() {
  }

  getData() {
    return {
      categoryList: this.categoryList,
      categoryMap: this.categoryMap,
      categoryTreeMap: this.categoryTreeMap,
      loadedParentCategoryMap: this.loadedParentCategoryMap,
    };
  }

  getLoadedParentCategoryMap() {
    return this.loadedParentCategoryMap;
  }

  async search(params: {
    offset?: number;
    limit?: number;
    searchKey: number | string;
    searchType?: SearchType;
  }) {
    const { searchKey, limit, offset = 0 } = params;
    const categoryMap = this.categoryMap;
    let cats: ICategory[] = [];
    // 六位数的数字被当作id搜索
    if (isNumber(searchKey) && String(searchKey).length === CategoryIdLength) {
      cats = this.categoryList
      .filter((cat) => cat.catId === Number(searchKey))
      .map((cat) => categoryMap.get(cat.catId!))
      .filter((cat) => cat) as ICategory[];
    } else {
      const lowKey = String(searchKey).toLowerCase();
      cats = this.categoryList
      .filter((cat) => cat.name?.toLowerCase().indexOf(lowKey) !== -1)
      .map((cat) => categoryMap.get(cat.catId!))
      .filter((cat) => cat) as ICategory[];
    }
    const result = cats.map((cat) => {
      const path: ICategory[] = [cat];
      while (cat.parentCatId) {
        cat = categoryMap.get(cat.parentCatId)!;
        cat && path.unshift(cat);
      }
      return path;
    });
    return {
      hasNext: limit !== undefined ? result.length > limit + offset : false,
      result: result.slice(offset, limit),
    };
  }

  async ensureDataLoaded(
    categoryIds: number[],
  ) {
    const allPromise: Promise<{ categoryList: ICategory[] }>[] = [];
    this.loading = true;
    [RootCatId, ...categoryIds].forEach((id) => {
      allPromise.push(this.loadData());
    });
    await Promise.all(allPromise);
    this.loading = false;
  }

  async loadData(params?: {
    maxLevel?: number;
  }) {
    const { maxLevel = 5 } = params || {};
    const id = RootCatId;
    const loadedMap = this.loadedParentCategoryMap;
    if (loadedMap.has(id)) {
      return loadedMap.get(id)!;
    }
    this.loading = true;
    const promise = this._getAllCategoryList({ maxLevel });
    promise
    .then((res) => {
      if (res) {
        this._setData(res);
      } else {
        // 超时等原因导致res为undefined
        loadedMap.delete(id);
      }
    })
    .catch(() => {
      loadedMap.delete(id);
    })
    .finally(() => {
      this.loading = false;
    });
    loadedMap.set(id, promise);
    return promise;
  }

  private _setData(res: { categoryList: ICategory[] }) {
    const categories: ICategory[] = res?.categoryList || [];
    const spreadTree = (children?: ICategory[]) => {
      children &&
      children.forEach((category) => {
        const parentId = category.parentCatId;
        let children = this.categoryTreeMap.get(parentId as number);
        if (!children) {
          children = [];
          this.categoryTreeMap.set(parentId as number, children);
        }
        children.push(category);
        this.categoryList.push(category);
        this.categoryMap.set(category.catId!, category);
        spreadTree(category.children);
      });
    };
    const setCategoryTree = (children: ICategory[], parentCatId: number) => {
      if (parentCatId === 0) {
        return categories;
      }
      const findIdx = children.findIndex((cat) => cat.catId === parentCatId);
      if (findIdx !== -1) {
        children[findIdx].children = categories;
        return children;
      } else {
        return children.map((cat) => {
          cat.children = cat.children
            ? setCategoryTree(cat.children, parentCatId)
            : undefined;
          return cat;
        });
      }
    };
    spreadTree(categories);
    this.categoryTree = setCategoryTree(
      this.categoryTree,
      categories[0]?.parentCatId || 0,
    );
  }

  private async _getAllCategoryList(params: { maxLevel?: number; }) {
    try {
      const categoryList = (await getGlobalCategoryList({ region: getCountry() }))?.cats || [];
      return {
        categoryList: formatCategoryList(categoryList, {
          level: 1,
          maxLevel: params.maxLevel,
          parentCatId: 0,
        }),
      };
    } catch (error) {
      return {
        categoryList: [],
      };
    }
  }

}
