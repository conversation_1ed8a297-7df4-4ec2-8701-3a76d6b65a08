import { printErrorMessageWithCopyReqID } from 'admin-upload-common';
import <PERSON><PERSON><PERSON> from 'papaparse';

import type { uploadAdmin } from 'src/api/uploadAdmin/uploadAdmin';
import { FEtoBEMultiplier } from '../constants';
import { QcMode } from '../typings';

export * from './str2Template';

export function convertNumberToCommaSeparatedString(input: string) {
  return input.toString().replace(/\B(?=(\d{3})+(?!\d))/g, ',');
}

export function formatCurrencyForItem(region: string, input: number) {
  switch (region) {
    case 'BR':
    case 'SG':
    case 'AR':
    case 'PL':
    case 'ES':
    case 'FR':
    case 'MY':
      return convertNumberToCommaSeparatedString(input.toFixed(2));
    case 'PH':
    case 'TH':
    case 'ID':
    case 'TW':
    case 'VN':
    case 'MM':
    case 'XX':
      return convertNumberToCommaSeparatedString(input.toFixed(0));
    case 'IR':
      return convertNumberToCommaSeparatedString((input / 10).toFixed(0));
    default:
      return convertNumberToCommaSeparatedString(input.toFixed(0));
  }
}

export const getQcModeUrlPrefix = (qcMode: QcMode) => {
  switch (qcMode) {
    case QcMode.SHOPEE_MALL:
      return 'shopee_mall_product';
    case QcMode.CB:
      return 'cross_border_product';
    case QcMode.PRODUCT_LICENSE:
      return 'product_license';
    case QcMode.SIP:
      return 'sip_affiliate';
    default:
      return 'product';
  }
};

export function ucs2ToBinaryString(str: string) {
  const escstr = encodeURIComponent(str);
  const binstr = escstr.replace(/%([0-9A-F]{2})/gi, function(match, hex) {
    const i = parseInt(hex, 16);
    return String.fromCharCode(i);
  });
  return binstr;
}

export const byteLengthValidator = (value: string, max: number) => {
  if (!value) {
    return Promise.resolve();
  }
  if (ucs2ToBinaryString(value).length > max) {
    return Promise.reject(`Maximum of ${max} bytes allowed`);
  }

  return Promise.resolve();
};

// Returns a picker string amongst 'year', 'month', 'date'
// based on the format passed in
export function getPickerFromDateFormat(
  format: string,
): 'date' | 'month' | 'year' {
  return format.includes('DD')
    ? 'date'
    : format.includes('MM')
      ? 'month'
      : 'year';
}

// Returns showtime format for datepicker amongst 'HH', 'HH:mm', 'HH:mm:ss' or false
export function getShowTimeFromDateFormat(
  format: string,
): { format: 'HH' | 'HH:mm' | 'HH:mm:ss' } | false {
  return !format.toLowerCase().includes('h')
    ? false
    : {
      format: format.includes('ss')
        ? 'HH:mm:ss'
        : format.includes('mm')
          ? 'HH:mm'
          : 'HH',
    };
}

export function printErrMsgWithDetail(args: {
  reqId: string;
  debugMsg?: string;
  errDetails?: uploadAdmin.IErrDetail[] | string[];
}) {
  let errorMsg = '';
  if (args.debugMsg) {
    errorMsg = errorMsg.concat(args.debugMsg);
  }
  if (args.errDetails) {
    for (const detail of args.errDetails) {
      errorMsg = errorMsg.concat('\n');
      if (detail.hasOwnProperty('field') && detail.hasOwnProperty('value')) {
        errorMsg = errorMsg.concat(
          `${
            (detail as uploadAdmin.IErrDetail).field
          }: ${
            (detail as uploadAdmin.IErrDetail).value?.includes(
              'price',
            )
              ? (detail as uploadAdmin.IErrDetail).value?.replace(
                /\d+/g,
                function(val: string) {
                  return (parseInt(val) / FEtoBEMultiplier).toString();
                },
              )
              : (detail as uploadAdmin.IErrDetail)
                .value
          }`,
        );
      } else {
        errorMsg = errorMsg.concat(detail as string);
      }
    }
  }
  if (errorMsg) {
    printErrorMessageWithCopyReqID(errorMsg, args.reqId);
  }
}

export function getRouterEnterTime() {
  /* eslint-disable-next-line  @typescript-eslint/no-explicit-any */
  return (window as any)?.app?.router?.getRouteInfo()?.routePerformance
    ?.requestStart;
}

export const getFileDataRows = (file: File) => {
  return new Promise<number>((resolve, reject) => {
    // eslint-disable-next-line @typescript-eslint/no-explicit-any
    Papaparse.parse<any>(file, {
      header: false,
      worker: true,
      complete: (results, file) => {
        const noEmptyData = results.data.filter((v) => v[0]);
        const fileRows = noEmptyData.length;
        const dataRows = fileRows > 1 ? fileRows - 1 : 0;
        resolve(dataRows);
      },
    });
  });
};
