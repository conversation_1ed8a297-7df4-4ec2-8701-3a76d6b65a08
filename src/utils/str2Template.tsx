import React from 'react';
// eslint-disable-next-line
interface ITemplateItem {
  type: 'url' | 'text';
  text: string;
  url?: string;
}
/* eslint-disable */
function parseTemplate2Obj(input: string): any {
  if (typeof input !== 'string') {
    return null;
  }
  let hasError = false;
  const kvMap = {};
  const len = input.length;
  let curpoint = 0;
  while (curpoint < len) {
    let key = '';
    let val = '';

    while (curpoint < len) {
      if (input[curpoint] === ' ' && key === '') {
        curpoint++;
        continue;
      }
      if (input[curpoint] === ' ' && key !== '') {
        curpoint++;
        break;
      }
      if (input[curpoint] === ':') {
        break;
      }
      if (/[a-zA-Z]/g.test(input[curpoint]) === true) {
        key += input[curpoint];
        curpoint++;
        continue;
      }
      hasError = true;
      break;
    }
    if (hasError || key === '') {
      hasError = true;
      break;
    }

    while (curpoint < len && input[curpoint] !== ':') {
      curpoint++;
    }
    curpoint++;
    if (curpoint >= len) {
      hasError = true;
      break;
    }

    while (curpoint < len) {
      const cur = input[curpoint];
      if (cur === ' ' && val === '') {
        curpoint++;
        continue;
      }
      if (cur === "'" && val === '') {
        val += "'";
        curpoint++;
        continue;
      }
      if (cur === "'" && val !== '') {
        val += "'";
        curpoint++;
        break;
      }
      if (val === '') {
        hasError = true;
        break;
      }
      val += cur;
      curpoint++;
    }
    if (hasError === true || val === '') {
      hasError = true;
      break;
    }

    while (curpoint < len) {
      const cur = input[curpoint];
      if (cur === ',') {
        curpoint++;
        break;
      }
      if (cur === ' ') {
        curpoint++;
        continue;
      }
      hasError = true;
      break;
    }
    if (hasError === true) {
      break;
    }
    // @ts-ignore
    kvMap[key] = val.substring(1, val.length - 1);
  }
  if (hasError === true) {
    return null;
  }
  return kvMap;
}
/* eslint-enable */

/**
 *
 * @param input template. sg: `你好，请点击： {{"text": "链接1为标准JSON", "url": "shopee.com" }}; `
 * @returns sg: [{'type':'text','text':'你好，请点击： '},{'text':'链接1为标准JSON','url':'shopee.com','type':'url'}]
 */
export function extractUrlFromTemplate(input: string): ITemplateItem[] {
  const regx = /\{\{(.)*?(\}\})/g; // 非贪婪匹配，禁止 text 或者 url 中出现两个花括号（}})
  const res: ITemplateItem[] = [];
  const inputLen: number = input.length;

  let startIndex = 0;

  while (startIndex < inputLen) {
    const match = regx.exec(input);

    if (match === null) break;

    const { index: matchStartIndex } = match;
    const matchStr = match[0];

    if (matchStartIndex > startIndex) {
      res.push({
        type: 'text',
        text: input.substring(startIndex, matchStartIndex),
      });
    }

    const nextIndex = matchStartIndex + matchStr.length;
    startIndex = nextIndex;

    try {
      const str2Parse = matchStr.substring(2, matchStr.length - 2);
      const template = parseTemplate2Obj(str2Parse);
      if (template !== null && template.text && template.url) {
        res.push({
          ...template,
          type: 'url',
        });
      } else {
        res.push({
          type: 'text',
          text: matchStr,
        });
      }
    } catch (error) {
      res.push({
        type: 'text',
        text: matchStr,
      });
    }
  }

  if (startIndex < inputLen) {
    res.push({ type: 'text', text: input.substring(startIndex, inputLen) });
  }
  return res;
}

export function getHtmlFromTemplate(input: string) {
  let res = '';
  const htmls = extractUrlFromTemplate(input);
  htmls.forEach(({ type, text, url }) => {
    if (type === 'text') {
      res += text;
      return;
    }
    res += `<a href=${url} target="_blank" style="color: #1F75D5;">${text}</a>`;
  });
  return <div dangerouslySetInnerHTML={{ __html: res }}></div>;
}
