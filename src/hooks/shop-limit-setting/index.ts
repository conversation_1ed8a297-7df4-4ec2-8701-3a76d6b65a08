import { getCountry } from 'admin-upload-common';
import { useRequest } from 'ahooks';
import { useForm } from 'antd/lib/form/Form';
import { useRef } from 'react';

import { getLimitSettingResult } from 'src/services/shop-limit';

export default function useShopLimiInfo() {
  const [form] = useForm();
  const page = useRef(1);
  const pageSize = useRef(10);
  const total = useRef(0);
  const offsetList = useRef([0, 0]);
  const { run, data, loading } = useRequest(async () => {
    const result = await getLimitSettingResult(
      +form.getFieldValue('shopId'),
      offsetList.current[page.current],
      pageSize.current,
      getCountry(),
    );
    if (result) {
      const { hasNext, nextOffset, rows } = result;
      if (hasNext) {
        const newTotal = pageSize.current * (page.current + 1);
        // 往回跳页时保持total不变
        if (total.current < newTotal) {
          total.current = newTotal;
          offsetList.current.push(nextOffset as number);
        }
      } else {
        if (total.current) {
          total.current = pageSize.current * page.current;
        }
      }
      return rows;
    }
    return [];
  });
  function updatePagination(newPage: number, newPageSize: number) {
    page.current = newPage;
    if (pageSize.current !== newPageSize) {
      pageSize.current = newPageSize;
      offsetList.current = [0, 0];
      total.current = 0;
    }
    run();
  }
  function updateFilter() {
    page.current = 1;
    pageSize.current = 10;
    total.current = 0;
    offsetList.current = [0, 0];
    run();
  }
  return {
    form,
    data,
    loading,
    updateFilter,
    page: page.current,
    pageSize: pageSize.current,
    total: total.current,
    updatePagination,
  };
}
