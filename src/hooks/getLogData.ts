import * as React from 'react';
import { useQuery } from 'react-query';

import {
  getCqcLogs,
  getForStockAudits,
  getItemInfoAudit,
  getModelQcLogs,
  getRuleQcLogs,
  getTopicalQcLogs
} from '../api/uploadAdmin';
import type { uploadAdmin } from '../api/uploadAdmin/uploadAdmin';

export function useGetInfoAuditLog(
  itemId: number,
  limit: number,
  current: number,
  filter?: uploadAdmin.IItemAuditFilter,
) {
  const [data, setData] = React.useState<
  uploadAdmin.IItemAudit[]
  >([]);
  const [loading, setLoading] = React.useState(false);
  const [offsetList, setOffsetList] = React.useState<string[]>([]);
  const [hasMore, setHasMore] = React.useState(true);

  React.useEffect(() => {
    if (itemId == null) {
      return;
    }
    setLoading(true);
    getItemInfoAudit({
      itemId: itemId,
      page: {
        limit: limit,
        offset: current * limit,
        offsetList: offsetList,
      },
      filter: filter,
      // @ts-expect-error
      respBignumFields: ['item_audits.audit_id'],
    }).then((result) => {
      setLoading(false);
      setData(result?.itemAudits || []);
      setHasMore(result?.page?.hasNext || false);
      setOffsetList(result?.page?.offsetList ? result.page.offsetList : []);
    });
  }, [current, limit, itemId, filter]);

  return {
    data,
    loading,
    hasMore,
  };
}

export function useGetStockAuditLog(
  itemId: number,
  limit: number,
  current: number,
  filter: uploadAdmin.IFilterCriteria,
) {
  const [data, setData] = React.useState<
  uploadAdmin.IItemAudit[]
  >([]);
  const [loading, setLoading] = React.useState(false);
  const [offsetList, setOffsetList] = React.useState<string[]>([]);
  const [hasMore, setHasMore] = React.useState(true);

  React.useEffect(() => {
    if (itemId == null) {
      return;
    }
    setLoading(true);
    getForStockAudits({
      itemId: itemId,
      page: {
        limit: limit,
        offset: current * limit,
        offsetList: offsetList,
      },
      filter: filter,
    }).then((result) => {
      setLoading(false);
      setData(result?.audits || []);
      setHasMore(result?.page?.hasNext || false);
      setOffsetList(result?.page?.offsetList || []);
    });
  }, [current, limit, itemId, filter]);

  return {
    data,
    loading,
    hasMore,
  };
}

// Used for populating empty logs
function createEmptyLogEntry<T>(num: number): T[] {
  return [...Array(num).keys()].map(() => ({} as T));
}

export function useGetRQCLog(
  itemId: number | null,
  limit: number,
  current: number,
  region: string,
) {
  const { data, isLoading } = useQuery(
    ['rqc_audit_log', itemId, limit, current, region],
    async () => {
      if (itemId === null) {
        return;
      }
      const result = await getRuleQcLogs({
        itemId: itemId,
        limit: limit,
        offset: current * limit,
        region: region,
      });

      const emptyLogs = createEmptyLogEntry<
      uploadAdmin.IRuleQCLog
      >(current * limit);

      if (result?.spDebugMsg || !result?.logs) {
        return {
          logs: emptyLogs,
          hasMore: false,
        };
      }

      return {
        logs: [...emptyLogs, ...(result.logs ?? [])],
        hasMore: result.hasMore ?? false,
      };
    },
  );

  return {
    data,
    isLoading,
  };
}

export function useGetMQCLog(
  itemId: number | null,
  limit: number,
  current: number,
) {
  const { data, isLoading } = useQuery(
    ['mqc_audit_log', itemId, limit, current],
    async () => {
      if (itemId === null) {
        return;
      }
      const result = await getModelQcLogs({
        itemId: itemId,
        limit: limit,
        offset: current * limit,
      });

      const emptyLogs = createEmptyLogEntry<
      uploadAdmin.IModelQCLog
      >(current * limit);

      if (result?.spDebugMsg || !result?.logs) {
        return {
          logs: emptyLogs,
          hasMore: false,
        };
      }

      const resultLogs = result.logs ?? [];
      return {
        logs: [...emptyLogs, ...resultLogs],
        hasMore: result.hasMore ?? false,
      };
    },
  );

  return {
    data,
    isLoading,
  };
}

export function useGetCqcLog(
  itemId: number | null,
  limit: number,
  current: number,
  region: string,
) {
  const { data, isLoading } = useQuery(
    ['cqc_audit_log', itemId, limit, current, region],
    async () => {
      if (itemId === null) {
        return;
      }
      const result = await getCqcLogs({
        itemId: itemId,
        limit: limit,
        offset: current * limit,
        region: region,
      });

      const emptyLogs = createEmptyLogEntry<
      uploadAdmin.ICQCItemLog
      >(current * limit);

      if (result?.spDebugMsg || !result?.logs) {
        return {
          logs: emptyLogs,
          hasMore: false,
        };
      }
      return {
        logs: [...emptyLogs, ...result.logs],
        hasMore: result?.hasMore ?? false,
      };
    },
  );

  return {
    data,
    isLoading,
  };
}

export function useGetTopicalQcLog(
  itemId: number | null,
  limit: number,
  current: number,
  region: string,
) {
  const { data, isLoading } = useQuery(
    ['tqc_audit_log', itemId, limit, current, region],
    async () => {
      if (itemId === null) {
        return;
      }
      const result = await getTopicalQcLogs({
        itemId: itemId,
        limit: limit,
        offset: current * limit,
        region: region,
      });

      const emptyLogs = createEmptyLogEntry<uploadAdmin.ICQCItemLog>(current * limit);

      if (result?.spDebugMsg || !result?.logs) {
        return {
          logs: emptyLogs,
          hasMore: false,
        };
      }
      return {
        logs: [...emptyLogs, ...result.logs],
        hasMore: result?.hasMore ?? false,
      };
    },
  );

  return {
    data,
    isLoading,
  };
}
