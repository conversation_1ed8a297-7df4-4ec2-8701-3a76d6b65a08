import { getCountry } from '@classification/admin-solution';
import {
  getCurrencyConfig,
} from '@shopee_common/currency';
import {
  useMemo,
} from 'react';

import { getCurrencySymbol } from 'src/utils/currency';

export const useCurrency = (region: string = getCountry()) => {
  const regionCode = useMemo(() => region.toUpperCase(), [region.toUpperCase()]);
  const currencyConfig = useMemo(() => {
    return getCurrencyConfig(regionCode as any)
  }, [regionCode]);
  const currencyDecimalPoint = useMemo(() => currencyConfig.decimalPoint || '.', [currencyConfig]);
  const priceCurrencyPrecision = useMemo(() => currencyConfig.precision, [currencyConfig]);
  const symbol = useMemo(() => {
    return getCurrencySymbol(regionCode);
  }, [regionCode]);

  return {
    symbol,
    priceCurrencyPrecision,
    currencyDecimalPoint,
    currencyConfig,
  }
}
