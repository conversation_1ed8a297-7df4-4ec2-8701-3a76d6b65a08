import { getCountry } from 'admin-upload-common';
import { useRequest } from 'ahooks';
import { message } from 'antd';
import React, { useCallback,useState } from 'react';

import MassEditAPI from '../api';
import { defaultPollingInterval } from '../components/MassUploadPage/constant';
import type {
  ExtraDataObj,
  MassEditTask} from '../typings';
import {
  DryRunStatus,
  MassEditStatus
} from '../typings';

export default function useMassEdit(
  host: string,
  massEditTypes: string[],
  pagination: { page: number; pageSize: number },
  region: string,
  pollingInterval = defaultPollingInterval,
  onAllTaskDone?: Function,
) {
  const ref = React.useRef<MassEditAPI | null>(null);
  const disabledLoading = React.useRef<boolean>(false);
  const hasDoingTask = React.useRef(false);
  const timeoutRef = React.useRef<NodeJS.Timeout>();
  const cancelPollingRef = React.useRef<boolean>(false);
  // MassEditAPI is created lazily once
  function getMassEditAPI() {
    if (ref.current === null) {
      ref.current = new MassEditAPI(region, host);
    }
    return ref.current;
  }
  const massEditAPIs = getMassEditAPI();

  const [totalTask, setTotalTask] = useState(0);

  const stopMassEditTask = async (query: {
    taskId: number;
    massEditType: string;
    massEditFetchFunction: ()=> Promise<MassEditTask[]>;
  }) => {
    const result = await massEditAPIs?.stopMassEditTask({
      taskId: query.taskId,
      massEditType: query.massEditType,
    });
    if (result) {
      query.massEditFetchFunction();
    }
  };

  const { data: massEditTasks, runAsync: getMassEditTasks, loading } = useRequest(
    async () => {
      const { page, pageSize } = pagination;
      const result = await massEditAPIs?.getMassEditTasks({
        massEditTypes: massEditTypes,
        offset: (page - 1) * pageSize,
        limit: pageSize,
        dryrunStatus: [DryRunStatus.NormalTask], // by default, we only show normal task (filter out dryrun task)
        region: getCountry(),
      });
      disabledLoading.current = false;
      if (result) {
        let needPolling = false;
        const tasks = result.data;
        tasks.forEach((task) => {
          if (task.taskStatus === MassEditStatus.Processing) {
            task.stopTask = () =>
              /* eslint-disable-next-line @typescript-eslint/no-use-before-define */
              stopMassEditTask({
                taskId: task.taskId,
                massEditType: task.massEditType,
                massEditFetchFunction: getMassEditTasks,
              });
          }
          if (
            task.taskStatus === MassEditStatus.Processing ||
            task.taskStatus === MassEditStatus.Pending
          ) {
            needPolling = true;
            hasDoingTask.current = true;
          }
          try {
            task.extraDataObj = JSON.parse(task.extraData);
          } catch {
            task.extraDataObj = {};
          }
        });

        setTotalTask(result.totalCount);
        if (needPolling && !cancelPollingRef.current) {
          if (timeoutRef.current) {
            clearTimeout(timeoutRef.current);
          }
          timeoutRef.current = setTimeout(() => {
            disabledLoading.current = true;
            getMassEditTasks();
          }, pollingInterval);
        }
        if (hasDoingTask.current && !needPolling && onAllTaskDone) {
          onAllTaskDone();
          hasDoingTask.current = false;
        }
        return result.data || [];
      }
      return [];
    },
    {
      refreshDeps: [
        ...Object.values(pagination),
        JSON.stringify(massEditTypes),
      ],
    },
  );
  const { data: massEditTaskProgress } = useRequest(
    async () => {
      if (massEditTasks) {
        let taskIds: number[] = [];
        massEditTasks.forEach((task) => {
          if (task.taskStatus === MassEditStatus.Processing) {
            taskIds = [...taskIds, task.taskId];
          }
        });
        if (taskIds.length) {
          const result = await massEditAPIs?.getMassEditTaskProgress({
            taskIds: taskIds,
          });

          if (result) {
            const progresses = result.data;
            const taskProgressMap: { [id: number]: number } = {};
            taskIds.forEach((id, idx) => {
              taskProgressMap[id] = progresses[idx];
            });
            return taskProgressMap;
          }
          return {};
        }
      }
    },
    {
      refreshDeps: [massEditTasks],
    },
  );

  const createMassEditTask = useCallback(
    async (query: {
      filePath: string;
      massEditType: string;
      originalFilename: string;
      extraData: ExtraDataObj;
      region: string;
    }) => {
      const extraDataStr = Object.keys(query.extraData).length
        ? JSON.stringify(query.extraData)
        : '';
      const result = await massEditAPIs?.createMassEditTask({
        filePath: query.filePath,
        massEditType: query.massEditType,
        originalFilename: query.originalFilename,
        extraData: extraDataStr,
        region: getCountry(),
      });
      if (result) {
        if (result.spErrorCode === 0) {
          message.success('Uploaded successfully');
        } else {
          message.error('Upload falied please try again later');
        }
        getMassEditTasks();
        hasDoingTask.current = true;
      }
    },
    [massEditTypes],
  );

  React.useEffect(() => {
    return () => {
      cancelPollingRef.current = true;
    };
  }, []);

  const massEditTaskWithProgress = (massEditTasks || []).map((task) => ({
    ...task,
    progress:
      task.taskStatus === MassEditStatus.Processing && massEditTaskProgress
        ? massEditTaskProgress[task.taskId]
        : 0,
  }));
  return {
    massEditTasks: massEditTaskWithProgress || [],
    totalTask,
    loading: !disabledLoading.current && loading,
    createMassEditTask,
    stopMassEditTask,
  };
}
