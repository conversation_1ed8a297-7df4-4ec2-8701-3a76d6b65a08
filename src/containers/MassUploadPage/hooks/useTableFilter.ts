import { createAction } from 'admin-upload-common';
import * as React from 'react';

// standalone createAction function, if you do not have @discreet
/*
 * function createAction<
 *   T extends string,
 *   AC extends (...args: any) => { type: T }
 * >(actionCreator: AC): AC {
 *   return actionCreator;
 * }
 */

const enum ActionTypes {
  SET_FILTER = 'SET_FILTER',
}
export const setFilter = createAction((filter: string[]) => ({
  type: ActionTypes.SET_FILTER,
  filter,
}));
type Action = ReturnType<typeof setFilter>;

type UseTableFilterHookConfig = {
  initialValue?: State;
};
type State = { filter: string[] };

const filterReducer = (
  //   state: State = { filter: ["mass_update_item_attr_value_mapping", "mass_edit_item_global_category", "mass_update_item_brand"] },
  state: State = { filter: [] },
  action: Action,
) => {
  switch (action.type) {
    case ActionTypes.SET_FILTER:
      return { filter: action.filter };
    default:
      return state;
  }
};
const defaultConfig = {
  //   initialValue: {filter: ["mass_update_item_attr_value_mapping", "mass_edit_item_global_category", "mass_update_item_brand"]},
  initialValue: { filter: [] },
};
export default function useTableFilterHook(
  config: UseTableFilterHookConfig = defaultConfig,
) {
  const { initialValue = defaultConfig.initialValue } = config;
  const [state, dispatch] = React.useReducer(filterReducer, initialValue);
  return {
    filter: state.filter,
    changeFilter: (filter: string[]) => {
      dispatch(setFilter(filter));
    },
  };
}
