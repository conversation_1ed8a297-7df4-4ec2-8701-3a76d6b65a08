// Doc: https://confluence.shopee.io/display/SPDEV/%5BHTTP+GATEWAY%5D+3.+Mass+edit+module
export enum MassEditStatus {
  Pending = 0,
  Processing = 1,
  Done = 2,
  Failed = 3,
  Stopped = 4,
  Interrupted = 5,
  DryRun = 6,
}
export enum DryRunStatus {
  NormalTask = 1,
  DryrunTask = 2,
}

export interface ExtraDataObj {
  data?: number;
}
export interface MassEditTask {
  taskId: number;
  ctime: number;
  mtime: number;
  massEditType: string;
  operator: string;
  inputFilename: string;
  originalFilename: string;
  outputFilename: string; // empty if the task has not been processed
  taskStatus: MassEditStatus; // 0 PENDING, 1 PROCESSING, 2 DONE 3 ERROR 4 STOPPED(manual stop)
  dryrunStatus: MassEditStatus;
  extraData: string; // json string
  extraDataObj: ExtraDataObj;
  progress?: number;
  stopTask: ()=> void;
}

export interface CreateMassEditTaskRequest {
  filePath: string;
  massEditType: string;
  originalFilename: string;
  massEditStatus?: MassEditStatus; // Default: MassEditStatus.Pending
  dryrunStatus?: DryRunStatus; // Default: DryRunStatus.NormalTask
  extraData?: string; // json string
  region: string;
}
export interface CreateMassEditTaskResponse {
  taskId: number;
  spErrorCode?: number;
  spDebugMsg?: string;
}
export interface GetMassEditTaskProgressRequest {
  taskIds: number[];
}
export interface GetMassEditTaskProgressResponse {
  data: number[];
}
export interface GetMassEditTasksRequest {
  massEditTypes: string[];
  taskIds?: number[];
  offset: number;
  limit: number;
  dryrunStatus?: DryRunStatus[]; // Default: [DryRunStatus.NormalTask]
  region: string;
}
export interface GetMassEditTasksResponse {
  data: MassEditTask[];
  hasMore: boolean;
  totalCount: number;
}
export interface StopMassEditTaskRequest {
  taskId: number;
  massEditType: string;
}
export interface StopMassTaskResponse {}
