import { Dropdown } from 'antd';
import * as React from 'react';

import MassCreateImg from '../assets/images/create_mass.png';
import MassEditImg from '../assets/images/edit_mass.png';
import { MassUploadPage } from '.';

const MassEditTypes = {
  'Mass Create': 'mass_create_fe_category',
  'Mass Edit': 'mass_update_fe_category',
};
const MassEditTemplates = [
  {
    name: 'Mass Create',
    link:
      'https://docs.google.com/spreadsheets/d/1fmvIR4Fz9Xh534pgMa0JuTeYU6MT48kTTh2H9JcrgCk/export?gid=0&format=csv',
    icon: MassCreateImg,
  },
  {
    name: 'Mass Edit',
    link:
      'https://docs.google.com/spreadsheets/d/1fmvIR4Fz9Xh534pgMa0JuTeYU6MT48kTTh2H9JcrgCk/export?gid=2069736316&format=csv',
    icon: MassEditImg,
  },
];
const MassEditTemplateHeaders = {
  'Mass Create':
    'tree_id,l1_cat_name,l2_cat_name,l3_cat_name,l4_cat_name,l5_cat_name,be_cat_ids',
  'Mass Edit': 'tree_id,cat_id,status',
};

// ===================== customTemplateButton =====================
type Props = { downloadTemplate: { link: string; title: string }[] };
// @ts-expect-error
function ExportBtn({ downloadTemplate }: Props) {
  return (
    <Dropdown
      menu={{
        items: downloadTemplate.map((eachTemp, index) => ({
          key: index,
          label: (<a href={eachTemp.link}>{eachTemp.title}</a>)
        }))
      }}>
      <img
        height="40"
        width="47"
        src={MassEditImg}
      />
    </Dropdown>
  );
}
// @ts-expect-error
const downloadTemplate = [
  {
    title: 'Mass create attribute',
    link:
      'https://docs.google.com/spreadsheets/d/1PBMPqpVzrv-h2aU_P-aFr5lp-g8ohhaXhOyTm6BorMM/export?format=csv&id=1PBMPqpVzrv-h2aU_P-aFr5lp-g8ohhaXhOyTm6BorMM&gid=1919968304',
  },
  {
    title: 'Mass create value',
    link:
      'https://docs.google.com/spreadsheets/d/1PBMPqpVzrv-h2aU_P-aFr5lp-g8ohhaXhOyTm6BorMM/export?format=csv&id=1PBMPqpVzrv-h2aU_P-aFr5lp-g8ohhaXhOyTm6BorMM&gid=1945779634',
  },
  {
    title: 'Mass map attribute',
    link:
      'https://docs.google.com/spreadsheets/d/1PBMPqpVzrv-h2aU_P-aFr5lp-g8ohhaXhOyTm6BorMM/export?format=csv&id=1PBMPqpVzrv-h2aU_P-aFr5lp-g8ohhaXhOyTm6BorMM&gid=0',
  },
];

export default function Demo() {
  return (
    <>
      <MassUploadPage
        // host="/item/shopee_admin/proxy"
        massEditTypes={MassEditTypes}
        templates={MassEditTemplates} // optional, if provided will show the template download buttons
        // customTemplateButton={
        //   // optional, if provided will show custom component at the template button place. Templates would be ignored if customTemplateButtons is provided.
        //   <ExportBtn downloadTemplate={downloadTemplate} />
        // }
        validateTemplateHeaders={MassEditTemplateHeaders} // optional, if provided will validate that the uploaded csv file header is the same as provided template header (case-sensitive).
        pageTitle="Hello"
        region={'SG'}
      />
    </>
  );
}
