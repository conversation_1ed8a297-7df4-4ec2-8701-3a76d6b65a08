import { UploadOutlined } from '@ant-design/icons';
import { Button, Form, message,Select, Upload } from 'antd';
import { useForm } from 'antd/lib/form/Form';
import type { SelectValue } from 'antd/lib/select';
import type { UploadChangeParam } from 'antd/lib/upload';
import type { RcFile, UploadFile } from 'antd/lib/upload/interface';
import React, { useEffect } from 'react';
import * as uuid from 'uuid';

import { FILE_UPLOAD } from 'src/constants';
import type { ExtraDataObj } from '../../typings';
import { validateHeaders } from '../../utils';
import styles from './styles.module.scss';

interface Props {
  region: string;
  massEditTypes: { [k: string]: string };
  /**
   * A map from MassEditLabel -> array of required headers
   */
  enforceTemplateHeaders?: { [k: string]: string[] };
  createMassEditTask: (query: {
    filePath: string;
    massEditType: string;
    originalFilename: string;
    extraData: ExtraDataObj;
    region: string;
  })=> Promise<void>;
  selectOnchange?: (value: SelectValue)=> void;
  hideTypeSelect?: boolean;
  beforeMassEditTaskCreate:
  | undefined
  | ((file: RcFile | undefined)=> Promise<ExtraDataObj>);
}
export function MassEditTaskUploader({
  region,
  massEditTypes,
  enforceTemplateHeaders,
  createMassEditTask,
  selectOnchange,
  hideTypeSelect,
  beforeMassEditTaskCreate,
}: Props) {
  const [form] = useForm();
  const [file, setFile] = React.useState<
  UploadFile<{
    data?: { filename?: string };
    error?: number;
    error_msg?: string;
  }>
  >();
  const [isProcessingFile, setIsProcessingFile] = React.useState(false);
  // file upload接口无鉴权。wsa 的S3配置要求region传 local region or GLOBAL 标记文件存储位置
  const nRegion = region === 'WW' ? 'GLOBAL' : region;

  const handleBeforeUpload = async (file: RcFile) => {
    const selectedType = form.getFieldValue('massEditType');
    if (!selectedType) {
      message.error('Please select a upload type');
      setIsProcessingFile(false);
      return Promise.reject();
    }

    setIsProcessingFile(true);
    const maxFileSize = 200;
    if (file.size > maxFileSize * 1024 * 1024) {
      message.error(`File size cannot exceed ${maxFileSize} MB.`);
      setIsProcessingFile(false);
      return Promise.reject();
    }
    if (file.size === 0) {
      message.error('Cannot upload empty file.');
      setIsProcessingFile(false);
      return Promise.reject();
    }
    if (/\.[0-9a-z]+$/i.exec(file.name)?.pop() !== '.csv') {
      message.error('Please upload csv file.');
      setIsProcessingFile(false);
      return Promise.reject();
    }
    if (!/^[\w-_\s]+$/g.test(file.name.replace('.csv', ''))) {
      message.error(
        'Filename can not contain special characters except for underscores, tabs, whitespace.',
      );
      setIsProcessingFile(false);
      return Promise.reject();
    }
    // Validate header
    if (enforceTemplateHeaders) {
      let flag = false;

      for (const type of Object.keys(massEditTypes)) {
        if (selectedType === massEditTypes[type]) {
          if (type in enforceTemplateHeaders) {
            try {
              await validateHeaders(file, enforceTemplateHeaders[type]);
            } catch (e) {
              setIsProcessingFile(false);
              message.error(
                `Please follow the template header. (${enforceTemplateHeaders[
                  type
                ].join(', ')})`,
              );
              flag = true;
            }
          }
        }
      }
      setIsProcessingFile(false);
      if (flag) return Promise.reject();
      return Promise.resolve();
    }
  };
  const handleFileRemove = () => {
    setFile(undefined);
    return false;
  };
  const handleFileUpload = (change: UploadChangeParam<UploadFile>) => {
    setFile(change.file);
  };

  const haveFiletoUpload = Boolean(file?.response?.data?.filename);

  const handleMassEditTaskCreate = (extraData: ExtraDataObj) => {
    if (haveFiletoUpload) {
      if (file?.name) {
        createMassEditTask({
          filePath: file?.response?.data?.filename as string,
          massEditType: form.getFieldValue('massEditType'),
          originalFilename: file.name,
          extraData: extraData,
          region,
        });
      }
    }
    setFile(undefined);
  };

  const handleCreateMassEditTaskOnClick = async () => {
    let extraData = {};
    if (beforeMassEditTaskCreate) {
      try {
        extraData = await beforeMassEditTaskCreate(file?.originFileObj);
      } catch (err) {
        return;
      }
    }
    handleMassEditTaskCreate(extraData);
  };

  useEffect(() => {
    if (file && file?.response?.error) {
      message.error(file?.response?.error_msg);
      setFile({
        ...file,
        status: 'error',
      });
    }
  }, [file?.response]);

  return (
    <Form layout="inline" colon={false} form={form}>
      <Form.Item
        label="Type"
        name="massEditType"
        initialValue={Object.values(massEditTypes)[0]}
        hidden={hideTypeSelect}
      >
        <Select
          style={{ minWidth: '220px' }}
          dropdownMatchSelectWidth={false}
          onChange={(value: SelectValue) => {
            if (selectOnchange) selectOnchange(value);
          }}
        >
          {Object.keys(massEditTypes).map((type) => (
            <Select.Option
              value={massEditTypes[type]}
              key={massEditTypes[type]}
            >
              {type}
            </Select.Option>
          ))}
        </Select>
      </Form.Item>
      <Form.Item label="File" style={{ width: '250px' }}>
        <Upload
          action={FILE_UPLOAD}
          accept=".csv"
          headers={{ 'Request-Id': uuid.v4(), Region: nRegion }}
          onChange={handleFileUpload}
          onRemove={handleFileRemove}
          beforeUpload={handleBeforeUpload}
          fileList={file ? [file] : []}
          className={styles.upload}
        >
          <Button style={{ width: '180px' }} loading={isProcessingFile}>
            <UploadOutlined /> Select CSV File
          </Button>
        </Upload>
      </Form.Item>
      <Form.Item>
        <Button
          type="primary"
          onClick={handleCreateMassEditTaskOnClick}
          disabled={!haveFiletoUpload}
        >
          Upload
        </Button>
      </Form.Item>
    </Form>
  );
}
