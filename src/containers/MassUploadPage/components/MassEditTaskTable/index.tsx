import { InfoCircleTwoTone } from '@ant-design/icons';
import { Popover,Table, Tag } from 'antd';
import type { ColumnProps, TablePaginationConfig, TableProps } from 'antd/lib/table';
import moment from 'moment';
import * as React from 'react';

import {
 MassEditTaskTableTitle,  MassEditTypeLabel,
  MassEditTypeLabelColor,
  MassEditTypeLabelExplanation } from '../../constants';
import type { MassEditTask } from '../../typings';
import { MassEditStatus } from '../../typings';
import type { ValueOf } from '../../typings/utils';
import { getFileDownloadLink } from '../../utils';
import ProgressWithStop from '../ProgressWithStop';

interface Props extends TableProps<MassEditTask> {
  massEditTasks: MassEditTask[];
  filterOptions?: { [key: string]: string };
  MassEditTypeNameMapping?: { [key: string]: string };
  changeFilter: (filter: string[])=> void;
  processColumn:
  | undefined
  | ((columns: ColumnProps<MassEditTask>[])=> ColumnProps<MassEditTask>[]);
}

export function MassEditTaskTable(props: Props) {
  const {
    massEditTasks,
    MassEditTypeNameMapping,
    filterOptions,
    changeFilter,
    processColumn,
  } = props;

  const columns: ColumnProps<MassEditTask>[] = [
    {
      title: MassEditTaskTableTitle.UpdateTime,
      dataIndex: 'mtime',
      key: 'mtime',
      width: 150,
      render: (time: number) => {
        return moment(time * 1000).format('YYYY-MM-DD HH:mm:ss');
      },
    },
    {
      title: MassEditTaskTableTitle.Type,
      dataIndex: 'massEditType',
      key: 'massEditType',
      width: 120,
      filters: filterOptions
        ? Object.entries(filterOptions).map(([key, val]) => {
          return {
            text: key,
            value: val,
          };
        })
        : undefined,
      onFilter: (value: string, record) => {
        //will handle filter in onChange function.
        return true;
      },
      render: (type) => {
        if (MassEditTypeNameMapping && type in MassEditTypeNameMapping) {
          return MassEditTypeNameMapping[type];
        } else return type;
      },
    },
    {
      title: MassEditTaskTableTitle.OriginalFile,
      dataIndex: 'originalFilename',
      key: 'originalFilename',
      width: 150,
      render: (filename: string, record) => (
        <a href={getFileDownloadLink(record.inputFilename, filename)}>
          {filename}
        </a>
      ),
    },
    {
      title: MassEditTaskTableTitle.ResultFile,
      dataIndex: 'outputFilename',
      key: 'outputFilename',
      width: 150,
      render: (filename: string) => (
        <a href={getFileDownloadLink(filename, filename)}>{filename}</a>
      ),
    },
    {
      title: (
        <Popover
          placement="bottomRight"
          content={
            <div style={{ width: 400 }}>
              {Object.values(MassEditTypeLabel).map(
                (massEditTypeLabel: ValueOf<typeof MassEditTypeLabel>) => (
                  <p key={massEditTypeLabel}>
                    <Tag color={MassEditTypeLabelColor[massEditTypeLabel]}>
                      {massEditTypeLabel}
                    </Tag>
                    {MassEditTypeLabelExplanation[massEditTypeLabel]}
                  </p>
                ),
              )}
            </div>
          }
        >
          {MassEditTaskTableTitle.Progress} <InfoCircleTwoTone />
        </Popover>
      ),
      dataIndex: 'taskStatus',
      key: 'status',
      width: 100,
      render: (status: MassEditStatus, row) => {
        return status === MassEditStatus.Processing ? (
          <ProgressWithStop
            percent={row.progress ?? 0}
            stopTask={row.stopTask}
          />
        ) : (
          <Tag color={MassEditTypeLabelColor[MassEditTypeLabel[status]]}>
            {MassEditTypeLabel[status]}
          </Tag>
        );
      },
    },
    {
      title: MassEditTaskTableTitle.Operator,
      dataIndex: 'operator',
      key: 'operator',
      width: 150,
    },
  ];

  const handleChange = (
    papigation: TablePaginationConfig,
    filters: { massEditType: string[] | null },
  ) => {
    //filters.massEditType is array of string when set filters, and is null when reset filters
    filterOptions &&
      changeFilter(filters.massEditType || Object.values(filterOptions));
  };

  return (
    <Table
      columns={processColumn ? processColumn(columns) : columns}
      dataSource={massEditTasks}
      rowKey="taskId"
      onChange={handleChange}
      {...props}
    ></Table>
  );
}
