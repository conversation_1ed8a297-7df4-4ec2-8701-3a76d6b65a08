import { Button, Dropdown } from 'antd';
import * as React from 'react';

export type Template = { link: string; name?: string; title?: string }; // TODO: To align with MassUpload Page template type, remove title and use name. Need to check dependencies before removing.
export type ExportBtnProps = { icon: string; templates: Template[] };
export function ExportBtn({ icon, templates }: ExportBtnProps) {
  return (
    <Dropdown
      menu={{
        items: templates.map((eachTemp: Template, index) => ({
          key: index,
          label: <a href={eachTemp.link}>{eachTemp.name || eachTemp.title}</a>,
        })),
      }}
    >
      <Button type="text">
        <img height="40" width="47" src={icon} />
      </Button>
    </Dropdown>
  );
}
