import { Button,Progress } from 'antd';
import React from 'react';

import styles from './styles.module.scss';

interface Props {
  percent: number;
  stopTask: ()=> void;
}
export default function ProgressWithStop({ percent, stopTask }: Props) {
  return (
    <Progress
      percent={percent}
      size="small"
      format={() => {
        return (
          <span className={styles.progressAction}>
            <Button
              className={styles.stopBotton}
              type="link"
              onClick={stopTask}
            >
              Stop
            </Button>
            <span className={styles.percent}>{`${percent  } %`}</span>
          </span>
        );
      }}
    />
  );
}
