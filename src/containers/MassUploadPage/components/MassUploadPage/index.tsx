// import ListingCustomLayout as CustomLayout from '[listing-CustomLayout]';
// import ListingUsePagination as usePagination from '[listing-usePagination]';
import { CustomLayout, getCountryForDisplay, usePaginationHook as usePagination } from 'admin-upload-common';
import { Button } from 'antd';
import type { SelectValue } from 'antd/lib/select';
import type { ColumnProps } from 'antd/lib/table';
import type { RcFile } from 'antd/lib/upload/interface';
import type { CustomPerfReporter } from 'listing-reporter/mdapReporter/customPerfReport';
import React, { useEffect, useState } from 'react';

import {
  mainReporter,
  POINT_FINISH_RENDER,
  POINT_FIRST_RENDER,
} from 'src/reporter';
import useMassEdit from '../../hooks';
import useTableFilter from '../../hooks/useTableFilter';
import type { ExtraDataObj, MassEditTask } from '../../typings';
import { MassEditTaskTable } from '../MassEditTaskTable';
import { MassEditTaskUploader } from '../MassEditTaskUploader';
import styles from './styles.module.scss';

export interface MassUploadPageProps {
  /**
   * (Optional) host URL
   * @default '/item/shopee_admin/proxy'
   */
  host?: string;

  /**
   * Map of mass edit names to their types
   * @example
   * ```ts
   * {
   *   'Mass Create': 'mass_create_fe_category',
   *   'Mass Edit': 'mass_update_fe_category',
   * };
   * ```
   */
  massEditTypes: { [k: string]: string };
  massEditDesc?: { [k: string]: React.ReactNode };
  /**
   * Downloadable templates for each mass edit type
   */
  templates?: {
    name: string;
    link: string;
    icon?: string;
  }[];

  /**
   * Custom template button, overrides `templates` if used
   */
  customTemplateButton?: React.ReactNode;

  /**
   * Map of mass edit type to headers required for validation. No header validation is done if left undefined
   * @deprecated use enforceTemplateHeaders if possible
   */
  validateTemplateHeaders?: { [k: string]: string };

  /**
   * A map from MassEditLabel -> array of required headers
   */
  enforceTemplateHeaders?: { [k: string]: string[] };

  /**
   * @default 'Mass Upload'
   */
  pageTitle?: string;

  /**
   * ReactNode containing information to be shown to the user
   * @example
   */
  instruction?: React.ReactNode;

  /**
   * Mass edit types that the user has permissions write permissions for
   */
  authMassEditLogTypes?: { [k: string]: string };
  region: string;
  /**
   * polling interval when upload csv file.
   * The default value is 3000
   */
  pollingInterval?: number;

  /*
    Handle select change
  */
  selectOnchange?: (value: SelectValue) => void;

  /*
    reporter instance
   */
  reporter?: CustomPerfReporter;
  customTopCardInstruction?: React.ReactNode;
  hideTypeSelect?: boolean;
  ShopLimitTable?: React.ReactNode;
  onAllTaskDone?: Function;
  beforeMassEditTaskCreate?: (
    file: RcFile | undefined,
  ) => Promise<ExtraDataObj>;
  processColumn?: (
    columns: ColumnProps<MassEditTask>[],
  ) => ColumnProps<MassEditTask>[];
}
export interface TemplateButtonsProps {
  massEditTemplates?:
  | {
    [k: string]: {
      link: string;
      icon: string;
    };
  }[]
  | React.ReactNode;
}

export function MassUploadPage({
  host = '/wsa/marketplace/listing/upload/upload_admin',
  massEditTypes,
  templates = [],
  customTemplateButton,
  validateTemplateHeaders = {},
  pageTitle = 'Mass Upload',
  instruction,
  authMassEditLogTypes,
  region,
  enforceTemplateHeaders = {},
  pollingInterval,
  selectOnchange,
  reporter,
  customTopCardInstruction,
  hideTypeSelect = false,
  ShopLimitTable,
  onAllTaskDone,
  massEditDesc,
  beforeMassEditTaskCreate,
  processColumn,
}: MassUploadPageProps) {
  const { page, pageSize, changePage, changePageSize } = usePagination();
  const { filter, changeFilter } = useTableFilter();
  const [currSelectTemp, setCurrSelectTemp] = useState<string>(Object.values(massEditTypes)[0]);
  const _massEditLogTypes = authMassEditLogTypes || massEditTypes;
  const { massEditTasks, totalTask, loading, createMassEditTask } = useMassEdit(
    host,
    filter.length ? filter : Object.values(_massEditLogTypes),
    {
      page,
      pageSize,
    },
    region,
    pollingInterval,
    onAllTaskDone,
  );

  const _enforceTemplateHeaders = enforceTemplateHeaders;
  if (validateTemplateHeaders) {
    Object.keys(validateTemplateHeaders).forEach((k) => {
      _enforceTemplateHeaders[k] = validateTemplateHeaders[k].split(',');
    });
  }

  useEffect(() => {
    if (reporter) reporter.setCustomPointTime(POINT_FIRST_RENDER);
  }, []);

  useEffect(() => {
    if (!loading && reporter) {
      reporter.setCustomPointTime(POINT_FINISH_RENDER);
      mainReporter.updateSDKconfig({
        region: getCountryForDisplay(),
      });
      reporter.report(0, {
        isApiError: Boolean(!massEditTasks).toString(),
      });
    }
  }, [loading]);
  const handleSelectOnchange = (value: string) => {
    setCurrSelectTemp(value);
    selectOnchange?.(value);
  };
  return (
    <CustomLayout
      title={pageTitle}
      extraInfo={instruction}
      topCard={
        <>
          {customTopCardInstruction && (
            <div style={{ marginBottom: '20px' }}>
              {customTopCardInstruction}
            </div>
          )}
          <div style={{ display: 'flex', alignItems: 'center' }}>
            <div>
              <MassEditTaskUploader
                region={region}
                massEditTypes={massEditTypes}
                createMassEditTask={createMassEditTask}
                enforceTemplateHeaders={_enforceTemplateHeaders}
                selectOnchange={handleSelectOnchange}
                hideTypeSelect={hideTypeSelect}
                beforeMassEditTaskCreate={beforeMassEditTaskCreate}
              />
              {
                massEditDesc?.[currSelectTemp] ? (
                  <div style={{ marginTop: 8, color: 'gray' }}>
                    {massEditDesc[currSelectTemp]}
                  </div>
                ) : null
              }
            </div>
            <div className={styles.templateDownloadButtons}>
              {customTemplateButton
                ? customTemplateButton
                : templates.map((template) => (
                  <React.Fragment key={template.name}>
                    <Button
                      type="text"
                      onClick={() => (window.location.href = template.link)}
                    >
                      {template.icon ? <img src={template.icon} /> : null}
                      <div>{template.name} Template</div>
                    </Button>
                  </React.Fragment>
                ))}
            </div>
          </div>
        </>
      }
      table={
        <>
          <MassEditTaskTable
            loading={loading}
            massEditTasks={massEditTasks}
            filterOptions={_massEditLogTypes}
            MassEditTypeNameMapping={Object.keys(_massEditLogTypes).reduce(
              (obj: { [k: string]: string }, key) => {
                obj[_massEditLogTypes[key]] = key;
                return obj;
              },
              {},
            )}
            title={() => <h3>Update Log</h3>}
            pagination={{
              total: totalTask,
              showSizeChanger: true,
              current: page,
              pageSize: pageSize,
              onChange: changePage,
              onShowSizeChange: (current, size) => {
                changePageSize(size);
              },
            }}
            changeFilter={changeFilter}
            processColumn={processColumn}
          ></MassEditTaskTable>
          {ShopLimitTable && ShopLimitTable}
        </>
      }
    />
  );
}
