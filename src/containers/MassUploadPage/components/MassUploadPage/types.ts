// Mapping of [Label Name] -> [Mass Edit Type]
export interface MassEditType {
  [k: string]: string;
}

export interface MassEditTemplate {
  name: string;

  // Link to the template, usually on a GDocs
  link: string;

  // Image source for the icon, if applicable
  icon?: string;
}

export interface MassEditOptions {
  // key is label name
  [k: string]: {
    // If a max file size is passed in, it will be used for validation
    maxFileSize?: number;
  };
}
