import type { AxiosInstance } from 'axios';
import Axios from 'axios';
import { v4 } from 'uuid';

import type {
  WithResponse} from '../../../api/helper/apiHandler';
import {
  apiHandler
} from '../../../api/helper/apiHandler';
import type {
  CreateMassEditTaskRequest,
  CreateMassEditTaskResponse,
  GetMassEditTaskProgressRequest,
  GetMassEditTaskProgressResponse,
  GetMassEditTasksRequest,
  GetMassEditTasksResponse,
  StopMassEditTaskRequest,
  StopMassTaskResponse,
} from '../typings';
import { deepCamel, deepSnake } from '../utils/transform';

export default class MassEditAPI {
  axios: AxiosInstance;
  constructor(region: string, host: string) {
    const axios = Axios.create({
      method: 'post',
      baseURL: host,
    });

    let COMMIT_HASH = '';
    try {
      //@ts-expect-error
      COMMIT_HASH = __COMMIT_HASH__;
    } catch (e) {}

    axios.interceptors.request.use(
      (config) => {
        return {
          ...config,
          headers: {
            ...config.headers,
            'Request-Id': v4(),
            Region: region,
            Version: COMMIT_HASH,
          },
        };
      },
      (error) => {
        return Promise.reject(error);
      },
    );
    axios.interceptors.request.use((config) => {
      const { data } = config;
      return {
        ...config,
        data: deepSnake(data),
      };
    });

    axios.interceptors.response.use((response) => {
      const camelData = deepCamel(response.data);
      response.data = camelData;
      return response;
    });
    this.axios = axios;
  }
  async createMassEditTask(requestBody: CreateMassEditTaskRequest) {
    return apiHandler(
      this.axios.request<WithResponse<CreateMassEditTaskResponse>>({
        url: '/new_task',
        data: requestBody,
      }),
      { returnFullResponse: true },
    );
  }
  async getMassEditTasks(requestBody: GetMassEditTasksRequest) {
    return apiHandler(
      this.axios.request<WithResponse<GetMassEditTasksResponse>>({
        url: '/get_tasks',
        data: requestBody,
      }),
      { returnFullResponse: true },
    );
  }
  async getMassEditTaskProgress(requestBody: GetMassEditTaskProgressRequest) {
    return apiHandler(
      this.axios.request<WithResponse<GetMassEditTaskProgressResponse>>({
        url: '/get_progress',
        data: requestBody,
      }),
      { returnFullResponse: true },
    );
  }
  async stopMassEditTask(requestBody: StopMassEditTaskRequest) {
    return apiHandler(
      this.axios.request<WithResponse<StopMassTaskResponse>>({
        url: '/stop_task',
        data: requestBody,
      }),
      { returnFullResponse: true },
    );
  }
}
