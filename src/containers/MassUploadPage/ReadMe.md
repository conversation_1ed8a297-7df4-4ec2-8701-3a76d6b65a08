# Mass Upload Component

Components and hooks for http-gateway mass-edit service

## Components

### MassUploadPage

In MassUploadPage, useMassEdit custom hook is called to get MassEditTasks which are rendered by MassEditTable. It also includes Upload csv file function which is used for creating a new MassEditTask.
For some special requirements that can not be fulfilled by MassUploadPage, you may use useMassEdit hook with the MassEditTable and develope your own upload logic and page layout.

| Property                | Description                                                                                                                                                                   | Type                                          | Default       |
| ----------------------- | ----------------------------------------------------------------------------------------------------------------------------------------------------------------------------- | --------------------------------------------- | ------------- |
| massEditTypes           | Required. A object to represent the mapping between displayed the mass-edit name (Frontend) and the mass_edit_type (Backend)                                                  | {[MassEditName: string]: string}              | -             |
| templates               | Optional. Configuration of template buttons.                                                                                                                                  | {name: string; link: string; icon: string;}[] | []            |
| validateTemplateHeaders | Optional. Mapping between mass-edit names and template headers. This will be used to validate if the uploaded csv file has the same header as the template's (case sensitive) | {[MassEditName: string]: string}              | {}            |
| customTemplateButton    | Optional. If provided will show custom component at the template button place. Templates would be ignored if customTemplateButtons is provided.                               | ReactNode                                     | -             |
| pageTitle               | Optional. The title of the page.                                                                                                                                              | string                                        | 'Mass Upload' |

**Upload file validations**

- File size cannot exceed 3 MB.
- File is not empty.
- Is a csv file
- File name can not contain special characters except for underscores.
- Cannot exceed 50000 rows in one file.
- (If massEditTemplateHeaders is provided) CSV file header is the same as the template header

### MassEditTaskTable

Table component to render MassEditTasks

      export interface MassEditTask {
          task_id: number;
          ctime: number;
          mtime: number;
          mass_edit_type: string;
          operator: string;
          input_filename: string;
          output_filename: string; // empty if the task has not been processed
          task_status: MassEditStatus; // 0 PENDING, 1 PROCESSING, 2 DONE 3 ERROR 4 STOPPED(manual stop)
          extra_data: string; // json string
          progress?: number;
          stopTask: () => void;
    }

| Property                | Description                                         | Type                             | Default |
| ----------------------- | --------------------------------------------------- | -------------------------------- | ------- |
| massEditTasks           | The mass edit tasks returned by http-gateway server | MassEditTask[]                   | []      |
| MassEditTypeNameMapping | The mapping from mass_edit_type to mass-edit name   | {[MassEditType: string]: string} | -       |

## Custom Hooks

### useMassEdit

Function Parameters

    host: string,
    mass_edit_types: string[],
    pagination: { page: number; pageSize: number },
    region: string,

Return Values

    {
        massEditTasks:  MassEditTasks [],
        totalTask: number,
        loading: boolean,
        createMassEditTask: (query: {filePath: string;  massEditType: string;}) =>  Promise<void>,
        stopMassEditTask:  (query: {filePath: string;  massEditType: string;}) =>  Promise<void>,
    }

## How it works?

Please check https://confluence.shopee.io/pages/viewpage.action?spaceKey=TD&title=Mass-Edit+Function
