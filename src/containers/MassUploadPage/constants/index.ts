import { MassEditStatus } from '../typings';

export const MassEditTypeLabel = {
  [MassEditStatus.Done]: 'Done',
  [MassEditStatus.Failed]: 'Failed',
  [MassEditStatus.Interrupted]: 'Interrupted',
  [MassEditStatus.Pending]: 'Pending',
  [MassEditStatus.Processing]: 'Processing',
  [MassEditStatus.Stopped]: 'Stopped',
  [MassEditStatus.DryRun]: 'Dry Run',
} as const;
export const MassEditTypeLabelColor = {
  [MassEditTypeLabel[MassEditStatus.Done]]: 'green',
  [MassEditTypeLabel[MassEditStatus.Failed]]: 'red',
  [MassEditTypeLabel[MassEditStatus.Interrupted]]: 'red',
  [MassEditTypeLabel[MassEditStatus.Pending]]: 'blue',
  [MassEditTypeLabel[MassEditStatus.Processing]]: 'blue',
  [MassEditTypeLabel[MassEditStatus.Stopped]]: 'default',
  [MassEditTypeLabel[MassEditStatus.DryRun]]: 'purple',
} as const;

export const MassEditTypeLabelExplanation = {
  [MassEditTypeLabel[MassEditStatus.Done]]:
    'All updates are successfully processed',
  [MassEditTypeLabel[MassEditStatus.Failed]]: 'Some updates are failed',
  [MassEditTypeLabel[MassEditStatus.Interrupted]]:
    'Process is stopped by system and should be rerun',
  [MassEditTypeLabel[MassEditStatus.Stopped]]: 'Process is stopped by the user',
  [MassEditTypeLabel[MassEditStatus.Pending]]: 'Task waiting for processing',
  [MassEditTypeLabel[MassEditStatus.Processing]]: 'Task is being processing',
  [MassEditTypeLabel[MassEditStatus.DryRun]]: 'It is a dry run task',
} as const;

export const MassEditTaskTableTitle = {
  UpdateTime: 'Update Time',
  Type: 'Type',
  OriginalFile: 'Original File',
  ResultFile: 'Result File',
  Progress: 'Progress',
  Operator: 'Operator',
  Data: 'Data',
} as const;
