import Papaparse from 'papaparse';

export function getFileDownloadLink(path: string, filename: string) {
  const _filename = `${
    filename.endsWith('.csv') ? filename : `${filename  }.csv`
  }`;
  return `${window.location.origin}/wsa/marketplace/listing/upload/upload_admin/file_download?path=${path}&filename=${_filename}`;
}

export const validateHeaders = (file: File, headers: string[]) => {
  return new Promise<void>((resolve, reject) => {
    // eslint-disable-next-line @typescript-eslint/no-explicit-any
    Papaparse.parse<any>(file, {
      header: false,
      worker: true,
      step: (results, parser) => {
        // We only care about the first line
        const fileHeaders = results.data;
        parser.abort();
        if (fileHeaders.length !== headers.length) {
          reject();
          return;
        }
        for (let i = 0; i < fileHeaders.length; i++) {
          if (fileHeaders[i] !== headers[i]) {
            reject();
            return;
          }
        }
        resolve();
      },
    });
  });
};

export function getCountry() {
  let country = window.localStorage.getItem('country');
  if (!country) {
    country = 'SG';
  }
  return country;
}
