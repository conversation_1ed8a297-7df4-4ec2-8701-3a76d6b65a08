import _ from 'lodash';

const createTransform = (
  transformKey: (string?: string | undefined)=> string,
) => {
  // @ts-expect-error
  const transformObject = (value: T, depth = -1): T => {
    if (
      depth === 0 ||
      value == null ||
      typeof value !== 'object' ||
      value instanceof FormData
    ) {
      return value;
    }

    if (Array.isArray(value)) {
      return value.map((item) => transformObject(item, depth - 1));
    }

    // @ts-expect-error
    const objectRes = {} as T;
    for (const key in value) {
      if (value.hasOwnProperty(key)) {
        const convertKey = transformKey(key);
        objectRes[convertKey] = transformObject(value[key], depth - 1);
      }
    }
    return objectRes;
  };
  return transformObject;
};

export const deepCamel = createTransform(_.camelCase.bind(_));

export const deepSnake = createTransform(_.snakeCase.bind(_));
