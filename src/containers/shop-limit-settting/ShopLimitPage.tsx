import { SearchOutlined } from '@ant-design/icons';
import { Button, Form,Input, Table } from 'antd';
import type { ColumnProps } from 'antd/lib/table';
import moment from 'moment';
import React from 'react';

import { MassUploadPage } from 'src/containers/MassUploadPage';
import type { MassUploadPageProps } from 'src/containers/MassUploadPage/components/MassUploadPage';
import { MassEditTaskTableTitle } from 'src/containers/MassUploadPage/constants';
import type { MassEditTask } from 'src/containers/MassUploadPage/typings';
import useShopLimiInfo from '../../hooks/shop-limit-setting';

export default function ShopLimitPage(props: MassUploadPageProps) {
  const {
    form,
    data,
    loading,
    updateFilter,
    page: current,
    pageSize,
    total,
    updatePagination,
  } = useShopLimiInfo();
  const columns = [
    {
      title: 'Shop ID',
      dataIndex: 'shopid',
    },
    {
      title: 'Shop Username',
      dataIndex: 'shopUsername',
    },
    {
      title: 'Upload Limit',
      dataIndex: 'uploadLimit',
    },
    {
      title: 'Update Time',
      dataIndex: 'modifyTime',
      render: (time: number) =>
        moment(time * 1000).format('YYYY-MM-DD HH:mm:ss'),
    },
    {
      title: 'Operator',
      dataIndex: 'operator',
    },
  ];

  const changeTableColumn = (columns: ColumnProps<MassEditTask>[]) => {
    const newColumn = columns.map((v) => {
      if (v.title === MassEditTaskTableTitle.Type) {
        v.filters = undefined;
      }
      return v;
    });
    return newColumn;
  };

  const onSearch = () => {
    form
      .validateFields()
      .then((res) => {
        updateFilter();
      })
      .catch((err) => {
        console.log(err);
      });
  };
  const onReset = () => {
    form.resetFields();
    updateFilter();
  };

  const table = (
    <>
      <div style={{ height: '16px', backgroundColor: '#f0f2f5' }}></div>
      <div style={{ padding: '16px 0' }}>
        <div style={{ padding: '0 16px' }}>
          <h2 style={{ fontSize: '16px', lineHeight: '18px' }}>
            Limit Setting Result
          </h2>
          <p style={{ fontSize: '14px', lineHeight: '16px', color: '#666' }}>
            Please search and check the listing limit after the uploaded file is
            processed completely.
          </p>
          <Form form={form} layout="inline" style={{ margin: '24px 0' }}>
            <Form.Item
              label="Shop ID"
              name="shopId"
              colon={false}
              rules={[
                { pattern: /^[0-9]*$/, message: 'Please enter an integer.' },
              ]}
            >
              <Input placeholder="Input" />
            </Form.Item>
            <Form.Item>
              <Button
                type="primary"
                icon={<SearchOutlined />}
                onClick={() => onSearch()}
              >
                Search
              </Button>
              <Button onClick={() => onReset()}>Reset</Button>
            </Form.Item>
          </Form>
        </div>
        <Table
          loading={loading}
          columns={columns}
          dataSource={data}
          pagination={{
            current,
            pageSize,
            total,
            showSizeChanger: true,
            onChange: updatePagination,
          }}
        />
      </div>
    </>
  );

  return (
    <MassUploadPage
      {...props}
      ShopLimitTable={table}
      onAllTaskDone={onReset}
      processColumn={changeTableColumn}
    />
  );
}
