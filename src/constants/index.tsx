import React from 'react';

import { AuditType as ItemAuditStatus, ItemAttributeType,ItemCondition, Status } from 'src/api/PBData/constants';
import { AttrInputValidatorType, AttrStatus,AuditType as PriceStockAuditType, ItemPriceStatus, ItemStockStatus, PromotionStatus, PromotionType } from 'src/api/uploadAdmin/constants';
import {
  CqcQueueType,
  QcAction,
  QcAddOnAction,
  QcLevel,
  QueueType,
  RuleModelQcStatus,
} from '../typings';

export const imageUrlPrefix = 'http://cf.shopee.sg/file/';
export const FILE_UPLOAD =
  '/wsa/marketplace/listing/upload/upload_admin/file_upload';
export const IMAGE_UPLOAD = '/item/shopee_admin/image/upload_with_compress';
export const regionsWithTax = ['ID', 'PL', 'ES', 'FR', 'CO','VN'];
export const regionsWithDangerousGoods = ['ID', 'MY', 'PH', 'VN', 'SG'];
// used to convert weight, dimensions, price between BE and FE
// TODO: use convertToBENumber & convertToFENumber instead in _shared/utils
export const FEtoBEMultiplier = 100000;
// artificial delay after submit before refreshing
export const refreshDelay = 1000;
const digitDecimalRegex = (n = 0) => {
  if (n) {
    return new RegExp(`^\\d+(?:\\.\\d{0,${n}})?$`);
  }
  return new RegExp(/^[-+]?\d*$/);
};
export const integerRegex = new RegExp(/^[-+]?\d*$/);
export const integerValueRule = {
  pattern: integerRegex,
  message: 'Please enter an integer',
};

export const oneDigitDecimalRule = {
  pattern: digitDecimalRegex(1),
  message: 'Please enter a decimal number (up to 1 decimal places)',
};

export const nonNegativeIntegerRegex = new RegExp(/^\d*$/);
export const positiveIntegerRegex = new RegExp(/^[1-9]\d*$/);
export const positiveIntegerValueRule = {
  pattern: positiveIntegerRegex,
  message: 'Please enter a positive integer',
};
export const fiveDigitDecimalRegex = new RegExp(/^\d+(?:\.\d{0,5})?$/);
export const fiveDigitDecimalRule = {
  pattern: fiveDigitDecimalRegex,
  message: 'Please enter a decimal number (up to 5 decimal places)',
};
export const threeDigitDecimalRegex = new RegExp(/^\d+(?:\.\d{0,3})?$/);
export const threeDigitDecimalRule = {
  pattern: threeDigitDecimalRegex,
  message: 'Please enter a decimal number (up to 3 decimal places)',
};
export const priceRegex = new RegExp(/^\d(\d|,)*(?:\.\d{0,5})?$/);
export const priceRegexRule = {
  pattern: priceRegex,
  message: 'Please enter a decimal number (up to 5 decimal places)',
};

export const counterfeitTagIds = {
  test: {
    BR: {
      fake: 823966827513346,
      not_fake: 823966867742210,
      pending: 823966874678786,
    },
    ID: {
      fake: 823915037689346,
      not_fake: 823915123000834,
      pending: 823915130780162,
    },
    MY: {
      fake: 823966901650946,
      not_fake: 823966907407362,
      pending: 823966912391170,
    },
    PH: {
      fake: 823966926627842,
      not_fake: 823966932340738,
      pending: 823966937796098,
    },
    SG: {
      fake: 823966951910914,
      not_fake: 823966959100418,
      pending: 823966962552834,
    },
    TH: {
      fake: 823966976699394,
      not_fake: 823966981800450,
      pending: 823966988733954,
    },
    TW: {
      fake: 823967002574338,
      not_fake: 823967006380034,
      pending: 823967011651074,
    },
    VN: {
      fake: 823967024171522,
      not_fake: 823967027398658,
      pending: 823967032839682,
    },
  },
  uat: {
    BR: {
      fake: 824636549126149,
      not_fake: 824636566467077,
      pending: 824636581024261,
    },
    ID: {
      fake: 824636608460805,
      not_fake: 824636622789641,
      pending: 824636638106633,
    },
    MX: {
      fake: 824636655663621,
      not_fake: 824636669530629,
      pending: 824636680037897,
    },
    CO: {
      fake: 824636655663621,
      not_fake: 824636669530629,
      pending: 824636680037897,
    },
    CL: {
      fake: 824636655663621,
      not_fake: 824636669530629,
      pending: 824636680037897,
    },
    MY: {
      fake: 824636829631493,
      not_fake: 824637018148873,
      pending: 824637031049225,
    },
    PH: {
      fake: 824637076475909,
      not_fake: 824637090212873,
      pending: 824637099701765,
    },
    SG: {
      fake: 824637125562885,
      not_fake: 824637134013961,
      pending: 824637540125699,
    },
    TH: {
      fake: 824637559127560,
      not_fake: 824637566345736,
      pending: 824637573809160,
    },
    TW: {
      fake: 824637588796419,
      not_fake: 824637597298691,
      pending: 824637604762120,
    },
    VN: {
      fake: 824638422190600,
      not_fake: 824638432740355,
      pending: 824638442596867,
    },
  },
  staging: {
    BR: {
      fake: 825124108853251,
      not_fake: 825124115679747,
      pending: 825124134227971,
    },
    ID: {
      fake: 825124188601347,
      not_fake: 825124201029635,
      pending: 825124208492035,
    },
    MY: {
      fake: 825124231961603,
      not_fake: 825124241747971,
      pending: 825124251027971,
    },
    PH: {
      fake: 825124264763395,
      not_fake: 825124272477187,
      pending: 825124298603011,
    },
    SG: {
      fake: 825124310944771,
      not_fake: 825124315405315,
      pending: 825124322041347,
    },
    TH: {
      fake: 825124339896835,
      not_fake: 825124352771075,
      pending: 825124356787715,
    },
    TW: {
      fake: 825124378444291,
      not_fake: 825124384254979,
      pending: 825124390799363,
    },
    VN: {
      fake: 825154490825219,
      not_fake: 825154503752195,
      pending: 825154481219075,
    },
  },
  stable: {
    BR: {
      fake: 825124108853251,
      not_fake: 825124115679747,
      pending: 825124134227971,
    },
    ID: {
      fake: 825124188601347,
      not_fake: 825124201029635,
      pending: 825124208492035,
    },
    MY: {
      fake: 825124231961603,
      not_fake: 825124241747971,
      pending: 825124251027971,
    },
    PH: {
      fake: 825124264763395,
      not_fake: 825124272477187,
      pending: 825124298603011,
    },
    SG: {
      fake: 825124310944771,
      not_fake: 825124315405315,
      pending: 825124322041347,
    },
    TH: {
      fake: 825124339896835,
      not_fake: 825124352771075,
      pending: 825124356787715,
    },
    TW: {
      fake: 825124378444291,
      not_fake: 825124384254979,
      pending: 825124390799363,
    },
    VN: {
      fake: 825154490825219,
      not_fake: 825154503752195,
      pending: 825154481219075,
    },
  },
  live: {
    BR: {
      fake: 825111668869153,
      not_fake: 825112730099773,
      pending: 825112738064956,
    },
    ID: {
      fake: 825112762195518,
      not_fake: 825112769239073,
      pending: 825112776139324,
    },
    MY: {
      fake: 825112801485374,
      not_fake: 825112806950413,
      pending: 825112812100644,
    },
    PH: {
      fake: 825112830943802,
      not_fake: 825112834646055,
      pending: 825112840096773,
    },
    SG: {
      fake: 825112861294658,
      not_fake: 825112865435692,
      pending: 825112871272508,
    },
    TH: {
      fake: 825112927769154,
      not_fake: 825112933995527,
      pending: 825112939056690,
    },
    TW: {
      fake: 825112969389117,
      not_fake: 825112974918718,
      pending: 825112981328909,
    },
    VN: {
      fake: 825154546012166,
      not_fake: 825154556286028,
      pending: 825154531091017,
    },
  },
};
export const counterfeitTagString = {
  fake: 'Fake',
  not_fake: 'Not Fake',
  pending: 'Pending',
};

export const dangerousGoodsTooltipTitle = {
  ID:
    'Please fill in DG accurately. Inaccurate DG may result in additional shipping fee or failed delivery.',
  MY: (
    <>
      Any product categorised as Dangerous Goods will not be allowed to be
      posted using air transportation from West Malaysia To East Malaysia for
      selected logistics channels.
      <div />
      <a href={'https://seller.shopee.com.my/edu/article/1472'}>Learn more</a>
    </>
  ),
  PH: (
    <>
      Shopee's integrated couriers have special rules for dangerous or regulated
      items. Check if your item is regulated.
      <a
        style={{ marginLeft: 8 }}
        href={'https://seller.shopee.ph/edu/courseDetail/366'}
      >
        Learn more
      </a>
    </>
  ),
};

export const dangerousGoodsYesText = {
  ID: 'Contains battery/magnet/liquid/flammable materials',
  PH: 'Item regulated by select couriers',
};

export const itemStatusString = {
  [Status.ITEM_DELETE]: 'Seller Deleted',
  [Status.ITEM_NORMAL]: 'Normal',
  [Status.ITEM_REVIEWING]: 'Reviewing',
  [Status.ITEM_BANNED]: 'Banned',
  [Status.ITEM_INVALID]: 'Admin Deleted',
  [Status.ITEM_INVALID_HIDE]: 'Admin Deleted Confirmed',
  [Status.ITEM_OFFENSIVE_HIDE]: 'Blacklisted',
  [Status.ITEM_AUDITING]: 'Auditing',
  [Status.ITEM_NORMAL_UNLIST]: 'Normal Unlist',
};

export const itemStatusTagColour = {
  [Status.ITEM_DELETE]: 'lime',
  [Status.ITEM_NORMAL]: 'green',
  [Status.ITEM_REVIEWING]: 'blue',
  [Status.ITEM_BANNED]: 'red',
  [Status.ITEM_INVALID]: 'gold',
  [Status.ITEM_INVALID_HIDE]: 'orange',
  [Status.ITEM_OFFENSIVE_HIDE]: 'purple',
  [Status.ITEM_AUDITING]: 'cyan',
  [Status.ITEM_NORMAL_UNLIST]: 'geekblue',
};

export const ruleModelQcStatusString = {
  [RuleModelQcStatus.PRE_REVIEW]: 'Pre Review',
  [RuleModelQcStatus.NEW]: 'New',
  [RuleModelQcStatus.PENDING]: 'Pending',
  [RuleModelQcStatus.REVIEWING]: 'Reviewing',
  [RuleModelQcStatus.ONHOLD_REVIEW]: 'Onhold Review',
  [RuleModelQcStatus.ABANDON]: 'Abandon',
  [RuleModelQcStatus.PASS]: 'Passed',
  [RuleModelQcStatus.BANNED]: 'Banned',
  [RuleModelQcStatus.DELETED]: 'Deleted',
};

export const userStatusString = {
  [Status.ACCOUNT_DELETE]: 'Deleted',
  [Status.ACCOUNT_NORMAL]: 'Normal',
  [Status.ACCOUNT_BANNED]: 'Banned',
  [Status.ACCOUNT_FROZEN]: 'Frozen',
};

export const shopStatusString = {
  [Status.SHOP_DELETE]: 'Deleted',
  [Status.SHOP_NORMAL]: 'Normal',
  [Status.SHOP_BANNED]: 'Banned',
};

export const attributeQcStatusString = {
  [Status.ATTR_VALUE_DELETED]: 'Deleted',
  [Status.ATTR_VALUE_FAILED]: 'Failed',
  [Status.ATTR_VALUE_PASSED]: 'Passed',
  [Status.ATTR_VALUE_NORMAL]: 'Normal',
  [Status.ATTR_VALUE_PENDING]: 'Pending',
  [Status.ATTR_VALUE_NA]: 'N/A',
};

export const conditionString = {
  [ItemCondition.NOT_SET]: 'Not Set',
  [ItemCondition.NEW_WITH_TAGS]: 'New With Tags',
  [ItemCondition.NEW_WITHOUT_TAGS]: 'New Without Tags',
  [ItemCondition.NEW_WITH_DEFECTS]: 'New With Defects',
  [ItemCondition.USED]: 'Used',
  [ItemCondition.NEW_OTHERS]: 'New Others',
  [ItemCondition.USED_LIKE_NEW]: 'Used Like New',
  [ItemCondition.USED_GOOD]: 'Used Good',
  [ItemCondition.USED_ACCEPTABLE]: 'Used Acceptable',
  [ItemCondition.USED_WITH_DEFECTS]: 'Used With Defects',
};

export const promotionTypeString: { [key: number]: string } = {
  [PromotionType.PROMOTION_TYPE_NORMAL]: 'Normal',
  [PromotionType.PROMOTION_TYPE_PRODUCT_PROMOTION_SG]: 'Product Promotion (SG)',
  [PromotionType.PROMOTION_TYPE_PRODUCT_PROMOTION_MY]: 'Product Promotion (MY)',
  [PromotionType.PROMOTION_TYPE_PRODUCT_PROMOTION_TH]: 'Product Promotion (TH)',
  [PromotionType.PROMOTION_TYPE_PRODUCT_PROMOTION_VN]: 'Product Promotion (VN)',
  [PromotionType.PROMOTION_TYPE_PRODUCT_PROMOTION_ID]: 'Product Promotion (ID)',
  [PromotionType.PROMOTION_TYPE_PRODUCT_PROMOTION_PH]: 'Product Promotion (PH)',
  [PromotionType.PROMOTION_TYPE_PRODUCT_PROMOTION_TW]: 'Product Promotion (TW)',
  [PromotionType.PROMOTION_TYPE_PRODUCT_PROMOTION_BR]: 'Product Promotion (BR)',
  [PromotionType.PROMOTION_TYPE_PRODUCT_PROMOTION_IN]: 'Product Promotion (IN)',
  [PromotionType.PROMOTION_TYPE_PRODUCT_PROMOTION_MX]: 'Product Promotion (MX)',
  [PromotionType.PROMOTION_TYPE_PRODUCT_PROMOTION_CO]: 'Product Promotion (CO)',
  [PromotionType.PROMOTION_TYPE_PRODUCT_PROMOTION_CL]: 'Product Promotion (CL)',
  [PromotionType.PROMOTION_TYPE_PRODUCT_PROMOTION_AR]: 'Product Promotion (AR)',
  [PromotionType.PROMOTION_TYPE_PRODUCT_PROMOTION_PL]: 'Product Promotion (PL)',
  [PromotionType.PROMOTION_TYPE_PRODUCT_PROMOTION_ES]: 'Product Promotion (ES)',
  [PromotionType.PROMOTION_TYPE_PRODUCT_PROMOTION_FR]: 'Product Promotion (FR)',
  [PromotionType.PROMOTION_TYPE_PRODUCT_PROMOTION_XX]: 'Product Promotion (XX)',
  [PromotionType.PROMOTION_TYPE_SELLER_DISCOUNT]: 'Seller Discount',
  [PromotionType.PROMOTION_TYPE_FLASH_SALE]: 'Flash Sale',
  [PromotionType.PROMOTION_TYPE_WHOLESALE]: 'Wholesale',
  [PromotionType.PROMOTION_TYPE_GROUP_BUY]: 'Group Buy',
  [PromotionType.PROMOTION_TYPE_BUNDLE_DEAL]: 'Bundle Deal',
  [PromotionType.PROMOTION_TYPE_WELCOME_PACKAGE_FREE_GIFT]:
    'Welcome Package Free Gift',
  [PromotionType.PROMOTION_TYPE_WELCOME_PACKAGE_EXCLUSIVE_ITEMS]:
    'Welcome Package Exclusive Items',
  [PromotionType.PROMOTION_TYPE_SLASH_PRICE]: 'Slash Price',
  [PromotionType.PROMOTION_TYPE_ADD_ON_DEAL_MAIN]: 'Add-on Deal Main',
  [PromotionType.PROMOTION_TYPE_ADD_ON_DEAL_SUB]: 'Add-on Deal Sub',
  [PromotionType.PROMOTION_TYPE_BRAND_SALE]: 'Brand Sale',
  [PromotionType.PROMOTION_TYPE_IN_SHOP_FLASH_SALE]: 'In-shop Flash Sale',
  [PromotionType.PROMOTION_TYPE_ADD_ON_FREE_GIFT_MAIN]: 'Add-on Free Gift Main',
  [PromotionType.PROMOTION_TYPE_ADD_ON_FREE_GIFT_SUB]: 'Add-on Free Gift Sub',
  [PromotionType.RULE_TYPE_XTRA_DISC_FLASH_SALE]: 'Xtra Disc Flash Sale',
  [PromotionType.RULE_TYPE_XTRA_DISC_BRAND_SALE]: 'Xtra Disc Brand Sale',
  [PromotionType.RULE_TYPE_ADD_ON_PURCHASE_MAIN]: 'Add-on Purchase Main',
  [PromotionType.RULE_TYPE_ADD_ON_PURCHASE_SUB]: 'Add-on Purchase Sub',
  [PromotionType.RULE_TYPE_SELLING_PRICE]: 'Selling Price',
  [PromotionType.RULE_TYPE_SETTLEMENT_PRICE]: 'Settlement Price',
  [PromotionType.RULE_TYPE_EXCLUSIVE_PRICE]: 'Exclusive Price',
  [PromotionType.RULE_TYPE_OVERALL_PURCHASE_LIMIT]:
    'Promotion for Overall Purchase Limit',
  [PromotionType.RULE_TYPE_CHAT_OFFER_PRICE]: 'Chat Offer Price',
  [PromotionType.RULE_TYPE_ADVISOR_PRICE]: 'Price Advisor Promotion',
};

export const promotionStatusString = {
  [PromotionStatus.STATUS_PROMOTION_DELETED]: 'Deleted',
  [PromotionStatus.STATUS_PROMOTION_NORMAL]: 'Normal',
};

export const attributeSnapshotStatusString = {
  [Status.ATTR_VALUE_DELETED]: 'Deleted',
  [Status.ATTR_VALUE_FAILED]: 'Failed',
  [Status.ATTR_VALUE_PASSED]: 'Passed',
  [Status.ATTR_VALUE_NORMAL]: 'Normal',
  [Status.ATTR_VALUE_PENDING]: 'Pending',
  [Status.ATTR_VALUE_NA]: 'N/A',
};

export const attributeTypeString = {
  0: 'None',
  [ItemAttributeType.INT_TYPE]: 'Integer',
  [ItemAttributeType.STRING_TYPE]: '',
  [ItemAttributeType.ENUM_TYPE]: '',
  [ItemAttributeType.FLOAT_TYPE]: 'Numbers (Integer/Decimal)',
  [ItemAttributeType.DATE_TYPE]: '',
  [ItemAttributeType.TIMESTAMP_TYPE]: '',
};

export const v0videoServerPrefix = {
  SG: 'http://cv.shopee.sg/video/',
  TH: 'http://cv.shopee.co.th/video/',
  ID: 'http://cv.shopee.co.id/video/',
  MY: 'http://cv.shopee.com.my/video/',
  VN: 'http://cv.shopee.vn/video/',
  PH: 'http://cv.shopee.ph/video/',
  TW: 'http://cv.shopee.tw/video/',
  BR: 'http://cv.shopee.com.br/video/',
  IR: 'http://cv.shopee.co.ir/video/',
  HK: 'http://cv.shopee.hk/video/',
  MM: 'http://cv.shopee.mm/video/',

  XX: 'http://cv.shopee.co.id/video/',
};

export const v1v2videoServerPrefix = {
  SG: 'http://cvf.shopee.sg/file/',
  TH: 'http://cvf.shopee.co.th/file/',
  ID: 'http://cvf.shopee.co.id/file/',
  MY: 'http://cvf.shopee.com.my/file/',
  VN: 'http://cvf.shopee.vn/file/',
  PH: 'http://cvf.shopee.ph/file/',
  TW: 'http://cvf.shopee.tw/file/',
  BR: 'http://cvf.shopee.com.br/file/',
  IR: 'http://cvf.shopee.co.ir/file/',
  HK: 'http://cvf.shopee.hk/file/',
  MM: 'http://cvf.shopee.mm/file/',

  XX: 'http://cvf.shopee.co.id/file/',
};

export const itemAuditStatusString = {
  [ItemAuditStatus.ITEM_NEW]: 'Item Created',
  [ItemAuditStatus.ITEM_EDIT]: 'Item Edited',
  [ItemAuditStatus.ITEM_REPORT]: 'Item Reported',
  [ItemAuditStatus.ITEM_DEL]: 'Item Deleted',
  [ItemAuditStatus.USER_REPORT]: 5,
  [ItemAuditStatus.BANKACC]: 6,
  [ItemAuditStatus.CHECKOUT]: 7,
  [ItemAuditStatus.REFUND]: 8,
  [ItemAuditStatus.ESCROW_RELEASE]: 9,
  [ItemAuditStatus.PAYMENT_STATUS]: 10,
  [ItemAuditStatus.MANUAL]: 'Operator Action',
  [ItemAuditStatus.ITEM_STATUS]: 'Item Status Changed',
  [ItemAuditStatus.SHOP_UPDATE]: 13,
  [ItemAuditStatus.MODEL_EDIT]: 'Item Model Edited',
  [ItemAuditStatus.MODEL_ADD]: 'Item Model Added',
  [ItemAuditStatus.MODEL_DEL]: 'Item Model Deleted',
  [ItemAuditStatus.VIDEO_ADD]: 'Video Added',
  [ItemAuditStatus.VIDEO_EDIT]: 'Video Edited',
  [ItemAuditStatus.VIDEO_DELETE]: 'Video Deleted',
  [ItemAuditStatus.ACCOUNT_UPDATE]: 'Account_Update',
  [ItemAuditStatus.REFERRAL_UPDATE]: 'Referral Update',
  [ItemAuditStatus.ITEM_UNLIST]: 'Item Delisted',
  [ItemAuditStatus.SLASH_PRICE_RULE_UPDATE]: 23,
  [ItemAuditStatus.ITEM_LICENSE]: 'Item License',
  [ItemAuditStatus.SLASH_PRICE_ITEM_CREATED]: 25,
  [ItemAuditStatus.SLASH_PRICE_ITEM_UPDATED]: 26,
  [ItemAuditStatus.SLASH_PRICE_ITEM_DELETED]: 27,
  [ItemAuditStatus.BRAND_NEW]: 28,
  [ItemAuditStatus.BRAND_EDIT]: 29,
  [ItemAuditStatus.BRAND_DELETE]: 30,
  [ItemAuditStatus.JKO_SELLER_UPDATE]: 31,
  [ItemAuditStatus.JKO_BUYER_UPDATE]: 32,
  [ItemAuditStatus.ITEM_FLAG_EDIT]: 'Item Flag Edited',
};

export const qcLevelString = {
  [QcLevel.MUST_QC]: 'Must QC',
  [QcLevel.GOOD_TO_QC]: 'Good to QC',
};

export const qcActionString = {
  [QcAction.Pass]: 'Pass',
  [QcAction.Ban]: 'Ban',
  [QcAction.Delete]: 'Delete',
  [QcAction.OnHold]: 'On Hold',
  [QcAction.Censor]: 'Censor',
  [QcAction.Deboost]: 'Deboost',
  [QcAction.UNCensor]: 'UN Censor',
};

export const qcAddOnActionString = {
  [QcAddOnAction.Censor]: 'Censor',
  [QcAddOnAction.Deboost]: 'Deboosted',
};

export const queueTypeString = {
  [QueueType.CB]: 'CB Normal',
  [QueueType.CB_SIP]: 'CB SIP',
  [QueueType.QueueTypeCBMall]: ' CB Mall',
  [QueueType.MALL]: 'Mall',
  [QueueType.NORMAL]: 'Normal',
  [QueueType.LICENSE]: 'License',
  [QueueType.REPORTED]: 'Reported',
  [QueueType.KeywordSpam]: 'Keyword Spam',
  [QueueType.BrandSpam]: 'Brand Spam',
  [QueueType.Counterfeit]: 'Counterfeit',
};
export const cqcQueueTypeString = {
  [CqcQueueType.NIL]: 'Nil',
  [CqcQueueType.BASELINE]: 'Baseline',
  [CqcQueueType.COUNTERFEIT]: 'Counterfeit',
  [CqcQueueType.SELLER_ABUSE]: 'Seller Abuse',
  [CqcQueueType.INDIVIDUAL_REVIEW]: 'Individual Review',
  [CqcQueueType.MASS_EDIT]: 'MassEdit',
  [CqcQueueType.MASS_DEBOOST]: 'Deboost',
  [CqcQueueType.OTHERS]: 'Other',
};

export const itemModelStatusString = {
  [Status.MODEL_DELETE]: 'Deleted',
  [Status.MODEL_NORMAL]: 'Normal',
  [Status.MODEL_UNAVAILABLE]: 'Unavailable',
};

export const auditTypeString = {
  [PriceStockAuditType.AUDIT_TYPE_SIP_UPDATE]: 'SIP Update',
  [PriceStockAuditType.AUDIT_TYPE_DP_UPDATE]: 'DP Update',
  [PriceStockAuditType.AUDIT_TYPE_UNKNOWN]: 'Unknown',
  [PriceStockAuditType.AUDIT_TYPE_PLACE_ORDER]: 'Place Order',
  [PriceStockAuditType.AUDIT_TYPE_CANCEL_ORDER]: 'Cancel Order',
  [PriceStockAuditType.AUDIT_TYPE_UPDATE_NORMAL]: 'Update Normal',
  [PriceStockAuditType.AUDIT_TYPE_NORMAL_STOCK]: 'Normal Stock',
  [PriceStockAuditType.AUDIT_TYPE_APPROVE_PROMOTION]: 'Approve Promotion',
  [PriceStockAuditType.AUDIT_TYPE_CANCEL_PROMOTION]: 'Cancel Promotion',
  [PriceStockAuditType.AUDIT_TYPE_PROMOTION_END]: 'Promotion End',
  [PriceStockAuditType.AUDIT_TYPE_PROMOTION_UPDATE]: 'Promotion Update',
};

export const priceAuditStatusString = {
  [ItemPriceStatus.STATUS_ITEM_PRICE_DELETED]: 'Deleted',
  [ItemPriceStatus.STATUS_ITEM_PRICE_NORMAL]: 'Normal',
  [ItemPriceStatus.STATUS_ITEM_PRICE_ENDED]: 'Ended',
};

export const stockAuditStatusString = {
  [ItemStockStatus.STATUS_ITEM_STOCK_DELETED]: 'Deleted',
  [ItemStockStatus.STATUS_ITEM_STOCK_NORMAL]: 'Normal',
  [ItemStockStatus.STATUS_ITEM_STOCK_ENDED]: 'Ended',
};

export const oplDisplayTimeFormat = 'DD-MM-YYYY HH:mm:ss ZZ';

export const maxCatLevel = 5;

export const globalAttributeStatusString = {
  [AttrStatus.ATTR_DELETED]: 'Deleted',
  [AttrStatus.ATTR_NORMAL]: 'Normal',
  [AttrStatus.ATTR_DISABLE]: 'Disabled',
};

export const globalAttributeValidateTypeString = {
  [AttrInputValidatorType.VALIDATOR_NOT_REQUIRED]: 'None',
  [AttrInputValidatorType.VALIDATOR_INTEGERS]: 'Integer',
  [AttrInputValidatorType.VALIDATOR_STRING]: '',
  [AttrInputValidatorType.VALIDATOR_NUMBERS]: 'Numbers (Integer/Decimal)',
  [AttrInputValidatorType.VALIDATOR_DATE]: '',
};

export const maxNumOfValuesInMultiValueAttribute = 5;

export enum AUTH_CODE {
  ITEM_CATALOG_READ = 'item_catalog_page.read',
  ITEM_DETAIL_READ = 'item_detail_page.read',
  ITEM_DETAIL_WRITE = 'item_detail_page.write',

  ITEM_DETAIL_SET_NORMAL_WRITE = 'item_detail_page.set_normal.write',

  ITEM_SPU_MANAGEMENT_SPU_CSPU_READ = 'item.spu.management.spu_cspu.read',
  ITEM_SPU_MANAGEMENT_SPU_CSPU_WRITE = 'item.spu.management.spu_cspu.write',

  ITEM_SPU_MANAGEMENT_SI_READ = 'item.spu.management.si.read',
  ITEM_SPU_MANAGEMENT_SI_WRITE = 'item.spu.management.si.write',

  SHOP_LISTING_LIMIT_READ = 'shop_listing_limit.read',
  SHOP_LISTING_LIMIT_WRITE = 'shop_listing_limit.write',

  ITEM_TAG_READ = 'item.tag.read',
  // ITEM_TAG_WRITE = 'item.tag.write',

  ITEM_TAG_UPLOADER_READ = 'item.tag.uploader.read',
  ITEM_TAG_EDITOR_READ = 'item.tag.editor.read',

  ITEM_TAG_ADMIN_READ = 'item.tag.admin.read',
  ITEM_QC_LICENSE_READ = 'listing.qc.license.read',

  ITEM_PRICE_STOCK_ADMIN_READ = 'item.price_stock.admin.read',

  // mass
  MASS_EDIT_ITEM_ALL_READ = 'mass_edit_item_all.read',
  MASS_EDIT_ITEM_ALL_WRITE = 'mass_edit_item_all.write',

  MASS_EDIT_ITEM_GLOBAL_TREE_MAPPING_WRITE = 'mass_edit_item_global_tree_mapping.write',
  MASS_EDIT_ITEM_PRICE_WRITE = 'mass_edit_item_price.write',
  MASS_EDIT_ITEM_DESCRIPTION_WRITE = 'mass_edit_item_title_description.write',
  MASS_EDIT_ITEM_DESCRIPTION_ALL_WRITE = 'mass_edit_item_title_description_all.write',
  MASS_EDIT_ITEM_COLOR_CODE_WRITE = 'mass_edit_item_color_code.write',
  MASS_EDIT_ITEM_GTIN_WRITE = 'mass_edit_item_gtin.write',
  MASS_EDIT_DANGEROUS_GOODS_SHIPPING_MASK_WRITE = 'mass_edit_dangerous_goods_shipping_mask.write',
  MASS_EDIT_ITEM_GLOBAL_CATEGORY_BYPASS_VALIDATION_WRITE = 'mass_edit_item_global_category_bypass_validation.write',

  MASS_EDIT_ITEM_VARIATIONS_READ = 'mass_edit_item_variations.read',
  MASS_EDIT_ITEM_VARIATIONS_WRITE = 'mass_edit_item_variations.write',
}

export enum ProductTaxType {
  NoTaxType = 0,
  TaxAble = 1,
  TaxFree = 2,
}

export enum ProductGroupType {
  NoTaxType = 0,
  Book = 1,
  Fresh = 2,
}

export const PRODUCT_TAX_TYPE_MAP = {
  [ProductTaxType.NoTaxType]: 'No tax-type',
  [ProductTaxType.TaxAble]: 'Tax-able',
  [ProductTaxType.TaxFree]: 'Tax-free',
};

export enum CatAutoCorrectionStatus {
  INITIAL = 0,
  AUTO_CORRECTED = 1,
  WRONG_CATEGORY_UNDER_APPEAL = 2,
  WRONG_CATEGORY_SUCCESS = 3,
  WRONG_CATEGORY_REJECTED = 4,
  AUTO_CORRECT_CONFIRM = 5,
}

export const CAT_AUTO_CORRECTION_STATUS_LABEL = {
  [CatAutoCorrectionStatus.INITIAL]: 'Initial',
  [CatAutoCorrectionStatus.AUTO_CORRECTED]: 'Auto-corrected',
  [CatAutoCorrectionStatus.WRONG_CATEGORY_UNDER_APPEAL]: 'Wrong category under appeal',
  [CatAutoCorrectionStatus.WRONG_CATEGORY_SUCCESS]: 'Wrong category appeal success',
  [CatAutoCorrectionStatus.WRONG_CATEGORY_REJECTED]: 'Wrong category appeal rejected',
  [CatAutoCorrectionStatus.AUTO_CORRECT_CONFIRM]: 'Auto-correct confirm',
};

export const DG_CAT_OPTIONS = [
  {
    label: 'DG A',
    value: 1,
  },
  {
    label: 'DG B',
    value: 2,
  },
  {
    label: 'DG C',
    value: 3,
  },
  {
    label: 'DG D',
    value: 4,
  },
]
