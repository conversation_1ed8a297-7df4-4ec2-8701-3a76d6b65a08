import {
  KitMigrationModifyType,
  KitMigrationOperationType,
  KitMigrationTaskStatus,
} from 'src/api/optimizer/constants';

export const KitMigrationStatusText: Record<number, string> = {
  [KitMigrationTaskStatus.KIT_MIGRATION_STATUS_WAITING_ACCEPT]: 'Pending Seller ',
  [KitMigrationTaskStatus.KIT_MIGRATION_STATUS_PENDING_REVIEW]: 'Pending Ops',
  [KitMigrationTaskStatus.KIT_MIGRATION_STATUS_CONFIRMED]: 'Confirmed',
  [KitMigrationTaskStatus.KIT_MIGRATION_STATUS_ABANDONED]: 'Abandoned',
  [KitMigrationTaskStatus.KIT_MIGRATION_STATUS_FINISHED]: 'Finished',
  [KitMigrationTaskStatus.KIT_MIGRATION_STATUS_FAILED]: 'FAILED',
  [KitMigrationTaskStatus.KIT_MIGRATION_STATUS_PENDING_MIGRATION]: 'Pending Kit Migration',
};

export enum KitMigrationOptimizerSource {
  MASS_MIGRATION = 0,
  SINGLE_MIGRATION = 1,
  SELLER_MIGRATION = 2
}

export const LastOperationText: Record<number, string> = {
  [KitMigrationOperationType.KIT_MIGRATION_OPERATION_SELLER_ACCEPT]: 'Seller-accept',
  [KitMigrationOperationType.KIT_MIGRATION_OPERATION_SELLER_EDIT]: ' Seller-save',
  [KitMigrationOperationType.KIT_MIGRATION_OPERATION_SELLER_CREATE]: 'Seller-create',
  [KitMigrationOperationType.KIT_MIGRATION_OPERATION_SELLER_REJECT]: 'Seller-reject',
  [KitMigrationOperationType.KIT_MIGRATION_OPERATION_SELLER_CONFIRM]: 'Seller-confirm',
  [KitMigrationOperationType.KIT_MIGRATION_OPERATION_SELLER_ABANDON]: 'Seller-abandon',
  [KitMigrationOperationType.KIT_MIGRATION_OPERATION_OPS_EDIT]: 'Ops-edit',
  [KitMigrationOperationType.KIT_MIGRATION_OPERATION_OPS_CREATE]: 'Ops-create',
  [KitMigrationOperationType.KIT_MIGRATION_OPERATION_OPS_APPROVE]: 'Ops-approve',
  [KitMigrationOperationType.KIT_MIGRATION_OPERATION_SYSTEM_AUTO_CREATE]: 'Auto-create',
  [KitMigrationOperationType.KIT_MIGRATION_OPERATION_SYSTEM_AUTO_ACCEPT]: 'Auto-accept',
  [KitMigrationOperationType.KIT_MIGRATION_OPERATION_SYSTEM_RETRY]: 'System-retry',
  [KitMigrationOperationType.KIT_MIGRATION_OPERATION_OPS_ABANDON]: 'Ops-abandon',
  [KitMigrationOperationType.KIT_MIGRATION_OPERATION_SYSTEM_EXECUTE]: 'System-execute',
};
export const KitMigrationModifyTypeText: Record<number, string> = {
  [KitMigrationModifyType.KIT_MIGRATION_SELLER_HAS_NOT_MODIFIED_TASK_YET]: 'Not Applicable',
  [KitMigrationModifyType.KIT_MIGRATION_SELLER_MODIFIED_TASK_AFTER_REVIEW]: 'Yes',
  [KitMigrationModifyType.KIT_MIGRATION_SELLER_DID_NOT_MODIFY_TASK_AFTER_REVIEW]: 'No',
};
export const CanApproveKitMigrationStatus = [
  KitMigrationTaskStatus.KIT_MIGRATION_STATUS_PENDING_REVIEW,
];
export const CanEditKitMigrationStatus = [KitMigrationTaskStatus.KIT_MIGRATION_STATUS_WAITING_ACCEPT, KitMigrationTaskStatus.KIT_MIGRATION_STATUS_PENDING_REVIEW];
export const CanAbandonMigrationStatus = [
  KitMigrationTaskStatus.KIT_MIGRATION_STATUS_WAITING_ACCEPT, KitMigrationTaskStatus.KIT_MIGRATION_STATUS_PENDING_REVIEW, KitMigrationTaskStatus.KIT_MIGRATION_STATUS_PENDING_MIGRATION,
];

export enum KitMigrationDetailType {
  EDIT = 'edit',
  ADD = 'add',
  VIEW = 'view',
}
