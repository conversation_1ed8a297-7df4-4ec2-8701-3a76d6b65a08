import { getCountry } from '@classification/admin-solution';
import type { IEnv } from '@classification/file-upload';
import { MMSFIleManagement } from '@classification/file-upload';

import { getEnvironment } from 'src/_shared/utils';

export const mmsManagement = new MMSFIleManagement({
  env: getEnvironment() as IEnv,
  region: getCountry(),
});

export const downloadFile = async (
  downloadLink: string,
  filename: string,
  type: string,
) => {
  const res = await fetch(downloadLink);
  if (!res.ok) {
    throw new Error(`${ res.status } ${ res.statusText }`);
  }
  const blobData = await res.blob();
  const blob = new Blob([blobData], { type });

  if (
    (
      navigator as unknown as {
        msSaveBlob: (blob: Blob, filename: string) => void;
      }
    ).msSaveBlob
  ) {
    // IE11 and Edge
    (
      navigator as unknown as {
        msSaveBlob: (blob: Blob, filename: string) => void;
      }
    ).msSaveBlob(blob, filename);
    return;
  }

  const lnk = document.createElement('a');
  const url = window.URL;
  const objectURL = url.createObjectURL(blob);

  lnk.type = type;
  lnk.download = filename;
  lnk.href = objectURL;
  lnk.dispatchEvent(new MouseEvent('click'));
  setTimeout(url.revokeObjectURL.bind(url, objectURL));
};
