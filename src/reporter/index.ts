import { getEnvironment } from 'admin-upload-common';
import { <PERSON>dapReporter } from 'listing-reporter';

export const mainReporter = new MdapReporter();
mainReporter.setSDKconfig({
  app_name: 'listing-admin-product',
  secret_key: {
    live: '5c8797777f017317f845c61162e56b68c9f3f17c2461f8a5d9742cf423ba0d89',
    test: '8300ab77604c6cbc411308f5b229f53c3c97886a1dcff8dcc5417307ab949562',
  },
  environment: getEnvironment() === 'live' ? 'live' : 'test',
  sample: 1,
  region: 'sg',
  customData: {
    environment: getEnvironment(),
  },
});
mainReporter.configApi({
  path: [
    /\/get_latest_updated_items/,
    /\/get_item_info_audit/,
    /\/get_rule_qc_logs/,
    /\/get_item_detail/,
    /\/api\/v1\/get_item_model_stock_list/,
    /\/api\/v1\/get_entity_attr_val/,
    /\/get_item_model_list/,
    /\/get_qc_tags/,
    /\/api\/v1\/get_item_model_stock_list/,
    /\/api\/v1\/get_item_model_list/,
    /\/api\/v1\/get_complaint_address/,
    /\/api\/v1\/get_licenses_for_item/,
    /\/get_product_attributes/,
    /\/get_global_product_attributes/,
    /\/wsa\/marketplace\/listing\/upload\/upload_admin\/get_tasks/,
  ],
});
mainReporter.configResource({
  path: [/\/product--(.*)?\.css$/, /\/product--(.*)?\.js$/],
});
mainReporter.configException({
  path: [/\/product\/(.)*/],
  includeJsPath: [/\/product--(.*)?\.js$/],
  ignoreError: ['ResizeObserver loop limit exceeded']
});
mainReporter.initMdap();

export const POINT_ROUTER_ENTER = 'POINT_ROUTER_ENTER';
export const POINT_JS_LOADED = 'POINT_JS_LOADED';
export const POINT_FIRST_RENDER = 'POINT_FIRST_RENDER';
export const POINT_FINISH_RENDER = 'POINT_FINISH_RENDER';

export const ProductListReporter = mainReporter.createCustomTask(
  'product-list',
);
export const ProductDetailReporter = mainReporter.createCustomTask(
  'product-detail',
);
export const MassEditItemReporter = mainReporter.createCustomTask(
  'mass-edit-item',
);
export const MassUpdateVariationReporter = mainReporter.createCustomTask(
  'mass-update-variation',
);
const customReporters = [
  {
    reporter: ProductListReporter,
    configs: [
      {
        reportId: '4e19734afd557ff89d1eaa737b743a92',
        startPoint: POINT_ROUTER_ENTER,
        endPoint: POINT_JS_LOADED,
      },
      {
        reportId: '3ef2e129fa0ada57690674bf404a5ee5',
        startPoint: POINT_ROUTER_ENTER,
        endPoint: POINT_FIRST_RENDER,
      },
      {
        reportId: '894c22cf7b9ca33adf3ca6fdef76d3d6',
        startPoint: POINT_ROUTER_ENTER,
        endPoint: POINT_FINISH_RENDER,
      },
    ],
  },
  {
    reporter: ProductDetailReporter,
    configs: [
      {
        reportId: 'd140390f8315759f88c2d2956391301c',
        startPoint: POINT_ROUTER_ENTER,
        endPoint: POINT_JS_LOADED,
      },
      {
        reportId: 'd5b4b31abc3f6d95a1900dbecc366e04',
        startPoint: POINT_ROUTER_ENTER,
        endPoint: POINT_FIRST_RENDER,
      },
      {
        reportId: '2810153d3cbaef7f02ed7c5c8f0bef2e',
        startPoint: POINT_ROUTER_ENTER,
        endPoint: POINT_FINISH_RENDER,
      },
    ],
  },
  {
    reporter: MassEditItemReporter,
    configs: [
      {
        reportId: 'baa60b6e8196357efe11a56465e4e2ae',
        startPoint: POINT_ROUTER_ENTER,
        endPoint: POINT_JS_LOADED,
      },
      {
        reportId: '6f17a179fca2e415221ed1f1db109d7c',
        startPoint: POINT_ROUTER_ENTER,
        endPoint: POINT_FIRST_RENDER,
      },
      {
        reportId: '65581d74370b108a42c442acfcf8f990',
        startPoint: POINT_ROUTER_ENTER,
        endPoint: POINT_FINISH_RENDER,
      },
    ],
  },
  {
    reporter: MassUpdateVariationReporter,
    configs: [
      {
        reportId: 'bf1c9ef580f64ce5d5de23e5be298984',
        startPoint: POINT_ROUTER_ENTER,
        endPoint: POINT_JS_LOADED,
      },
      {
        reportId: '255a7f1a096d50e0a748c29932453012',
        startPoint: POINT_ROUTER_ENTER,
        endPoint: POINT_FIRST_RENDER,
      },
      {
        reportId: 'b4927a01ec5080af0542d1736a4b1c60',
        startPoint: POINT_ROUTER_ENTER,
        endPoint: POINT_FINISH_RENDER,
      },
    ],
  },
];

customReporters.forEach((c) => {
  c.reporter.configPerfCustom(c.configs);
});
