import type { ICreateAppParams} from '@classification/admin-solution';
import { createApp } from '@classification/admin-solution';
import React from 'react';

import { AUTH_CODE } from './constants';

import './reporter';
import '@classification/admin-solution/dist/style/index.css';

const siteConfig: ICreateAppParams = {
  // index.html中的dom节点ID，推荐设置为子应用名称
  root: '#listing-admin-product',
  //应用标识
  appName: 'listing-admin-product',
  // 路由前缀
  routePrefix: '/product',
  beforeMount: function () {
    // 子应用被加载前的回调，返回false则不加载。但子应用首次被加载时，该回调不会被执行
  },
  mounted: function () {
    // 子应用被加载
  },
  unMounted: function () {
    // 子应用被卸载（切换到其他子应用了）
  },
  onRouteChange: function () {},
  menu: [
    {
      title: 'Products',
      children: [
        {
          title: 'Kit Migration',
          route: '/kit-migration/list',
          countrySet: ['BR'],
          component: React.lazy(() => import('./pages/KitMigrationList')),
          children: [{
            route: '/kit-migration/detail',
            component: React.lazy(() => import('./pages/AddEditKitMigration')),
          }]
        },
        {
          title: 'Disclaimers Management',
          route: '/disclaimers-management',
          component: React.lazy(() => import('./pages/DisclaimerManagement')),
          children: [
            {
              route: '/disclaimers-management/log',
              component: React.lazy(() => import('./pages/DisclaimerLog')),
            },
            {
              route: '/disclaimers-management/detail',
              component: React.lazy(() => import('./pages/DisclaimerEditOrAdd')),
            }
          ]
        },
        {
          title: 'Listing',
          route: '/listing',
          component: React.lazy(() => import('./pages/CatalogPage')),
          children:[{
            component: React.lazy(() => import('./pages/DetailPage')),
            route: '/listing/detail',
            permissions:[AUTH_CODE.ITEM_DETAIL_READ]
          }, {
            component: React.lazy(() => import('./pages/CodingMonkey')),
            route: '/codingmonkey',
          }]
        },
        {
          title: 'Mass Edit Item',
          component: React.lazy(() => import('./pages/MassUploadPage')),
          route: '/upload',
        },
        {
          title: 'Mass Update Variation',
          component: React.lazy(() => import('./pages/MassUploadVariation')),
          route: '/variation',
        },
        {
          title: 'Listing Limit Setting',
          component: React.lazy(() => import('./pages/ListingLimitSetting')),
          route: '/limit-setting',
        },
        {
          title: 'Mass Delist Item',
          component: React.lazy(() => import('./pages/MassDelistPage')),
          route: '/delist',
        },
        {
          title: 'Anp Reports',
          component: React.lazy(() => import('./pages/AnpReports')),
          route: '/anp-reports',
        },
        {
          title: 'Anatel Reports',
          component: React.lazy(() => import('./pages/AnatelReports')),
          route: '/anatel-reports',
        },
        {
          title: 'GTIN Reports',
          component: React.lazy(() => import('./pages/GtinReports')),
          route: '/gtin-reports',
        },
        {
          title: 'Gov License',
          component: React.lazy(() => import('./pages/GovLicense')),
          route: '/gov-license',
        }
      ],
    },
  ],
};
createApp(siteConfig);
