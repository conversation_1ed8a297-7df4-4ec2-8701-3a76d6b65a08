import type { UploadFile } from 'antd/es/upload/interface';

export enum RuleModelQcStatus {
  PRE_REVIEW,
  NEW,
  PENDING,
  REVIEWING,
  ONHOLD_REVIEW,
  ABANDON,
  PASS,
  BANNED,
  DELETED,
}
export interface ImageListsType {
  productImages: UploadFile[];
  productVideos: UploadFile[];
  variationImages: UploadFile[];
  whiteBackgroundImages: UploadFile[];
  sizeChart: UploadFile[];
  productLongImages: UploadFile[];
}

export interface ImageResponse {
  data?: {
    imageId: string;
  };
  userMessage: string;
  message: string;
  code: number;
}

export enum QcMode {
  SHOPEE_MALL = 1,
  CB = 2,
  PRODUCT_LICENSE = 3,
  SIP = 4,
}

export enum QcLevel {
  MUST_QC = 1,
  GOOD_TO_QC = 2,
}

export enum QcAction {
  Pass = 1,
  Ban = 2,
  Delete = 3,
  OnHold = 4,
  Censor = 5,
  Deboost = 8,
  UNCensor = 11
}
export enum QcAddOnAction {
  Censor = 5,
  Deboost = 8,
}
export enum QueueType {
  CB = 1,
  CB_SIP = 2,
  MALL = 3,
  NORMAL = 4,
  LICENSE = 5,
  REPORTED = 6,
  KeywordSpam = 7,
  BrandSpam = 8,
  Counterfeit = 9,
  QueueTypeCBMall = 10,
}

export enum CqcQueueType {
  NIL = 0,
  BASELINE = 1,
  COUNTERFEIT = 2,
  SELLER_ABUSE = 3,
  INDIVIDUAL_REVIEW = 1001,
  MASS_EDIT = 1002,
  MASS_DEBOOST = 1003,
  OTHERS = 9999,
}
