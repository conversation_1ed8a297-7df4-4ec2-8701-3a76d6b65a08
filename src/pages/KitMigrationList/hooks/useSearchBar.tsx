import type { ISearchProps } from '@classification/table/types/hooks/useSearchbarHooks';
import {DatePicker} from 'antd'
import moment from 'moment';
import React, {
  useCallback,
  useEffect,
  useMemo,
} from 'react';

import {
  KitMigrationModifyType,
  KitMigrationOperationType,
  KitMigrationTaskStatus,
} from 'src/api/optimizer/constants';
import {
  KitMigrationModifyTypeText,
  KitMigrationOptimizerSource,
  KitMigrationStatusText,
  LastOperationText,
} from 'src/constants/kit-migration';
import style from './styles.module.scss'

const { RangePicker } = DatePicker;

interface ICreateTime {
  startCtime?: number;
  endCtime?: number;
}

const DateRangePicker = ({value, onChange}: {value?: ICreateTime, onChange?: (value?: ICreateTime) => void}) => {

  const [innerValue, setInnerValue] = React.useState<[moment.Moment, moment.Moment] | undefined>(undefined);

  const onValueChange = useCallback((value: [moment.Moment, moment.Moment]) => {
    if (!value) {
      onChange?.({});
      return;
    }
    onChange?.({
      startCtime: Math.floor(value?.[0]?.startOf('day')?.valueOf() / 1000),
      endCtime: Math.floor(value?.[1]?.endOf('day')?.valueOf() / 1000),
    });
  }, []);
  useEffect(() => {
    setInnerValue((value?.startCtime && value.endCtime) ? [moment(value?.startCtime * 1000), moment(value?.endCtime * 1000)] : undefined);
  }, [value])
  return <RangePicker value={innerValue} onChange={onValueChange} format='DD/MM/YYYY'></RangePicker>
}

const useSearchBar = (): ISearchProps => {
  const searchPrpos: ISearchProps = useMemo(() => {
    return {
      resetBtn: {
        children: 'Reset',
      },
      searchBtn: {
        type: 'primary',
        children: 'Search',
      },
      form: {
        className: style.form,
        items: [
          {
            inputType: 'InputNumber',
            name: 'itemId',
            label: 'Product ID',
            inputCompProps: {
              placeholder: 'Enter Product ID',
            },
          },
          {
            inputType: 'Input',
            name: 'operator',
            label: 'Operator',
            inputCompProps: {
              placeholder: 'Enter Operator',
            },
          },
          {
            inputType: 'Select',
            name: 'taskStatusList',
            label: 'Status',
            inputCompProps: {
              allowClear: true,
              mode: 'multiple',
              placeholder: 'Select Status',
              options: [
                KitMigrationTaskStatus.KIT_MIGRATION_STATUS_WAITING_ACCEPT,
                KitMigrationTaskStatus.KIT_MIGRATION_STATUS_PENDING_REVIEW,
                KitMigrationTaskStatus.KIT_MIGRATION_STATUS_PENDING_MIGRATION,
              ].map((item) => ({
                label: KitMigrationStatusText[item],
                value: item,
              })),
            },
          },
          {
            inputType: 'Select',
            name: 'optimizerSource',
            label: 'Source',
            inputCompProps: {
              allowClear: true,
              placeholder: 'Select Source',
              options: [{
                label: 'Seller',
                value: KitMigrationOptimizerSource.SELLER_MIGRATION,
              }, {
                label: 'Ops',
                value: KitMigrationOptimizerSource.SINGLE_MIGRATION
              }],
            },
          },
          {
            inputType: 'Select',
            name: 'lastOperation',
            label: 'Last Operation',
            inputCompProps: {
              placeholder: 'Select Last Operation',
              allowClear: true,
              options: [
                KitMigrationOperationType.KIT_MIGRATION_OPERATION_SYSTEM_AUTO_CREATE,
                KitMigrationOperationType.KIT_MIGRATION_OPERATION_OPS_CREATE,
                KitMigrationOperationType.KIT_MIGRATION_OPERATION_SELLER_CREATE,
                KitMigrationOperationType.KIT_MIGRATION_OPERATION_SELLER_EDIT,
                KitMigrationOperationType.KIT_MIGRATION_OPERATION_SELLER_REJECT,
                KitMigrationOperationType.KIT_MIGRATION_OPERATION_OPS_EDIT,
                KitMigrationOperationType.KIT_MIGRATION_OPERATION_OPS_APPROVE,
              ].map((item) => ({
                label: LastOperationText[item],
                value: item,
              })),
            },
          },
          {
            inputType: 'Select',
            name: 'modifyType',
            label: 'Kit Component Change',
            inputCompProps: {
              allowClear: true,
              options: [
                KitMigrationModifyType.KIT_MIGRATION_SELLER_HAS_NOT_MODIFIED_TASK_YET,
                KitMigrationModifyType.KIT_MIGRATION_SELLER_MODIFIED_TASK_AFTER_REVIEW,
                KitMigrationModifyType.KIT_MIGRATION_SELLER_DID_NOT_MODIFY_TASK_AFTER_REVIEW,
              ].map((item) => ({
                label: KitMigrationModifyTypeText[item],
                value: item,
              })),
            },
          },
          {
            component: <DateRangePicker />,
            inputType: 'DatePicker',
            name: 'createTime',
            label: 'Date Created',
          },
        ],
      },
    }
  }, []);
  return searchPrpos;
}
export default useSearchBar;
