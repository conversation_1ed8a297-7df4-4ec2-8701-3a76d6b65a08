import {
  CheckCircleOutlined,
  CheckOutlined,
  CloseCircleOutlined,
  EditOutlined,
  EyeOutlined,
  PlusCircleOutlined,
} from '@ant-design/icons';
import {
  navigate,
  useUserInfo,
} from '@classification/admin-solution';
import { useTableHooks } from '@classification/table';
import { withErrorBoundary } from 'admin-upload-common';
import { useMount } from 'ahooks';
import {
  Button,
  message,
  Modal,
} from 'antd';
import type { TableProps } from 'antd/lib/table';
import moment from 'moment/moment';
import * as React from 'react';
import {
  useCallback,
  useRef,
  useState,
} from 'react';

import {
  batchUpsertKitMigrationTaskByOps,
  searchKitMigrationTaskList,
} from 'src/api/optimizer';
import {
  KitMigrationOperationType,
  KitMigrationTaskStatus,
} from 'src/api/optimizer/constants';
import type {
  optimizer
} from 'src/api/optimizer/optimizer';
import type { IVirtualSKUModelInfo } from 'src/api/uploadBffAdmin/types';
import KitComponentTable from 'src/components/KitComponentTable';
import ProductInfo from 'src/components/ProductInfo';
import { Routes } from 'src/config/routes';
import {
  CanAbandonMigrationStatus,
  CanApproveKitMigrationStatus,
  CanEditKitMigrationStatus,
  KitMigrationDetailType,
  KitMigrationStatusText,
  LastOperationText,
} from 'src/constants/kit-migration';
import { mainReporter } from 'src/reporter';
import CategoryDataControl from 'src/utils/category-datacontrol';
import useSearchBar from './hooks/useSearchBar';
import styles from './styles.module.scss';

import '@classification/table/dist/style/index.css';

const canApprove = (task: optimizer.IKitMigrationTask) => {
  return CanApproveKitMigrationStatus.includes(task.taskStatus!) && task.lastOperation !== KitMigrationOperationType.KIT_MIGRATION_OPERATION_SELLER_REJECT;
};

const KitMigrationList = () => {
  const dataControlRef = useRef(new CategoryDataControl());

  const searchBar = useSearchBar();

  const [selected, setSelected] = useState<optimizer.IKitMigrationTask[]>([]);
  const [saving, setSaving] = useState<boolean>(false);
  const { user } = useUserInfo() || {};

  const [loadingCategory, setLoadingCategory] = useState(false);

  const usePreData = useRef(false);
  const preData = useRef<{
    data: optimizer.IKitMigrationTask[];
    cursor: string;
  }>({
    data: [],
    cursor: '',
  });
  const rowSelection: TableProps<optimizer.IKitMigrationTask>['rowSelection'] = {
    type: 'checkbox',
    selectedRowKeys: selected.map(item => item.taskId!),
    onChange: (selectedRowKeys, selectedRows) => {
      setSelected(selectedRows);
    },
    getCheckboxProps: record => {
      return {
        disabled: !canApprove(record),
      }
    },
  };

  const getData = useCallback(
    async (params: {
      createTime: {
        startCtime: number;
        endCtime: number;
      };
      optimizerSource: number;
    }) => {
      try {
        if (usePreData.current) {
          return preData.current;
        }
        const { createTime, ...rest } = params;
        const { pageInfo, tasks } = await searchKitMigrationTaskList({
          ...createTime,
          ...rest,
        });
        const data = {
          cursor: pageInfo?.hasNext ? pageInfo?.cursor! : '',
          data: tasks || [],
        };
        preData.current = data;
        return data;
      } catch {
        return preData.current = {
          data: [],
          cursor: '',
        };
      } finally {
        usePreData.current = false;
      }
    },
    [],
  );

  const gotoDetailPage = useCallback((type: KitMigrationDetailType, params?: {
    taskId?: string,
    shopId?: number,
    productId?: number,
  }) => {
    navigate({
      to: Routes.KIT_MIGRATION_DETAIL,
      params: {
        type,
        ...params
      }
    });
  }, []);
  const { table, refresh } = useTableHooks({
    titleBar: {
      title: (
        <div className={ styles.titleBar }>
          <Button
            onClick={ () => batchUpdateStatus(selected) }
            disabled={ !selected?.length || saving }
            icon={ <CheckCircleOutlined/> }
          >
            Approve All
          </Button>
          <Button
            icon={ <PlusCircleOutlined/> }
            type={ 'primary' }
            onClick={ () => gotoDetailPage(KitMigrationDetailType.ADD) }
          >
            Add New Kit Migration
          </Button>
        </div>
      ),
    },
    searchProps: searchBar,
    table: {
      rowKey: 'taskId',
      rowSelection,
      dataFilterConfig: {
        nextOffsetFieldName: 'cursor',
        offsetFieldName: 'cursor',
        cursorOffset: true,
      },
      getData,
      pagination: {
        pageSize: 50,
        showSizeChanger: false,
        showQuickJumper: false,
      },
      columns: [
        {
          title: 'Inform Kit',
          dataIndex: 'kitMigration',
          render: (kit: optimizer.IKitMigration) => {
            const categoryMap = dataControlRef.current.getData().categoryMap;
            const category = loadingCategory ? '' : kit?.catIds?.map((id: number) => categoryMap.get(id)?.name).filter(item => !!item).join(' > ');
            const {stdTierVariationList} = kit || {};
            const [oneTier, twoTier] = stdTierVariationList || [];

            return (
              <ProductInfo
                product={ {
                  name: kit.name,
                  image: kit.coverImage,
                  category,
                  variations: kit.isDefault ? [] : oneTier?.valueList?.reduce((pre: {name?: string; image?: string}[], cur) => {
                    // two-tier-variation
                    if (twoTier?.valueList?.length) {
                      twoTier?.valueList?.forEach(item => {
                        pre.push({
                          name: `${cur.customValue},${item.customValue}`,
                          image: cur.imageId,
                        });
                      });
                    } else { // one-tier-variation
                      pre.push({
                        name: cur.customValue,
                        image: cur.imageId,
                      });
                    }
                    return pre;

                  }, []),
                }}
              ></ProductInfo>
            );
          },
        },
        {
          title: 'New Kit',
          dataIndex: 'kitMigration',
          render: (kit: optimizer.IKitMigration) => {
            const kitComponents = kit.kitComponents?.map(item => {
              return {
                id: item.kitId,
                imageId: item.image,
                name: kit.isDefault ? kit.name : item.kitName,
                price: item.price,
                sku: item.sku,
                componentSkuList: item.kitComponentSkuList?.map(component => {
                  return {
                    name: component.itemName,
                    modelName: component.modelName,
                    modelSku: component.sku,
                    imageId: component.image,
                    mpskuItemId: component.mpskuItemId,
                    mpskuModelId: component.mpskuModelId,
                    quantity: component.quantity,
                    mainSku: component.mainSku,
                    inputPrice: component.costPrice,
                  }
                })
              } as IVirtualSKUModelInfo;
            });
            return (
              <ProductInfo
                product={ {
                  name: kit.name,
                  image: kit.coverImage,
                }}
              >
                <Button
                  type="link"
                  className={ styles.kitComponentButton }
                  onClick={ () => {
                    Modal.info({
                      className: styles.modal,
                      closable: true,
                      maskClosable: true,
                      width: 1000,
                      content: <KitComponentTable kitComponents={kitComponents} type={ 'view' }/>,
                    });
                  } }
                >
                  Kit Components
                </Button>
              </ProductInfo>
            );
          },
        },
        {
          title: 'Date Created',
          dataIndex: 'ctime',
          render: (time: number) => moment(time * 1000).format('YYYY-MM-DD HH:mm:ss'),
        },
        {
          title: 'Date Updated',
          dataIndex: 'mtime',
          render: (time: number) => moment(time * 1000).format('YYYY-MM-DD HH:mm:ss'),
        },
        {
          title: 'Operator',
          dataIndex: 'operator',
        },
        {
          title: 'Status',
          dataIndex: 'taskStatus',
          render: (status: KitMigrationTaskStatus) => KitMigrationStatusText[status],
        },
        {
          title: 'Last Operation',
          dataIndex: 'lastOperation',
          render: (lastOperation: KitMigrationOperationType) => LastOperationText[lastOperation] || '',
        },
        {
          title: 'Action',
          width: 200,
          dataIndex: 'taskStatus',
          render: (status: KitMigrationTaskStatus, record: optimizer.IKitMigrationTask) => {
            return (
              <>
                <Button
                  type={ 'link' }
                  icon={ <EyeOutlined/> }
                  onClick={ () => gotoDetailPage(KitMigrationDetailType.VIEW, {
                    taskId: record.taskId,
                    shopId: record.shopId,
                    productId: record.kitMigration?.itemId,
                  }) }
                />
                <Button
                  disabled={ !canApprove(record) || saving }
                  type={ 'link' }
                  onClick={ () => batchUpdateStatus([record])}
                  icon={ <CheckOutlined/> }
                />
                <Button
                  disabled={ !CanAbandonMigrationStatus.includes(status) || saving }
                  type={ 'link' }
                  onClick={ () => batchUpdateStatus([record], KitMigrationOperationType.KIT_MIGRATION_OPERATION_OPS_ABANDON) }
                  icon={ <CloseCircleOutlined/> }
                />
                <Button
                  disabled={!CanEditKitMigrationStatus.includes(status)}
                  type={'link'}
                  icon={<EditOutlined/> }
                  onClick={ () => gotoDetailPage(KitMigrationDetailType.EDIT, {
                    taskId: record.taskId,
                    shopId: record.shopId,
                    productId: record.kitMigration?.itemId,
                  })}
                />
              </>
            );
          },
        },
      ],
    },
  });
  const batchUpdateStatus = async (records: optimizer.IKitMigrationTask[], type = KitMigrationOperationType.KIT_MIGRATION_OPERATION_OPS_APPROVE) => {
    try {
      setSaving(true);
      const { failedTasks, error } = await batchUpsertKitMigrationTaskByOps({
        taskShopItemIds: records.map(record => ({
          itemId: record.itemId,
          shopId: record.shopId,
          taskId: record.taskId,
        })),
        operator: user?.email,
        operationType: type,
      });
      if (error) {
        return;
      }
      const failedTasksMap = failedTasks?.reduce((pre: Record<string, optimizer.IFailedUpsertKitMigration>, task) => {
        pre[task.taskId!] = task;
        return pre;
      }, {}) || {};
      preData.current.data = preData.current.data.map(item => {
        if (records.find(record => record.taskId === item.taskId)) {
          return {
            ...item,
            taskStatus: failedTasksMap[item.taskId!] ? item.taskStatus : type === KitMigrationOperationType.KIT_MIGRATION_OPERATION_OPS_APPROVE ? KitMigrationTaskStatus.KIT_MIGRATION_STATUS_PENDING_MIGRATION : KitMigrationTaskStatus.KIT_MIGRATION_STATUS_FINISHED,
          }
        }
        return item;
      });
      setSelected([]);

      failedTasks?.length ?
        message.error(`These Tasks updates failed: ${failedTasks?.map(task => task.taskId).join(', ')  }`) :
        message.success('successfully updated');
      usePreData.current = true;
      refresh();
    } finally {
      setSaving(false);
    }
  };
  useMount(async () => {
    setLoadingCategory(true);
    await dataControlRef.current.loadData();
    setLoadingCategory(false);
  })

  return (
    <div className={ styles.container }>
      <div className={ styles.title }>Kit Migration</div>
      { table }
    </div>
  );
};

export default withErrorBoundary(KitMigrationList, {
  onComponentDidCatch: (error: Error, _errorInfo: React.ErrorInfo) => {
    mainReporter.errorReport(error);
  },
});
