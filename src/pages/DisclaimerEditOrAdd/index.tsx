import {
  CloseOutlined,
  DeleteOutlined,
  DownOutlined,
  PlusOutlined,
  RightOutlined,
  SettingOutlined,
} from '@ant-design/icons';
import {
  Button,
  Card,
  Col,
  Input,
  message,
  Popconfirm,
  Row,
  Select,
  Space,
  Switch,
  Typography,
} from 'antd';
import React, {
  useEffect,
  useState,
} from 'react';
import {
  useNavigate,
  useSearchParams,
} from 'react-router-dom';

import {
  AttrInputType,
  AttrInputValidatorType,
} from 'src/api/uploadAdmin/constants';
import UploadFile from 'src/components/Upload';
import style from './style.module.scss';

const { TextArea } = Input;
const { Title, Text } = Typography;

// 根据属性输入类型和输入验证器类型确定操作符
const getOperators = (inputType: AttrInputType, validatorType: AttrInputValidatorType): string[] => {
  // 基础操作符（根据属性输入类型）
  const baseOperators = {
    [AttrInputType.SINGLE_DROP_DOWN]: ['=', '!='],
    [AttrInputType.SINGLE_COMBO_BOX]: ['=', '!=', '>', '<', '>=', '<='],
    [AttrInputType.FREE_TEXT_FILED]: ['=', '!=', '>', '<', '>=', '<='],
    [AttrInputType.MULTI_DROP_DOWN]: ['=', '!='],
    [AttrInputType.MULTI_COMBO_BOX]: ['=', '!=', '>', '<', '>=', '<='],
  };

  // 验证器类型限制（某些操作符不支持特定类型）
  const validatorRestrictions: Record<AttrInputValidatorType, { exclude: string[] }> = {
    [AttrInputValidatorType.VALIDATOR_STRING]: {
      exclude: ['>', '<', '>=', '<='], // String不支持数值比较操作符
    },
    [AttrInputValidatorType.VALIDATOR_INTEGERS]: {
      exclude: [], // Integer支持所有操作符
    },
    [AttrInputValidatorType.VALIDATOR_DATE]: {
      exclude: [], // Date支持所有操作符
    },
    [AttrInputValidatorType.VALIDATOR_NUMBERS]: {
      exclude: [], // Number支持所有操作符
    },
    [AttrInputValidatorType.VALIDATOR_NOT_REQUIRED]: {
      exclude: [], // Number支持所有操作符
    },
  };

  // 获取基础操作符
  const operators = baseOperators[inputType] || [];

  // 应用验证器类型限制
  const restrictions = validatorRestrictions[validatorType];

  if (restrictions?.exclude) {
    return operators.filter(op => !restrictions.exclude.includes(op));
  }
  return operators;
};

// 类型定义
interface AttributeRule {
  id: string;
  attribute: string;
  attributeInputType: AttrInputType;
  inputValidatorType: AttrInputValidatorType;
  operator: string;
  value: string;
  valueType: 'id' | 'custom' | 'input';
}

interface AdvancedRule {
  id: string;
  categoryId: number;
  attributeRules: AttributeRule[];
}

interface DisclaimerFormData {
  id?: string;
  name: string;
  description: string;
  status: boolean;
  categories: string[];
  advancedRules: AdvancedRule[];
  legalDocument?: {
    filename: string;
    hash: string;
  };
}

// 可复用的表单字段组件
interface FormFieldProps {
  label: string;
  required?: boolean;
  children: React.ReactNode;
}

const FormField: React.FC<FormFieldProps> = ({ label, required = false, children }) => (
  <div className={ style.formField }>
    <Text strong>
      { label } { required && '*' }
    </Text>
    <div style={ { marginTop: 8 } }>
      { children }
    </div>
  </div>
);

// 可复用的规则卡片组件
interface RuleCardProps {
  rule: AdvancedRule;
  index: number;
  allRules: AdvancedRule[]; // 添加所有规则的引用
  availableCategories: Category[];
  categoryAttributes: Map<number, Attribute[]>;
  categoriesLoading: boolean;
  onUpdate: (ruleId: string, updates: Partial<AdvancedRule>) => void;
  onDelete: (ruleId: string) => void;
  onAddAttributeRule: (ruleId: string) => void;
  onUpdateAttributeRule: (ruleId: string, attributeRuleId: string, updates: Partial<AttributeRule>) => void;
  onDeleteAttributeRule: (ruleId: string, attributeRuleId: string) => void;
  onCategoryChange: (categoryId: number) => void;
}

const RuleCard: React.FC<RuleCardProps> = ({
  rule,
  index,
  allRules,
  availableCategories,
  categoryAttributes,
  categoriesLoading,
  onUpdate,
  onDelete,
  onAddAttributeRule,
  onUpdateAttributeRule,
  onDeleteAttributeRule,
  onCategoryChange,
}) => {
  // 辅助函数：检查分类是否被使用
  const isCategoryUsed = (category: number): boolean => {
    return allRules.some(otherRule =>
      otherRule.id !== rule.id && otherRule.categoryId === category,
    );
  };

  // 辅助函数：检查属性是否被使用
  const isAttributeUsed = (attribute: string, excludeAttributeRuleId?: string): boolean => {
    return rule.attributeRules.some(ar =>
      ar.id !== excludeAttributeRuleId && ar.attribute === attribute,
    );
  };

  // 辅助函数：检查属性值是否被使用
  const isAttributeValueUsed = (attribute: string, value: string, excludeAttributeRuleId?: string): boolean => {
    return rule.attributeRules.some(ar =>
      ar.id !== excludeAttributeRuleId &&
      ar.attribute === attribute &&
      ar.value === value,
    );
  };

  return (
    <Card key={ rule.id } className={ style.ruleCard } size="small">
      <div className={ style.ruleHeader }>
        <Title level={ 5 }>Rule { index + 1 } — { rule.categoryId || 'Unnamed Category' }</Title>
        <Popconfirm
          title="Delete this rule?"
          onConfirm={ () => onDelete(rule.id) }
          okText="Yes"
          cancelText="No"
        >
          <Button
            type="text"
            danger
            icon={ <DeleteOutlined/> }
            className={ style.deleteButton }
          />
        </Popconfirm>
      </div>

      <div className={ style.ruleContent }>
        <FormField label="Associated Category" required>
          <Select
            placeholder={ categoriesLoading ? 'Loading categories...' : 'Select category' }
            value={ rule.categoryId }
            onChange={ (value) => {
              onUpdate(rule.id, { categoryId: value });
              // 当分类变化时，加载该分类的属性
              if (value) {
                onCategoryChange(value);
              }
            } }
            style={ { width: '100%' } }
            loading={ categoriesLoading }
            disabled={ categoriesLoading }
            options={ availableCategories.map(cat => ({
              label: cat.name,
              value: cat.id,
              disabled: isCategoryUsed(cat.id),
            })) }
          >
          </Select>
        </FormField>

        { rule.attributeRules.map((attrRule, attrIndex) => (
          <div key={ attrRule.id } className={ style.attributeRule }>
            <div className={ style.attributeRuleHeader }>
              <Text strong>Attribute Rule { attrIndex + 1 }</Text>
              <Button
                type="text"
                danger
                size="small"
                icon={ <DeleteOutlined/> }
                onClick={ () => onDeleteAttributeRule(rule.id, attrRule.id) }
              />
            </div>

            <Row gutter={ 16 }>
              <Col span={ 8 }>
                <Text>Associated Attribute</Text>
                <Select
                  placeholder="Select attribute"
                  value={ attrRule.attribute }
                  onChange={ (value) => {
                    const currentCategoryAttributes = categoryAttributes.get(rule.categoryId) || [];
                    const selectedAttr = currentCategoryAttributes.find(attr => attr.name === value);
                    if (selectedAttr) {
                      const operators = getOperators(selectedAttr.inputType, selectedAttr.validatorType);
                      onUpdateAttributeRule(rule.id, attrRule.id, {
                        attribute: value,
                        attributeInputType: selectedAttr.inputType,
                        inputValidatorType: selectedAttr.validatorType,
                        operator: operators[0] || '=',
                        value: '',
                        valueType: selectedAttr.values ? 'id' : 'custom',
                      });
                    }
                  } }
                  style={ { width: '100%', marginTop: 4 } }
                  options={ categoryAttributes.get(rule.categoryId)?.map(attr => ({
                    label: attr.name,
                    value: attr.name,
                    disabled: isAttributeUsed(attr.name, attrRule.id),
                  })) }
                >
                </Select>
              </Col>
              <Col span={ 4 }>
                <Text>Operator</Text>
                <Select
                  value={ attrRule.operator }
                  onChange={ (value) => onUpdateAttributeRule(rule.id, attrRule.id, { operator: value }) }
                  style={ { width: '100%', marginTop: 4 } }
                  options={ getOperators(attrRule.attributeInputType, attrRule.inputValidatorType).map(op => ({
                    label: op,
                    value: op,
                  })) }
                >
                </Select>
              </Col>
              <Col span={ 12 }>
                <Text>Value</Text>
                { attrRule.attribute && (() => {
                  const currentCategoryAttributes = categoryAttributes.get(rule.categoryId) || [];
                  const selectedAttr = currentCategoryAttributes.find(attr => attr.name === attrRule.attribute);
                  if (selectedAttr?.values) {
                    return (
                      <Select
                        placeholder="Select value"
                        value={ attrRule.value }
                        onChange={ (value) => onUpdateAttributeRule(rule.id, attrRule.id, { value }) }
                        style={ { width: '100%', marginTop: 4 } }
                        options={ selectedAttr.values.map(val => ({
                          label: val.label,
                          value: val.id,
                          disabled: isAttributeValueUsed(attrRule.attribute, val.id, attrRule.id),
                        })) }
                      >
                      </Select>
                    );
                  } else {
                    return (
                      <Input
                        placeholder="Enter value"
                        value={ attrRule.value }
                        onChange={ (e) => onUpdateAttributeRule(rule.id, attrRule.id, { value: e.target.value }) }
                        style={ { width: '100%', marginTop: 4 } }
                      />
                    );
                  }
                })() }
              </Col>
            </Row>
          </div>
        )) }

        <Button
          type="dashed"
          icon={ <PlusOutlined/> }
          onClick={ () => onAddAttributeRule(rule.id) }
          style={ { width: '100%', marginTop: 16 } }
        >
          Add Attribute Rule
        </Button>
      </div>
    </Card>
  );
};

// Mock API 接口
interface MockApiResponse<T> {
  success: boolean;
  data: T;
  message?: string;
}

// 分类数据结构
interface Category {
  id: number;
  name: string;
  code: string;
  parentId?: string;
  level: number;
  isActive: boolean;
}

// 使用真实的IGlobalAttr接口
export interface IGlobalAttr {
  id?: number;
  name?: string;
  /** ref: AttrStatus */
  status?: number;
  /** ref: AttrType; this field cannot be updated */
  attrType?: number;
  /** ref: AttrInputType */
  inputType?: number;
  /** ref: AttrInputValidatorType; this field cannot be updated */
  inputValidator?: number;
  ctime?: number;
  mtime?: number;
  /** this can only be updated via UpdateAttrLocalSettingRequest; return only for local request */
  localSetting?: any; // IAttrLocalSettingList
  /** ref: AttrFormatType */
  formatType?: number;
  extInfo?: any; // IAttrExtInfo
  /** display names for cross-border seller in different languages */
  cbDisplayNames?: any; // IMultiLangList
  /** default attrs are applied to all global categories by default */
  /** default values settings are in CategoryAttrValueSetting with global_cat_id = 0 */
  isDefaultAttr?: boolean;
}

// 属性数据结构（兼容性接口）
interface Attribute {
  id: string;
  name: string;
  code: string;
  categoryId: string;
  inputType: AttrInputType;
  validatorType: AttrInputValidatorType;
  values?: Array<{ id: string; label: string }>;
  allowCustom?: boolean;
  isRequired?: boolean;
  description?: string;
}

// 模拟分类数据
const mockCategoriesData: Category[] = [
  {
    id: 1,
    name: 'Cerveja e Cidra',
    code: 'CERVEJA_CIDRA',
    level: 1,
    isActive: true,
  },
  {
    id: 2,
    name: 'Vinho e Champanhe',
    code: 'VINHO_CHAMPANHE',
    level: 1,
    isActive: true,
  },
  {
    id: 3,
    name: 'Licores e Destilados',
    code: 'LICORES_DESTILADOS',
    level: 1,
    isActive: true,
  },
  {
    id: 4,
    name: 'Milk Formula',
    code: 'MILK_FORMULA',
    level: 1,
    isActive: true,
  },
  {
    id: 5,
    name: 'Baby Food',
    code: 'BABY_FOOD',
    level: 1,
    isActive: true,
  },
  {
    id: 6,
    name: 'Electronics',
    code: 'ELECTRONICS',
    level: 1,
    isActive: true,
  },
  {
    id: 7,
    name: 'Sports Equipment',
    code: 'SPORTS_EQUIPMENT',
    level: 1,
    isActive: true,
  },
];

// 模拟属性数据 - 使用IGlobalAttr接口
const mockAttributesData: IGlobalAttr[] = [
  // ========== STRING 类型属性 ==========
  {
    id: 1,
    name: 'Baby life Stage',
    status: 1, // 激活状态
    attrType: 1, // 字符串类型
    inputType: 1, // SINGLE_DROPDOWN
    inputValidator: 1, // STRING
    formatType: 1,
    isDefaultAttr: false,
  },
  {
    id: 2,
    name: 'Age Group',
    status: 1,
    attrType: 1,
    inputType: 1, // SINGLE_DROPDOWN
    inputValidator: 1, // STRING
    formatType: 1,
    isDefaultAttr: false,
  },
  {
    id: 3,
    name: 'Brand',
    status: 1,
    attrType: 1,
    inputType: 2, // SINGLE_COMBOBOX
    inputValidator: 1, // STRING
    formatType: 1,
    isDefaultAttr: false,
  },
  {
    id: 4,
    name: 'Alcohol Percentage',
    status: 1,
    attrType: 2, // 数字类型
    inputType: 2, // SINGLE_COMBOBOX
    inputValidator: 3, // NUMBER
    formatType: 2,
    isDefaultAttr: false,
  },
  {
    id: 5,
    name: 'Origin',
    status: 1,
    attrType: 1,
    inputType: 1, // SINGLE_DROPDOWN
    inputValidator: 1, // STRING
    formatType: 1,
    isDefaultAttr: false,
  },
  {
    id: 6,
    name: 'Size',
    status: 1,
    attrType: 1,
    inputType: 1, // SINGLE_DROPDOWN
    inputValidator: 1, // STRING
    formatType: 1,
    isDefaultAttr: false,
  },
  {
    id: 7,
    name: 'Weight (grams)',
    status: 1,
    attrType: 3, // 整数类型
    inputType: 2, // SINGLE_COMBOBOX
    inputValidator: 2, // INTEGER
    formatType: 3,
    isDefaultAttr: false,
  },
  {
    id: 8,
    name: 'Expiry Date',
    status: 1,
    attrType: 4, // 日期类型
    inputType: 2, // SINGLE_COMBOBOX
    inputValidator: 4, // DATE
    formatType: 4,
    isDefaultAttr: false,
  },
];

// Mock API 函数
const mockApi = {
  // 获取所有符合条件的分类
  async getCategories(filters?: { isActive?: boolean; level?: number }): Promise<MockApiResponse<Category[]>> {
    // 模拟API延迟
    await new Promise(resolve => setTimeout(resolve, 300));

    let filteredCategories = mockCategoriesData;

    if (filters) {
      if (filters.isActive !== undefined) {
        filteredCategories = filteredCategories.filter(cat => cat.isActive === filters.isActive);
      }
      if (filters.level !== undefined) {
        filteredCategories = filteredCategories.filter(cat => cat.level === filters.level);
      }
    }

    return {
      success: true,
      data: filteredCategories,
      message: 'Categories retrieved successfully',
    };
  },

  // 根据分类ID获取该分类的所有属性
  async getAttributesByCategory(categoryId: number): Promise<MockApiResponse<IGlobalAttr[]>> {
    // 模拟API延迟
    await new Promise(resolve => setTimeout(resolve, 200));

    // 根据分类ID查找对应的属性ID列表
    const category = mockCategoriesData.find(cat => cat.id === categoryId);
    if (!category) {
      return {
        success: false,
        data: [],
        message: `Category not found: ${categoryId}`,
      };
    }

    const attributeIds = categoryAttributeMapping[category.name] || [];
    const categoryAttributes = mockAttributesData.filter(attr =>
      attr.id && attributeIds.includes(attr.id),
    );

    if (categoryAttributes.length === 0) {
      return {
        success: false,
        data: [],
        message: `No attributes found for category ID: ${categoryId}`,
      };
    }

    return {
      success: true,
      data: categoryAttributes,
      message: `Found ${categoryAttributes.length} attributes for category`,
    };
  },

  // 根据分类名称获取该分类的所有属性
  async getAttributesByCategoryName(categoryName: string): Promise<MockApiResponse<IGlobalAttr[]>> {
    // 模拟API延迟
    await new Promise(resolve => setTimeout(resolve, 200));

    const category = mockCategoriesData.find(cat => cat.name === categoryName);
    if (!category) {
      return {
        success: false,
        data: [],
        message: `Category not found: ${ categoryName }`,
      };
    }

    return this.getAttributesByCategory(category.id);
  },

  // 搜索分类
  async searchCategories(query: string): Promise<MockApiResponse<Category[]>> {
    // 模拟API延迟
    await new Promise(resolve => setTimeout(resolve, 150));

    const searchResults = mockCategoriesData.filter(cat =>
      cat.name.toLowerCase().includes(query.toLowerCase()) ||
      cat.code.toLowerCase().includes(query.toLowerCase()),
    );

    return {
      success: true,
      data: searchResults,
      message: `Found ${ searchResults.length } categories matching "${ query }"`,
    };
  },

};

// 分类-属性映射关系
const categoryAttributeMapping: Record<string, number[]> = {
  'Milk Formula': [1, 2, 3, 7, 8], // Baby life Stage, Age Group, Brand, Weight, Expiry Date
  'Baby Food': [2, 3, 7, 8], // Age Group, Brand, Weight, Expiry Date
  'Cerveja e Cidra': [4, 5, 6], // Alcohol Percentage, Origin, Size
  'Vinho e Champanhe': [4, 5, 6], // Alcohol Percentage, Origin, Size
  'Licores e Destilados': [4, 5, 6], // Alcohol Percentage, Origin, Size
  'Electronics': [3], // Brand
  'Sports Equipment': [3], // Brand
};

const DisclaimerEditOrAdd: React.FC = () => {
  const navigate = useNavigate();
  const [searchParams] = useSearchParams();
  const id = searchParams.get('id');
  const isEdit = !!id;

  // 表单状态
  const [formData, setFormData] = useState<DisclaimerFormData>({
    name: '',
    description: '',
    status: true,
    categories: [],
    advancedRules: [],
  });

  // UI状态
  const [advancedSettingsExpanded, setAdvancedSettingsExpanded] = useState(false);
  const [loading, setLoading] = useState(false);
  const [validationErrors, setValidationErrors] = useState<string[]>([]);

  // 动态数据状态
  const [availableCategories, setAvailableCategories] = useState<Category[]>([]);
  const [categoryAttributes, setCategoryAttributes] = useState<Map<number, Attribute[]>>(new Map());
  const [categoriesLoading, setCategoriesLoading] = useState(false);

  // 初始化数据
  useEffect(() => {
    // 加载可用分类
    loadAvailableCategories();

    if (isEdit) {
      loadDisclaimerData();
    }
  }, [id]);

  // 加载可用分类
  const loadAvailableCategories = async () => {
    setCategoriesLoading(true);
    try {
      const response = await mockApi.getCategories({ isActive: true, level: 1 });
      if (response.success) {
        setAvailableCategories(response.data);
      } else {
        message.error('Failed to load categories');
      }
    } catch (error) {
      message.error('Failed to load categories');
    } finally {
      setCategoriesLoading(false);
    }
  };

  // 根据分类加载属性
  const loadCategoryAttributes = async (id: number) => {
    // 检查是否已经加载过该分类的属性
    if (categoryAttributes.has(id)) {
      return;
    }

    try {
      const response = await mockApi.getAttributesByCategory(id);
      if (response.success) {
        setCategoryAttributes(prev => new Map(prev).set(id, response.data));
      } else {
      }
    } catch (error) {
    }
  };

  // 加载免责声明数据
  const loadDisclaimerData = async () => {
    setLoading(true);
    try {
      // 模拟API调用
      await new Promise(resolve => setTimeout(resolve, 1000));

      // 根据ID加载不同的模拟数据
      if (id === '1') {
        // Alcohol Consumption
        setFormData({
          id: '1',
          name: 'Alcohol Consumption',
          description: 'The sale and consumption of alcoholic beverages are prohibited for minors. Drink responsibly.',
          status: true,
          categories: ['Cerveja e Cidra', 'Vinho e Champanhe', 'Licores e Destilados'],
          advancedRules: [],
          legalDocument: {
            filename: 'alcohol_legal_approval.pdf',
            hash: 'alcohol_legal_approval_hash',
          },
        });
      } else if (id === '2') {
        // Baby Formula Warning
        setFormData({
          id: '2',
          name: 'Baby Formula Warning',
          description: 'This product should only be administered under the supervision of a qualified healthcare professional. Breastfeeding is recommended up to 2 (two) years of age or beyond, as it is essential for the child\'s development. The information provided here does not replace personalized medical advice.',
          status: true,
          categories: [],
          advancedRules: [
            {
              id: '1',
              categoryId: 1,
              attributeRules: [
                {
                  id: '1',
                  attribute: 'Baby life Stage',
                  attributeInputType: AttrInputType.SINGLE_DROP_DOWN,
                  inputValidatorType: AttrInputValidatorType.VALIDATOR_STRING,
                  operator: '=',
                  value: '4', // ID for 'Growing-Up Milk (3+ years)'
                  valueType: 'id',
                },
              ],
            },
          ],
        });
      }
    } catch (error) {
      message.error('Failed to load disclaimer data');
    } finally {
      setLoading(false);
    }
  };

  // 处理表单字段变化
  const handleFieldChange = (field: keyof DisclaimerFormData, value: any) => {
    setFormData(prev => ({
      ...prev,
      [field]: value,
    }));
  };

  // 处理分类变化
  const handleCategoryChange = (categories: string[]) => {
    setFormData(prev => ({
      ...prev,
      categories,
    }));
  };

  // 添加高级规则
  const addAdvancedRule = () => {
    // 找到第一个未被使用的分类
    const usedCategories = formData.advancedRules.map(rule => rule.categoryId);
    const availableCategory = availableCategories
      .find(cat => !usedCategories.includes(cat.id)); // 使用 cat.id

    const newRule: AdvancedRule = {
      id: Date.now().toString(),
      categoryId: availableCategory?.id!, // 使用 availableCategory.id
      attributeRules: [],
    };

    setFormData(prev => ({
      ...prev,
      advancedRules: [...prev.advancedRules, newRule],
    }));

    // 如果没有可用分类，显示提示
    if (!availableCategory) {
      message.warning('All categories are already used. Please remove a rule first or use a different category.');
    }
  };

  // 删除高级规则
  const removeAdvancedRule = (ruleId: string) => {
    setFormData(prev => ({
      ...prev,
      advancedRules: prev.advancedRules.filter(rule => rule.id !== ruleId),
    }));
  };

  // 更新高级规则
  const updateAdvancedRule = (ruleId: string, updates: Partial<AdvancedRule>) => {
    setFormData(prev => ({
      ...prev,
      advancedRules: prev.advancedRules.map(rule => {
        if (rule.id === ruleId) {
          // 如果更新的是分类，清空所有属性规则
          if (updates.categoryId && updates.categoryId !== rule.categoryId) {
            return {
              ...rule,
              ...updates,
              attributeRules: [], // 清空属性规则
            };
          }
          return { ...rule, ...updates };
        }
        return rule;
      }),
    }));
  };

  // 添加属性规则
  const addAttributeRule = (ruleId: string) => {
    const newAttributeRule: AttributeRule = {
      id: Date.now().toString(),
      attribute: '',
      attributeInputType: AttrInputType.SINGLE_DROP_DOWN,
      inputValidatorType: AttrInputValidatorType.VALIDATOR_STRING,
      operator: '',
      value: '',
      valueType: 'id',
    };

    setFormData(prev => ({
      ...prev,
      advancedRules: prev.advancedRules.map(rule =>
        rule.id === ruleId
          ? { ...rule, attributeRules: [...rule.attributeRules, newAttributeRule] }
          : rule,
      ),
    }));
  };

  // 删除属性规则
  const removeAttributeRule = (ruleId: string, attributeRuleId: string) => {
    setFormData(prev => ({
      ...prev,
      advancedRules: prev.advancedRules.map(rule =>
        rule.id === ruleId
          ? { ...rule, attributeRules: rule.attributeRules.filter(ar => ar.id !== attributeRuleId) }
          : rule,
      ),
    }));
  };

  // 更新属性规则
  const updateAttributeRule = (ruleId: string, attributeRuleId: string, updates: Partial<AttributeRule>) => {
    setFormData(prev => ({
      ...prev,
      advancedRules: prev.advancedRules.map(rule =>
        rule.id === ruleId
          ? {
            ...rule,
            attributeRules: rule.attributeRules.map(ar =>
              ar.id === attributeRuleId ? { ...ar, ...updates } : ar,
            ),
          }
          : rule,
      ),
    }));
  };

  // 处理文件上传
  const handleFileUpload = async (data?: { hash?: string; filename?: string }) => {
    if (data) {
      setFormData(prev => ({
        ...prev,
        legalDocument: {
          filename: data.filename || '',
          hash: data.hash || '',
        },
      }));
      message.success('File uploaded successfully');
    } else {
      setFormData(prev => ({
        ...prev,
        legalDocument: undefined,
      }));
      message.success('File removed successfully');
    }
  };

  // 校验属性规则是否完整
  const validateAttributeRules = (): { isValid: boolean; errors: string[] } => {
    const errors: string[] = [];

    formData.advancedRules.forEach((rule, ruleIndex) => {
      if (rule.attributeRules.length > 0) {
        rule.attributeRules.forEach((attrRule, attrIndex) => {
          // 检查属性是否选择
          if (!attrRule.attribute.trim()) {
            errors.push(`Rule ${ ruleIndex + 1 }: Attribute ${ attrIndex + 1 } - Please select an attribute`);
          }

          // 检查操作符是否选择
          if (!attrRule.operator.trim()) {
            errors.push(`Rule ${ ruleIndex + 1 }: Attribute ${ attrIndex + 1 } - Please select an operator`);
          }

          // 检查值是否填写
          if (!attrRule.value.trim()) {
            errors.push(`Rule ${ ruleIndex + 1 }: Attribute ${ attrIndex + 1 } - Please enter a value`);
          }

          // 检查类型是否设置
          if (!attrRule.attributeInputType) {
            errors.push(`Rule ${ ruleIndex + 1 }: Attribute ${ attrIndex + 1 } - Attribute input type is missing`);
          }

          if (!attrRule.inputValidatorType) {
            errors.push(`Rule ${ ruleIndex + 1 }: Attribute ${ attrIndex + 1 } - Input validator type is missing`);
          }
        });
      }
    });

    return {
      isValid: errors.length === 0,
      errors,
    };
  };

  // 保存免责声明
  const handleSave = async () => {
    // 基础字段校验
    if (!formData.name.trim()) {
      message.error('Please enter disclaimer name');
      return;
    }

    if (!formData.description.trim()) {
      message.error('Please enter disclaimer description');
      return;
    }

    // 属性规则校验
    const validation = validateAttributeRules();
    if (!validation.isValid) {
      // 存储错误信息到状态中
      setValidationErrors(validation.errors);
      // 展开高级设置区域以显示错误
      setAdvancedSettingsExpanded(true);
      // 显示第一个错误信息
      message.error(`Validation failed: ${ validation.errors.length } error(s) found. Please check the Advanced Settings section.`);
      return;
    }

    // 清除之前的错误信息
    setValidationErrors([]);

    setLoading(true);
    try {
      // 模拟API调用
      await new Promise(resolve => setTimeout(resolve, 1000));

      message.success(`Disclaimer ${ isEdit ? 'updated' : 'created' } successfully`);
      navigate('/product/disclaimers-management');
    } catch (error) {
      message.error(`Failed to ${ isEdit ? 'update' : 'create' } disclaimer`);
    } finally {
      setLoading(false);
    }
  };

  // 取消编辑
  const handleCancel = () => {
    navigate('/product/disclaimers-management');
  };

  // 获取可用的分类
  const getAvailableCategories = (excludeRuleId?: string): number[] => {
    const usedCategories = formData.advancedRules
    .filter(rule => rule.id !== excludeRuleId)
    .map(rule => rule.categoryId);

    return availableCategories
    .map(cat => cat.id)
    .filter(id => !usedCategories.includes(id));
  };

  // 生成规则摘要
  const generateRuleSummary = () => {
    const { categories, advancedRules } = formData;

    if (categories.length > 0 && advancedRules.length === 0) {
      return `All items in: ${ categories.join(', ') } categories`;
    } else if (advancedRules.length > 0) {
      const summaries = advancedRules.map(rule => {
        const category = availableCategories.find(cat => cat.id === rule.categoryId);
        const name = category?.name || category?.id;
        if (rule.attributeRules.length === 0) {
          return `Items in "${ name }" category`;
        } else {
          const attributeSummary = rule.attributeRules
          .map(ar => `${ ar.attribute } ${ ar.operator } ${ ar.value }`)
          .join(', ');
          return `Items in "${ name }" category where ${ attributeSummary }`;
        }
      });
      return summaries.join('; ');
    }

    return 'No rules configured';
  };

  return (
    <div className={ style.container }>
      {/* Header */ }
      <div className={ style.header }>
        <Title level={ 2 }>
          { isEdit ? 'Edit' : 'Add' } Disclaimer — { formData.name || 'New Disclaimer' }
        </Title>
        <Button
          type="text"
          icon={ <CloseOutlined/> }
          onClick={ handleCancel }
          className={ style.closeButton }
        />
      </div>

      {/* General Information */ }
      <Card title="General Information" className={ style.sectionCard }>
        <FormField label="Disclaimer Name" required>
          <Input
            placeholder="Enter disclaimer name"
            value={ formData.name }
            onChange={ (e) => handleFieldChange('name', e.target.value) }
          />
        </FormField>

        <FormField label="Disclaimer Description" required>
          <TextArea
            placeholder="Enter disclaimer description"
            value={ formData.description }
            onChange={ (e) => handleFieldChange('description', e.target.value) }
            rows={ 4 }
          />
        </FormField>

        <FormField label="Status">
          <div className={ style.statusToggle }>
            <Switch
              checked={ formData.status }
              onChange={ (checked) => handleFieldChange('status', checked) }
            />
            <Text className={ style.statusText }>
              { formData.status ? 'Active' : 'Inactive' }
            </Text>
          </div>
        </FormField>
      </Card>

      {/* Basic Settings */ }
      <Card title="Basic Settings" className={ style.sectionCard }>
        <div className={ style.sectionDescription }>
          <Text type="secondary">Quick setup: Select categories where this disclaimer should always appear.</Text>
        </div>

        <FormField label="Associated Categories">
          <Select
            mode="multiple"
            placeholder={ categoriesLoading ? 'Loading categories...' : 'Select categories' }
            value={ formData.categories }
            onChange={ handleCategoryChange }
            style={ { width: '100%' } }
            options={ availableCategories.map(cat => ({ label: cat.name, value: cat.name })) }
            loading={ categoriesLoading }
            disabled={ categoriesLoading }
          />
        </FormField>
      </Card>

      {/* Advanced Settings */ }
      <Card
        title={
          <div className={ style.sectionTitle }>
            <SettingOutlined className={ style.sectionIcon }/>
            Advanced Settings
          </div>
        }
        className={ style.sectionCard }
        extra={
          <Button
            type="text"
            icon={ advancedSettingsExpanded ? <DownOutlined/> : <RightOutlined/> }
            onClick={ () => setAdvancedSettingsExpanded(!advancedSettingsExpanded) }
          />
        }
      >
        <div className={ style.sectionDescription }>
          <Text type="secondary">Configure specific rules based on category + attribute conditions</Text>
          { formData.advancedRules.length > 0 && (
            <div style={ { marginTop: 8 } }>
              <Text type="secondary" style={ { fontSize: '12px' } }>
                Available categories: { getAvailableCategories().join(', ') || 'None' }
              </Text>
            </div>
          ) }
        </div>

        { advancedSettingsExpanded && (
          <div className={ style.advancedContent }>
            {/* 校验错误提示 */ }
            { validationErrors.length > 0 && (
              <div className={ style.validationErrors }>
                <Text type="danger" strong>Validation Errors:</Text>
                <ul className={ style.errorList }>
                  { validationErrors.map((error, index) => (
                    <li key={ index } className={ style.errorItem }>
                      <Text type="danger">{ error }</Text>
                    </li>
                  )) }
                </ul>
              </div>
            ) }

            { formData.advancedRules.length === 0 ? (
              <div className={ style.noRules }>
                <Text type="secondary">No advanced rules configured</Text>
              </div>
            ) : (
              formData.advancedRules.map((rule, index) => (
                <RuleCard
                  key={ rule.id }
                  rule={ rule }
                  index={ index }
                  allRules={ formData.advancedRules }
                  availableCategories={ availableCategories }
                  categoryAttributes={ categoryAttributes }
                  categoriesLoading={ categoriesLoading }
                  onUpdate={ updateAdvancedRule }
                  onDelete={ removeAdvancedRule }
                  onAddAttributeRule={ addAttributeRule }
                  onUpdateAttributeRule={ updateAttributeRule }
                  onDeleteAttributeRule={ removeAttributeRule }
                  onCategoryChange={ loadCategoryAttributes }
                />
              ))
            ) }

            <Button
              type="primary"
              icon={ <PlusOutlined/> }
              onClick={ addAdvancedRule }
              style={ { marginTop: 16 } }
            >
              Add Another Rule
            </Button>
          </div>
        ) }
      </Card>

      {/* Rule Summary */ }
      <Card title="Rule Summary" className={ style.sectionCard }>
        <div className={ style.ruleSummary }>
          <Text>This disclaimer will be applied to:</Text>
          <div className={ style.ruleSummaryContent }>
            • { generateRuleSummary() }
          </div>
        </div>
      </Card>

      {/* Legal Approval Document */ }
      <Card title="Legal Approval Document" className={ style.sectionCard }>
        <FormField label="Approval Document">
          <UploadFile
            file={ formData.legalDocument || { hash: '', filename: '' } }
            onChange={ handleFileUpload }
            mode="edit"
          />
        </FormField>

        <div className={ style.uploadNote }>
          <Text type="secondary">This file is only for internal reference.</Text>
        </div>
      </Card>

      {/* Footer Actions */ }
      <div className={ style.footer }>
        <Space>
          <Button onClick={ handleCancel }>
            Cancel
          </Button>
          <Button
            type="primary"
            loading={ loading }
            onClick={ handleSave }
          >
            Save Disclaimer
          </Button>
        </Space>
      </div>
    </div>
  );
};

export default DisclaimerEditOrAdd;

