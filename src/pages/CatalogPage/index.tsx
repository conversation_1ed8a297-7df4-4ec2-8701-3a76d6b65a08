import { useNavigate } from '@classification/admin-solution';
import { Modal } from 'antd';
import React from 'react';

import styles from './styles.module.scss';

export default function OldList() {
  const NEW_PAGE = '/product-list/mpsku';
  const STORAGE_KEY = 'NEW_MPSKU_LIST';
  const navTo = useNavigate();
  if (localStorage.getItem(STORAGE_KEY)) {
    // location.href = NEW_PAGE
    navTo(NEW_PAGE);
  } else {
    Modal.info({
      title: 'Message',
      content:
        'The product list has been moved to a new address, sure to go to the new address?',
      onOk() {
        localStorage.setItem(STORAGE_KEY, '1');
        // location.href = NEW_PAGE
        navTo(NEW_PAGE);
      },
    });
  }

  return <div className={styles.noUseClassName}></div>;
}
