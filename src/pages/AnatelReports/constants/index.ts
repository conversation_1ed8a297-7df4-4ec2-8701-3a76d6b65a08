import type { IGenerateParams } from '@classification/mass-upload';
import MassUploadPage from '@classification/mass-upload';

import { getEnvironment } from 'src/_shared/utils';

const env = getEnvironment();

// TODO: wait for <PERSON><PERSON> to update groupId and configId
export const OnlineValidationResultsConfigId = { test: 234, stable: 0, uat: 110, staging: 106, live: 122 }[env];
export const CONFIGS: IGenerateParams = {
  groupId: { test: 44, stable: 44, uat: 15, staging: 15, live: 18 }[env],
  tasks: {
    'Offline Listing Validation': {
      type: 'mass_validate_anatel_attributes',
      configId: { test: 182, stable: 182, uat: 87, staging: 87, live: 95 }[env],
      headers: [
        'category_id',
        'item_id',
        'manufacturer',
        'model',
        'parent_attribute_id',
        'parent_attribute_value_id',
        'registration_id',
        'shop_id'
      ],
      template:
        'https://docs.google.com/spreadsheets/d/1L1jSYQcm0kteelVcwNVDGo77WQAymB1GCE-WQARojZE/export?gid=400577212&format=csv',
      icon: MassUploadPage.MASS_EDIT_IMG,
      validateRow: {
        // Demo as follow
        // category_id: [
        //   {
        //     checkDuplicate: true,
        //     duplicateDependencies: ['country'],
        //   },
        // ],
      },
    },
    'Online Validation Results': {
      type: 'anatel_attributes_validation_result_report',
      configId: OnlineValidationResultsConfigId,
      headers: [],
      template: '',
      useDateGenerate: true,
    },
  },
};
