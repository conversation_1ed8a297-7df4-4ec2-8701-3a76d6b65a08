import { getCountry, TEST_REGION_MASK, withErrorBoundary } from 'admin-upload-common';
import MassEditImg from 'admin-upload-common/es/assets/images/edit_mass.png';
import { message } from 'antd';
import type { SelectValue } from 'antd/lib/select';
import * as React from 'react';
import { useEffect } from 'react';

import { ExportBtn, MassUploadPage } from 'src/containers/MassUploadPage';
import {
  mainReporter,
  MassEditItemReporter,
  POINT_JS_LOADED,
  POINT_ROUTER_ENTER,
} from 'src/reporter';
import { getRouterEnterTime } from 'src/utils';

const MassEditTypes = {
  'Mass edit Title and Description Multi-language info':
    'mass_edit_item_trans_title_and_desc',
  'Mass Edit Item Attribute': 'mass_update_item_attr_value_mapping',
  'Mass Edit Item Category': 'mass_edit_item_global_category',
  'Mass Edit Item Brand': 'mass_update_item_brand',
  'Mass Edit Price': 'mass_edit_item_price',
  'Mass Edit Title Description': 'mass_edit_item_title_description',
  'Mass Edit Title Description All': 'mass_edit_item_title_description_all',
  'Mass Edit Color Code': 'mass_edit_item_color_code',
  'Mass Edit GTIN': 'mass_edit_item_gtin',
  'Mass Edit DG': 'mass_update_dangerous_goods',
  'Mass Edit Item Category Bypass All Validations':
    'mass_edit_item_global_category_bypass_validation',
  'Mass Edit Weight And Dimension': 'mass_edit_item_dimension',
  'Mass Edit Item Shipping Channel': 'mass_edit_item_logistic',
  'Mass Edit Item Shipping Channel In This Shop': 'mass_edit_shop_logistic',
  'Mass update high-end brand new item fields': 'mass_update_high_end_brand_item',
  'Mass Edit Items in Umbrella Brand Collection': 'mass_update_auth_brand_item',
  "Mass Edit Compatibility": "mass_item_compatibility_insertion",
  "Mass Publish Outlet Sku": "mass_publish_outlet_sku",
  "Mass Publish Mart Sku": "mass_publish_mart_sku",
};

const regionSpecificAction: { [key: string]: { [key: string]: string } } = {
  IN: {
    [`Mass Update HS Code (${TEST_REGION_MASK[getCountry()]
      })`]: 'mass_set_in_item_hs_code',
    [`Mass Update GST Code (${TEST_REGION_MASK[getCountry()]
      })`]: 'mass_set_in_item_gst_code',
  },
};

const regionSpecificTemplate: {
  [key: string]: Array<{ name: string; link: string }>;
} = {
  IN: [
    {
      name: 'Mass Update HS Code',
      link:
        'https://docs.google.com/spreadsheets/d/1TpwGsHbstkFhW-QB2qpreRazmVcSoKXDa_x9Pwg4-zQ/export?format=csv',
    },
    {
      name: 'Mass Update GST Code',
      link:
        'https://docs.google.com/spreadsheets/d/1ehVOYILw-RfAMlD1_TCJC5TmUY63Ie4bjlmFptAe1iQ/export?format=csv',
    },
  ],
};

export const downloadTemplate = [
  {
    name: 'Mass edit Title and Description Multi-language info',
    link:
      'https://docs.google.com/spreadsheets/d/16F2NDLZrxkPJi2PoiG6WqeHbYvvQwSC9YU3bwh6uR7U/export?format=csv',
  },
  {
    name: 'Mass Edit Item Attribute',
    link:
      'https://docs.google.com/spreadsheets/d/1Ix527qVhSQ53z2oNp5mH205PYLPWk-yLQ9PRCvsYZNQ/export?format=csv&id=1Ix527qVhSQ53z2oNp5mH205PYLPWk-yLQ9PRCvsYZNQ&gid=0',
  },
  {
    name: 'Mass Edit Item Category',
    link:
      'https://docs.google.com/spreadsheets/d/1OMIrymHuzBNZ9Ie13jPUTuUdtSzbT1Clzx-W1gdemhY/export?format=csv&id=1OMIrymHuzBNZ9Ie13jPUTuUdtSzbT1Clzx-W1gdemhY&gid=0',
  },
  {
    name: 'Mass Edit Item Brand',
    link:
      'https://docs.google.com/spreadsheets/d/1iRSFcq6APSjyOh3z-kgvCqUHgJ-75zBv92jJdno3nfs/export?format=csv',
  },
  {
    name: 'Mass Edit Price',
    link:
      'https://docs.google.com/spreadsheets/d/1B6npBRL-D9ySR5UyHTm_QcxB4xHtWmtHNj1WwodmDyU/export?format=csv&gid=580210359',
  },
  {
    name: 'Mass Edit Title Description',
    link:
      'https://docs.google.com/spreadsheets/d/1B6npBRL-D9ySR5UyHTm_QcxB4xHtWmtHNj1WwodmDyU/export?format=csv&gid=1368404213',
  },
  {
    name: 'Mass Edit Title Description All',
    link:
      'https://docs.google.com/spreadsheets/d/1B6npBRL-D9ySR5UyHTm_QcxB4xHtWmtHNj1WwodmDyU/export?format=csv&gid=1368404213',
  },
  {
    name: 'Mass Edit Color Code',
    link:
      'https://docs.google.com/spreadsheets/d/1B6npBRL-D9ySR5UyHTm_QcxB4xHtWmtHNj1WwodmDyU/export?format=csv&gid=1846460867',
  },
  {
    name: 'Mass Edit GTIN',
    link:
      'https://docs.google.com/spreadsheets/d/1B6npBRL-D9ySR5UyHTm_QcxB4xHtWmtHNj1WwodmDyU/export?format=csv&gid=1768695439',
  },
  {
    name: 'Mass Edit DG',
    link:
      'https://docs.google.com/spreadsheets/d/12MGnmu9AhZvFRZ7LPg4nT9kiBAGhKGkA6O7msq9MsPY/export?format=csv&gid=1970544904',
  },
  {
    name: 'Mass Edit Item Category Bypass All Validations',
    link:
      'https://docs.google.com/spreadsheets/d/1cgVFrxxbIoT2zJVWvTtN4TALGsluDPFlZr-thSIka2o/export?format=csv&gid=1904473021',
  },
  {
    name: 'Mass Edit Weight And Dimension',
    link: 'https://docs.google.com/spreadsheets/d/1M6LUun8XbMIyzlRw3kj8E9z65eDY3YLcxztogzQF-Nc/export?format=csv&edit#gid=0'
  },
  {
    name: 'Mass Edit Item Shipping Channel',
    link: 'https://docs.google.com/spreadsheets/d/1wIBLSzOaL79DF3upkk3nobuo-5Rc5F_ZL-It-zf_vYM/export?format=csv&edit#gid=0'
  },
  {
    name: 'Mass Edit Item Shipping Channel In This Shop',
    link: 'https://docs.google.com/spreadsheets/d/1g6N8OpZyafMAdLypbJzKdcu0gAcqJPJVEOVlLQBCrYo/export?format=csv&edit#gid=0'
  },
  {
    name: 'Mass update high-end brand new item fields',
    link: 'https://docs.google.com/spreadsheets/d/1f2Px5Q3Uwvd843nPqTHyB1MFwHlMsFID42CdQHSncnU/export?format=csv&edit#gid=0'
  },
  {
    name: 'Mass Edit Items in Umbrella Brand Collection',
    link: 'https://docs.google.com/spreadsheets/d/1WFRXPG2-5b3F2VA65DXrBBdRp6nQ-o4sLgjMvI5bYPo/export?format=csv&edit#gid=0'
  },
  {
    name: 'Mass Edit Compatibility',
    link: 'https://docs.google.com/spreadsheets/d/162u2bsSlDhCKybNg7APCnbwLFf574doKY_3NSCgPUv8/export?format=csv&edit#gid=0'
  },
  {
    name: 'Mass Publish Outlet Sku',
    link: 'https://docs.google.com/spreadsheets/d/1MlLYfcuUBR8_kFK0A5PiRCdOPFlw1E0Uvcrc93jY8dA/export?format=csv&edit#gid=0'
  },
  {
    name: 'Mass Publish Mart Sku',
    link: 'https://docs.google.com/spreadsheets/d/1As8SWT0_BQlu7Hci_zgK9EFZ8YjTDr1k5pmZHL-18-w/export?format=csv&edit#gid=0'
  }
];

export const RequiredHeaders = {
  'Mass Edit Item Attribute':
    'item_id,shop_id,global_attr_id,global_val_id,attri_val_customized,attri_val_quant_number_unit,attri_val_datetime',
  'Mass Edit Item Category':
    'shop_id,item_id,cat_id,global_attri_id_global_val_id,global_attri_id_customized_text,global_attri_id_quant_number_unit,global_attri_id_datetime,brand_id',
  'Mass Edit Item Brand': 'item_id,shop_id,brand_id',
  'Mass Edit Price': 'user_name,shop_id,item_id,model_id,price',
  'Mass Edit Title Description': 'shop_id,item_id,title,description',
  'Mass Edit Title Description All': 'shop_id,item_id,title,description',
  'Mass Edit Color Code': 'item_id,model_id,color_code,operation',
  'Mass Edit GTIN': 'item_id,model_id,GTIN',
  'Mass Edit DG': 'shop_id,item_id,Dangerous Goods,Dangerous Categorization',
  'Mass Edit Item Category Bypass All Validations': 'shop_id,item_id,cat_id',
  [`Mass Update HS Code (${TEST_REGION_MASK[getCountry()]
    })`]: 'shopid,productid,hs_code',
  [`Mass Update GST Code (${TEST_REGION_MASK[getCountry()]
    })`]: 'shopid,productid,gst_code',
  'Mass Edit Weight And Dimension': 'ShopId,ItemId,ModelId,Weight,Length,Width,Height',
  'Mass Edit Item Shipping Channel': 'ShopId,ItemId,ChannelId,ChannelToggle,CoverShippingFee,SizeRange,SizeUnit',
  'Mass Edit Item Shipping Channel In This Shop': 'Shop id,Channel id,Channel Toggle(On/Off),Cover Shipping Fee Toggle(On/Off)',
  'Mass update high-end brand new item fields': 'Shop,Item id,Size Guide,Designer info,Shipping info,Style with',
  'Mass Edit Items in Umbrella Brand Collection': 'Shop_ID,Item_ID,Brand_ID,Action',
  "Mass Edit Compatibility": 'shop_id,item_id,vehicle_setting_list',
};
const massEditDesc = {
  mass_item_compatibility_insertion: 'This function won’t edit information directly, it redirects the changes to Shopee Modify for You tool'
}
function MassEdit() {
  const region = getCountry();
  const displayAction = {
    ...MassEditTypes,
    ...(regionSpecificAction[region] ? regionSpecificAction[region] : {}),
  };

  const displayTemplate = [
    ...downloadTemplate,
    ...(regionSpecificTemplate[region] ? regionSpecificTemplate[region] : []),
  ];

  const selectOnchange = (value: SelectValue) => {
    if (value === 'mass_edit_item_title_description') {
      message.info({
        content: 'cannot modify non-cb/non-offical shops',
        duration: 3,
      });
    }
  };

  useEffect(() => {
    MassEditItemReporter.setCustomPointTime(
      POINT_ROUTER_ENTER,
      getRouterEnterTime() || +new Date(),
    );
    return () => {
      mainReporter.clearAll();
    };
  }, []);

  return (
    <>
      <MassUploadPage
        pageTitle="Mass Edit"
        reporter={MassEditItemReporter}
        massEditTypes={displayAction}
        massEditDesc={massEditDesc}
        customTemplateButton={
          <ExportBtn templates={displayTemplate} icon={MassEditImg} />
        }
        pollingInterval={500}
        validateTemplateHeaders={RequiredHeaders}
        region={region}
        selectOnchange={selectOnchange}
      />
    </>
  );
}

export default withErrorBoundary(MassEdit, {
  onComponentDidCatch: (error: Error, _errorInfo: React.ErrorInfo) => {
    mainReporter.errorReport(error);
  },
});

MassEditItemReporter.setCustomPointTime(POINT_JS_LOADED);
