.container {
  background-color: #f0f2f5;
  height: calc(100vh - 56px);
  padding: 16px 24px;
}

.titleBar {
  display: flex;
  justify-content: flex-end;
  align-items: center;
  margin: 16px 0;
}
.searchForm {
  display: flex;
  padding: 12px;
  background-color: #fff;
  border-radius: 4px;
  :global {
    .ant-form-item:first-child {
      flex: 1;
    }
    .ant-form-item:last-child {
      margin-right: 0;
    }
  }
}

.pageTitle {
  padding-bottom: 12px;
  color: rgba(0, 0, 0, 0.87);
  font: 500 18px / 22px sans-serif;
}

.actionButtons {
  display: flex;
  gap: 8px;
}

.actionIcon {
  color: #1890ff;
  cursor: pointer;
  font-size: 16px;

  &:hover {
    color: #40a9ff;
  }
}

.modalContent {
  .modalField {
    margin-bottom: 16px;

    strong {
      display: inline-block;
      width: 120px;
      color: #262626;
    }

    span {
      color: #595959;
    }
  }
}


.deleteConfirmModal {
  .deleteConfirm {
    display: flex;
    justify-content: center;
    align-items: center;
    flex-direction: column;
    .deleteConfirmIcon {
      color: #d19a28;
      font-size: 20px;

      :global {
        .anticon-warning {
          width: 40px;
          height: 40px;
          background: #fef9c4;
          border-radius: 50%;
          svg {
            margin-top: 8px;
          }
        }
      }
    }

    .title {
      font-weight: 500;
      text-align: center;
      font-size: 20px;
    }
    .content {
      color: #898f9a;
      text-align: center;
      .name {
        font-weight: 500;
        color: #000;
      }
    }
  }
  :global {
    .ant-modal-confirm-btns {
      display: flex;
      button {
        flex: 1;
      }
    }
  }
}



