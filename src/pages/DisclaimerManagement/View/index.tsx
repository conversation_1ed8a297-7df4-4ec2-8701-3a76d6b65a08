import {
  Button,
  Col,
  message,
  Modal,
  Row,
  Spin,
  Tag,
} from 'antd';
import React, {
  useEffect,
  useState,
} from 'react';

import UploadFile from 'src/components/Upload';
import Status from '../Status';
import style from './style.module.scss';

// 字段配置接口
interface FieldConfig {
  key?: string;
  label?: string;
  render: (value: any, record: any) => React.ReactNode;
  className?: string;
}

// 分组配置接口
interface SectionConfig {
  title: string;
  fields: FieldConfig[];
}

interface DisclaimerViewProps {
  visible: boolean;
  disclaimer: any;
  onClose: () => void;
}

// 模拟获取disclaimer详情的API函数
const fetchDisclaimerDetail = async (id: string | number) => {
  // 模拟API调用延迟
  await new Promise(resolve => setTimeout(resolve, 1500));

  // 模拟从API获取的完整数据
  const mockDetailData = {
    id,
    name: `Disclaimer ${ id }`,
    description: `This is a detailed description for disclaimer ${ id }. It contains comprehensive information about the disclaimer including its purpose, scope, and implementation details.`,
    disclaimerStatus: 'active',
    approvalFileAddr: {
      filename: '新版线下-如何呈现一场精彩的面授课程-完整版-2023 7月研发面授版本.pdf',
      hash: 'br-11195001-bbvn4-mdb8bpqvxrrad3',
    },
    categories: ['Milk Formula', 'Baby Food'],
    attributes: ['Baby life Stage', 'Age Group'],
    rules: [
      'Baby life Stage = Growing-Up Milk (3+ years)',
      'Age Group = 3-6 years',
      'Product Type = Nutritional Supplement',
    ],
    createdAt: '2024-01-15',
    updatedAt: '2024-01-20',
    createdBy: '<EMAIL>',
    approvedBy: '<EMAIL>',
    approvalDate: '2024-01-18',
  };

  return mockDetailData;
};

const DisclaimerView: React.FC<DisclaimerViewProps> = ({
  visible,
  disclaimer,
  onClose,
}) => {
  const [detailData, setDetailData] = useState<any>(null);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  // 当Modal打开时，调用接口获取详情
  useEffect(() => {
    if (visible && disclaimer?.id) {
      setLoading(true);
      setError(null);

      fetchDisclaimerDetail(disclaimer.id)
      .then(data => {
        setDetailData(data);
      })
      .catch(err => {
        setError(err.message || 'Failed to fetch disclaimer details');
        message.error('Failed to fetch disclaimer details');
      })
      .finally(() => {
        setLoading(false);
      });
    }
  }, [visible, disclaimer?.id]);

  // 当Modal关闭时，清理数据
  useEffect(() => {
    if (!visible) {
      setDetailData(null);
      setError(null);
      setLoading(false);
    }
  }, [visible]);

  // 字段配置
  const fieldConfigs: SectionConfig[] = [
    {
      title: 'General Information',
      fields: [
        {
          key: 'name',
          label: 'Disclaimer Name',
          render: (value) => <span>{ value }</span>,
        },
        {
          key: 'description',
          label: 'Disclaimer Description',
          render: (value) => <span className={ style.description }>{ value }</span>,
        },
        {
          key: 'disclaimerStatus',
          label: 'Disclaimer Status',
          render: (value) => <Status status={ value }/>,
        },
      ],
    },
    {
      title: 'Trigger Conditions',
      fields: [
        {
          key: 'categories',
          label: 'Categories',
          render: (value) => (
            <div className={ style.tagGroup }>
              { Array.isArray(value) ? value.map((cat, index) => (
                <Tag key={ index } className={ style.categoryTag }>{ cat }</Tag>
              )) : (
                <Tag className={ style.categoryTag }>{ value }</Tag>
              ) }
            </div>
          ),
        },
        {
          key: 'attributes',
          label: 'Attributes',
          render: (value) => (
            <div className={ style.tagGroup }>
              { Array.isArray(value) ? value.map((attr, index) => (
                <Tag key={ index } className={ style.attributeTag }>{ attr }</Tag>
              )) : (
                <Tag className={ style.attributeTag }>{ value }</Tag>
              ) }
            </div>
          ),
        },
        {
          key: 'rules',
          label: 'Rules',
          render: (value) => (
            <div className={ style.ruleText }>
              { Array.isArray(value) ? value.map((rule, index) => (
                <div key={ index } className={ style.ruleItem }>
                  <span className={ style.bullet }>•</span>
                  <span>{ rule }</span>
                </div>
              )) : (
                <div className={ style.ruleItem }>
                  <span className={ style.bullet }>•</span>
                  <span>{ value }</span>
                </div>
              ) }
            </div>
          ),
        },
      ],
    },
    {
      title: 'Legal Approval Document',
      fields: [
        {
          key: 'approvalFileAddr',
          label: 'Approval Document',
          render: (value) => {
            console.log(value)
            return <div className={ style.uploadStatus }>
              { value ? (
                <>
                  <UploadFile file={ value } mode="view"/>
                </>
              ) : (
                <span className={ style.noFile }>No file uploaded.</span>
              ) }
            </div>
          },
        },
        {
          render: () => (
            <div className={ style.approvalNote }>
              This document is for internal reference only and was uploaded as part of legal compliance.
            </div>
          ),
        },
      ],
    },
  ];

  // 通用字段渲染组件
  const renderField = (field: FieldConfig, record: any) => {
    const value = field.key ? record[field.key] : null;
    if (!field.label) {
      return <>{ field.render(value, record) }</>
    }
    return (
      <Col span={ 24 } key={ field.key || 'no-key' }>
        <Row align={ 'top' }>
          <Col span={ 6 } className={ style.labelCol }>
            <label>{ field.label }</label>
          </Col>
          <Col span={ 18 } className={ field.className }>
            { field.render(value, record) }
          </Col>
        </Row>
      </Col>
    );
  };

  // 通用分组渲染组件
  const renderSection = (section: SectionConfig, record: any) => (
    <div className={ style.section } key={ section.title }>
      <h3 className={ style.sectionTitle }>{ section.title }</h3>
      <div>
        <Row gutter={ [16, 16] }>
          { section.fields.map(field => renderField(field, record)) }
        </Row>
      </div>
    </div>
  );

  // 渲染loading状态
  const renderLoading = () => (
    <div className={ style.loadingContainer }>
      <Spin size="large"/>
      <div className={ style.loadingText }>Fetching disclaimer details...</div>
    </div>
  );

  // 渲染错误状态
  const renderError = () => (
    <div className={ style.errorContainer }>
      <div className={ style.errorIcon }>⚠️</div>
      <div className={ style.errorText }>{ error }</div>
      <Button
        type="primary"
        onClick={ () => {
          setError(null);
          setLoading(true);
          fetchDisclaimerDetail(disclaimer.id)
          .then(data => setDetailData(data))
          .catch(err => setError(err.message))
          .finally(() => setLoading(false));
        } }
      >
        Retry
      </Button>
    </div>
  );

  return (
    <Modal
      title={
        <div className={ style.modalTitle }>
          View Disclaimer – { disclaimer?.name || 'Loading...' }
        </div>
      }
      open={ visible }
      onCancel={ onClose }
      footer={ [
        <Button key="close" onClick={ onClose }>
          Close
        </Button>,
      ] }
      width={ 800 }
      className={ style.detailModal }
    >
      <div className={ style.modalContent }>
        { loading && renderLoading() }
        { error && renderError() }
        { !loading && !error && detailData && (
          <>
            { fieldConfigs.map(section => renderSection(section, detailData)) }
          </>
        ) }
      </div>
    </Modal>
  );
};

export default DisclaimerView;
