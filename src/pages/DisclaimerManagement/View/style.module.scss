.modalTitle {
  font-size: 18px;
  font-weight: 600;
  color: #262626;
}

.detailModal {
  :global {
    .ant-modal-header {
      border-bottom: 1px solid #f0f0f0;
      padding: 16px 24px;
    }

    .ant-modal-body {
      padding: 24px;
    }

    .ant-modal-footer {
      border-top: 1px solid #f0f0f0;
      padding: 16px 24px;
    }
  }
}

.modalContent {
  .section {
    margin-bottom: 32px;

    &:last-child {
      margin-bottom: 0;
    }
  }

  .sectionTitle {
    font-size: 16px;
    font-weight: 600;
    color: #262626;
    margin: 0 0 16px 0;
    padding-bottom: 8px;
    border-bottom: 1px solid #f0f0f0;
  }

  .description {
    white-space: pre-line;
    line-height: 1.6;
  }

  .tagGroup {
    display: flex;
    gap: 8px;
    flex-wrap: wrap;
  }

  .categoryTag {
    background-color: #e6f7ff;
    border-color: #91d5ff;
    color: #1890ff;
  }

  .attributeTag {
    background-color: #f9f0ff;
    border-color: #d3adf7;
    color: #722ed1;
  }

  .ruleText {
    display: flex;
    flex-direction: column;
    align-items: flex-start;
    gap: 8px;
    line-height: 1.5;

    .bullet {
      color: #1890ff;
      font-weight: bold;
      font-size: 16px;
      line-height: 1;
    }
  }

  .uploadStatus {
    :global {
      .download {
        color: #1890ff;
        cursor: pointer;
      }
    }
    .noFile {
      color: #bfbfbf;
      font-style: italic;
    }
  }

  .approvalNote {
    font-size: 12px;
    color: #8c8c8c;
    line-height: 1.4;
    margin-top: 8px;
    padding: 8px;
  }

  .labelCol {
    label {
      font-size: 14px;
      color: #acafb8;
      display: block;
      margin-bottom: 0;
    }
  }

  .loadingContainer {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    padding: 40px 20px;
    text-align: center;
  }

  .loadingText {
    margin-top: 16px;
    font-size: 14px;
    color: #8c8c8c;
  }

  .errorContainer {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    padding: 40px 20px;
    text-align: center;
  }

  .errorIcon {
    font-size: 48px;
    margin-bottom: 16px;
  }

  .errorText {
    font-size: 14px;
    color: #ff4d4f;
    margin-bottom: 16px;
    text-align: center;
  }

  .ruleItem {
    display: flex;
    align-items: flex-start;
    gap: 8px;
    line-height: 1.5;
    margin-bottom: 8px;

    &:last-child {
      margin-bottom: 0;
    }
  }

}
