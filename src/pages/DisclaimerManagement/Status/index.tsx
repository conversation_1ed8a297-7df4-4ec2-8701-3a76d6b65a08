import { WarningOutlined } from '@ant-design/icons';
import { Tag } from 'antd';
import React from 'react';

import style from './style.module.scss';

const Status = ({status}: {status: string}) => {
  switch (status) {
    case 'active':
      return <Tag className={ style.tag } color="green">Active</Tag>;
    case 'inactive':
      return <Tag className={ style.tag } color="default">Inactive</Tag>;
    case 'invalid':
      return (
        <Tag className={ style.tag } color="red" icon={ <WarningOutlined/> }>
          Invalid
        </Tag>
      );
    default:
      return <></>;
  }
}
export default Status;
