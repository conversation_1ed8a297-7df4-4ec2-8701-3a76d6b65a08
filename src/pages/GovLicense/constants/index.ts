import type { IGenerateParams } from '@classification/mass-upload';
import MassUploadPage from '@classification/mass-upload';

import { getEnvironment } from 'src/_shared/utils';

const env = getEnvironment();

export const CONFIGS: IGenerateParams = {
  groupId: { 
    test: 59, 
    stable: 44, 
    uat: 25,
    staging: 24,
    live: 30
  }[env],
  tasks: {
    'Gov License Validation': {
      type: 'mass_validate_fda_attributes',
      configId: {
        test: 243,
        stable: 182,
        uat: 129,
        staging: 127,
        live: 144
      }[env],
      headers: [
        'attribute_type',
        'attribute_value',
      ],
      template:
        'https://docs.google.com/spreadsheets/d/1XtjAFFkhl2QhZuZoxEa2Hvm2Qhq-aYYpZb51ZW9AaVA/export?gid=0&format=csv',
        // 'https://docs.google.com/spreadsheets/d/1L1jSYQcm0kteelVcwNVDGo77WQAymB1GCE-WQARojZE/export?gid=400577212&format=csv',
      icon: MassUploadPage.MASS_EDIT_IMG,
      validateRow: {
        // Demo as follow
        // category_id: [
        //   {
        //     checkDuplicate: true,
        //     duplicateDependencies: ['country'],
        //   },
        // ],
      },
    },
  },
};
