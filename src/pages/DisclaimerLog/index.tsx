import type { IAuditLog } from '@classification/audit-log-table';
import AuditLogTable from '@classification/audit-log-table';
import type { ColumnsType } from 'antd/es/table';
import React from 'react';

import 'antd/dist/antd.css';

const auditLogUrl = 'wsa/api/v2/classification/audit/get';
const refEntityIdPattern = 'item.tag:tag:1946556733021184';
const defaultColumns = [
  {
    title: 'Disclaimer Name',
    width: '200px',
    dataIndex: 'disclaimerName',
  },
  // {
  //   title: 'Old Values',
  //   dataIndex: '',
  // },
  // {
  //   title: 'New Values',
  //   dataIndex: '',
  // },
  // {
  //   title: 'Operator',
  //   dataIndex: '',
  // },
];

const DisclaimerLog = () => {
  const onChange = () => {
    console.log('change');
  };

  return (
    <>
      <AuditLogTable
        title="Upload History"
        requestUrl={ auditLogUrl }
        refEntityIdPattern={ refEntityIdPattern }
        logStartTime={ 1647846211351 }
        columns={ defaultColumns }
        onChange={ onChange }
      />
    </>
  );
}

export default DisclaimerLog;
