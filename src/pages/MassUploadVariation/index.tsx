import { getCountry, withErrorBoundary } from 'admin-upload-common';
import MassEditImg from 'admin-upload-common/es/assets/images/edit_mass.png';
import * as React from 'react';
import { useEffect } from 'react';

import { ExportBtn,MassUploadPage } from 'src/containers/MassUploadPage';
import {
  mainReporter,
  MassUpdateVariationReporter,
  POINT_JS_LOADED,
  POINT_ROUTER_ENTER,
} from 'src/reporter';
import { getRouterEnterTime } from 'src/utils';

const MassEditTypes = {
  'Mass update Variation Multi-language info':
    'mass_edit_item_trans_variations',
  'Mass Get Variation': 'mass_get_item_variations',
  'Mass Update Variation': 'mass_edit_item_variations',
};
export const downloadTemplate = [
  {
    name: 'Mass update Variation Multi-language info',
    link:
      'https://docs.google.com/spreadsheets/d/1_J0sgiixtLtFAJy7ogddA5EgcGUJXe1SkG9mWIj1I4w/export?format=csv',
  },
  {
    name: 'Mass Get Variation',
    link:
      'https://docs.google.com/spreadsheets/d/1B6npBRL-D9ySR5UyHTm_QcxB4xHtWmtHNj1WwodmDyU/export?format=csv&gid=187072742',
  },
  {
    name: 'Mass Update Variation',
    link:
      'https://docs.google.com/spreadsheets/d/1RZZIgmYSykFcAnBjLqdye0m1eVT9-7GNA6kRzXA5afw/export?format=csv&gid=0',
  },
];

export const RequiredHeaders = {
  'Mass Get Variation':
    'shop_id,item_id',
  'Mass Update Variation':
    'shop_id,item_id,model_id,model_name,variation_1_id,variation_1,group_1_id,group_1,options_1_id,options_1,variation_2_id,variation_2,group_2_id,group_2,options_2_id,options_2',
};

function MassUploadVariation() {
  const region = getCountry();
  useEffect(() => {
    MassUpdateVariationReporter.setCustomPointTime(
      POINT_ROUTER_ENTER,
      getRouterEnterTime() || +new Date(),
    );
    return () => {
      mainReporter.clearAll();
    };
  }, []);
  return (
    <>
      <MassUploadPage
        pageTitle="Mass Edit"
        reporter={MassUpdateVariationReporter}
        massEditTypes={MassEditTypes}
        customTemplateButton={
          <ExportBtn templates={downloadTemplate} icon={MassEditImg} />
        }
        pollingInterval={500}
        validateTemplateHeaders={RequiredHeaders}
        region={region}
      />
    </>
  );
}

export default withErrorBoundary(MassUploadVariation, {
  onComponentDidCatch: (error: Error, _errorInfo: React.ErrorInfo) => {
    mainReporter.errorReport(error);
  },
});

MassUpdateVariationReporter.setCustomPointTime(POINT_JS_LOADED);
