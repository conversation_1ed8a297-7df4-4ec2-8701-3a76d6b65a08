import { getCountry,useRoute } from '@classification/admin-solution';
import { withErrorBoundary } from 'admin-upload-common';
import { useRequest } from 'ahooks';
import { Card } from 'antd';
import React from 'react';

import { getItemInfoForCodingMonkey } from 'src/api/uploadAdmin';
import { mainReporter } from 'src/reporter';
import styles from './styles.module.scss';

function CodingMonkey() {
  const region = getCountry();
  const routeParams = ((useRoute().params ?? {}) as unknown) as {
    shopId: string;
    itemId: string;
  };
  console.log(routeParams);
  const { data } = useRequest(() =>
    getItemInfoForCodingMonkey({
      shopId: Number(routeParams.shopId),
      itemId: Number(routeParams.itemId),
      region: region,
    }),
  );

  return (
    <div className={styles.content}>
      <div className={styles.header}>
        <Card title={'Product'}>
          <pre>
            {data?.data ? JSON.stringify(JSON.parse(data.data), null, 4) : ''}
          </pre>
        </Card>
      </div>
    </div>
  );
}

export default withErrorBoundary(CodingMonkey, {
  onComponentDidCatch: (error: Error, _errorInfo: React.ErrorInfo) => {
    mainReporter.errorReport(error);
  },
});
