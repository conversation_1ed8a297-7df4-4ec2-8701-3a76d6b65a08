.container {
  margin: 36px 36px 0 36px;
  background-color: #fff;
  padding: 20px;
  display: flex;
  flex-direction: column;
  gap: 36px;
  .item {
    :global {
      .ant-descriptions-item-container >.ant-descriptions-item-label {
        width: 150px;
        color: #5b5959;
        font-weight: 400;
      }
    }
  }
}
.footer {
  margin: 0 36px;
  padding: 0 18px 16px 0;
  text-align: right;
  background: #fff;
}

.loading {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 100%;
}

.kitDescription {
  :global {
    .ant-descriptions-item-content .async-component {
      width: 100%;
    }
  }
}
