import { Descriptions } from 'antd';
import React from 'react';

import Thumbnail from 'src/components/Thumbnail';
import style from './style.module.scss';

const labelStyle = {width: '80px', fontWeight: 'normal'};
const Images = ({ images, productLongImages }: { images?: string[], productLongImages?: string[] }) => {
  return <Descriptions column={ 1 }>
    { images?.length ? <Descriptions.Item labelStyle={labelStyle} label="1:1 Image">
      <div className={ style.images }>
        { images?.map((img) => {
          return <Thumbnail className={style.image} width={ 56 } source={ img } key={ img }/>
        }) }
      </div>
    </Descriptions.Item> : null }
    {
      productLongImages?.length ? <Descriptions.Item labelStyle={labelStyle} label="3:4 Image">
          <div className={ style.images }>
            { productLongImages?.map((img) => {
              return <Thumbnail className={style.image} width={ 56 } source={ img } key={ img }/>
            }) }
          </div>
        </Descriptions.Item>
        :
        null
    }
  </Descriptions>;
}
export default Images;
