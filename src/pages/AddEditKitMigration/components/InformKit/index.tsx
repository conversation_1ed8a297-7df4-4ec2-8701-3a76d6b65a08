import { SearchOutlined } from '@ant-design/icons';
import { getCountry } from '@classification/admin-solution';
import { ProductDescriptionEditor } from '@listing/ls-upload-cmpt-sdk-react';
import { getEnvironment } from 'admin-upload-common';
import {
  Button,
  InputNumber,
  Modal,
} from 'antd';
import cx from 'classnames';
import React, { useCallback, useEffect, useState } from 'react';

import {
  getKitDetail,
} from 'src/api/uploadAdmin';
import type {
  uploadAdmin
} from 'src/api/uploadAdmin/uploadAdmin';
import Card from 'src/pages/AddEditKitMigration/components/Card';
import Images from 'src/pages/AddEditKitMigration/components/Images';
import style from './style.module.scss';

const InformKit = ({
  value,
  disabled,
  onChange,
}: {
  value?: number;
  disabled?: boolean;
  onChange: (product?: uploadAdmin.IGetItemDetailResponse) => void;
}) => {
  const [searchLoading, setSearchLoading] = useState(false);
  const [productId, setProductId] = useState<number | undefined>(value);
  const [preProductId, setPreProductId] = useState<number | undefined>(value);
  const [modalVisible, setModalVisible] = useState(false);
  const [detail, setDetail] = useState<uploadAdmin.IGetItemDetailResponse | null>(null);

  const searchProduct = useCallback(async () => {
    setSearchLoading(true);
    try {
      const detail = await getKitDetail({
        itemId: productId!,
      });
      if (detail?.itemId) {
        setDetail(detail);
        setModalVisible(true);
      } else {
        onChange?.({
          itemId: productId,
        });
      }
    } finally {
      setSearchLoading(false);
    }
  }, [productId]);

  const handleModalOk = useCallback(() => {
    if (detail) {
      onChange(detail);
      setPreProductId(detail.itemId);
    }
    setModalVisible(false);
  }, [detail, onChange]);

  const handleModalCancel = useCallback(() => {
    // 取消时恢复原来填写的product id
    preProductId && setProductId(preProductId);
    setModalVisible(false);
  }, [preProductId]);

  useEffect(() => {
    setProductId(value!);
  }, [value]);

  return (
    <>
      <Card title='Informal Kit'>
        <div className={style.informalKitDes}>Inform the product ID of the kit you want to migrate to a formal kit</div>
        <div className={cx(style.inline, style.search)}>
          {/*@ts-expect-error*/}
          <InputNumber disabled={disabled} value={productId} onChange={setProductId} />
          <Button
            disabled={disabled || !productId}
            loading={searchLoading}
            type='primary'
            icon={<SearchOutlined />}
            onClick={searchProduct}
          >
            Search
          </Button>
        </div>
      </Card>

      <Modal
        width={700}
        title="Is this product you are looking for?"
        open={modalVisible}
        onOk={handleModalOk}
        onCancel={handleModalCancel}
        okText="Confirm"
        cancelText="Cancel"
      >
        {detail && (
          <div>
            <div className={style.title}>Product Name</div>
            <div className={style.description}>{detail.productName}</div>
            <div className={style.title}>Description</div>
            <div className={style.description}>
              <ProductDescriptionEditor
                bizId="1001"
                env={getEnvironment()}
                region={getCountry()}
                descriptionType={detail.descriptionType as any}
                description={detail.descriptionType === 'json' ? JSON.stringify({ field_list: JSON.parse(detail.description || '{}') }) : detail.description}
                locale="en"
                placeholder="Please enter product description characters or add Images"
                itemId={detail.itemId}
                shopId={detail.shopId}
                disabled={true}
                isDraft={false}
                listingToggle={{
                  szDescriptionSupportImage: detail.descriptionType === 'json'
                }}
                lockInfo={{}}
                productConstraints={{
                  descriptionImageAspectRatioMin: 0.5,
                  descriptionImageAspectRatioMax: 32,
                } as any}
              />
            </div>

            <div className={style.title}>Product Image</div>
            <div className={style.description}>
              <Images
                images={detail.productImages?.images}
                productLongImages={detail.productImages?.productLongImages}
              />
            </div>
          </div>
        )}
      </Modal>
    </>
  );
};

export default InformKit;
