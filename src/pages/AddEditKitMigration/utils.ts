import { formatCurrency } from 'src/utils/currency';
import { formatNumber } from 'src/utils/number-format';

/**
 * 计算Item价格 （当所有model price一致时，展示单一价格，当价格不一致时展示价格区间 RPXXX - RPXXX
 */
export function computedDisplayPrice(priceDetail: any = {}): string {
  const { priceMin, priceMax } = priceDetail;
  if (!priceMin || !priceMax) {
    return '-';
  }
  if (priceMin === priceMax) {
    return transformPriceRange(priceMin);
  }
  return `${transformPriceRange(priceMin)} - ${transformPriceRange(priceMax)}`;
}

// 默认会返回带货币符号的当前站点的格式化字符串
// 传入country字段会返回带货币符号的对应地区的格式化字符串
export function transformPriceRange(range: string[] | number[] | number | string, country?: string): string {
  const options: any = { hasCurrencySymbol: true, separator: true };
  if (country) {
    options.country = country;
  }

  if (Array.isArray(range)) {
    if (range.length === 1) {
      return formatCurrency(range[0], options);
    } if (range.length === 2) {
      return `${formatCurrency(range[0], options)} - ${formatCurrency(range[1], options)}`;
    }
    return '';
  }
  return formatCurrency(range, options);
}


export function formatStockNumber(stock: number): string | undefined | null {
  return formatNumber(
    stock,
    { compactNumber: true, precision: 0, compactStartFrom: 999 },
  );
}
