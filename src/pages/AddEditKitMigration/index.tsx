import { getCountry, navigate, useRoute } from '@classification/admin-solution';
import { ProductDescriptionEditor } from '@listing/ls-upload-cmpt-sdk-react';
import { getEnvironment } from 'admin-upload-common';
import { useMount } from 'ahooks';
import { Affix, Button, Descriptions, Empty, Input, message, Spin, Upload } from 'antd';
import type { FormInstance } from 'antd/lib/form';
import cx from 'classnames';
import React, { useCallback, useMemo, useRef, useState } from 'react';

import { getItemModelList } from 'src/api/uploadAdmin';
import type {
  uploadAdmin
} from 'src/api/uploadAdmin/uploadAdmin';
import {
  editKitMigrateTask,
  getKitMigrateTaskInfo,
  submitKitMigrateTask,
} from 'src/api/uploadBffAdmin';
import type { IVirtualSKUModelInfo } from 'src/api/uploadBffAdmin/types';
import type { uploadBffAdmin } from 'src/api/uploadBffAdmin/uploadBffAdmin';
import KitComponentTable from 'src/components/KitComponentTable';
import { Routes } from 'src/config/routes';
import { KitMigrationDetailType } from 'src/constants/kit-migration';
import { formatItemDetail } from 'src/utils/kit-migration';
import Card from './components/Card';
import Images from './components/Images';
import InformKit from './components/InformKit';
import style from './style.module.scss';

const AddEditKitMigration = () => {
  const { params } = useRoute();
  const { type: viewType, taskId, shopId, productId } = params;
  const [loadingSubmit, setLoadingSubmit] = useState(false);
  const [productInfo, setProductInfo] = useState<uploadAdmin.IGetItemDetailResponse>();
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState(false);
  const tableRef = useRef<FormInstance>(null);
  const [kitComponents, setKitComponents] = useState<IVirtualSKUModelInfo[]>([]);
  const [migrateInfo, setMigrateInfo] =
    useState<uploadBffAdmin.IGetKitMigrateTaskInfoResponseData>();

  const formatProductDetail = useMemo(() => {
    if (!productInfo?.productName) {
      return;
    }
    return formatItemDetail(productInfo);
  }, [productInfo]);

  const isEdit = useMemo(() => {
    return viewType === KitMigrationDetailType.EDIT && !!taskId;
  }, [viewType]);

  const isView = useMemo(() => {
    return viewType === KitMigrationDetailType.VIEW && !!taskId;
  }, [viewType]);

  const onProductChange = useCallback(async (value: uploadAdmin.IGetItemDetailResponse) => {
    setProductInfo(value);
    const components: IVirtualSKUModelInfo[] = [];
    if (value.productName) {
      const res = await getItemModelList({
        shopId: value?.shopId,
        itemId: value?.itemId,
        region: getCountry(),
        status: undefined,
        needLink: false,
      });
      if (res?.modelList) {
        const tierVariationOne = value.tierVariationList?.[0];
        res.modelList.forEach((item) => {
          const tierIndexOne = item?.tierIndex?.[0]!;
          const imageId = tierVariationOne?.images?.[tierIndexOne];
          components.push({
            id: item.modelId,
            imageId,
            name: item.modelName,
            price: `${(item.originalPrice || 0) / 100000}`,
            sku: item.sku,
          })
        });
      }
    }

    setKitComponents(components);
    tableRef.current?.setFieldsValue({ kitComponents: components });
  }, []);
  const handleSubmit = useCallback(async () => {
    try {
      setLoadingSubmit(true);
      await tableRef.current?.validateFields();
      const values = tableRef.current?.getFieldsValue()?.kitComponents as IVirtualSKUModelInfo[];
      const modelList: uploadBffAdmin.IVirtualSKUModelInfo[] = [];
      values?.forEach(item => {
        modelList.push({
          id: item.id,
          componentSkuList: item.componentSkuList?.map(model => ({
            mpskuModelId: model.mpskuModelId,
            mpskuItemId: model.mpskuItemId,
            quantity: model.quantity,
            mainSku: !!model.mainSku,
          }))
        });
      });
      if (isEdit) {
        const res = await editKitMigrateTask({
          taskId: taskId as unknown as number,
          productId: migrateInfo?.productId,
          shopId: productInfo?.shopId,
          vskuSetting: {
            modelList,
          },
        });
        if (res.code === 0) {
          navigate(Routes.KIT_MIGRATION_LIST_PAGE);
        }
        return;
      }
      const createRequest: uploadBffAdmin.ISubmitKitMigrateTaskRequest = {
        productId: productInfo?.itemId,
        shopId: productInfo?.shopId,
        vskuSetting: {
          modelList,
        },
      };
      const res = await submitKitMigrateTask(createRequest);
      if (res.code === 0) {
        navigate(Routes.KIT_MIGRATION_LIST_PAGE);
      }
    } catch (e) {
      console.log(e);
    } finally {
      setLoadingSubmit(false);
    }
  }, [productInfo, migrateInfo, isEdit]);

  useMount(async () => {
    try {
      setLoading(true);
      if (
        [KitMigrationDetailType.EDIT, KitMigrationDetailType.VIEW].includes(
          viewType as KitMigrationDetailType
        ) &&
        taskId
      ) {
        const res = await getKitMigrateTaskInfo({
          shopId: parseInt(shopId as string),
          taskId: taskId as unknown as number,
          productId: parseInt(productId as string),
        });
        if (!res.data?.productId) {
          message.error('Kit migration task not found');
          setError(true);
          return;
        }
        const { name, descriptionInfo, images, longImages, videoList } = res.data.vskuSetting || {};
        let description = descriptionInfo?.descriptionType === 'json' ? '{ field_list: [] }' : '';
        try {
          const fieldList = JSON.parse(descriptionInfo?.description ?? '')?.field_list;
          if (Array.isArray(fieldList)) {
            description = JSON.stringify(fieldList);
          }
        } catch {
          description = descriptionInfo?.description  ?? '';
        }
        setProductInfo({
          productName: name,
          shopId: parseInt(shopId as string),
          itemId: res.data?.productId,
          description,
          descriptionType: descriptionInfo?.descriptionType,
          productImages: {
            images,
            productLongImages: longImages,
          },
          videoInfoList: videoList,
        });
        setMigrateInfo(res.data);
        const { stdTierVariationList } = res.data.vskuSetting || {};
        const stdTierVariationOne = stdTierVariationList?.[0];
        const stdTierVariationTwo = stdTierVariationList?.[1];
        setKitComponents(
          res.data.vskuSetting?.modelList?.map(item => {
            const tierIndexOne = item?.tierIndex?.[0]!;
            const tierIndexTwo = item?.tierIndex?.[1]!;
            const imageId = stdTierVariationOne?.valueList?.[tierIndexOne]?.imageId;
            const oneTierName = (stdTierVariationOne?.valueList?.[tierIndexOne]?.customValue || '');
            const tweTierName = stdTierVariationTwo?.valueList?.[tierIndexTwo]?.customValue;
            return {
              ...item,
              imageId,
              name: (oneTierName + (tweTierName ? `,${tweTierName}` : '')),
            };
          }) || [],
        );
      }
    } catch (error) {
      setError(true);
    } finally {
      setLoading(false);
    }
  });

  if (loading) {
    return <Spin className={style.loading} />;
  }

  if (error) {
    return <Empty description='Failed to load data' />;
  }

  return (
    <>
      <div className={style.container}>
        <InformKit
          disabled={isView || isEdit}
          value={productInfo?.itemId}
          onChange={onProductChange}
        />
        {formatProductDetail ? (
          <Card title='New Kit'>
            <Descriptions size='small' layout={'vertical'} column={1}>
              <Descriptions.Item className={style.item} label='Kit Name'>
                <Input value={formatProductDetail?.name} disabled />
              </Descriptions.Item>
              <Descriptions.Item
                label='Kit Description'
                className={cx(style.kitDescription, style.item)}
              >
                <ProductDescriptionEditor
                  bizId='1001'
                  env={getEnvironment()}
                  region={getCountry()}
                  descriptionType={formatProductDetail.descriptionType as any}
                  description={formatProductDetail.descriptionType === 'json' ? JSON.stringify({ field_list: JSON.parse(formatProductDetail.description || '{}') }) : formatProductDetail.description}
                  locale='en'
                  placeholder='Please enter product description characters or add Images'
                  itemId={formatProductDetail.itemId}
                  shopId={formatProductDetail.shopId}
                  disabled={true}
                  isDraft={false}
                  listingToggle={{
                    szDescriptionSupportImage: formatProductDetail.descriptionType === 'json',
                  }}
                  lockInfo={{}}
                  productConstraints={
                    {
                      descriptionImageAspectRatioMin: 0.5,
                      descriptionImageAspectRatioMax: 32,
                    } as any
                  }
                />
              </Descriptions.Item>
              <Descriptions.Item className={style.item} label='Product Images'>
                <Images
                  images={formatProductDetail?.images}
                  productLongImages={formatProductDetail?.productLongImages}
                />
              </Descriptions.Item>
              {formatProductDetail?.videos?.length && (
                <Descriptions.Item className={style.item} label='Product Video'>
                  <Upload listType='picture-card' fileList={formatProductDetail?.videos} />
                </Descriptions.Item>
              )}
              {kitComponents?.length ? (
                <Descriptions.Item className={style.item} label='Kit Components'>
                  <KitComponentTable
                    shopId={productInfo?.shopId}
                    itemId={productInfo?.itemId}
                    type={isView ? 'view' : 'edit'}
                    ref={tableRef}
                    kitComponents={kitComponents}
                    onChange={setKitComponents}
                  />
                </Descriptions.Item>
              ) : null}
            </Descriptions>
          </Card>
        ) : null}
      </div>
      <Affix offsetBottom={0}>
        <div className={style.footer}>
          <Button
            onClick={() => {
              navigate(Routes.KIT_MIGRATION_LIST_PAGE);
            }}
          >
            Discard
          </Button>
          {isView ? null : (
            <Button disabled={!productInfo?.productName} loading={loadingSubmit} onClick={handleSubmit} type='primary'>
              Submit
            </Button>
          )}
        </div>
      </Affix>
    </>
  );
};

export default AddEditKitMigration;
