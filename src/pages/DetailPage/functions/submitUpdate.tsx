import { convertToBENumber } from 'admin-upload-common';
import type { FormInstance } from 'antd/lib/form/Form';
import moment from 'moment';

import type { uploadAdmin } from 'src/api/uploadAdmin/uploadAdmin';
import { ItemAttributeType } from '../../../api/PBData/constants';
import { updateItemBasicInfo } from '../../../api/uploadAdmin';
import * as uploadAdminConstant from '../../../api/uploadAdmin/constants';
import { maxCatLevel,regionsWithTax } from '../../../constants';
import type { ImageListsType } from '../../../typings';
import getDisplayName from '../components/InfoTab/components/ProductDetails/utils/localName';

const { TIMESTAMP_TYPE } = ItemAttributeType;

const {
  AttrDateTimeFormat,
  AttrInputType,
  AttrInputValidatorType,
  AttrFormatType,
} = uploadAdminConstant;
interface SubmitArgs {
  itemDetail: uploadAdmin.IGetItemDetailResponse;
  region: string;
  form: FormInstance;
  images: ImageListsType;
  itemModelList: uploadAdmin.IItemModel[];
  editModeDataSource: uploadAdmin.IProductAttr[];
  editModeGlobalDataSource:
  | uploadAdmin.IGlobalProductAttrList
  | undefined;
  isDefaultDTSPrompted: boolean;
  productAttributeData?: uploadAdmin.IGetProductAttributesResponse;
  globalProductAttributeData?: uploadAdmin.IGetGlobalProductAttributesResponse;
}

export default function submitUpdate(args: SubmitArgs) {
  // If either global or local cats need to change, send both as BE may need to check
  // with either
  let shouldSetCats = false;
  const isModelLevelDts = args.itemModelList.some(m=>m.modelDtsSetting);
  const catListToSet: number[] | undefined = [];
  for (let i = 1; i <= maxCatLevel; i++) {
    if (args.form.getFieldValue(`l${i}_category`)) {
      catListToSet.push(args.form.getFieldValue(`l${i}_category`));
    }
  }
  if (catListToSet.length > 0) {
    if (
      !args.itemDetail?.categoryList ||
      catListToSet.length !== args.itemDetail?.categoryList.length ||
      catListToSet.some(
        (cat, idx) => cat !== args.itemDetail?.categoryList?.[idx].catId,
      )
    ) {
      shouldSetCats = true;
    }
  } else if (
    !args.itemDetail ||
    (args.itemDetail.categoryList && args.itemDetail?.categoryList.length !== 0)
  ) {
    shouldSetCats = true;
  }

  const globalCatListToSet: number[] | undefined = [];
  for (let i = 1; i <= maxCatLevel; i++) {
    if (args.form.getFieldValue(`l${i}_global_category`)) {
      globalCatListToSet.push(args.form.getFieldValue(`l${i}_global_category`));
    }
  }
  if (globalCatListToSet.length > 0) {
    if (
      !args.itemDetail?.globalCategoryList ||
      globalCatListToSet.length !==
        args.itemDetail?.globalCategoryList.length ||
      globalCatListToSet.some(
        (cat, idx) => cat !== args.itemDetail?.globalCategoryList?.[idx].catId,
      )
    ) {
      shouldSetCats = true;
    }
  } else if (
    !args.itemDetail ||
    (args.itemDetail.globalCategoryList &&
      args.itemDetail?.globalCategoryList.length !== 0)
  ) {
    shouldSetCats = true;
  }

  const shouldSetAttrSnapshotSetting =
    // has at least one different value
    args.productAttributeData?.attrSnapshotList &&
    args.productAttributeData?.attrSnapshotList.find(
      (snapshot) =>
        snapshot.value !==
        (snapshot.attribute?.attrType === TIMESTAMP_TYPE
          ? moment(
            args.form.getFieldValue(
              `attribute${snapshot.attribute?.attrId}_value`,
            ),
          ).format('X')
          : args.form.getFieldValue(
            `attribute${snapshot.attribute?.attrId}_value`,
          )),
    );

  const attrSnapshotSettingToSet:
  | uploadAdmin.IAttributeSnapshotSetting
  | undefined = shouldSetAttrSnapshotSetting
    ? {
      attrValues: args.editModeDataSource.map((attrValue) => {
        return {
          attrId: attrValue.attrId,
          value:
              attrValue.attrType === TIMESTAMP_TYPE
                ? moment(
                  args.form.getFieldValue(
                    `attribute${attrValue.attrId}_value`,
                  ),
                ).format('X')
                : args.form.getFieldValue(`attribute${attrValue.attrId}_value`),
        };
      }),
      attrModelId: args.productAttributeData?.productAttrModel?.modelId,
    }
    : undefined;

  // if there was no snapshot before, or if snapshot has differing values from form,
  // or if an attribute has a user value and attribute was not in snapshot
  const shouldSetGlobalAttrSnapshotSetting =
    // no existing snapshot
    !args.globalProductAttributeData?.attrSnapshotList ||
    // snapshot has no same value from form
    !args.globalProductAttributeData?.attrSnapshotList?.some((snapshot) => {
      let formValue = args.form.getFieldValue(
        `global_attribute${snapshot.attrId}_value`,
      );

      // special handling for free text + unit
      if (
        args.form.getFieldValue(`global_attribute${snapshot.attrId}_value_unit`)
      ) {
        formValue =
          formValue +
          args.form.getFieldValue(
            `global_attribute${snapshot.attrId}_value_unit`,
          );
      }
      return (
        snapshot.attrValue === formValue ||
        (Array.isArray(formValue) && formValue.includes(snapshot.attrValue))
      );
    }) ||
    args.globalProductAttributeData.productAttrList?.globalAttributes?.some(
      (attr) => {
        let formValue = args.form.getFieldValue(
          `global_attribute${attr.attrId}_value`,
        );

        // special handling for free text + unit
        if (
          formValue &&
          attr.inputType === AttrInputType.FREE_TEXT_FILED &&
          attr.attrFormatType === AttrFormatType.FORMAT_QUANTITATIVE_WITH_UNIT
        ) {
          formValue =
            formValue +
            args.form.getFieldValue(
              `global_attribute${attr.attrId}_value_unit`,
            );
        }

        // form has values not in snapshot (for multi)
        if (Array.isArray(formValue)) {
          if (
            !formValue.some((value) =>
              args.globalProductAttributeData?.attrSnapshotList?.some(
                (snapshotAttr) => value === snapshotAttr.attrValue,
              ),
            )
          ) {
            return true;
          }
        }
        return (
          // form value for the attribute, but no value for this attribute in snapshot
          formValue &&
          (!Array.isArray(formValue) || formValue.length > 0) &&
          !args.globalProductAttributeData?.attrSnapshotList?.some(
            (snapshotAttr) => snapshotAttr === attr.attrId,
          )
        );
      },
    ) ||
    // has snapshot, but new one has no value
    (args.globalProductAttributeData?.attrSnapshotList &&
      !args.globalProductAttributeData.productAttrList?.globalAttributes?.some(
        (attr) => {
          args.form.getFieldValue(`global_attribute${attr.attrId}_value`);
        },
      ));

  const globalAttrSnapshotSettingToSet:
  | uploadAdmin.IGlobalAttributeSnapshotSetting
  | undefined = shouldSetGlobalAttrSnapshotSetting
    ? {
      attrValueSettingList: args.editModeGlobalDataSource?.globalAttributes
          // child attributes without linked parent values should not be added
          ?.filter(
            (attr) =>
              !attr.isChild ||
              // if there is a mapping in which parent attr value in the mapping
              // has been selected
              args.editModeGlobalDataSource?.parentChildMappingAttrs?.some(
                (mapping) => {
                  if (mapping.childAttrId !== attr.attrId) {
                    return false;
                  }

                  const parentAttr = args.editModeGlobalDataSource?.globalAttributes?.find(
                    (globalAttr) => globalAttr.attrId === mapping.parentAttrId,
                  );
                  const parentValue = parentAttr?.attrValueList?.find(
                    (value) =>
                      value.value ===
                      (parentAttr.validateType ===
                      AttrInputValidatorType.VALIDATOR_DATE
                        ? moment(
                          args.form.getFieldValue(
                            `global_attribute${mapping.parentAttrId}_value`,
                          ),
                            parentAttr.attrExtInfo?.datetimeFormat ===
                              AttrDateTimeFormat.YEAR_MONTH_DATE
                              ? 'DD/MM/YYYY'
                              : 'MM/YYYY',
                        ).format('X')
                        : args.form.getFieldValue(
                          `global_attribute${mapping.parentAttrId}_value`,
                        )),
                  );

                  if (parentValue?.id === mapping.parentValueId) {
                    return true;
                  } else {
                    return false;
                  }
                },
              ),
          )
          .reduce(
            (
              result: uploadAdmin.IGlobalAttributeValueSetting[],
              attr: uploadAdmin.IGlobalProductAttr,
            ) => {
              const formValue = args.form.getFieldValue(
                `global_attribute${attr.attrId}_value`,
              );

              if (formValue == null) {
                return result;
              }

              let valuesArray = [];
              if (Array.isArray(formValue)) {
                valuesArray = formValue;
              } else {
                valuesArray = [formValue];
              }

              // remove the empty string from the value list to avoid subsequent formatting errors
              valuesArray = valuesArray.filter((item) => item !== '');

              // go through every value, and push to result
              // NOTE: attributes with multi values will be pushed multiple times,
              // once for each value
              for (const currValue of valuesArray) {
                const valueString =
                  attr.validateType === AttrInputValidatorType.VALIDATOR_DATE
                    ? moment(
                      currValue,
                        attr.attrExtInfo?.datetimeFormat ===
                          AttrDateTimeFormat.YEAR_MONTH_DATE
                          ? 'DD/MM/YYYY'
                          : 'MM/YYYY',
                    ).format('X')
                    : currValue;
                const value = attr.attrValueList?.find((v) =>
                  attr.validateType === AttrInputValidatorType.VALIDATOR_DATE
                    ? v.value === valueString
                    : getDisplayName(
                        v.attrDefaultLangValue?.val || '',
                        v.value || '',
                    ) === valueString,
                );
                if (value || valueString) {
                  result.push({
                    attrId: attr.attrId,
                    attrValueId: value ? value.id : 0,
                    attrValExtInfo: !value
                      ? attr.attrFormatType ===
                        AttrFormatType.FORMAT_QUANTITATIVE_WITH_UNIT
                        ? attr.inputType === AttrInputType.FREE_TEXT_FILED
                          ? {
                            // unit handling in free text uses 2 forms, value and unit
                            rawVal: valueString.toString(),
                            unit: args.form.getFieldValue(
                              `global_attribute${attr.attrId}_value_unit`,
                            ),
                          }
                          : {
                            // unit handling in combobox is 1 form, `${value}${unit}`
                            rawVal: parseFloat(valueString).toString(),
                            unit: valueString.replace(/[\d+.?\d*]/g, ''),
                          }
                        : { customisedValue: valueString }
                      : undefined,
                  });
                }
              }

              return result;
            },
            [],
          ),
    }
    : undefined;

  const isGlobalAttrSnapshotSettingChanged = (
    globalAttrSnapshotSettingToSet:
    | uploadAdmin.IGlobalAttributeSnapshotSetting
    | undefined,
  ) => {
    const allAttributesMap: Map<
    number,
    uploadAdmin.IGlobalProductAttr
    > = new Map<
    number,
    uploadAdmin.IGlobalProductAttr
    >();
    args.globalProductAttributeData?.productAttrList?.globalAttributes?.forEach(
      (attr) => allAttributesMap.set(attr.attrId as number, attr),
    );
    const attributesFormMap: Map<number, Array<string | undefined>> = new Map<
    number,
    Array<string | undefined>
    >();
    globalAttrSnapshotSettingToSet?.attrValueSettingList?.forEach((attr) => {
      const attrId = attr.attrId;
      const attrInfo = allAttributesMap.get(attrId as number);
      let valueStr = '';
      if (attr.attrValueId) {
        if (attrInfo) {
          valueStr =
            attrInfo.attrValueList?.find((v) => v.id === attr.attrValueId)
              ?.value || '';
        }
      } else {
        if (attr.attrValExtInfo?.customisedValue) {
          valueStr = attr.attrValExtInfo?.customisedValue;
        } else {
          valueStr = `${attr.attrValExtInfo?.rawVal || ''}${attr.attrValExtInfo
            ?.unit || ''}`;
        }
      }
      if (
        attrInfo &&
        attrInfo.validateType === AttrInputValidatorType.VALIDATOR_DATE
      ) {
        const dateFormat =
          attrInfo.validateType === AttrInputValidatorType.VALIDATOR_DATE
            ? attrInfo.attrExtInfo?.datetimeFormat ===
              AttrDateTimeFormat.YEAR_MONTH_DATE
              ? 'DD/MM/YYYY'
              : attrInfo.attrExtInfo?.datetimeFormat ===
                AttrDateTimeFormat.YEAR_MONTH
                ? 'MM/YYYY'
                : undefined
            : undefined;
        valueStr = moment.unix(parseInt(valueStr)).format(dateFormat);
      }
      if (attributesFormMap.has(attrId as number)) {
        // @ts-expect-error
        attributesFormMap.get(attrId).push(valueStr);
      } else {
        attributesFormMap.set(attrId as number, [valueStr]);
      }
    });
    const attributesSnapshotMap: Map<
    number,
    Array<string | undefined>
    > = new Map<number, Array<string | undefined>>();
    args.globalProductAttributeData?.attrSnapshotList?.forEach((attr) => {
      const id = attr.attrId as number;
      if (attributesSnapshotMap.has(id)) {
        // @ts-expect-error
        attributesSnapshotMap.get(id).push(attr.attrValue);
      } else {
        attributesSnapshotMap.set(id, [attr.attrValue]);
      }
    });
    if (attributesFormMap.size !== attributesSnapshotMap.size) return true;
    for (const [attrId, value] of attributesFormMap) {
      if (!attributesSnapshotMap.has(attrId)) return true;
      const formValues = value.sort();
      // @ts-expect-error
      const snapshotValues = attributesSnapshotMap.get(attrId).sort();
      if (formValues.length !== snapshotValues.length) return true;
      for (let i = 0; i < formValues.length; ++i) {
        if (formValues[i] != snapshotValues[i]) return true;
      }
    }
    return false;
  };

  let modelSettingListToSet:
  | uploadAdmin.IModelSettingList
  | undefined = {
    modelSettings: [] as uploadAdmin.IModelSetting[],
  };
  if (args.itemModelList) {
    args.itemModelList
      .filter(
        (model) => {
          const getModelFieldValue =(field: string)=> args.form.getFieldValue(`model${model.modelId}_${field}`)
         return model.modelName !==
            args.form.getFieldValue(`model${model.modelId}_model_name`) ||
          (regionsWithTax.includes(args.region)
            ? args.form.getFieldValue(
              `model${model.modelId}_original_price_before_tax`,
            ) == null ||
              model.originalPriceBeforeTax !==
                convertToBENumber(
                  parseFloat(
                    args.form
                      .getFieldValue(
                        `model${model.modelId}_original_price_before_tax`,
                      )
                      ?.replaceAll(',', ''),
                  ),
                )
            : args.form.getFieldValue(`model${model.modelId}_original_price`) ==
                null ||
              model.originalPrice !==
                convertToBENumber(
                  parseFloat(
                    args.form
                      .getFieldValue(`model${model.modelId}_original_price`)
                      ?.replaceAll(',', ''),
                  ),
                )) ||
          (args.form.getFieldValue(`model${model.modelId}_stock`) != null &&
            model.stock !==
              parseInt(
                args.form.getFieldValue(`model${model.modelId}_stock`),
              )) ||
          (args.form.getFieldValue(`model${model.modelId}_gtin`) != null &&
            model.gtin !== args.form.getFieldValue(`model${model.modelId}_gtin`)) ||
          !!Object.keys(args.form.getFieldValue(['modelStock', model.modelId!]) || {})?.length ||
          (args.form.getFieldValue([`model${model.modelId}_dts_setting`,'dayToShip']) != null &&
          model.modelDtsSetting?.dayToShip !== args.form.getFieldValue([`model${model.modelId}_dts_setting`,'dayToShip'])) ||
          (getModelFieldValue('weight') !== null && model.weight !== convertToBENumber(getModelFieldValue('weight'))) ||
          Object.keys(getModelFieldValue('dimension')).some(k => getModelFieldValue('dimension')[k] !== null && model.dimension?.[k as 'height'] !==  convertToBENumber(getModelFieldValue('dimension')[k]))
        }
      )
      .map((model) => {
        const getModelFieldValue =(field: string)=> args.form.getFieldValue(`model${model.modelId}_${field}`);
        const currModelStock = args.form.getFieldValue(['modelStock', model.modelId!]);
        modelSettingListToSet?.modelSettings?.push({
          itemId: args.itemDetail.itemId,
          modelId: model.modelId,
          name:
            !args.itemDetail?.hasTierVariation &&
            model.modelName !==
              args.form.getFieldValue(`model${model.modelId}_model_name`)
              ? args.form.getFieldValue(`model${model.modelId}_model_name`)
              : undefined,
          normalInputPrice: regionsWithTax.includes(args.region)
            ? args.form.getFieldValue(
              `model${model.modelId}_original_price_before_tax`,
            ) == null ||
              model.originalPriceBeforeTax !==
                convertToBENumber(
                  parseFloat(
                    args.form
                      .getFieldValue(
                        `model${model.modelId}_original_price_before_tax`,
                      )
                      ?.replaceAll(',', ''),
                  ),
                )
              ? convertToBENumber(
                parseFloat(
                    args.form
                      .getFieldValue(
                        `model${model.modelId}_original_price_before_tax`,
                      )
                      ?.replaceAll(',', ''),
                ),
              )
              : undefined
            : args.form.getFieldValue(`model${model.modelId}_original_price`) ==
                null ||
              model.originalPrice !==
                convertToBENumber(
                  parseFloat(
                    args.form
                      .getFieldValue(`model${model.modelId}_original_price`)
                      ?.replaceAll(',', ''),
                  ),
                )
              ? convertToBENumber(
                parseFloat(
                  args.form
                    .getFieldValue(`model${model.modelId}_original_price`)
                    ?.replaceAll(',', ''),
                ),
              )
              : undefined,
          normalStock:
            model.stock !==
            parseInt(args.form.getFieldValue(`model${model.modelId}_stock`))
              ? parseInt(args.form.getFieldValue(`model${model.modelId}_stock`))
              : undefined,
          gtin:
            model.gtin !== args.form.getFieldValue(`model${model.modelId}_gtin`)
              ? args.form.getFieldValue(`model${model.modelId}_gtin`)
              : undefined,
          stockSettingList: currModelStock ? Object.keys(currModelStock)?.map(locationId => ({
            locationId,
            sellableStock: currModelStock[locationId]
          })) : undefined,
          modelDtsSetting: args.form.getFieldValue([`model${model.modelId}_dts_setting`,'dayToShip']) !== model.modelDtsSetting?.dayToShip ? args.form.getFieldValue(`model${model.modelId}_dts_setting`) : undefined,
          weight: model.weight !== convertToBENumber(getModelFieldValue('weight')) ? convertToBENumber(getModelFieldValue('weight')): undefined,
          dimension: Object.keys(getModelFieldValue('dimension')).some(k => model.dimension?.[k as 'height'] !==  convertToBENumber(getModelFieldValue('dimension')[k]))
          ? Object.keys(getModelFieldValue('dimension')).reduce((d,k)=> {
            d[k] = convertToBENumber(getModelFieldValue('dimension')[k])
            return d;
          }, {} as any)
          : undefined
        });
      });
  }
  if (modelSettingListToSet.modelSettings?.length === 0) {
    modelSettingListToSet = undefined;
  }

  const shouldSetVariationList = args.itemDetail?.tierVariationList?.some(
    (variation, variationNumber) =>
      variation.options?.some(
        (option, idx) =>
          args.form.getFieldValue(`option_${variationNumber}_${idx}`) != null &&
          option !==
            args.form.getFieldValue(`option_${variationNumber}_${idx}`),
      ),
  );
  const descriptionInfo = () => {
    const description =
      args.form.getFieldValue('description') != null &&
      args.form.getFieldValue('description') !== args.itemDetail?.description
        ? args.form.getFieldValue('description')
        : undefined;
    const descriptionType =
      description === undefined ? undefined : args.itemDetail?.descriptionType;
    return { description, descriptionType };
  };

  const installmentSetting = () => {
    const enabledTenures: string = args.form.getFieldValue('enabledTenures');
    if (!['TH', 'MY'].includes(args.region)) {
      return;
    }
    // shop level 未开启
    if (!args.itemDetail.itemInstallmentTenure?.shopLevelEnabled) {
      return;
    }

    // off => off
    if (!args.itemDetail.itemInstallmentTenure?.itemLevelEnabled && !enabledTenures.length) {
      return;
    }

    // 分期数未改变
    if (args.itemDetail.itemInstallmentTenure?.tenures?.join() === enabledTenures) {
      return;
    }

    return {
      enableInstallment: !!enabledTenures,
      enabledTenures: enabledTenures ? enabledTenures.split(',').map(Number) : []
    };
  };
  return updateItemBasicInfo({
    shopId: args.itemDetail.shopId,
    itemId: args.itemDetail.itemId,
    region: args.region,
    auditParam: undefined,
    daysToShip:
      !isModelLevelDts
        ? args.form.getFieldValue('daysToShip') != null &&
          parseInt(args.form.getFieldValue('daysToShip')) !==
          args.itemDetail?.daysToShip
          ? parseInt(args.form.getFieldValue('daysToShip'))
          : undefined
        : undefined,
    weight:
      args.form.getFieldValue('weight') != null &&
      convertToBENumber(args.form.getFieldValue('weight')) !==
        args.itemDetail?.weight
        ? convertToBENumber(args.form.getFieldValue('weight'))
        : undefined,
    dimensions:
      (args.form.getFieldValue('width') != null &&
        convertToBENumber(args.form.getFieldValue('width')) !=
          args.itemDetail?.dimensions?.width) ||
      (args.form.getFieldValue('length') != null &&
        convertToBENumber(args.form.getFieldValue('length')) !=
          args.itemDetail?.dimensions?.length) ||
      (args.form.getFieldValue('height') != null &&
        convertToBENumber(args.form.getFieldValue('height')) !=
          args.itemDetail?.dimensions?.height)
        ? {
          width: args.form.getFieldValue('width')
            ? convertToBENumber(args.form.getFieldValue('width'))
            : undefined,
          length: args.form.getFieldValue('length')
            ? convertToBENumber(args.form.getFieldValue('length'))
            : undefined,
          height: args.form.getFieldValue('height')
            ? convertToBENumber(args.form.getFieldValue('height'))
            : undefined,
          unit: args.itemDetail.dimensions?.unit,
        }
        : undefined,
    catList: shouldSetCats ? { catIds: catListToSet } : undefined,
    attrSnapshotSetting: attrSnapshotSettingToSet,
    productName:
      args.form.getFieldValue('productName') != null &&
      args.form.getFieldValue('productName') !== args.itemDetail?.productName
        ? args.form.getFieldValue('productName')
        : undefined,
    ...descriptionInfo(),
    condition:
      args.form.getFieldValue('condition') != null &&
      args.form.getFieldValue('condition') !== args.itemDetail?.condition
        ? args.form.getFieldValue('condition')
        : undefined,
    brand:
      args.form.getFieldValue('brand') != null &&
      args.form.getFieldValue('brand') !== args.itemDetail?.brand
        ? args.form.getFieldValue('brand')
        : undefined,
    productImages:
      (args.images.productImages &&
        (args.images.productImages.find(
          (image) => image.response?.data?.imageId,
        ) ||
          args.images.productImages.length !==
            args.itemDetail?.productImages?.images?.length)) ||
      args.images.productImages.some(
        (image, idx) =>
          image.uid !== args.itemDetail?.productImages?.images![idx],
      )
        ? {
          images: args.images.productImages
            .filter((image) => image.status === 'done')
            .map(
              (image) =>
                  image.response?.data?.imageId
                    ? image.response.data.imageId // newly added
                    : (image.uid.split('__')?.[0]), // existing
            ),
        }
        : args.images.productImages.length === 0 &&
          args.itemDetail?.productImages?.images &&
          args.itemDetail.productImages.images.length > 0
          ? {
            images: [],
          }
          : undefined,
    deleteSizeChart: args.form.getFieldValue('deleteSizeChart')
      ? args.form.getFieldValue('deleteSizeChart')
      : undefined,
    inputPrice: regionsWithTax.includes(args.region)
      ? args.form.getFieldValue('original_price_before_tax') != null &&
        convertToBENumber(
          parseFloat(
            args.form
              .getFieldValue('originalPriceBeforeTax')
              ?.replaceAll(',', ''),
          ),
        ) !== args.itemDetail?.originalPriceBeforeTax
        ? convertToBENumber(
          parseFloat(
            args.form
              .getFieldValue('originalPriceBeforeTax')
              .replaceAll(',', ''),
          ),
        )
        : undefined
      : args.form.getFieldValue('original_price') != null &&
        convertToBENumber(
          parseFloat(
            args.form.getFieldValue('original_price')?.replaceAll(',', ''),
          ),
        ) !== args.itemDetail?.originalPrice
        ? convertToBENumber(
          parseFloat(
            args.form.getFieldValue('original_price')?.replaceAll(',', ''),
          ),
        )
        : undefined,
    stock:
      args.form.getFieldValue('stock') != null &&
      parseInt(args.form.getFieldValue('stock')) !== args.itemDetail?.stock
        ? parseInt(args.form.getFieldValue('stock'))
        : undefined,
    gtin:
      args.form.getFieldValue('gtin') != null &&
      args.form.getFieldValue('gtin') !== args.itemDetail?.gtin
        ? args.form.getFieldValue('gtin')
        : undefined,
    variationList: shouldSetVariationList
      ? {
        variations: args.itemDetail?.tierVariationList
          ? args.itemDetail?.tierVariationList.map(
                (variation, variationNumber) => {
                  return {
                    options: variation.options?.map((option, idx) =>
                      args.form.getFieldValue(
                        `option_${variationNumber}_${idx}`,
                      )
                        ? args.form.getFieldValue(
                          `option_${variationNumber}_${idx}`,
                        )
                        : undefined,
                    ),
                  };
                },
              )
          : [],
      }
      : undefined,
    modelSettingList: modelSettingListToSet,
    whiteBackgroundImage:
      args.images.whiteBackgroundImages &&
      args.images.whiteBackgroundImages.length > 0
        ? args.images.whiteBackgroundImages[0].response?.data?.imageId &&
          args.images.whiteBackgroundImages[0].status === 'done'
          ? args.images.whiteBackgroundImages[0].response.data.imageId
          : // existing white background image, do not send
          undefined
        : args.itemDetail?.whiteBackgroundImage
          ? // removed image
          ''
          : // no image originally and no image aded
          undefined,
    isCounterfeit:
      args.form.getFieldValue('isCounterfeit') != null &&
      args.form.getFieldValue('isCounterfeit') !==
        args.itemDetail?.isCounterfeit
        ? args.form.getFieldValue('isCounterfeit')
        : undefined,
    seoDescription:
      args.form.getFieldValue('seoDescription') != null &&
      args.form.getFieldValue('seoDescription') !==
        args.itemDetail?.seoDescription
        ? args.form.getFieldValue('seoDescription')
        : undefined,
    nonSearchable:
      args.form.getFieldValue('nonSearchable') != null &&
      args.form.getFieldValue('nonSearchable') !==
        args.itemDetail?.nonSearchable
        ? args.form.getFieldValue('nonSearchable')
        : undefined,
    isHidden:
      args.form.getFieldValue('isHidden') != null &&
      args.form.getFieldValue('isHidden') !== args.itemDetail?.isHidden
        ? args.form.getFieldValue('isHidden')
        : undefined,
    isUnlisted:
      args.form.getFieldValue('isUnlisted') != null &&
      args.form.getFieldValue('isUnlisted') !== args.itemDetail?.isUnlisted
        ? args.form.getFieldValue('isUnlisted')
        : undefined,
    isNonrestock:
      args.form.getFieldValue('restock') != null &&
      args.form.getFieldValue('restock') !== args.itemDetail?.restock
        ? args.form.getFieldValue('restock')
          ? false
          : true
        : undefined,
    checkOnlyForCats:
      !isModelLevelDts
        ? args.form.getFieldValue('isPreOrder') && shouldSetCats && !args.isDefaultDTSPrompted
          ? true
          : undefined
        : undefined,
    isPreOrder:
      !isModelLevelDts
        ? args.form.getFieldValue('isPreOrder') != null &&
        // dts改变也需要提交isPreOrder
          (args.form.getFieldValue('isPreOrder') !== args.itemDetail?.isPreOrder || args.form.getFieldValue('daysToShip') !== args.itemDetail?.daysToShip)
          ? args.form.getFieldValue('isPreOrder')
          : undefined
        : undefined,

    globalCatList: shouldSetCats ? { catIds: globalCatListToSet } : undefined,
    globalBrandId:
      args.form.getFieldValue('globalBrandId') != null &&
      // globalBrandId will be set 0(No Brand) when it's undefined, but there is no change.
      !(
        args.form.getFieldValue('globalBrandId') === 0 &&
        args.itemDetail?.globalBrandId === undefined
      ) &&
      args.form.getFieldValue('globalBrandId') !==
        args.itemDetail?.globalBrandId
        ? args.form.getFieldValue('globalBrandId')
        : undefined,
    globalAttrSnapshotSetting: isGlobalAttrSnapshotSettingChanged(
      globalAttrSnapshotSettingToSet,
    )
      ? globalAttrSnapshotSettingToSet
      : undefined,
    dangerousGoods:
      args.form.getFieldValue('dangerousGoods') != null &&
      (args.form.getFieldValue('dangerousGoods') !==
        args.itemDetail?.dangerousGoods || args.form.getFieldValue('dangerousCategorizationByShopee') !==
        args.itemDetail?.dangerousCategorizationByShopee)
        ? args.form.getFieldValue('dangerousGoods')
        : undefined,
    dangerousCategorizationByShopee:
      args.form.getFieldValue('dangerousCategorizationByShopee') != null &&
      args.form.getFieldValue('dangerousCategorizationByShopee') !==
        args.itemDetail?.dangerousCategorizationByShopee
        ? args.form.getFieldValue('dangerousCategorizationByShopee')
        : undefined,
    minPurchaseLimit:
      args.form.getFieldValue('minPurchaseLimit') != null &&
      parseInt(args.form.getFieldValue('minPurchaseLimit')) !==
        args.itemDetail?.minPurchaseLimit
        ? parseInt(args.form.getFieldValue('minPurchaseLimit'))
        : undefined,
    taxType:
      args.form.getFieldValue('taxType') != null &&
      args.form.getFieldValue('taxType') !== args.itemDetail?.taxType
        ? args.form.getFieldValue('taxType')
        : undefined,
    installmentSetting: installmentSetting()
  });
}
