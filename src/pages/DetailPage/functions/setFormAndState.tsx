import type { FormInstance } from 'antd/lib/form';
import type { Dispatch } from 'react';
import * as uuid from 'uuid';

import type { uploadAdmin } from 'src/api/uploadAdmin/uploadAdmin';
import {
  FEtoBEMultiplier,
  imageUrlPrefix,
  v0videoServerPrefix,
  v1v2videoServerPrefix,
} from 'src/constants';
import type { ImageListsType } from 'src/typings';
import { formatCurrencyForItem } from 'src/utils';

interface SetFormAndStateArgs {
  itemDetail: uploadAdmin.IGetItemDetailResponse;
  region: string;
  form: FormInstance;
  setImages: Dispatch<ImageListsType>;
  setVariationList: Dispatch<
  uploadAdmin.ITierVariation[]
  >;
}

export const getV0videoCdn = (region: string) => {
  return (
    v0videoServerPrefix[region as keyof typeof v0videoServerPrefix] ??
    v0videoServerPrefix.SG
  );
};

export const getV1v2videoCdn = (region: string) => {
  return (
    v1v2videoServerPrefix[region as keyof typeof v1v2videoServerPrefix] ??
    v1v2videoServerPrefix.SG
  );
};

// sets form values and state objects based on data received
export default function setFormAndState(args: SetFormAndStateArgs) {
  args.form.setFieldsValue({
    isCounterfeit: args.itemDetail.isCounterfeit,
    seoDescription: args.itemDetail.seoDescription,
    nonSearchable: args.itemDetail.nonSearchable,
    isHidden: args.itemDetail.isHidden,
    restock: args.itemDetail.restock,
    isUnlisted: args.itemDetail.isUnlisted,
    productName: args.itemDetail.productName,
    // Categories are set in sub component instead of here
    brand: args.itemDetail.brand,
    originalPrice:
      args.itemDetail.originalPrice != null
        ? formatCurrencyForItem(
          args.region,
          args.itemDetail.originalPrice / FEtoBEMultiplier,
        )
        : undefined,
    originalPriceBeforeTax:
      args.itemDetail.originalPriceBeforeTax != null
        ? formatCurrencyForItem(
          args.region,
          args.itemDetail.originalPriceBeforeTax / FEtoBEMultiplier,
        )
        : undefined,
    daysToShip: args.itemDetail.daysToShip,
    isPreOrder: args.itemDetail.isPreOrder,
    stock: args.itemDetail.stock,
    weight:
      args.itemDetail.weight != null
        ? args.itemDetail.weight / FEtoBEMultiplier
        : undefined,
    width:
      args.itemDetail.dimensions?.width != null
        ? args.itemDetail.dimensions.width / FEtoBEMultiplier
        : undefined,
    length:
      args.itemDetail.dimensions?.length != null
        ? args.itemDetail.dimensions.length / FEtoBEMultiplier
        : undefined,
    height:
      args.itemDetail.dimensions?.height != null
        ? args.itemDetail.dimensions.height / FEtoBEMultiplier
        : undefined,
    condition: args.itemDetail.condition,
    gtin: args.itemDetail.gtin,
    promotionType: args.itemDetail.promotionType,
    description: args.itemDetail.description,
    // Default: no brand
    globalBrandId: args.itemDetail.globalBrandId
      ? args.itemDetail.globalBrandId
      : 0,
    dangerousGoods: args.itemDetail.dangerousGoods ? 1 : 0, //0(means not) and 1(means yes)
    dangerousCategorizationByShopee: args.itemDetail.dangerousCategorizationByShopee,
    minPurchaseLimit: args.itemDetail.minPurchaseLimit,
    taxType: args.itemDetail.taxType,
    enabledTenures: args.itemDetail.itemInstallmentTenure?.shopLevelEnabled
      ? args.itemDetail.itemInstallmentTenure?.tenures?.sort((a, b) => a - b).join(',') ?? ''
      : ''
  });
  args.setImages({
    productImages: args.itemDetail.productImages?.images
      ? args.itemDetail.productImages.images.map(
        (image: string, idx: number) => {
          return {
            uid: `${image  }__${  uuid.v4()}`, // same image will have differnt uid
            name: args.itemDetail.productImages?.options
              ? args.itemDetail.productImages.options[idx]
              : '',
            status: 'done',
            url: imageUrlPrefix + image,
            thumbUrl: `${imageUrlPrefix}${image}_tn`,
            size: 600,
            type: 'image/png',
          };
        },
      )
      : [],
    productLongImages: args.itemDetail.productImages?.productLongImages?.map(
      (image: string, idx: number) => {
        return {
          uid: image,
          name: '',
          url: imageUrlPrefix + image,
          thumbUrl: `${imageUrlPrefix}${image}_tn`,
        };
      },
    )
    ?? [],
    productVideos: args.itemDetail.videoInfoList
      ? args.itemDetail.videoInfoList.map(
        (video: uploadAdmin.IVideoInfo) => {
          return {
            uid: video.videoId!,
            name: video.url!,
            status: 'done',
            url:
                video.version === 1 || video.version === 2
                  ? `${getV1v2videoCdn(args.region)}${video.url}`
                  : `${getV0videoCdn(args.region)}${video.url}`,
            thumbUrl: `${imageUrlPrefix}${video.thumbUrl}_tn`,
            size: 600,
            type: 'video/mp4',
          };
        },
      )
      : [],
    variationImages: args.itemDetail.variationImages?.images
      ? args.itemDetail.variationImages.images.map(
        (image: string, idx: number) => {
          return {
            uid: image,
            name: args.itemDetail.variationImages?.options
              ? args.itemDetail.variationImages.options[idx]
              : '',
            status: 'done',
            url: imageUrlPrefix + image,
            thumbUrl: `${imageUrlPrefix}${image}_tn`,
            size: 600,
            type: 'image/png',
          };
        },
      )
      : [],
    whiteBackgroundImages: args.itemDetail.whiteBackgroundImage
      ? [
        {
          uid: args.itemDetail.whiteBackgroundImage,
          name: 'White Background Image',
          status: 'done',
          url: imageUrlPrefix + args.itemDetail.whiteBackgroundImage,
          thumbUrl: `${imageUrlPrefix}${args.itemDetail.whiteBackgroundImage}_tn`,
          size: 600,
          type: 'image/png',
        },
      ]
      : [],
    sizeChart: args.itemDetail.sizeChart
      ? [
        {
          uid: args.itemDetail.sizeChart,
          name: 'Size Chart',
          status: 'done',
          url: imageUrlPrefix + args.itemDetail.sizeChart,
          thumbUrl: `${imageUrlPrefix}${args.itemDetail.sizeChart}_tn`,
          size: 600,
          type: 'image/png',
        },
      ]
      : [],
  });
  args.setVariationList(args?.itemDetail?.tierVariationList!);
  args.itemDetail.tierVariationList &&
    args.itemDetail.tierVariationList.map((variation, variationNumber) => {
      variation.options?.map((option, idx) => {
        args.form.setFieldsValue({
          [`option_${variationNumber}_${idx}`]: option,
        });
      });
    });
}
