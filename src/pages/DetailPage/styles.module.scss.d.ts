declare const classNames: {
  readonly condensedForm: "condensedForm";
  readonly antFormItem: "antFormItem";
  readonly content: "content";
  readonly antInput: "antInput";
  readonly antSelectDisabled: "antSelectDisabled";
  readonly antSelectSelectionItem: "antSelectSelectionItem";
  readonly antRadioWrapperDisabled: "antRadioWrapperDisabled";
  readonly padding: "padding";
  readonly paddedCard: "paddedCard";
  readonly page: "page";
  readonly header: "header";
  readonly title: "title";
  readonly subtitle: "subtitle";
  readonly card: "card";
  readonly center: "center";
  readonly table: "table";
  readonly anticonFilter: "anticonFilter";
  readonly reduceCardPaddingTop: "reduceCardPaddingTop";
  readonly antCardBody: "antCardBody";
  readonly flexboxContainer: "flexboxContainer";
  readonly flexwrapContainer: "flexwrapContainer";
  readonly formValue: "formValue";
  readonly label: "label";
  readonly shortFormLabel: "shortFormLabel";
  readonly tooltip: "tooltip";
  readonly longImageBox: "longImageBox";
  readonly antUploadListPictureCardContainer: "antUploadListPictureCardContainer";
  readonly antUploadDraggableListItem: "antUploadDraggableListItem";
  readonly antUploadListItemThumbnail: "antUploadListItemThumbnail";
  readonly imageUploadBox: "imageUploadBox";
  readonly headerTag: "headerTag";
  readonly infoIcon: "infoIcon";
  readonly detailCard: "detailCard";
  readonly antCard: "antCard";
  readonly antSelect: "antSelect";
};
export = classNames;
