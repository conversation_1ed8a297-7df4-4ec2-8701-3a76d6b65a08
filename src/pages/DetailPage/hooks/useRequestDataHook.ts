import { hasOwnProperty } from 'admin-upload-common';
import { useRequest } from 'ahooks';
import { message } from 'antd';
import React from 'react';

import {
  getItemDetail,
  getItemListByItemIds,
} from 'src/api/uploadAdmin';
import { Routes } from '../../../config/routes';

export default function useRequestItemDetailHook<T extends object>(
  routeId: string,
  routeParam: T | undefined,
  region: string,
  needFetchItemInfo: boolean,
) {
  const [shopId, setShopId] = React.useState(0);
  const [itemId, setItemId] = React.useState(0);
  React.useEffect(() => {
    let shopId = 0;
    let itemId = 0;
    if (routeParam && hasOwnProperty(routeParam, 'shopId')) {
      shopId = Number(routeParam.shopId);
    }
    if (routeParam && hasOwnProperty(routeParam, 'itemId')) {
      itemId = Number(routeParam.itemId);
    }

    if (!shopId) {
      shopId = Number(routeId.split('/')[4]);
    }
    if (!itemId) {
      itemId = Number(routeId.split('/')[5]);
    }

    if (needFetchItemInfo) {
      const getShopAndRegion = async () => {
        const response = await getItemListByItemIds({
          itemIds: [Number(itemId)],
          needDeleted: true,
          needModels: true,
          ignoreExtinfo: true,
          needAllFeCats: true,
          ignoreDescription: true,
          needOverlayImage: true,
          needCustomizedTaxInfo: true,
        });
        if (
          response &&
          response.items &&
          response.items[0] &&
          response.items[0].country &&
          response.items[0].shopid
        ) {
          const realRegion = response.items[0].country;
          shopId = response.items[0].shopid;
          location.href = `${Routes.EDIT_PAGE}?shopId=${shopId}&itemId=${itemId}&region=${realRegion}`;
        } else {
          message.error('item not found');
        }
      };
      getShopAndRegion();
    } else {
      setShopId(shopId);
      setItemId(itemId);
    }
  }, []);

  const { data, loading, runAsync: run } = useRequest(
    async () => {
      if (shopId && itemId) {
        const res = await getItemDetail({
          shopId: Number(shopId),
          itemId: Number(itemId),
          region: region,
        });
        return res;
      }
    },

    { refreshDeps: [shopId, itemId], onSuccess() {} },
  );

  return {
    data,
    loading,
    run,
    itemId,
    shopId,
  };
}
