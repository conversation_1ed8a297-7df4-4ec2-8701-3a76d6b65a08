$formLabelWidth: 25%;

.condensed-form {
  :global {
    .ant-form-item {
      margin-bottom: 8px;
    }
  }
}

.content {
  background-color: #f0f2f5;
  height: calc(100vh - 56px);
  padding: 16px 24px;

  :global {
    .ant-input[disabled] {
      color: #666;
    }

    .ant-select-disabled .ant-select-selection-item {
      color: #666;
    }

    .ant-radio-wrapper-disabled {
      input,
      span {
        color: #666;
      }
    }
  }
}

.padding {
  padding: 16px;
}

.page {
  @extend .padding;
}

.header {
  align-items: center;
  display: flex;
  padding: 20px 0;
}

.title {
  color: rgba(0, 0, 0, 0.87);
  flex: 1;
  font: 500 18px /22px sans-serif;
}

.subtitle {
  color: rgba(0, 0, 0, 0.87);
  font: 500 14px /16px sans-serif;
}

.card {
  background-color: white;
}

.paddedCard {
  @extend .card;
  @extend .padding;
}

button + button {
  margin-left: 12px;
}

.center {
  left: 50%;
  position: absolute;
  top: 50%;
  transform: translate(-50%, -50%);
}

.table {
  :global {
    .anticon-filter {
      top: 50% !important;
    }
  }
}

.reduceCardPaddingTop {
  :global {
    .ant-card-body {
      padding: 8px 24px 24px 24px !important;
    }
  }
}

.flexboxContainer {
  display: flex;
  flex-direction: row;

  :global(.ant-form-item) {
    margin-right: 10px;
  }
}

.flexwrapContainer {
  display: flex;
  flex-wrap: wrap;
}

.formValue {
  width: calc(100% - #{$formLabelWidth});
}

.label {
  color: rgba(0, 0, 0, 0.85);
  width: $formLabelWidth;
}

.shortFormLabel {
  color: rgba(0, 0, 0, 0.85);
  width: 15%;
}

.tooltip {
  color: #1890ff;
  padding-left: 4px;
}

.longImageBox {
  :global(.ant-upload-list-picture-card-container) {
    height: calc(86px * 4 / 3 + 16px);
  }

  > * {
    :global(.ant-upload-draggable-list-item) {
      width: 100%;
      height: 100%;
    }

    :global(.ant-upload-list-item-thumbnail) {
      font-size: 12px;
    }
  }
}

.imageUploadBox {
  > * {
    :global(.ant-upload-draggable-list-item) {
      width: 100%;
      height: 100%;
    }

    :global(.ant-upload-list-item-thumbnail) {
      font-size: 12px;
    }
  }
}

.headerTag {
  font-size: 14px;
}

.info-icon {
  color: #1890ff;
  font-size: 12px;
  padding-right: 4px;
}

.detailCard {
  :global(.ant-card) {
    margin: 12px 0 12px 0;

    :global(.ant-form-item) {
      margin: 0;
    }
  }
}

:global(.ant-select) {
  width: 100%;
}