import type { FormInstance } from 'antd/lib/form/Form';
import React from 'react';

import type { uploadAdmin } from 'src/api/uploadAdmin/uploadAdmin';
import Description from './components/Description';
import ProductName from './components/ProductName';
import VariationName from './components/VariationName';

export interface Props {
  itemDetail: uploadAdmin.IGetItemDetailResponse;
  showVariation: boolean;
  form: FormInstance;
  isEditing?: boolean;
  translationInfo?: uploadAdmin.ITranslationInfo;
  langs: uploadAdmin.ILanguageBasicInfo[];
}

export default function MultiLangTab(props: Props) {
  return (
    <div>
      <ProductName {...props} />
      {props.showVariation && <VariationName {...props} />}
      <Description {...props} />
    </div>
  );
}
