import { Form,Input, Table } from 'antd';
import React, { useMemo } from 'react';

import type { Props } from '..';
import style from '../style.module.scss';

export default function ProductName(props: Props) {
  const nameMap = useMemo(() => {
    return (
      props.translationInfo?.basicInfoList?.reduce((map, e) => {
        map[e.lang as string] = e.name as string;
        return map;
      }, {} as { [x: string]: string }) ?? {}
    );
  }, [props.translationInfo]);

  const columns = useMemo(() => {
    return [
      {
        title: 'Origin content',
        type: 'origin',
      },
      ...props.langs?.map((lang) => {
        return {
          title: lang.displayName,
          type: lang.language,
        };
      }),
    ].map((col) => {
      return {
        ...col,
        onCell: () => {
          const { type } = col;
          const isOrigin = col.type === 'origin';

          return {
            type,
            dataIndex: `productName.${  type}`,
            title: col.title,
            defaultValue: isOrigin
              ? props.itemDetail.productName
              : nameMap[type as string],
          };
        },
      };
    });
  }, [props.langs]);

  return (
    <div className={style.container}>
      <h3>Products name</h3>
      <Form form={props.form} component={false}>
        <Table
          className={style.table}
          pagination={false}
          dataSource={[{}]}
          columns={columns}
          components={{
            body: {
              cell: (opt: {
                title: string;
                type: string;
                dataIndex: number;
                defaultValue: string;
              }) => {
                return opt.type === 'origin' ? (
                  <td>
                    <Input disabled={true} defaultValue={opt.defaultValue} />
                  </td>
                ) : (
                  <td>
                    <Form.Item
                      noStyle
                      name={opt.dataIndex}
                      initialValue={opt.defaultValue}
                      rules={[
                        {
                          required: true,
                          message: `Please input ${opt.title} product name!`,
                        },
                      ]}
                    >
                      <Input disabled={!props.isEditing} />
                    </Form.Item>
                  </td>
                );
              },
            },
          }}
        />
      </Form>
    </div>
  );
}
