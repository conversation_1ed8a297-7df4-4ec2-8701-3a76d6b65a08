import { Form,Input } from 'antd';
import React, { useMemo } from 'react';

import type { Props } from '..';
import style from '../style.module.scss';

export default function Description(props: Props) {
  const descMap = useMemo(() => {
    return (props.translationInfo?.descInfoList?.reduce(
      (
        map: { [x: string]: string },
        e: { lang: string; description: string },
      ) => {
        map[e.lang] = e.description;
        return map;
      },
      {} as { [x: string]: string },
    ) ?? {}) as { [x: string]: string };
  }, [props.translationInfo]);

  return (
    <div className={style.container}>
      <h3>Description</h3>
      <div className={style.subContainer}>
        <div className={style.subtitle}>Origin content</div>
        <Input.TextArea
          disabled={true}
          style={{ resize: 'none' }}
          autoSize={{ minRows: 5, maxRows: 10 }}
          defaultValue={props.itemDetail.description}
        />
      </div>

      <Form form={props.form} component={false}>
        {props.langs.map((lang, i) => {
          return (
            <div key={lang.language} className={style.subContainer}>
              <div className={style.subtitle}>{lang.displayName}</div>
              <Form.Item
                noStyle
                name={`description.${  lang.language}`}
                initialValue={descMap[lang.language as string]}
                rules={[
                  {
                    required: true,
                    message: `Please input ${lang.displayName} description!`,
                  },
                ]}
              >
                <Input.TextArea
                  disabled={!props.isEditing}
                  style={{ resize: 'none' }}
                  autoSize={{ minRows: 5, maxRows: 10 }}
                />
              </Form.Item>
            </div>
          );
        })}
      </Form>
    </div>
  );
}
