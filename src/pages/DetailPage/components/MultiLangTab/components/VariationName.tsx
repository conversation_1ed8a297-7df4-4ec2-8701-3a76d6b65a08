import { Form,Input, Table } from 'antd';
import React, { useMemo } from 'react';

import type { Props } from '..';
import style from '../style.module.scss';

interface Row {
  varIdx: number;
  optIdx?: number;
}

export default function VariationName(props: Props) {
  const variationMap = useMemo(() => {
    return (
      props.translationInfo?.basicInfoList?.reduce(
        (map, e) => {
          map[e.lang as string] = e.tierVariationList as any;
          return map;
        },
        {} as {
          [lang: string]: {
            name: string;
            options: string[];
          }[];
        },
      ) ?? {}
    );
  }, [props.translationInfo]);

  const rows = useMemo(() => {
    const ret = [] as Row[];

    props.itemDetail;

    const list = props.itemDetail.tierVariationList;

    if (!list?.length) return ret;

    list.forEach((v, i) => {
      ret.push({
        varIdx: i,
      });

      v.options?.forEach((opt, j) => {
        ret.push({
          varIdx: i,
          optIdx: j,
        });
      });
    });

    return ret;
  }, [props.translationInfo]);

  const columns = useMemo(() => {
    return [
      {
        title: 'Field',
        type: 'field',
      },
      {
        title: 'Origin content',
        type: 'origin',
      },
      ...props.langs?.map((lang) => {
        return {
          title: lang.displayName,
          type: lang.language,
        };
      }),
    ].map((col) => {
      const isOrigin = col.type === 'origin';
      const isField = col.type === 'field';

      return {
        ...col,
        onCell: (row: Row) => {
          const { type } = col;

          const variations = variationMap[type as string] ?? [];
          const { varIdx, optIdx } = row;

          const variation = variations[varIdx] ?? {};
          const variationName = variations[varIdx]?.name;

          const isVariation = optIdx == null;

          let defaultValue;
          let dataIndex;
          if (isField) {
            defaultValue = isVariation
              ? `Variation - ${varIdx + 1}`
              : `Option - ${(optIdx as number) + 1}`;
          } else if (isOrigin) {
            const v = props.itemDetail?.tierVariationList?.[varIdx]; // itemDetail 中原始variation
            defaultValue = isVariation ? v?.name : v?.options?.[optIdx as number];
          } else {
            // 可编辑字段
            if (isVariation) {
              defaultValue = variationName;
              dataIndex = `variation.${varIdx}.${  type}`;
            } else {
              defaultValue = variation.options?.[optIdx as number] ?? '';
              dataIndex = `variation.${varIdx}.${optIdx}.${  type}`;
            }
          }

          return {
            type,
            dataIndex,
            title: col.title,
            defaultValue: defaultValue,
            varIdx,
            optIdx,
            isOrigin,
            isField,
          };
        },
      };
    });
  }, [props.langs]);

  return (
    <div className={style.container}>
      <h3>Variation name</h3>
      <Form form={props.form} component={false}>
        <Table
          className={style.table}
          pagination={false}
          dataSource={rows}
          columns={columns}
          rowClassName={(row, i) => {
            return i !== 0 && row.optIdx == null ? style.variationRow : '';
          }}
          components={{
            body: {
              cell: (opt: {
                title: string;
                type: string;
                isField: boolean;
                isOrigin: boolean;
                defaultValue: string;
                dataIndex: number;
                varIdx: number;
                optIdx: number;
              }) => {
                if (opt.isField) {
                  return <td>{opt.defaultValue}</td>;
                }

                if (opt.isOrigin) {
                  return (
                    <td>
                      <Input disabled={true} defaultValue={opt.defaultValue} />
                    </td>
                  );
                }

                let message = `Please input ${
                  opt.title
                } in variation ${opt.varIdx + 1}`;

                if (opt.optIdx != null) {
                  message += `, options ${opt.optIdx + 1}!`;
                }

                return (
                  <td>
                    <Form.Item
                      noStyle
                      name={opt.dataIndex}
                      initialValue={opt.defaultValue}
                      rules={[
                        {
                          required: true,
                          message,
                        },
                      ]}
                    >
                      <Input disabled={!props.isEditing} />
                    </Form.Item>
                  </td>
                );
              },
            },
          }}
        />
      </Form>
    </div>
  );
}
