import { Tabs } from 'antd';
import React from 'react';
import { QueryClient, QueryClientProvider } from 'react-query';

import styles from '../../styles.module.scss';
import CentralizedQCLogs from './components/CentralizedQCLogs';
import ItemInfoAuditLog from './components/ItemInfoAuditLog';
import MQCLogs from './components/MQCLogs';
import PriceAuditLog from './components/PriceAuditLogPlaceholder';
import RQCLogs from './components/RQCLogs';
import StockAuditLog from './components/StockAuditLog';
import TopicalQCLogs from './components/TopicalQCLogs';

const queryClient = new QueryClient({
  defaultOptions: {
    queries: {
      retry: false,
    },
  },
});

interface LogTabProps {
  itemId: number;
  region: string;
}

function LogTab(props: LogTabProps) {

  return (
    <QueryClientProvider client={queryClient}>
      <div className={styles.card}>
        <Tabs
          defaultActiveKey="1"
          className={styles.card}
          style={{ height: '80vh', overflowY: 'scroll' }}
          items={[{
            key: '1',
            label: 'Item Info Audit Log',
            children: <ItemInfoAuditLog itemId={props.itemId} region={props.region} />
          }, {
            key: '2',
            label: 'Price Audit Log',
            children: <PriceAuditLog itemId={props.itemId} region={props.region} />
          }, {
            key: '3',
            label: 'Stock Audit Log',
            children: <StockAuditLog itemId={props.itemId} />
          }]}
        />
        <Tabs
          defaultActiveKey="1"
          className={styles.card}
          style={{ height: '80vh', overflowY: 'scroll' }}
          items={[{
            key: '1',
            label: 'Rule QC Review Logs',
            children: <RQCLogs itemId={props.itemId} region={props.region} />
          }, {
            key: '2',
            label: 'Model QC Review Logs',
            children: <MQCLogs itemId={props.itemId} />
          }, {
            key: '3',
            label: 'Centralized QC Review Logs',
            children: <CentralizedQCLogs itemId={props.itemId} region={props.region} />
          }, {
            key: '4',
            label: 'Topical QC Review Logs',
            children: <TopicalQCLogs itemId={props.itemId} region={props.region} />
          }]}
        />
      </div>
    </QueryClientProvider>
  );
}

export default LogTab;
