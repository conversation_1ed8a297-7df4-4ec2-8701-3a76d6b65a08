import { convertToDisplayTime,usePaginationHook  } from 'admin-upload-common';
import { Table } from 'antd';
import type { ColumnsType } from 'antd/lib/table';
import React from 'react';

import type { uploadAdmin } from 'src/api/uploadAdmin/uploadAdmin';
import { qcActionString, qcLevelString } from 'src/constants';
import { useGetMQCLog } from 'src/hooks/getLogData';
import type { QcAction, QcLevel } from 'src/typings';

interface MQCLogsProps {
  itemId: number;
}

function MQCLogs(props: MQCLogsProps) {
  const { page, pageSize, changePage, changePageSize } = usePaginationHook({
    initialValue: { page: 1, pageSize: 5 },
    scrollOnPageChange: true,
  });
  const { data, isLoading } = useGetMQCLog(props.itemId, pageSize, page - 1);
  const columns: ColumnsType<uploadAdmin.IModelQCLog> = [
    {
      title: 'Review Log ID',
      dataIndex: 'logId',
      width: '10%',
    },
    {
      title: 'Create Time',
      dataIndex: 'createTime',
      width: '12%',
      render: (value) => {
        return convertToDisplayTime(value);
      },
    },
    {
      title: 'Update Time',
      dataIndex: 'updateTime',
      width: '12%',
      render: (value) => {
        return convertToDisplayTime(value);
      },
    },
    {
      title: 'Reviewer',
      dataIndex: 'reviewer',
      width: '15%',
    },
    {
      title: 'Action',
      dataIndex: 'action',
      width: '11%',
      render: (value) => qcActionString[value as QcAction],
    },
    {
      title: 'Fail Reason',
      dataIndex: 'failReason',
      width: '11%',
    },
    {
      title: 'Model Name',
      dataIndex: 'modelName',
      width: '10%',
    },
    {
      title: 'QC level',
      dataIndex: 'qcLevel',
      width: '10%',
      render: (value) => qcLevelString[value as QcLevel],
    },
    {
      title: 'Remark',
      dataIndex: 'remark',
      width: '15%',
    },
  ];
  return (
    <>
      <Table
        dataSource={data?.logs ?? []}
        columns={columns}
        loading={isLoading}
        rowKey={'auditId'}
        pagination={{
          pageSizeOptions: [
            '5',
            '10',
            '15',
            '20',
            '25',
            '30',
            '35',
            '40',
            '45',
            '50',
          ],
          defaultPageSize: 5,
          showSizeChanger: true,
          current: page,
          pageSize: pageSize,
          total: (data?.logs?.length ?? 0) + (data?.hasMore ? 1 : 0),
          onChange: changePage,
          onShowSizeChange: (cur, size) => changePageSize(size),
        }}
      />
    </>
  );
}

export default MQCLogs;
