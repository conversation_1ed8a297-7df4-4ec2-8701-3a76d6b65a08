import { convertToDisplayTime,usePaginationHook  } from 'admin-upload-common';
import { Table } from 'antd';
import type { ColumnsType } from 'antd/lib/table';
import React from 'react';

import type { uploadAdmin } from 'src/api/uploadAdmin/uploadAdmin';
import {
  cqcQueueTypeString,
  qcActionString,
  qcAddOnActionString,
  qcLevelString,
} from 'src/constants';
import { useGetCqcLog } from 'src/hooks/getLogData';
import type { CqcQueueType,QcAction, QcAddOnAction, QcLevel } from 'src/typings';
import { getHtmlFromTemplate } from 'src/utils';
import styles from '../../styles.module.scss';

interface CentralizedQCLogProps {
  itemId: number;
  region: string;
}

const cellStyle = () => {
  const style = { borderRight: '1px solid #f0f0f0' };
  return {
    onCell: () => ({ style }),
    onHeaderCell: () => ({ style }),
  };
};

function CentralizedQCLogs(props: CentralizedQCLogProps) {
  const { page, pageSize, changePage, changePageSize } = usePaginationHook({
    initialValue: { page: 1, pageSize: 5 },
    scrollOnPageChange: true,
  });
  const { data, isLoading } = useGetCqcLog(
    props.itemId,
    pageSize,
    page - 1,
    props.region,
  );

  const queueResultColumns: ColumnsType<uploadAdmin.IFailQueueResult> = [
    {
      title: 'Review Log ID',
      dataIndex: 'reviewLogId',
      width: 130,
    },
    {
      title: 'Create Time',
      dataIndex: 'createTime',
      width: 130,
      render: (value) => {
        return convertToDisplayTime(value);
      },
    },
    {
      title: 'Update Time',
      dataIndex: 'updateTime',
      width: 130,
      render: (value) => {
        return convertToDisplayTime(value);
      },
    },
    {
      title: 'Reviewer',
      dataIndex: 'reviewer',
      width: 130,
    },
    {
      title: 'Queue Action',
      dataIndex: 'cqcAction',
      width: 180,
      render: (
        cqcAction: uploadAdmin.ICQCAction,
      ) => {
        return (
          <div style={{ width: 150 }}>
            <div>{qcActionString[cqcAction.action as QcAction]} </div>
            <div>Reason: {cqcAction.failedReason}</div>
            <div>Hint: {getHtmlFromTemplate(cqcAction?.hint || '')}</div>
          </div>
        );
      },
    },
    {
      title: 'Add-on Action',
      dataIndex: 'cqcAddOnAction',
      width: 180,
      render: (
        cqcAddOnAction: uploadAdmin.IQcAddOnAction,
      ) => {
        return cqcAddOnAction?.deboosts?.length ? (
          <div style={{ width: 150 }}>
            <div>
              {cqcAddOnAction?.deboosts
                ?.map((v: QcAddOnAction) => qcAddOnActionString[v])
                .filter((v) => v)
                .join(', ')}
            </div>
            <div>Reason: {cqcAddOnAction.failedReason}</div>
            <div>Hint: {getHtmlFromTemplate(cqcAddOnAction?.hint || '')}</div>
          </div>
        ) : (
          '-'
        );
      },
    },
    {
      title: 'QC queue',
      dataIndex: 'qcQueue',
      width: 130,
      render: (value) => cqcQueueTypeString[value as CqcQueueType],
    },
    {
      title: 'QC level',
      dataIndex: 'qcLevel',
      width: 130,
      render: (value) => qcLevelString[value as QcLevel],
    },
    {
      title: 'Remark',
      width: 130,
      render: (_, row) => {
        return (
          <>
            {row.remark && <div>{row.remark}</div>}
            {row.deboostRemark && <div>{row.deboostRemark}</div>}
          </>
        );
      },
    },
  ];

  const columns: ColumnsType<uploadAdmin.ICQCItemLog> = [
    {
      title: 'Review Event ID',
      dataIndex: 'reviewEventId',
      width: 140,
    },
    {
      title: 'Final action',
      dataIndex: 'finalAction',
      width: 140,
      ...cellStyle(),
      render: (val) => qcActionString[val as QcAction],
    },
    {
      title: (
        <div style={{ display: 'flex', justifyContent: 'space-around' }}>
          {queueResultColumns.map((item) => (
            <div>{item.title as string}</div>
          ))}
        </div>
      ),
      dataIndex: 'queueResult',
      onCell: () => ({
        style: {
          paddingLeft: 0,
          marginLeft: -33,
        },
      }),
      render: (
        queueResult: uploadAdmin.IFailQueueResult[] = [],
        row,
        idx,
      ) => {
        return (
          <Table
            className={styles.queueResultTable}
            style={{ marginTop: idx ? undefined : -55 }}
            rowKey={'reviewLogId'}
            columns={queueResultColumns.map((item) => ({
              ...item,
              onCell: () => ({ style: { overflowWrap: 'anywhere' } }),
            }))}
            dataSource={queueResult}
            pagination={false}
            showHeader={!idx}
            onHeaderRow={() => ({ style: { height: 50 } })}
          />
        );
      },
    },
  ];
  return (
    <>
      <Table
        dataSource={data?.logs ?? []}
        columns={columns}
        loading={isLoading}
        rowKey={'reviewEventId'}
        scroll={{ x: 'max-content' }}
        onHeaderRow={() => ({ style: { height: 50 } })}
        pagination={{
          pageSizeOptions: [
            '5',
            '10',
            '15',
            '20',
            '25',
            '30',
            '35',
            '40',
            '45',
            '50',
          ],
          defaultPageSize: 5,
          showSizeChanger: true,
          current: page,
          pageSize: pageSize,
          total: (data?.logs?.length ?? 0) + (data?.hasMore ? 1 : 0),
          onChange: changePage,
          onShowSizeChange: (cur, size) => changePageSize(size),
        }}
      />
    </>
  );
}

export default CentralizedQCLogs;
