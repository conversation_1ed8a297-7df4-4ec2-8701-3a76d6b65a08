import { convertToDisplayTime,usePaginationHook  } from 'admin-upload-common';
import { Table, Typography } from 'antd';
import type { ColumnsType } from 'antd/lib/table';
import moment from 'moment';
import React, { useState } from 'react';

import type { uploadAdmin } from 'src/api/uploadAdmin/uploadAdmin';
import { promotionTypeString, stockAuditStatusString } from 'src/constants';
import { useGetStockAuditLog } from '../../../../../../hooks/getLogData';
import { colonWithTrailingSpace } from '../../constants';
import styles from '../../styles.module.scss';
import { SearchBar } from './components/SearchBar';

const Text = Typography.Text;

interface StockAuditLogProps {
  itemId: number;
}

function StockAuditLog(props: StockAuditLogProps) {
  const { page, pageSize, changePage, changePageSize } = usePaginationHook({
    initialValue: { page: 1, pageSize: 60 },
    scrollOnPageChange: true,
  });
  const [filters, setFilters] = useState<
  uploadAdmin.IFilterCriteria
  >({
    source: undefined,
    ruleType: undefined,
    ruleId: undefined,
    modelId: undefined,
    isOrder: undefined,
  });
  const { data, loading, hasMore } = useGetStockAuditLog(
    props.itemId,
    pageSize,
    page - 1,
    filters,
  );
  const columns: ColumnsType<uploadAdmin.IStockAudit> = [
    {
      title: 'Update Time',
      dataIndex: 'createTime',
      width: '8%',
      render: (value) => {
        return moment(parseInt(value) * 1000).format('YYYY-MM-DD HH:mm:ss ZZ');
      },
    },
    {
      title: 'Model',
      dataIndex: 'modelId',
      width: '7%',
    },
    {
      title: 'Old Values',
      dataIndex: 'diffs',
      key: 'oldVal',
      width: '14%',
      render: (value, row) => {
        return row.auditDeltas?.map((row, idx) => {
          return (
            <div key={idx}>
              <Text className={styles.fieldName}>{row.name}</Text>
              {colonWithTrailingSpace}
              {row.name === 'status'
                ? `${row.old != null &&
                    stockAuditStatusString[
                      Number(row.old) as keyof typeof stockAuditStatusString
                    ]}`
                : row.name?.includes('time')
                  ? convertToDisplayTime(row.old, undefined, row.old)
                  : row.old}
            </div>
          );
        });
      },
    },
    {
      title: 'New Values',
      dataIndex: 'diffs',
      key: 'newVal',
      width: '14%',
      render: (value, row) => {
        return row.auditDeltas?.map((row, idx) => {
          return (
            <div key={idx}>
              <Text className={styles.fieldName}>{row.name}</Text>
              {colonWithTrailingSpace}
              {row.name === 'status'
                ? `${row.new != null &&
                    stockAuditStatusString[
                      Number(row.new) as keyof typeof stockAuditStatusString
                    ]}`
                : row.name?.includes('time')
                  ? convertToDisplayTime(row.new, undefined, row.new)
                  : row.new}
            </div>
          );
        });
      },
    },
    {
      title: 'Event',
      dataIndex: 'reason',
      width: '9%',
    },
    {
      title: 'Promotion ID',
      dataIndex: 'ruleId',
      width: '5%',
    },
    {
      title: 'Promotion Type',
      dataIndex: 'ruleType',
      render: (type) =>
        `${type} (${
          promotionTypeString[type as keyof typeof promotionTypeString]
        })`,
      width: '10%',
    },
    {
      title: 'Operator',
      dataIndex: 'operator',
      width: '10%',
    },
    {
      title: 'Source',
      dataIndex: 'source',
      width: '9%',
    },
    {
      title: 'Buy/Return Related',
      dataIndex: 'isOrder',
      render: (value) => (value ? 'Yes' : 'No'),
      width: '6%',
    },
    {
      title: 'Remark',
      key: 'remark',
      width: '8%',
    },
  ];
  return (
    <>
      <SearchBar
        searchValues={filters}
        setSearchValues={setFilters}
        resetPagination={() => {
          changePage(1);
        }}
      />
      <Table
        dataSource={
          data
            ? data.map((log, idx) => {
              return { ...log, key: idx };
            })
            : []
        }
        columns={columns}
        loading={loading}
        pagination={{
          pageSizeOptions: [
            '60',
            '120',
            '180',
            '240',
            '300',
            '360',
            '420',
            '480',
            '540',
            '600',
          ],
          defaultPageSize: 60,
          showSizeChanger: true,
          current: page,
          pageSize: pageSize,
          total: pageSize * page + (hasMore ? 1 : 0),
          onChange: changePage,
          onShowSizeChange: (cur, size) => changePageSize(size),
        }}
      />
    </>
  );
}

export default StockAuditLog;
