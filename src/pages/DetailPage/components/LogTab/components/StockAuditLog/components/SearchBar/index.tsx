import { Button, Form, Input, Select } from 'antd';
import type { Dispatch, SetStateAction } from 'react';
import React from 'react';

import type { uploadAdmin } from 'src/api/uploadAdmin/uploadAdmin';
import { integerRegex } from 'src/constants';
import styles from './styles.module.scss';

const { Option } = Select;

type SearchBarProps = {
  searchValues: uploadAdmin.IFilterCriteria;
  setSearchValues: Dispatch<
  SetStateAction<uploadAdmin.IFilterCriteria>
  >;
  resetPagination: ()=> void;
};

export function SearchBar(props: SearchBarProps) {
  const search = (
    values: uploadAdmin.IFilterCriteria,
  ) => {
    props.setSearchValues({
      source: values.source ? values.source : undefined,
      ruleType:
        String(values.ruleType) === '' || isNaN(Number(values.ruleType))
          ? undefined
          : Number(values.ruleType),
      modelId:
        String(values.modelId) === '' || isNaN(Number(values.modelId))
          ? undefined
          : Number(values.modelId),
      ruleId:
        String(values.ruleId) === '' || isNaN(Number(values.ruleId))
          ? undefined
          : Number(values.ruleId),
      isOrder:
        values.isOrder?.toString() === 'true'
          ? true
          : values.isOrder?.toString() === 'false'
            ? false
            : undefined,
    });
    props.resetPagination();
  };
  const reset = () => {
    props.setSearchValues({
      source: undefined,
      ruleType: undefined,
      modelId: undefined,
      ruleId: undefined,
      isOrder: undefined,
    });
    props.resetPagination();
  };
  return (
    <div className={styles.searchCard}>
      <Form
        layout="inline"
        colon={false}
        onFinish={search}
        fields={Object.keys(props.searchValues).map(
          (k: keyof typeof props.searchValues) =>
            k === 'isOrder'
              ? {
                name: [k],
                value:
                    props.searchValues[k] === true
                      ? 'true'
                      : props.searchValues[k] === false
                        ? 'false'
                        : undefined,
              }
              : {
                name: [k],
                value: props.searchValues[k],
              },
        )}
      >
        <Form.Item
          name="modelId"
          rules={[
            {
              pattern: integerRegex,
              message: 'Please enter only numeric values',
            },
          ]}
        >
          <Input placeholder="Search Model ID" />
        </Form.Item>
        <Form.Item
          name="ruleId"
          rules={[
            {
              pattern: integerRegex,
              message: 'Please enter only numeric values',
            },
          ]}
        >
          <Input placeholder="Search Promotion ID" />
        </Form.Item>
        <Form.Item
          name="ruleType"
          rules={[
            {
              pattern: integerRegex,
              message: 'Please enter only numeric values',
            },
          ]}
        >
          <Input placeholder="Search Promotion Type" />
        </Form.Item>
        <Form.Item name="source">
          <Input placeholder="Search Source" />
        </Form.Item>
        <Form.Item name="isOrder">
          <Select placeholder="Filter by Buy/Return Related">
            <Option value={'true'}>Yes</Option>
            <Option value={'false'}>No</Option>
          </Select>
        </Form.Item>
        <Button
          onClick={reset}
          disabled={
            props.searchValues.modelId == null &&
            props.searchValues.ruleType == null &&
            !props.searchValues.source &&
            props.searchValues.ruleId == null &&
            props.searchValues.isOrder == null
          }
        >
          Reset
        </Button>
        <Button type="primary" htmlType="submit">
          Search
        </Button>
      </Form>
    </div>
  );
}
