import { convertToDisplayTime,usePaginationHook  } from 'admin-upload-common';
import { Table, Typography } from 'antd';
import type { ColumnsType } from 'antd/lib/table';
import diff from 'fast-diff';
import React, { useState } from 'react';

import type { AuditType } from 'src/api/PBData/constants';
import type { uploadAdmin } from 'src/api/uploadAdmin/uploadAdmin';
import {
  FEtoBEMultiplier,
  imageUrlPrefix,
  itemAuditStatusString,
  itemStatusString,
} from 'src/constants';
import { useGetInfoAuditLog } from 'src/hooks/getLogData';
import { formatCurrencyForItem } from 'src/utils';
import { SearchBar } from './components/SearchBar';
import styles from './styles.module.scss';

const Text = Typography.Text;

interface ItemInfoAuditLogProps {
  itemId: number;
  region: string;
}

function ItemInfoAuditLog(props: ItemInfoAuditLogProps) {
  const { page, pageSize, changePage, changePageSize } = usePaginationHook({
    initialValue: { page: 1, pageSize: 60 },
    scrollOnPageChange: true,
  });
  const [filters, setFilters] = useState<
  uploadAdmin.IItemAuditFilter
  >({
    source: undefined,
    valueType: undefined,
    modelId: undefined,
    auditTypes: undefined,
    status: undefined,
  });
  const { data, loading, hasMore } = useGetInfoAuditLog(
    props.itemId,
    pageSize,
    page - 1,
    filters,
  );

  const formatedColumnValue = (
    row: uploadAdmin.IItemAudit,
    field: 'old' | 'new',
  ) => {
    return row.data?.map((row, idx) => {
      if (row.name === 'status') {
        return (
          <div key={idx}>
            {row.name}:
            {
              itemStatusString[
                Number(row[field]) as keyof typeof itemStatusString
              ]
            }
          </div>
        );
      } else if (row[field]) {
        if (row[field] && (row.name === 'name' || row.name === 'description')) {
          let diffs: diff.Diff[];
          if (field === 'old') diffs = diff(row.old || '', row.new || '');
          else diffs = diff(row.new || '', row.old || '');
          const images: string[] = [];
          if (row.name === 'description') {
            try {
              const descriptionData: {
                img?: { id: string; ratio: number };
                text?: string;
              }[] = JSON.parse(row[field] || '[]');
              descriptionData.forEach((v) => {
                if (v.img) {
                  images.push(v.img.id);
                }
              });
            } catch {
              // JSON parse failed
            }
          }
          return (
            <div key={idx}>
              {row.name}:
              {row.name === 'description' && !!images.length && (
                <div className={styles.imgContainer}>
                  {images.map((hash) => {
                    const url = `${imageUrlPrefix}/${hash}`;
                    return (
                      <div className={styles.imgItem}>
                        <img
                          src={url}
                          key={hash}
                          onClick={() => window.open(url, '_blank')}
                        />
                      </div>
                    );
                  })}
                </div>
              )}
              {diffs
                .filter((line) => line[0] <= 0)
                .map((line) =>
                  line[0] < 0 ? <Text mark>{line[1]}</Text> : line[1],
                )}
            </div>
          );
        } else if (row.name?.includes('price')) {
          return (
            <div key={idx}>
              {row.name}:
              {row[field] != null
                ? formatCurrencyForItem(
                  props.region,
                  Number(row[field]) / FEtoBEMultiplier,
                )
                : ''}
            </div>
          );
        } else if (['images', 'license', 'long_images'].includes(row.name!)) {
          let images: string[] = [];
          if (['images', 'long_images'].includes(row.name!)) {
            images = row[field]?.split(',') || [];
          }
          if (row.name === 'license' && row[field]) {
            try {
              images = JSON.parse(row[field] || '[]');
            } catch {
              console.log('JSON parse failed');
            }
          }
          return (
            <div key={idx}>
              <div>{row.name}:</div>
              <div>
                {images.map((hash) => {
                  const url = `${imageUrlPrefix}/${hash}`;
                  return (
                    <img
                      style={{ cursor: 'pointer' }}
                      src={url}
                      width={100}
                      height={100}
                      key={hash}
                      onClick={() => window.open(url, '_blank')}
                    />
                  );
                })}
              </div>
            </div>
          );
        } else {
          return (
            <div key={idx}>
              {row.name}:{row[field]}
            </div>
          );
        }
      }
    });
  };
  const columns: ColumnsType<uploadAdmin.IItemAudit> = [
    {
      title: 'Update Time',
      dataIndex: 'ctime',
      width: '8%',
      render: (value, row) => {
        return `${convertToDisplayTime(value)} [${row.auditId}]`;
      },
    },
    {
      title: 'Value Type',
      dataIndex: 'diffs',
      key: 'val_type',
      width: '7%',
      render: (value, row) => {
        return row.data?.map((auditDelta, idx) => {
          // show all fields except unchanged model ID
          // unchanged model ID indicates there were changes in the model
          if (
            auditDelta.name !== 'modelid' ||
            auditDelta.old !== auditDelta.new
          ) {
            return <div key={idx}>{auditDelta.name}</div>;
          }
        });
      },
    },
    {
      title: 'Old Values',
      dataIndex: 'diffs',
      key: 'oldVal',
      width: '17%',
      render: (value, row) => {
        return formatedColumnValue(row, 'old');
      },
    },
    {
      title: 'New Values',
      dataIndex: 'diffs',
      key: 'newVal',
      width: '17%',
      render: (value, row) => {
        return formatedColumnValue(row, 'new');
      },
    },
    {
      title: 'Model',
      dataIndex: 'modelId',
      width: '7%',
      render: (_, row) =>
        row.data?.find((auditDelta) => auditDelta.name === 'modelid')?.new,
    },
    {
      title: 'Event',
      dataIndex: 'auditType',
      width: '8%',
      render: (value) =>
        itemAuditStatusString[value as AuditType],
    },
    {
      title: 'Operator',
      dataIndex: 'operator',
      width: '10%',
    },
    {
      title: 'Source',
      dataIndex: 'source',
      width: '10%',
    },
    {
      title: 'Remark',
      dataIndex: 'reason',
      width: '15%',
    },
  ];
  return (
    <>
      <SearchBar
        searchValues={filters}
        setSearchValues={setFilters}
        resetPagination={() => {
          changePage(1);
        }}
      />
      <Table
        dataSource={
          data
            ? data.map((log, idx) => {
              return { ...log, key: idx };
            })
            : []
        }
        columns={columns}
        loading={loading}
        pagination={{
          pageSizeOptions: [
            '60',
            '120',
            '180',
            '240',
            '300',
            '360',
            '420',
            '480',
            '540',
            '600',
          ],
          defaultPageSize: 60,
          showSizeChanger: true,
          current: page,
          pageSize: pageSize,
          total: pageSize * page + (hasMore ? 1 : 0),
          onChange: changePage,
          onShowSizeChange: (cur, size) => changePageSize(size),
        }}
      />
    </>
  );
}

export default ItemInfoAuditLog;
