import { Button, Form, Input } from 'antd';
import type { Dispatch, SetStateAction } from 'react';
import React from 'react';

import type { uploadAdmin } from 'src/api/uploadAdmin/uploadAdmin';
import { integerRegex } from 'src/constants';
import styles from './styles.module.scss';

type SearchBarProps = {
  searchValues: uploadAdmin.IItemAuditFilter;
  setSearchValues: Dispatch<
  SetStateAction<uploadAdmin.IItemAuditFilter>
  >;
  resetPagination: ()=> void;
};

export function SearchBar(props: SearchBarProps) {
  const search = (
    values: uploadAdmin.IItemAuditFilter,
  ) => {
    props.setSearchValues({
      ...values,
      source: values.source ? values.source : undefined,
      valueType: values.valueType ? values.valueType : undefined,
      modelId:
        String(values.modelId) === '' || isNaN(Number(values.modelId))
          ? undefined
          : Number(values.modelId),
    });
    props.resetPagination();
  };
  const reset = () => {
    props.setSearchValues({
      source: undefined,
      valueType: undefined,
      modelId: undefined,
      auditTypes: undefined,
      status: undefined,
    });
    props.resetPagination();
  };
  return (
    <div className={styles.searchCard}>
      <Form
        layout="inline"
        colon={false}
        onFinish={search}
        fields={Object.keys(props.searchValues).map(
          (k: keyof typeof props.searchValues) => ({
            name: [k],
            value: props.searchValues[k],
          }),
        )}
      >
        <Form.Item
          name="modelId"
          rules={[
            {
              pattern: integerRegex,
              message: 'Please enter only numeric values',
            },
          ]}
        >
          <Input placeholder="Search Model ID" />
        </Form.Item>
        <Form.Item name="valueType">
          <Input placeholder="Search Value Type" />
        </Form.Item>
        <Form.Item name="source">
          <Input placeholder="Search Source" />
        </Form.Item>
        <Button
          onClick={reset}
          disabled={
            props.searchValues.modelId == null &&
            !props.searchValues.auditTypes &&
            !props.searchValues.source &&
            !props.searchValues.status &&
            !props.searchValues.valueType
          }
        >
          Reset
        </Button>
        <Button type="primary" htmlType="submit">
          Search
        </Button>
      </Form>
    </div>
  );
}
