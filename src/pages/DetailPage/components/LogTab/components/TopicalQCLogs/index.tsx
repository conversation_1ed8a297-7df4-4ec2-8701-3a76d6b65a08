import { convertToDisplayTime,usePaginationHook } from 'admin-upload-common';
import { Table } from 'antd';
import React from 'react';

import type { uploadAdmin } from 'src/api/uploadAdmin/uploadAdmin';
import {
  qcActionString,
  qcAddOnActionString,
} from 'src/constants';
import { useGetTopicalQcLog } from 'src/hooks/getLogData';
import type { QcAction, QcAddOnAction } from 'src/typings';
import { getHtmlFromTemplate } from 'src/utils';

interface TopicalQCLogProps {
  itemId: number;
  region: string;
}

export default function TopicalQCLogs(props: TopicalQCLogProps) {
  const { page, pageSize, changePage, changePageSize } = usePaginationHook({
    initialValue: { page: 1, pageSize: 5 },
    scrollOnPageChange: true,
  });
  const { data, isLoading } = useGetTopicalQcLog(
    props.itemId,
    pageSize,
    page - 1,
    props.region,
  );

  
  const columns = [
    {
      title: 'Review Log ID',
      dataIndex: 'reviewLogId',
      width: 130,
    },
    {
      title: 'Create Time',
      dataIndex: 'createTime',
      width: 130,
      render: (value: number) => {
        return convertToDisplayTime(value);
      },
    },
    {
      title: 'Update Time',
      dataIndex: 'updateTime',
      width: 130,
      render: (value: number) => {
        return convertToDisplayTime(value);
      },
    },
    {
      title: 'Reviewer',
      dataIndex: 'reviewer',
      width: 130,
    },
    {
      title: 'Action',
      dataIndex: 'topicalQcAction',
      render: (
        tqcAction: uploadAdmin.IQcAction,
      ) => {
        return (
          <div style={{ width: 150 }}>
            <div>{qcActionString[tqcAction.action as QcAction] || '-'} </div>
            <div>Reason: {tqcAction.failedReason}</div>
          </div>
        );
      },
    },
    {
      title: 'Add-on Action',
      dataIndex: 'topicalQcAddOnAction',
      render: (
        tqcAddOnAction: uploadAdmin.IQcAddOnAction,
      ) => {
        return tqcAddOnAction?.deboosts?.length ? (
          <div style={{ width: 150 }}>
            <div>
              {tqcAddOnAction?.deboosts
                ?.map((v: QcAddOnAction) => qcAddOnActionString[v])
                .filter((v) => v)
                .join(', ')}
            </div>
            <div>Reason: {tqcAddOnAction.failedReason}</div>
            <div>Remark: {getHtmlFromTemplate(tqcAddOnAction?.hint || '')}</div>
          </div>
        ) : (
          '-'
        );
      },
    },
    {
      title: 'Project',
      dataIndex: 'project',
    },
    {
      title: 'Task ID',
      dataIndex: 'taskId',
    },
    {
      title: 'Remark',
      dataIndex: 'remark',
    },
  ];
  
  return (
    <>
      <Table
        dataSource={data?.logs ?? []}
        columns={columns}
        loading={isLoading}
        rowKey={'reviewEventId'}
        scroll={{ x: 'max-content' }}
        onHeaderRow={() => ({ style: { height: 50 } })}
        pagination={{
          pageSizeOptions: [
            '5',
            '10',
            '15',
            '20',
            '25',
            '30',
            '35',
            '40',
            '45',
            '50',
          ],
          defaultPageSize: 5,
          showSizeChanger: true,
          current: page,
          pageSize: pageSize,
          total: (data?.logs?.length ?? 0) + (data?.hasMore ? 1 : 0),
          onChange: changePage,
          onShowSizeChange: (cur, size) => changePageSize(size),
        }}
      />
    </>
  );
}