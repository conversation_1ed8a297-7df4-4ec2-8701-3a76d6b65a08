import { convertToDisplayTime,usePaginationHook  } from 'admin-upload-common';
import { Table } from 'antd';
import type { ColumnsType } from 'antd/lib/table';
import React from 'react';

import type { uploadAdmin } from 'src/api/uploadAdmin/uploadAdmin';
import {
  qcActionString,
  qcAddOnActionString,
  qcLevelString,
  queueTypeString,
} from 'src/constants';
import { useGetRQCLog } from 'src/hooks/getLogData';
import type { QcAction, QcAddOnAction,QcLevel, QueueType } from 'src/typings';
import { getHtmlFromTemplate } from 'src/utils';

interface RQCLogsProps {
  itemId: number;
  region: string;
}

function RQCLogs(props: RQCLogsProps) {
  const { page, pageSize, changePage, changePageSize } = usePaginationHook({
    initialValue: { page: 1, pageSize: 5 },
    scrollOnPageChange: true,
  });
  const { data, isLoading } = useGetRQCLog(
    props.itemId,
    pageSize,
    page - 1,
    props.region,
  );
  const columns: ColumnsType<uploadAdmin.IRuleQCLog> = [
    {
      title: 'Review Log ID',
      dataIndex: 'logId',
      width: '10%',
    },
    {
      title: 'Create Time',
      dataIndex: 'createTime',
      width: '10%',
      render: (value) => {
        return convertToDisplayTime(value);
      },
    },
    {
      title: 'Update Time',
      dataIndex: 'updateTime',
      width: '10%',
      render: (value) => {
        return convertToDisplayTime(value);
      },
    },
    {
      title: 'Reviewer',
      dataIndex: 'reviewer',
      width: '10%',
    },
    {
      title: 'Action',
      dataIndex: 'qcAction',
      width: '15%',
      render: (qcAction: uploadAdmin.IQcAction) => {
        return (
          <div>
            <div>{qcActionString[qcAction.action as QcAction]} </div>
            <div>Reason: {qcAction.failedReason}</div>
            <div>Hint: {getHtmlFromTemplate(qcAction?.hint || '')}</div>
          </div>
        );
      },
    },
    {
      title: 'Add-on Action',
      dataIndex: 'qcAddOnAction',
      width: '15%',
      render: (
        qcAddOnAction: uploadAdmin.IQcAddOnAction,
      ) => {
        return (
          !!qcAddOnAction?.deboosts?.length && (
            <div>
              <div>
                {qcAddOnAction?.deboosts
                  ?.map((v: QcAddOnAction) => qcAddOnActionString[v])
                  .filter((v) => v)
                  .join(', ')}
              </div>
              <div>Reason: {qcAddOnAction.failedReason}</div>
              <div>Hint: {getHtmlFromTemplate(qcAddOnAction?.hint || '')}</div>
            </div>
          )
        );
      },
    },
    {
      title: 'QC queue',
      dataIndex: 'qcQueue',
      width: '10%',
      render: (value) => queueTypeString[value as QueueType],
    },
    {
      title: 'QC level',
      dataIndex: 'qcLevel',
      width: '10%',
      render: (value) => qcLevelString[value as QcLevel],
    },
    {
      title: 'Remark',
      width: '10%',
      render: (_, row) => {
        return (
          <>
            {row.remark && <div>{row.remark}</div>}
            {row.deboostRemark && <div>{row.deboostRemark}</div>}
          </>
        );
      },
    },
  ];
  return (
    <>
      <Table
        dataSource={data?.logs ?? []}
        columns={columns}
        loading={isLoading}
        rowKey={'auditId'}
        pagination={{
          pageSizeOptions: [
            '5',
            '10',
            '15',
            '20',
            '25',
            '30',
            '35',
            '40',
            '45',
            '50',
          ],
          defaultPageSize: 5,
          showSizeChanger: true,
          current: page,
          pageSize: pageSize,
          total: (data?.logs?.length ?? 0) + (data?.hasMore ? 1 : 0),
          onChange: changePage,
          onShowSizeChange: (cur, size) => changePageSize(size),
        }}
      />
    </>
  );
}

export default RQCLogs;
