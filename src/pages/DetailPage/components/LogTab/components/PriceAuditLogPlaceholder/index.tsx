import { useNavigate } from '@classification/admin-solution';
import { Typography } from 'antd';
import React from 'react';

const Text = Typography.Text;

interface PriceAuditLogProps {
  itemId: number;
  region: string;
}

const PRICE_AUDIT_LOG_LINK = '/price-management/priceinfo';

/**
 * This page has moved to the price management module as part of the Price Log Improvement feature.
 */
function PriceAuditLogPlaceholder({ itemId, region }: PriceAuditLogProps) {
  const navTo = useNavigate();

  return (
    <div>
      <Text>
        This page has moved here:{' '}
        <a
          onClick={() =>
            navTo({ to: PRICE_AUDIT_LOG_LINK, params: { id: itemId, type: 'audit' } })
          }
        >
          Price Info
        </a>
      </Text>
    </div>
  );
}

export default PriceAuditLogPlaceholder;
