import { ClockCircleOutlined } from '@ant-design/icons';
import { useNavigate, useRoute } from '@classification/admin-solution';
import { getImageLink } from 'admin-upload-common';
import { Alert, Button, Image,Modal , Typography } from 'antd';
import type { ModalProps } from 'antd/lib/modal';
import moment from 'moment';
import React from 'react';

import type { uploadAdmin } from '../../../../../../api/uploadAdmin/uploadAdmin';
import { TabKey } from '../../../..';
import styles from './styles.module.scss';

const { Text } = Typography;
interface Props extends ModalProps {
  licenses?: uploadAdmin.ILicenseWithMetaInfo[];
}
export default function LicenseModal({ licenses, ...modalProps }: Props) {
  const { routeId, params: routeParams } = useRoute();
  const navTo = useNavigate();

  const navToLog = (e: React.MouseEvent<HTMLElement, MouseEvent>) => {
    navTo({ to: routeId, params: { ...routeParams, tab: TabKey.Log } });
    modalProps.onCancel && modalProps.onCancel(e);
  };
  if (!licenses?.length) return null;
  return (
    <Modal
      width={780}
      title="Recent Product License"
      footer={
        <Button onClick={modalProps.onCancel} type="primary">
          Close
        </Button>
      }
      {...modalProps}
    >
      <Alert
        message={
          <>
            Showing recent 10 product licenses. To see more licenses please
            check product <a onClick={navToLog}>audit logs</a>.
          </>
        }
        type="info"
        showIcon
      />
      <div className={styles.licenseContainer}>
        {licenses.map((license, idx) => (
          <div className={styles.license} key={idx}>
            <Image
              preview={true}
              width={100}
              src={getImageLink(license.license?.licenseMd5 || '')}
            />
            <label>
              <Text type="secondary">
                <ClockCircleOutlined />{' '}
                {license.uploadTime &&
                  moment(license.uploadTime * 1000).format('YYYY-MM-DD HH:mm')}
              </Text>
            </label>
          </div>
        ))}
      </div>
    </Modal>
  );
}
