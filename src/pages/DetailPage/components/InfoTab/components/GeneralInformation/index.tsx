import { InfoCircleOutlined } from '@ant-design/icons';
import { getPCHost,getUserAdminHost,isObj<PERSON>ey  } from 'admin-upload-common';
import { useRequest } from 'ahooks';
import { Card, Divider,Form, Radio, Tag, Tooltip } from 'antd';
import type  { FormInstance } from 'antd/lib/form';
import moment from 'moment';
import React from 'react';

import { ItemFlags } from 'src/api/PBData/constants';
import {
  getItemTag,
  getQcTags,
} from 'src/api/uploadAdmin';
import type { uploadAdmin } from 'src/api/uploadAdmin/uploadAdmin';
import {
  attributeQcStatusString,
  CAT_AUTO_CORRECTION_STATUS_LABEL,
  counterfeitTagIds,
  counterfeitTagString,
  dangerousGoodsTooltipTitle,
  dangerousGoodsYesText,
  DG_CAT_OPTIONS,
  regionsWithDangerousGoods,
  ruleModelQcStatusString,
  shopStatusString,
  userStatusString,
} from 'src/constants';
import type { RuleModelQcStatus } from 'src/typings';
import styles from '../../../../styles.module.scss';
import LicenseModal from '../LicenseModal';

interface GeneralInformationProps {
  item: uploadAdmin.IGetItemDetailResponse;
  region: string;
  env: 'test' | 'uat' | 'staging' | 'stable' | 'live';
  itemModelList: uploadAdmin.IItemModel[];
  form: FormInstance;
  isEditing: boolean;
  licenses?: uploadAdmin.ILicenseWithMetaInfo[];
}

function GeneralInformation(props: GeneralInformationProps) {
  const [showLicenseModal, setShowLicenseModal] = React.useState(false);

  const { data: qcTagData } = useRequest(() =>
    getQcTags({
      itemId: props.item.itemId,
      shopId: props.item.shopId,
      region: props.region,
    }),
  );

  const { data: counterfeitTagData } = useRequest<
  uploadAdmin.IGetItemTagResponse | undefined
  , []>(() =>
    getItemTag({
      country: props.region,
      itemIds: [props.item.itemId!],
      tagIds:
        counterfeitTagIds[props.env] &&
        isObjKey(props.region, counterfeitTagIds[props.env])
          ? Object.values(counterfeitTagIds[props.env][props.region])
          : undefined,
    }),
  );

  const renderCounterfeitTags = () => {
    if (
      counterfeitTagData?.itemTags &&
      counterfeitTagIds[props.env] &&
      isObjKey(props.region, counterfeitTagIds[props.env])
    ) {
      const latestTag = counterfeitTagData?.itemTags.sort(
        (tag1, tag2) =>
          (tag2.updatedTime ? tag2.updatedTime : 0) -
          (tag1.updatedTime ? tag1.updatedTime : 0),
      )[0];
      if (latestTag) {
        const tagName = Object.keys(
          counterfeitTagIds[props.env][props.region],
        ).find(
          (tagName) =>
            // @ts-expect-error
            counterfeitTagIds[props.env][props.region][tagName] ===
            latestTag.tagId,
        );
        if (
          !tagName ||
          (tagName !== 'fake' &&
            tagName !== 'not_fake' &&
            tagName !== 'pending')
        ) {
          return 'N/A';
        } else {
          return `${counterfeitTagString[tagName]}: ${moment(
            latestTag.updatedTime! * 1000,
          ).format('DD-MM-YYYY HH:mm ZZ')}`;
        }
      } else {
        return 'N/A';
      }
    } else {
      return 'N/A';
    }
  };
  const formItemLayout = {
    labelCol: { span: 6 },
    wrapperCol: { span: 14 },
  };

  return (
    <Card title="General Information">
      <Form
        className={styles.condensedForm}
        form={props.form}
        labelAlign="left"
        size="small"
        {...formItemLayout}
      >
        {/* Username */}
        <Form.Item label="Username">
          <span className="ant-form-text">{props.item.userName}</span>
        </Form.Item>

        {/* User ID */}
        <Form.Item label="User ID">
          <span>
            <a
              href={`${getUserAdminHost()}/detail?userid=${props.item.userId}&region=${props.region.toUpperCase()}`}
              target="_blank"
              rel="noopener noreferrer"
            >
              {props.item.userId}
            </a>
          </span>
        </Form.Item>

        {/* User Status */}
        <Form.Item label="User Status">
          {
            userStatusString[
              props.item.userStatus as keyof typeof userStatusString
            ]
          }
        </Form.Item>

        {/* Shop Name */}
        <Form.Item label="Shop Name">{props.item.shopName}</Form.Item>

        {/* Shop ID */}
        <Form.Item label="Shop ID">
          <a
            href={`${getUserAdminHost()}/detail?userid=${props.item.userId}&region=${props.region.toUpperCase()}`}
            target="_blank"
            rel="noopener noreferrer"
          >
            {props.item.shopId}
          </a>
        </Form.Item>

        {/* Shop Status */}
        <Form.Item label="Shop Status">
          {
            shopStatusString[
              props.item.shopStatus as keyof typeof shopStatusString
            ]
          }
        </Form.Item>
        {/* Centralized QC */}
        <Form.Item label="Centralized QC Status">
          <a
            href={`//listingqc.${
              props.env === 'live' ? '' : `${props.env}.`
            }shopee.io/centralisedqc/review?source=2&item_id=${
              props.item.itemId
            }&shop_id=${props.item.shopId}&region=${
              props.region
            }&queue_type=-99`}
            target="_blank"
            rel="noopener noreferrer"
          >
            {props.item.cqcStatus != null
              ? ruleModelQcStatusString[
              // eslint-disable-next-line @typescript-eslint/no-unnecessary-type-assertion
                props.item.cqcStatus as RuleModelQcStatus
              ]
              : 'N/A'}
          </a>
        </Form.Item>

        {/* Rule QC Status */}
        <Form.Item label="Rule QC Status">
          {props.item.ruleQcStatus != null
            ? ruleModelQcStatusString[
            // eslint-disable-next-line @typescript-eslint/no-unnecessary-type-assertion
              props.item.ruleQcStatus as RuleModelQcStatus
            ]
            : 'N/A'}
        </Form.Item>

        {/* Attribute QC Status */}
        <Form.Item label="Attribute QC Status">
          <span className="ant-form-text">
            {
              attributeQcStatusString[
                props.item.attrQcStatus as keyof typeof attributeQcStatusString
              ]
            }
          </span>
        </Form.Item>

        {/* Model QC Status */}
        <Form.Item label="Model QC Status">
          {props.item.modelQcStatus != null
            ? ruleModelQcStatusString[
            // eslint-disable-next-line @typescript-eslint/no-unnecessary-type-assertion
              props.item.modelQcStatus as RuleModelQcStatus
            ]
            : 'N/A'}
        </Form.Item>

        {/* QC Tag */}
        <Form.Item label="QC Deboost Reason">
          {qcTagData?.reasons?.join(',') || 'N/A'}
        </Form.Item>

        {/* Most Recent PQR (%) */}
        <Form.Item label="Most Recent PQR (%)">
          {props.item.pqrInfo?.pqr ? `${props.item.pqrInfo?.pqr}%` : 'N/A' }
        </Form.Item>

        {/* Previous Consecutive weeks exceeding PQR */}
        <Form.Item label="Previous Consecutive weeks exceeding PQR">
          {props.item.pqrInfo?.consecutiveWeeks ?? 'N/A'}
        </Form.Item>

        {/* Bad reviews last weeks */}
        <Form.Item label="Bad reviews last weeks">
          {props.item.pqrInfo?.ratingBadCnt ?? 'N/A'}
        </Form.Item>

        {/* Order Delivered/Reviewed last week */}
        <Form.Item label="Order Delivered/Reviewed last week">
          {(props.item.pqrInfo?.order ?? 0) + (props.item.pqrInfo?.reviewRatings ?? 0)}
        </Form.Item>

        {/* Wrong Category Appeal Status */}
        <Form.Item label="Wrong Category Appeal Status">
          {CAT_AUTO_CORRECTION_STATUS_LABEL[props.item.autoCorrectCategoryInfo?.appealStatus as keyof typeof CAT_AUTO_CORRECTION_STATUS_LABEL] || 'N/A'}
        </Form.Item>

        {/* Product License */}
        <Form.Item label="Product License">
          {props.licenses?.length ? (
            <a onClick={() => setShowLicenseModal(true)}>View</a>
          ) : (
            'N/A'
          )}
        </Form.Item>
        <LicenseModal
          licenses={props.licenses}
          open={showLicenseModal}
          onCancel={() => setShowLicenseModal(false)}
        />

        {/* Product Page */}
        <Form.Item label="Product Page">
          <span>
            <a
              href={`${getPCHost(props.region)}/product/${props.item.shopId}/${
                props.item.itemId
              }?__mobile__=1`}
              target="_blank"
              rel="noopener noreferrer"
            >
              Mobile Mall
            </a>
            <Divider type="vertical" />
            <a
              href={`${getPCHost(props.region)}/product/${props.item.shopId}/${
                props.item.itemId
              }`}
              target="_blank"
              rel="noopener noreferrer"
            >
              PC Mall
            </a>
            {props.item.isChildVsku && (
              <>
                <Divider type="vertical" />
                <a
                  href={`//admin.listing.${
                    props.env === 'live' ? '' : `${props.env}.`
                  }shopee.com/virtualsku/editpage?shopid=${
                    props.item.shopId
                  }&itemid=${props.item.itemId}&modelid=${
                    props.itemModelList[0]?.modelId
                  }`}
                  target="_blank"
                  rel="noopener noreferrer"
                >
                  Config Page
                </a>
              </>
            )}
          </span>
        </Form.Item>

        {/* Visible in Frontend */}
        <Form.Item
          label={
            <>
              {'Visible in Frontend'}
              <Tooltip
                title={
                  'Possible reasons that items cannot be displayed in frontend: 1. Item status is invalid; 2. Shop status is invalid; 3. User status is invalid 4. Phone is not verified'
                }
                className={styles.tooltip}
              >
                <InfoCircleOutlined />
              </Tooltip>
            </>
          }
        >
          {props.item.visibleInFe ? 'Yes' : 'No'}
        </Form.Item>

        {/* Parent SKU */}
        <Form.Item label="Parent SKU">{props.item.parentSku}</Form.Item>

        {/* Likes */}
        <Form.Item label="Likes">{props.item.itemLikedCount}</Form.Item>

        {/* Page View */}
        <Form.Item label="Page View">{props.item.productPageView}</Form.Item>

        {/* Sold */}
        <Form.Item label="Sold">{props.item.sold}</Form.Item>

        {/* Time Added */}
        <Form.Item label="Time Added">
          <span>
            {moment((props.item.createTime ?? 0) * 1000).format('DD-MM-YYYY HH:mm ZZ')}
          </span>
        </Form.Item>

        {/* Time Updated */}
        <Form.Item label="Time Updated">
          <span>
            {moment((props.item.updateTime ?? 0) * 1000).format('DD-MM-YYYY HH:mm ZZ')}
          </span>
        </Form.Item>

        {/* Booked selling time */}
        <Form.Item label="Booked selling time">
          <span>
            {props.item.scheduledPublishTime ? moment((props.item.scheduledPublishTime ?? 0) * 1000).format('DD-MM-YYYY HH:mm ZZ') : ''}
          </span>
        </Form.Item>

        {/* Counterfeit Tags */}
        <Form.Item label="Counterfeit Tags">
          {renderCounterfeitTags()}
        </Form.Item>

        {/* Counterfeit */}
        <Form.Item label="Counterfeit" name="isCounterfeit">
          <Radio.Group disabled={!props.isEditing}>
            <Radio value={true}>Yes</Radio>
            <Radio value={false}>No</Radio>
          </Radio.Group>
        </Form.Item>

        {/* SEO Description */}
        <Form.Item label="SEO Description" name="seoDescription">
          <Radio.Group disabled={!props.isEditing}>
            <Radio value={true}>Yes</Radio>
            <Radio value={false}>No</Radio>
          </Radio.Group>
        </Form.Item>

        {/* Non Searchable */}
        <Form.Item label="Non Searchable" name="nonSearchable">
          <Radio.Group disabled={!props.isEditing}>
            <Radio value={true}>Yes</Radio>
            <Radio value={false}>No</Radio>
          </Radio.Group>
        </Form.Item>

        {/* Hidden */}
        <Form.Item label="Hidden" name="isHidden">
          <Radio.Group disabled={!props.isEditing}>
            <Radio value={true}>Yes</Radio>
            <Radio value={false}>No</Radio>
          </Radio.Group>
        </Form.Item>

        {/* Re-stock */}
        <Form.Item label="Re-stock" name="restock">
          <Radio.Group disabled={!props.isEditing}>
            <Radio value={true}>Yes</Radio>
            <Radio value={false}>No</Radio>
          </Radio.Group>
        </Form.Item>

        {regionsWithDangerousGoods.includes(props.region) && (
          <>
            {/* Dangerous Goods */}
            <Form.Item
              name="dangerousGoods"
              label={
                <>
                  Dangerous Goods{' '}
                  <Tooltip
                    title={
                      dangerousGoodsTooltipTitle[
                        props.region as keyof typeof dangerousGoodsTooltipTitle
                      ]
                    }
                    className={styles.tooltip}
                  >
                    <InfoCircleOutlined />
                  </Tooltip>
                </>
              }
            >
              <Radio.Group disabled={!props.isEditing}>
                <Radio value={1}>
                  {props.region in dangerousGoodsYesText
                    ? dangerousGoodsYesText[
                      props.region as keyof typeof dangerousGoodsYesText
                    ]
                    : 'Yes'}
                </Radio>
                <Radio value={0}>No</Radio>
              </Radio.Group>
            </Form.Item>
            {/* DG tagged by Shopee */}
            <Form.Item label="DG tagged by Shopee">
              { props.item.shopeeDangerousGoods ? 'Yes' : 'No' }
            </Form.Item>
            {/* Dangerous Categorization */}
            <Form.Item 
              noStyle 
              shouldUpdate={(prevValues, curValues) => prevValues.dangerousGoods !== curValues.dangerousGoods}>
              {
                ({ getFieldValue, setFieldValue }) => {
                  const isDg = getFieldValue('dangerousGoods');
                  if (!isDg) {
                    setFieldValue('dangerousCategorizationByShopee', 0);
                  }
                  return <Form.Item label="Dangerous Categorization" name="dangerousCategorizationByShopee">
                    <Radio.Group disabled={!props.isEditing || !isDg}>
                      { DG_CAT_OPTIONS.map(c => <Radio value={c.value}>{c.label}</Radio>) }
                    </Radio.Group>
                  </Form.Item>
                }
              }
            </Form.Item>
          </>
        )}

        {/* Selling Status */}
        <Form.Item label="Selling Status" name="isUnlisted">
          <Radio.Group disabled={!props.isEditing}>
            <Radio value={false}>Published</Radio>
            <Radio value={true}>Unlisted</Radio>
          </Radio.Group>
        </Form.Item>

        {/* Tag */}
        <Form.Item label="Tag">
          <span>
            {props.item.isPreferred && (
              <Tag color={'green'}>Preferred Shop</Tag>
            )}
            {props.item.isOfficial && <Tag color={'cyan'}>Official Shop</Tag>}
            {props.item.isCb && <Tag color={'gold'}>CrossBorder</Tag>}
          </span>
        </Form.Item>

        {/* Item Flags */}
        <Form.Item label="Item Flags">
          <span>
            {props.item.flagList
              ? props.item.flagList?.map((flag) => ItemFlags[flag]).join(', ')
              : ''}
          </span>
        </Form.Item>
      </Form>
    </Card>
  );
}

export default GeneralInformation;
