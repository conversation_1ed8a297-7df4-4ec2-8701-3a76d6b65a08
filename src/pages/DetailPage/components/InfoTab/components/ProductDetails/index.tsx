import {
  InfoCircleFilled,
  InfoCircleOutlined,
  PlusOutlined,
} from '@ant-design/icons';
import {
  convertToDisplayTime,
  DraggableUploadListItem,
  getAdminHost,
  getCountry,
  getFileExtension,
  ImageUploader,
  isPageAnIframe,
  ThumbnailWrap
} from 'admin-upload-common';
import { deepCamel } from 'admin-upload-common/es/utils/transform';
import {
  Button,
  Card,
  Checkbox,
  Descriptions,
  Form,
  Input,
  InputNumber,
  message,
  Popover,
  Radio,
  Select,
  Switch,
  Table,
  Tooltip,
  Upload,
} from 'antd';
import type { FormInstance } from 'antd/lib/form';
import type {
  RcFile,
  UploadChangeParam,
  UploadFile,
} from 'antd/lib/upload/interface';
import update from 'immutability-helper';
import moment from 'moment';
import type {
  Dispatch
} from 'react';
import React, {
  useCallback,
  useEffect,
  useMemo,
  useRef,
  useState,
} from 'react';
import { createDndContext, DndProvider } from 'react-dnd';
import { HTML5Backend } from 'react-dnd-html5-backend';
import * as uuid from 'uuid';

import { ItemAttributeType } from 'src/api/PBData/constants';
import * as uploadAdminConstant from 'src/api/uploadAdmin/constants';
import type { uploadAdmin } from 'src/api/uploadAdmin/uploadAdmin';
import {   conditionString,
  FEtoBEMultiplier,
  fiveDigitDecimalRule,
  IMAGE_UPLOAD,
  imageUrlPrefix,
  integerValueRule,
maxCatLevel ,
  nonNegativeIntegerRegex,
  oplDisplayTimeFormat, PRODUCT_TAX_TYPE_MAP
} from 'src/constants';
import type { ImageListsType, ImageResponse } from 'src/typings';
import styles from '../../../../styles.module.scss';
import Categories, { CategoryType } from './components/Categories';
import TaxRateTips from './components/Categories/components/TaxRateTips';
import ComplaintPolicy from './components/ComplaintPolicy';
import Description from './components/Description';
import GlobalAttribute from './components/GlobalAttribute';
import GlobalBrand from './components/GlobalBrand';
import ItemModels from './components/ItemModels';
import NewPayment from './components/NewPayment';
import NewShippingInfo from './components/NewShippingInfo';
import PriceSection from './components/PriceSection';
import ProductAttributes from './components/ProductAttributes';
import Wholesale from './components/Wholesale';
import getDisplayName from './utils/localName';

const { TextArea } = Input;

const {
  PromotionStatus,
  AttrInputType,
  AttrFormatType,
} = uploadAdminConstant;

enum ImageType {
  PRODUCT,
  VARIATION,
  WHITE_BACKGROUND,
}

enum SettingType {
  ShopTypeSetting = 1,
  ShopIDSetting = 2,
  MpskuSetting = 3,
}
const displaySettingType = {
  [SettingType.MpskuSetting]: 'Mpsku Model',
  [SettingType.ShopIDSetting]: 'ShopId Type',
  [SettingType.ShopTypeSetting]: 'Shop Type',
};

const { FREE_TEXT_FILED, MULTI_DROP_DOWN, MULTI_COMBO_BOX } = AttrInputType;

const EuInvoiceOptions: { [s: number]: string } = {
  1: 'No Invoices',
  2: 'VAT margin schme invoices',
  3: 'VAT invoices',
  4: 'Non-VAT invoices',
};

interface ProductDetailsProps {
  item: uploadAdmin.IGetItemDetailResponse;
  region: string;
  env: string;
  stockList: uploadAdmin.IStockBreakDownList[];
  floatingStockLists: uploadAdmin.IFloatingStockList[];
  itemStock: uploadAdmin.IItemStock[];
  itemModelList: uploadAdmin.IItemModel[];
  productAttributeData:
  | uploadAdmin.IGetProductAttributesResponse
  | undefined;
  globalProductAttributeData:
  | uploadAdmin.IGetGlobalProductAttributesResponse
  | undefined;
  setLeafCat: Dispatch<number | undefined>;
  globalLeafCat: number | undefined;
  setGlobalLeafCat: Dispatch<number | undefined>;
  form: FormInstance;
  isShopWhitelisted: boolean;
  isEditing: boolean;
  images: ImageListsType;
  setImages: Dispatch<ImageListsType>;
  editModeDataSource: uploadAdmin.IProductAttr[];
  setEditModeDataSource: Dispatch<
    uploadAdmin.IProductAttr[]
  >;
  editModeGlobalDataSource:
  | uploadAdmin.IGlobalProductAttrList
  | undefined;
  setEditModeGlobalDataSource: Dispatch<
    uploadAdmin.IGlobalProductAttrList | undefined
  >;
  variationList: uploadAdmin.ITierVariation[];
  setVariationList: Dispatch<
    uploadAdmin.ITierVariation[]
  >;
  updateModelSettingV2: (params: {
    modelsSetting: uploadAdmin.IModelSettingV2[];
  }) => void;
  getBooksInfoByIsbnData: () => Promise<
    uploadAdmin.IGetBooksInfoByIsbnResponse
  >;
}
const RNDContext = createDndContext(HTML5Backend);
const reader = new FileReader();
function handleBeforeProductImageUpload(file: RcFile): Promise<void> {
  const extension = getFileExtension(file.name);
  if (extension !== '.png' && extension !== '.jpg' && extension !== '.jpeg') {
    message.error('You can only upload JPG/PNG file.');
    return Promise.reject();
  } else if (file.size > 2 * 1024 * 1024) {
    message.error('You can only upload file less than or equal to 2MB.');
    return Promise.reject();
  } else {
    return Promise.resolve();
  }
}

const changeAction = <T,>(
  hook: (callback: (v: T[]) => T[]) => void,
  index: number,
  params: Partial<T & { loading?: boolean; editing?: boolean }>,
) => {
  hook((data) => {
    return [
      ...data.slice(0, index),
      { ...data[index], ...params },
      ...data.slice(index + 1),
    ];
  });
};

function handleBeforeWhiteBgImageUpload(file: RcFile): Promise<void> {
  const extension = getFileExtension(file.name);
  if (extension !== '.png') {
    message.error('You can only upload PNG file.');
    return Promise.reject();
  }
  return new Promise<void>((resolve, reject) => {
    reader.readAsDataURL(file);
    reader.onload = function (e) {
      const image = new Image();
      if (!e.target?.result) {
        return reject();
      }
      image.src = e.target.result as string;
      image.onload = function () {
        const height = image.height;
        const width = image.width;
        if (width != 800 || height != 800) {
          message.error('Resolution of 800*800 required.');
          return reject();
        } else if (file.size > 2 * 1024 * 1024) {
          message.error('You can only upload file less than or equal to 2MB.');
          return reject();
        }
        return resolve();
      };
    };
  });
}

function ProductDetails(props: ProductDetailsProps) {
  const [stockMergedList, setStockMergedList] = useState<
    (uploadAdmin.IStockBreakDownList &
      uploadAdmin.IFloatingStockList & {
        isEnabled?: boolean;
      })[]
  >([]);
  const [stockSettings, setStockSettings] = useState<
    {
      modelId?: number;
      blockedWarehouse?: boolean;
      blockedSeller?: boolean;
      settingType?: number;
    }[]
  >([]);
  const manager = useRef(RNDContext);
  const [canDrag, setCanDrag] = React.useState(false);
  const [stockTotalMap, setStockTotalMap] = useState<Record<number, number>>({});

  useEffect(() => {
    if (!props.isEditing && props.stockList?.length) {
      const modelStock = props.stockList.reduce((map, stock) => {
        if (stock.locationId?.endsWith('Z')) {
          map[`${stock.modelId!}`] = {
            ...map[`${stock.modelId!}`],
            [stock.locationId]: stock.sellableStock
          };
        }
        return map;
      }, {} as Record<string, Record<string, number | undefined>>);

      props.form.setFieldsValue({ modelStock });
    }
  }, [props.isEditing, props.stockList]);

  const calcTotalStock = (modelId: number) => {
    const total = stockMergedList.filter(stock => stock.modelId === modelId).reduce((total, stock) => {
      total += (props.form.getFieldValue(['modelStock', `${stock.modelId!}`, stock.locationId!]));
      return total;
    }, 0);
    setStockTotalMap({
      ...stockTotalMap,
      [modelId]: total
    });
  };

  const handleImageChange = useCallback(
    (info: UploadChangeParam<UploadFile>, type: ImageType) => {
      switch (type) {
        case ImageType.PRODUCT: {
          console.log('first', info, type);
          props.setImages({
            ...props.images,
            productImages: info.fileList
              .map((file) => {
                const camelResponse = deepCamel(file.response) as ImageResponse;
                return {
                  ...file,
                  response: camelResponse,
                  url: camelResponse?.data?.imageId
                    ? imageUrlPrefix + camelResponse.data.imageId
                    : undefined,
                };
              })
              .filter((image, index: number, self) => {
                return (
                  self.findIndex(
                    (img) =>
                      ((img.url || image.url) && img.url === image.url) ||
                      img.uid === image.uid,
                  ) === index
                );
              }),
          });
          break;
        }
        case ImageType.WHITE_BACKGROUND: {
          props.setImages({
            ...props.images,
            whiteBackgroundImages: info.fileList.map((file) => {
              const camelResponse = deepCamel(file.response) as ImageResponse;
              return {
                ...file,
                response: camelResponse,
                url: camelResponse?.data?.imageId
                  ? imageUrlPrefix + camelResponse.data.imageId
                  : undefined,
              };
            }),
          });
          break;
        }
      }
    },
    [props.setImages, props.images],
  );

  const moveRow = useCallback(
    (dragIndex: number, hoverIndex: number) => {
      const dragRow = props.images.productImages[dragIndex];
      props.setImages({
        ...props.images,
        productImages: update(props.images.productImages, {
          $splice: [
            [dragIndex, 1],
            [hoverIndex, 0, dragRow],
          ],
        }),
      });
    },
    [props.images.productImages],
  );

  useEffect(() => {
    props.setEditModeDataSource(
      (props.productAttributeData?.productAttrModel?.attributes?.dependentAttrs
        ? props.productAttributeData.productAttrModel.attributes.dependentAttrs
        : []
      ).concat(
        props.productAttributeData?.productAttrModel?.attributes
          ?.independentAttrs
          ? props.productAttributeData.productAttrModel.attributes
            .independentAttrs
          : [],
      ),
    );
    if (props.productAttributeData?.attrSnapshotList) {
      for (const snapshot of props.productAttributeData.attrSnapshotList) {
        props.form.setFieldsValue({
          [`attribute${snapshot.attribute?.attrId}_value`]:
            snapshot.attribute?.attrType === ItemAttributeType.TIMESTAMP_TYPE
              ? moment
                .unix(Number(snapshot.value))
                .format('YYYY-MM-DD HH:mm:ss')
              : snapshot.value,
        });
      }
    }
  }, [props.productAttributeData]);

  useEffect(() => {
    props.setEditModeGlobalDataSource(
      props.globalProductAttributeData?.productAttrList,
    );
    if (
      props.globalProductAttributeData?.attrSnapshotList &&
      props.globalProductAttributeData?.productAttrList?.globalAttributes
    ) {
      // For every attribute in attr list, if attr input type multiple, check snapshot list for all
      // if not multiple, only check for first value
      for (const attribute of props.globalProductAttributeData.productAttrList
        .globalAttributes) {
        if (
          attribute.inputType === MULTI_DROP_DOWN ||
          attribute.inputType === MULTI_COMBO_BOX
        ) {
          const valuesToSet = props.globalProductAttributeData.attrSnapshotList
            .filter((snapshot) => snapshot.attrId === attribute.attrId)
            .map((snapshot) =>
              getDisplayName(
                snapshot.attrDefaultLangValue?.val || '',
                snapshot.attrValue || '',
              ),
            );
          props.form.setFieldsValue({
            [`global_attribute${attribute.attrId}_value`]: valuesToSet,
          });
        } else if (
          attribute.inputType === FREE_TEXT_FILED &&
          attribute.attrFormatType ===
          AttrFormatType.FORMAT_QUANTITATIVE_WITH_UNIT
        ) {
          const value = props.globalProductAttributeData.attrSnapshotList.find(
            (snapshot) => snapshot.attrId === attribute.attrId,
          )?.attrValue;
          props.form.setFieldsValue({
            [`global_attribute${attribute.attrId}_value`]:
              value !== undefined ? parseFloat(value) : undefined,
            [`global_attribute${attribute.attrId}_value_unit`]: value?.replace(
              /[\d+.?\d*]/g,
              '',
            ),
          });
        } else {
          const item = props.globalProductAttributeData.attrSnapshotList.find(
            (snapshot) => snapshot.attrId === attribute.attrId,
          );
          props.form.setFieldsValue({
            [`global_attribute${attribute.attrId}_value`]: getDisplayName(
              item?.attrDefaultLangValue?.val || '',
              item?.attrValue || '',
            ),
          });
        }
      }
    }
  }, [props.globalProductAttributeData]);

  useEffect(() => {
    // 对 stockList 和 floatingStockLists,对 total 的 数据，填充 float stock
    const stockMergedList: (uploadAdmin.IStockBreakDownList &
      uploadAdmin.IFloatingStockList & { modelIdOfTotal?: number })[] = props.stockList.map(
        (
          item: uploadAdmin.IStockBreakDownList,
          index: number,
        ) => {
          const { modelId } = item;

          // 非 Total
          if (modelId) {
            const stockSetting = props.itemStock.find(
              (v) => item.modelId === v.modelId,
            );

            const stockBreakdownByLocation = stockSetting?.stockBreakdownByLocation?.find(
              (v) => item.locationId === v.locationId,
            );
            const isEnabled = stockBreakdownByLocation?.isEnabled || false;
            return { ...item, isEnabled };
          }

          // total
          const modelIdOfTotal = props.stockList[index - 1]?.modelId;
          const floatStock = props.floatingStockLists.find(
            (item: uploadAdmin.IFloatingStockList) =>
              item.modelId === modelIdOfTotal,
          );
          if (!floatStock) {
            return { ...item, modelIdOfTotal };
          }
          const { modelId: noUseModelId, ...otherFloatStock } = floatStock;
          return {
            ...item,
            ...otherFloatStock,
            modelIdOfTotal
          };
        },
      );
    // 排序 避免每次调用接口数据顺序不一致导致UI变化
    // @ts-expect-error
    stockMergedList.sort((pre, cur) => (pre.modelId || pre.modelIdOfTotal) - (cur.modelId || cur.modelIdOfTotal));
    setStockMergedList(stockMergedList);
  }, [props.stockList, props.itemStock]);

  const parseBlockedFulfilmentType = (blockedFulfilmentType = 0) => {
    const blockedSeller = [2, 3].includes(blockedFulfilmentType);
    const blockedWarehouse = [1, 3].includes(blockedFulfilmentType);
    return { blockedSeller, blockedWarehouse };
  };

  useEffect(() => {
    const stockSettings = props.itemStock
      .filter((item) => item.stockBreakdownByLocation?.length)
      .map((item) => {
        const { blockedWarehouse, blockedSeller } = parseBlockedFulfilmentType(
          item.blockedFulfilmentType,
        );

        return {
          modelId: item.modelId,
          blockedWarehouse,
          blockedSeller,
          settingType: item.settingType,
        };
      });
    setStockSettings(stockSettings);
  }, [props.itemStock]);

  const getBlockedFulfilmentType = (params: {
    blockedWarehouse?: boolean;
    blockedSeller?: boolean;
  }) => {
    const { blockedWarehouse, blockedSeller } = params;
    let blockedFulfilmentType = 0;
    if (blockedWarehouse && blockedSeller) {
      blockedFulfilmentType = 3;
    } else if (blockedWarehouse) {
      blockedFulfilmentType = 1;
    } else if (blockedSeller) {
      blockedFulfilmentType = 2;
    }
    return blockedFulfilmentType;
  };

  const changeLocationSetting = useCallback(
    (params: {
      modelId: number;
      locationId: string;
      isEnabled: boolean;
      index: number;
    }) => {
      const { modelId, index } = params;
      changeAction(setStockMergedList, index, { isEnabled: params.isEnabled });

      const { blockedWarehouse, blockedSeller } =
        stockSettings.find((item) => item.modelId === modelId) || {};

      const blockedFulfilmentType = getBlockedFulfilmentType({
        blockedWarehouse,
        blockedSeller,
      });
      const locationSettings =
        stockMergedList
          .filter((item) => item.modelId === modelId)
          ?.map(({ locationId, isEnabled }) => ({
            locationId,
            isEnabled:
              locationId === params.locationId
                ? params.isEnabled
                : isEnabled || false,
          })) || [];
      props.updateModelSettingV2({
        modelsSetting: [
          {
            modelId,
            blockedFulfilmentType,
            locationSettings,
          },
        ],
      });
    },
    [
      props.updateModelSettingV2,
      stockSettings,
      setStockMergedList,
      stockMergedList,
    ],
  );
  const changeBlockedFulfillmentType = useCallback(
    (params: {
      modelId?: number;
      blockedWarehouse?: boolean;
      blockedSeller?: boolean;
      settingType?: number;
      index: number;
    }) => {
      const { blockedWarehouse, blockedSeller } = params;
      const { modelId, index, ...otherParams } = params;
      changeAction(setStockSettings, index, otherParams);

      const blockedFulfilmentType = getBlockedFulfilmentType({
        blockedWarehouse,
        blockedSeller,
      });

      const modelsSetting = modelId
        ? [
          {
            modelId,
            blockedFulfilmentType,
          },
        ]
        : [];

      props.updateModelSettingV2({
        modelsSetting,
      });
    },
    [setStockSettings, props.updateModelSettingV2],
  );

  const ProductImages = useMemo(() => {
    return (
      <div
        onMouseEnter={() => setCanDrag(true)}
        onMouseLeave={() => setCanDrag(false)}
      >
        {/* @ts-expect-error */}
        <DndProvider manager={manager.current.dragDropManager!}>
          <ImageUploader
            accept={'.jpg,.jpeg,.png'}
            listType="picture-card"
            action={IMAGE_UPLOAD}
            mmsOptions={{ biz: 4207 }}
            headers={{
              'Request-Id': uuid.v4(),
              Region: getCountry(),
            }}
            fileList={props.images.productImages}
            beforeUpload={handleBeforeProductImageUpload}
            onChange={(info) => handleImageChange(info, ImageType.PRODUCT)}
            showUploadList={{
              showPreviewIcon: true,
              showRemoveIcon: props.isEditing,
              showDownloadIcon: false,
            }}
            onRemove={
              props.images.productImages.length === 1
                ? () => {
                  message.error('Sorry, last image cannot be deleted.');
                  return false;
                }
                : undefined
            }
            className={styles.imageUploadBox}
            itemRender={(originNode, file: any, currFileList: any) => {
              try {
                const node = ThumbnailWrap(originNode, file);
                return props.isEditing && canDrag ? (
                  <DraggableUploadListItem
                    originNode={node}
                    file={file}
                    fileList={currFileList}
                    moveRow={moveRow}
                  />
                ) : (
                  node
                );
              } catch (e) {
                try {
                  return props.isEditing && canDrag ? (
                    <DraggableUploadListItem
                      originNode={originNode}
                      file={file}
                      fileList={currFileList}
                      moveRow={moveRow}
                    />
                  ) : (
                    originNode
                  );
                } catch (e1) {
                  return originNode;
                }
              }
            }}
          >
            {props.isEditing &&
              props.images.productImages.length <
              (props.region === 'TW' &&
                (props.item.isOfficial || props.item.isPreferred)
                ? 12
                : 9) && <PlusOutlined />}
          </ImageUploader>
        </DndProvider>
      </div>
    );
  }, [
    props.images.productImages,
    props.isEditing,
    props.region,
    props.item.isOfficial,
    props.item.isPreferred,
    manager.current.dragDropManager,
    handleImageChange,
    canDrag,
    setCanDrag,
    moveRow,
  ]);

  const ProductLongImages = useMemo(() => {
    return (
      <div className={styles.flexwrapContainer}>
        {props.images.productLongImages.map((image, idx) => (
          <span key={`${image.name}_${idx}`}>
            <Upload
              listType="picture-card"
              fileList={[image]}
              showUploadList={{
                showPreviewIcon: true,
                showRemoveIcon: false,
                showDownloadIcon: false,
              }}
              className={styles.longImageBox}
              itemRender={(originNode, file) => ThumbnailWrap(originNode, file)}
            />
          </span>
        ))}
      </div>
    );
  }, [props.images.productLongImages]);

  const VariationImages = useMemo(() => {
    return (
      <Card title="Variation Images:" size="small">
        <div className={styles.flexwrapContainer}>
          {props.images.variationImages.map((image, idx) => (
            <span key={`${image.name}_${idx}`}>
              {image.name}
              <Upload
                listType="picture-card"
                data={(file: UploadFile) => ({
                  file,
                  thumbnail_size: 640,
                  force_override: true,
                  multi_size: false,
                })}
                fileList={[image]}
                showUploadList={{
                  showPreviewIcon: true,
                  showRemoveIcon: false,
                  showDownloadIcon: false,
                }}
                className={styles.imageUploadBox}
                itemRender={(originNode, file) =>
                  ThumbnailWrap(originNode, file)
                }
              />
            </span>
          ))}
        </div>
      </Card>
    );
  }, [props.images.variationImages]);

  const CategoriesComponent = useMemo(() => {
    return (
      <Categories
        item={props.item}
        region={props.region}
        form={props.form}
        setLeafCat={props.setLeafCat}
        isEditing={props.isEditing}
        categoryType={CategoryType.LOCAL}
      />
    );
  }, [props.item, props.region, props.form, props.setLeafCat, props.isEditing]);

  const PreOrderDisplay = ({ value }: { value?: boolean }) => <div>{value ? 'Yes' : 'No'}</div>
  const PriceSectionComponent = useMemo(() => {
    return (
      <PriceSection
        item={props.item}
        region={props.region}
        form={props.form}
        itemModelList={props.itemModelList}
        isEditing={props.isEditing}
      />
    );
  }, [
    props.item,
    props.region,
    props.form,
    props.itemModelList,
    props.isEditing,
  ]);

  const Stock = useMemo(() => {
    return (
      <div className={styles.flexboxContainer}>
        <span className={styles.label}>Stock:</span>
        <>
          {props.item.useNewStockStructure ||
            (props.itemModelList.length > 0 &&
              props.itemModelList.find(
                (model) =>
                  model.status === PromotionStatus.STATUS_PROMOTION_NORMAL,
              )) ? (
            `${props.item.stock}`
          ) : (
            <Form.Item name={'stock'} rules={[fiveDigitDecimalRule]}>
              <Input size={'small'} disabled={!props.isEditing} />
            </Form.Item>
          )}
        </>
      </div>
    );
  }, [
    props.item.useNewStockStructure,
    props.itemModelList,
    props.item.stock,
    props.isEditing,
  ]);

  const MPQ = useMemo(() => {
    return (
      <div className={styles.flexboxContainer}>
        <span className={styles.label}>Minimum Purchase Quantity:</span>
        {props.isShopWhitelisted ? (
          <Form.Item
            name="minPurchaseLimit"
            rules={[
              {
                pattern: nonNegativeIntegerRegex,
                message: 'MPQ should be integer, 0-999,999',
              },
            ]}
          >
            <Input size={'small'} disabled={!props.isEditing} />
          </Form.Item>
        ) : (
          props.item.minPurchaseLimit
        )}
      </div>
    );
  }, [props.isShopWhitelisted, props.isEditing, props.item.minPurchaseLimit]);

  const OverallPurchaseLimitForOrder = useMemo(() => {
    return (
      props.item.orderMaxPurchaseLimit != null &&
      props.item.customizedPurchaseLimit == null && (
        <div className={styles.flexboxContainer}>
          <span className={styles.label}>
            Overall Purchase Limit (Order Level)
            <Tooltip
              title={
                'Overall Purchase limit (Order Level) is an item/ order level field. It limits the number of this item that buyers can buy per order.'
              }
              className={styles.tooltip}
            >
              <InfoCircleOutlined />
            </Tooltip>
            :
          </span>
          {props.item.orderMaxPurchaseLimit}
        </div>
      )
    );
  }, [props.item.orderMaxPurchaseLimit, props.item.customizedPurchaseLimit]);

  const OverallPurchaseLimitForCustomised = useMemo(() => {
    return (
      props.item.customizedPurchaseLimit != null && (
        <div className={styles.flexboxContainer}>
          <span className={styles.label}>
            Overall Purchase Limit (Customised Level)
            <Tooltip
              title={
                'Overall Purchase limit (Customised Level) is an item/ user/ time period level field. It limits the number of this item that each buyer can buy in the specified time period.'
              }
              className={styles.tooltip}
              placement={'top'}
            >
              <InfoCircleOutlined />
            </Tooltip>
            :
          </span>
          <div className={styles.formValue}>
            <div className={styles.flexboxContainer}>
              <span className={styles.shortFormLabel}>
                Ongoing/Upcoming Round Duration:
              </span>
              {`${convertToDisplayTime(
                props.item.customizedPurchaseLimit.startTime,
                oplDisplayTimeFormat,
                'Invalid start date',
                true,
              )} - ${convertToDisplayTime(
                props.item.customizedPurchaseLimit.endTime,
                oplDisplayTimeFormat,
                'Invalid end date',
                true,
              )}`}
            </div>
            <div className={styles.flexboxContainer}>
              <span className={styles.shortFormLabel}>
                Repeat Time
                <Tooltip
                  title={'Remaining repeat times + 1 (the ongoing one)'}
                  className={styles.tooltip}
                  placement={'top'}
                >
                  <InfoCircleOutlined />
                </Tooltip>
                :
              </span>
              {props.item.customizedPurchaseLimit.remainingRepeatedTimes}
            </div>
            <div className={styles.flexboxContainer}>
              <span className={styles.shortFormLabel}>
                Recurrence End Time:
              </span>
              {convertToDisplayTime(
                props.item.customizedPurchaseLimit.recurrenceEndTime,
                oplDisplayTimeFormat,
                'Invalid end date',
                true,
              )}
            </div>
            <div className={styles.flexboxContainer}>
              <span className={styles.shortFormLabel}>Purchase Limit:</span>
              {props.item.customizedPurchaseLimit.purchaseLimit}
            </div>
          </div>
        </div>
      )
    );
  }, [props.item.customizedPurchaseLimit]);

  const Conditions = useMemo(() => {
    return (
      <div className={styles.flexboxContainer}>
        <span className={styles.label}>Condition:</span>
        <Form.Item name="condition">
          <Select
            style={{ width: '160px' }}
            disabled={!props.isEditing}
            size={'small'}
          >
            {Object.keys(conditionString).map((condition, idx) => (
              <Select.Option value={Number(condition)} key={idx}>
                {
                  conditionString[
                  Number(condition) as keyof typeof conditionString
                  ]
                }
              </Select.Option>
            ))}
          </Select>
        </Form.Item>
      </div>
    );
  }, [props.isEditing]);

  const GTIN = useMemo(() => {
    return (
      props.itemModelList.length === 0 ||
      (!props.itemModelList.find(
        (model) => model.status === PromotionStatus.STATUS_PROMOTION_NORMAL,
      ) && (
          <div className={styles.flexboxContainer}>
            <span className={styles.label}>GTIN:</span>
            <Form.Item name="gtin">
              <Input size={'small'} disabled={!props.isEditing} />
            </Form.Item>
          </div>
        ))
    );
  }, [props.itemModelList, props.isEditing]);

  const ProductAttributesComponent = useMemo(() => {
    return (
      <ProductAttributes
        item={props.item}
        form={props.form}
        productAttributeData={props.productAttributeData}
        isEditing={props.isEditing}
        editModeDataSource={props.editModeDataSource}
      />
    );
  }, [
    props.item,
    props.form,
    props.productAttributeData,
    props.isEditing,
    props.editModeDataSource,
  ]);


  const categoryFrom = Array.from({length: maxCatLevel}, (_, i) => Form.useWatch(`l${i + 1}_global_category`, props.form));

  const GlobalCategory = useMemo(() => {
    const qcVerifiedNode = props.item.autoCorrectCategoryInfo?.verifiedCatNodes?.map(n => n.catNodeName).join(' > ');
    const categoryPath: number[] = props.isEditing
    ? Array.from({length: maxCatLevel}, (_, i) => props.form.getFieldValue(`l${i + 1}_global_category`)).filter((item) => item) as number[]
    : ( props.item.globalCategoryList ?.map((cat) => cat.catId) ?? []) as number[];

    return (<>
      <Card title={'Global Category'} size="small">
        <p>
          Agent verified category will be updated if you re-select category in Listing Admin, which determines product penalty. Current agent verified node is: {qcVerifiedNode || 'N/A'}
        </p>
        <Categories
          item={props.item}
          region={props.region}
          form={props.form}
          setLeafCat={props.setGlobalLeafCat}
          isEditing={props.isEditing}
          categoryType={CategoryType.GLOBAL}
        />
      </Card>
     <TaxRateTips categoryPath={categoryPath}/>
     </>
    );
  }, [props.item, props.region, props.form, props.setLeafCat, props.isEditing,categoryFrom]);

  const GlobalBrandComponent = useMemo(() => {
    return (
      <GlobalBrand
        form={props.form}
        savedBrandId={props.item.globalBrandId}
        savedBrandName={props.item.globalBrandName}
        savedBrandLocalName={props.item.globalBrandLocalDisplayName}
        globalLeafCat={props.globalLeafCat}
        region={props.region}
        isEditing={props.isEditing}
      />
    );
  }, [
    props.form,
    props.item.globalBrandId,
    props.item.globalBrandName,
    props.item.globalBrandLocalDisplayName,
    props.globalLeafCat,
    props.region,
    props.isEditing,
  ]);

  const GlobalAttributeComponent = useMemo(() => {
    return (
      <GlobalAttribute
        globalProductAttributeData={props.globalProductAttributeData}
        form={props.form}
        editModeGlobalDataSource={props.editModeGlobalDataSource}
        isEditing={props.isEditing}
        getBooksInfoByIsbnData={props.getBooksInfoByIsbnData}
      />
    );
  }, [
    props.globalProductAttributeData,
    props.form,
    props.editModeGlobalDataSource,
    props.isEditing,
  ]);

  const ShippingInfo = useMemo(() => {
    return (
      <Card title={'Shipping Information'} size="small">
        {props.item.oldShippingList &&
          props.item.oldShippingList.map((oldShippingInfo, idx) => {
            return (
              <div className={styles.flexboxContainer} key={idx}>
                <span
                  className={styles.label}
                >{`${oldShippingInfo.property} (${oldShippingInfo.channelName})`}</span>
                <span>{oldShippingInfo.value}</span>
              </div>
            );
          })}
      </Card>
    );
  }, [props.item.oldShippingList]);

  const ComplaintPolicyComponent = useMemo(() => {
    return (
      <ComplaintPolicy
        data={props.item.complaintPolicy}
        shopId={props.item.shopId}
      />
    );
  }, [props.item.complaintPolicy, props.item.shopId]);

  const ItemModelsComponent = useMemo(() => {
    return (
      <ItemModels
        item={props.item}
        region={props.region}
        form={props.form}
        itemModelList={props.itemModelList}
        variationList={props.variationList}
        setVariationList={props.setVariationList}
        isEditing={props.isEditing}
      />
    );
  }, [
    props.item,
    props.region,
    props.form,
    props.itemModelList,
    props.variationList,
    props.setVariationList,
    props.isEditing,
  ]);

  const StockSetting = useMemo(() => {
    return (
      <Card title={'Stock Setting'} size="small">
        <Table
          pagination={false}
          dataSource={stockSettings}
          columns={[
            {
              title: 'Model ID',
              dataIndex: 'modelId',
            },
            {
              title: (
                <>
                  <Tooltip title="Blocked fulfillment type settings will impact location status, i.e. if Block Seller = Yes, seller location status will be changed to OFF">
                    <InfoCircleFilled className={styles.infoIcon} />
                  </Tooltip>
                  Blocked Fulfillment Type
                </>
              ),
              render(text, record, index) {
                return (
                  <>
                    <div>
                      <span className={styles.label}>Block Warehouse: </span>
                      <Radio.Group
                        disabled={!props.isEditing}
                        value={record.blockedWarehouse}
                        onChange={({ target }) => {
                          changeBlockedFulfillmentType({
                            modelId: record.modelId,
                            blockedWarehouse: target.value,
                            blockedSeller: record.blockedSeller,
                            settingType: record.settingType,
                            index,
                          });
                        }}
                      >
                        <Radio value={true}>Yes</Radio>
                        <Radio value={false}>No</Radio>
                      </Radio.Group>
                    </div>
                    <div>
                      <span className={styles.label}>Block Seller: </span>
                      <Radio.Group
                        disabled={!props.isEditing}
                        value={record.blockedSeller}
                        onChange={({ target }) => {
                          changeBlockedFulfillmentType({
                            modelId: record.modelId,
                            blockedSeller: target.value,
                            blockedWarehouse: record.blockedWarehouse,
                            settingType: record.settingType,
                            index,
                          });
                        }}
                      >
                        <Radio value={true}>Yes</Radio>
                        <Radio value={false}>No</Radio>
                      </Radio.Group>
                    </div>
                  </>
                );
              },
            },
            {
              title: 'Applied Settings',
              dataIndex: 'settingType',
              render: (v: SettingType) => {
                return displaySettingType[v];
              },
            },
          ]}
          scroll={{ y: 770 }}
        />
      </Card>
    );
  }, [props.isEditing, stockSettings, changeBlockedFulfillmentType]);

  const StockBreakdownTable = useMemo(() => {
    return (
      <Card title={'Stock Breakdown Table'} size="small">
        <Table
          pagination={false}
          dataSource={stockMergedList.map((list, index) => {
            return { ...list, key: index };
          })}
          columns={[
            {
              title: 'Model ID',
              dataIndex: 'modelId',
            },
            {
              title: 'Location ID',
              dataIndex: 'locationId',
            },
            {
              title: 'Sellable Stock',
              dataIndex: 'sellableStock',
              render: (sellableStock, row, index) => {
                if (!props.item.useNewStockStructure) {
                  return row.sellableStock;
                }
                if (row.locationId?.toLocaleUpperCase() === 'TOTAL') {
                  return stockTotalMap[stockMergedList[index - 1]?.modelId!] ?? row.sellableStock;
                }
                if (row.locationId?.toLocaleUpperCase().endsWith('Z')) {
                  return <Form.Item name={['modelStock', `${row.modelId!}`, row.locationId]}>
                    <InputNumber disabled={!props.isEditing} min={0} onChange={() => calcTotalStock(row.modelId!)} />
                  </Form.Item>;
                }
                return row.sellableStock;
              }
            },
            {
              title: 'Fixed Reserved Stock',
              dataIndex: 'reservedStock',
            },
            {
              title: 'Floating Reserved Stock',
              dataIndex: 'totalFloatingStock',
              render: (text, record) => {
                // 详见 [SPLT-1518] PRD - Multi-Warehouse Promotion Reserved Stock Improvement
                const { floatingStockBreakdowns, modelId } = record;
                const columns = [
                  {
                    title: 'Promo ID',
                    dataIndex: 'ruleId',
                    key: 'ruleId',
                  },
                  {
                    title: 'Stock',
                    dataIndex: 'floatingStock',
                    key: 'floatingStock',
                  },
                  {
                    title: 'Location',
                    dataIndex: 'locationId',
                  },
                ];
                const table = (
                  <Table
                    size="small"
                    pagination={false}
                    dataSource={floatingStockBreakdowns}
                    columns={columns}
                  />
                );
                return (
                  <div key={modelId}>
                    <Popover
                      trigger="click"
                      placement="rightBottom"
                      content={table}
                      title="Breakdown"
                    >
                      <Button type="link" size="small">
                        {text}
                      </Button>
                    </Popover>
                  </div>
                );
              },
            },
            {
              title: 'Locked Stock',
              dataIndex: 'lockedStock',
            },
            {
              title: (
                <>
                  <Tooltip title="Do note that status = OFF, stock from this location will not be shown on Shopee FE for sale">
                    <InfoCircleFilled className={styles.infoIcon} />
                  </Tooltip>
                  Status
                </>
              ),
              dataIndex: 'isEnabled',
              render: (enabled, record, index) => {
                if (!record.modelId) {
                  return null;
                }

                const displayText = enabled ? 'On' : 'Off';
                return props.isEditing ? (
                  <Switch
                    checkedChildren="On"
                    unCheckedChildren="Off"
                    checked={!!enabled}
                    onChange={(isEnabled) => {
                      const { modelId = 0, locationId = '' } = record;
                      changeLocationSetting({
                        index,
                        modelId,
                        locationId,
                        isEnabled,
                      });
                    }}
                  />
                ) : (
                  displayText
                );
              },
            },
          ]}
          scroll={{ y: 620 }}
        />
      </Card>
    );
  }, [stockMergedList, changeLocationSetting, props.isEditing, stockTotalMap]);

  const SKU = useMemo(() => {
    return (
      props.item.isChildVsku && (
        <Card title={'Original SKU Info'} size="small">
          <Table
            pagination={false}
            dataSource={props.item.parentVskuList}
            rowKey={'parentItemId'}
            columns={[
              {
                title: 'Ori SKU Shop ID',
                dataIndex: 'parentShopId',
              },
              {
                title: 'Ori SKU Item ID',
                dataIndex: 'parentItemId',
              },
              {
                title: 'Ori SKU Model ID',
                dataIndex: 'parentModelId',
              },
              {
                title: 'Qty',
                dataIndex: 'quantity',
              },
              {
                title: 'Cost Price',
                dataIndex: 'costPrice',
                render: (cost) => {
                  return cost / FEtoBEMultiplier;
                },
              },
              {
                title: 'BE Link',
                key: 'link',
                render: (_, sku) => {
                  return isPageAnIframe ? (
                    <a
                      href={`${getAdminHost(
                        props.region,
                      )}/product/listing/v2/detail/${sku.parentShopId}/${sku.parentItemId
                        }/`}
                      target="_blank"
                      rel="noopener noreferrer"
                    >
                      Link
                    </a>
                  ) : (
                    <a
                      href={`//admin.listing.${props.env === 'live' ? '' : `${props.env}.`
                        }shopee.com/product/listing/detail/${sku.parentShopId}/${sku.parentItemId
                        }`}
                      target="_blank"
                      rel="noopener noreferrer"
                    >
                      Link
                    </a>
                  );
                },
              },
            ]}
          />
        </Card>
      )
    );
  }, [props.item.isChildVsku, props.item.parentVskuList, props.env]);

  return (
    <Card title="Product Details" size="small" className={styles.detailCard}>
      <Form form={props.form}>
        {/* Product Images */}
        <Card title="Product Images:" size="small">
          <Descriptions column={1}>
            <Descriptions.Item label="1:1 Image">
              {ProductImages}
            </Descriptions.Item>
            <Descriptions.Item label="3:4 Image">
              {ProductLongImages}
            </Descriptions.Item>
          </Descriptions>
        </Card>
        {/* Product Videos */}
        <Card title="Product Videos:" size="small">
          <Upload
            listType="picture-card"
            fileList={props.images.productVideos}
            showUploadList={{
              showPreviewIcon: true,
              showRemoveIcon: false,
              showDownloadIcon: false,
            }}
            className={styles.imageUploadBox}
            itemRender={(originNode, file) => ThumbnailWrap(originNode, file)}
          />
        </Card>
        {/* Variation Images */}
        {VariationImages}
        {/* White Background Images */}
        <Card title="White Background Images:" size="small">
          <ImageUploader
            multiple={false}
            mmsOptions={{ biz: 4207 }}
            accept={'.png'}
            listType="picture-card"
            action={IMAGE_UPLOAD}
            headers={{
              'Request-Id': uuid.v4(),
              Region: getCountry(),
            }}
            data={(file: UploadFile) => ({
              file,
              thumbnail_size: 640,
              force_override: true,
              multi_size: false,
            })}
            fileList={props.images.whiteBackgroundImages}
            beforeUpload={handleBeforeWhiteBgImageUpload}
            onChange={(info) =>
              handleImageChange(info, ImageType.WHITE_BACKGROUND)
            }
            showUploadList={{
              showPreviewIcon: true,
              showRemoveIcon: props.isEditing,
              showDownloadIcon: false,
            }}
            className={styles.imageUploadBox}
            itemRender={(originNode, file) => ThumbnailWrap(originNode, file)}
          >
            {props.isEditing &&
              props.images.whiteBackgroundImages.length === 0 && (
                <PlusOutlined />
              )}
          </ImageUploader>
        </Card>

        {/* Size Chart */}
        {props.item.sizeChart && (
          <>
            <div className={styles.flexboxContainer}>
              <div style={{ paddingRight: '8px' }}>Size Chart:</div>
              {props.isEditing && (
                <Form.Item name="deleteSizeChart" valuePropName="checked">
                  <Checkbox style={{ paddingTop: '4px' }}> remove </Checkbox>
                </Form.Item>
              )}
            </div>
            <Upload
              listType="picture-card"
              data={(file: UploadFile) => ({
                file,
                thumbnail_size: 640,
                force_override: true,
                multi_size: false,
              })}
              fileList={props.images.sizeChart}
              showUploadList={{
                showPreviewIcon: true,
                showRemoveIcon: false,
                showDownloadIcon: false,
              }}
              className={styles.imageUploadBox}
              itemRender={(originNode, file) => ThumbnailWrap(originNode, file)}
            />
          </>
        )}

        {/* Product Name */}
        <div className={styles.flexboxContainer}>
          <span className={styles.label}>Product Name:</span>
          <Form.Item
            className={styles.formValue}
            initialValue={props.item.productName}
            name="productName"
          >
            <TextArea
              disabled={!props.isEditing}
              autoSize={{ minRows: 1, maxRows: 2 }}
            />
          </Form.Item>
        </div>

        {/* Categories */}
        {CategoriesComponent}

        {/* Category HS Code */}
        {['ID', 'BR', 'PL', 'ES', 'FR'].includes(props.region) && (
          <div className={styles.flexboxContainer}>
            <span className={styles.label}>Category HS Code:</span>
            {props.item.categoryHsCode != null
              ? props.item.categoryHsCode
              : '-'}
          </div>
        )}
        {props.region === 'PL' && (
          <>
            <div className={styles.flexboxContainer}>
              <span className={styles.label}>Invoice option:</span>
              {props.item.euInvoiceOption != null
                ? EuInvoiceOptions[props.item.euInvoiceOption]
                : '-'}
            </div>
            <div className={styles.flexboxContainer}>
              <span className={styles.label}>Vat rate:</span>
              {props.item.euVatRate != null
                ? props.item.euVatRate / 100000
                : '-'}
            </div>
          </>
        )}
        {props.region === 'IN' && (
          <>
            <div className={styles.flexboxContainer}>
              <span className={styles.label}>HS Code:</span>
              {props.item.itemHsCode != null ? props.item.itemHsCode : '-'}
            </div>
            <div className={styles.flexboxContainer}>
              <span className={styles.label}>Gst Code:</span>
              {props.item.itemTaxCode != null ? props.item.itemTaxCode : '-'}
            </div>
          </>
        )}

        {/* Brand */}
        <div className={styles.flexboxContainer}>
          <span className={styles.label}>Brand:</span>
          <Form.Item name="brand">
            <Input size={'small'} disabled={!props.isEditing} />
          </Form.Item>
        </div>

        {/* Price */}
        {PriceSectionComponent}

        {/* Pre Order */}
        <div className={styles.flexboxContainer}>
          <span className={styles.label}>Pre Order:</span>
          <Form.Item noStyle name="isPreOrder">
            <PreOrderDisplay />
          </Form.Item>
        </div>
        {/* Days to Ship */}
        <div className={styles.flexboxContainer}>
          <span className={styles.label}>Days to Ship:</span>
          {props.itemModelList.every(m => !m.modelDtsSetting) ?
            props.isEditing ? (
              <Form.Item name="daysToShip" rules={[integerValueRule]}>
                <InputNumber min={0} size={'small'} onChange={(value: number) => {
                  props.form.setFieldsValue({
                    isPreOrder: +value !== +(props.item.nonPreOrderLimit ?? -1)
                  });
                }} />
              </Form.Item>
            ) : (props.item.daysToShip)
            : <Tooltip title='display the longest dts for all models here, please edit the model dts through model tab'>
              <span>{props.item.daysToShip}</span>
            </Tooltip>}
        </div>

        {/* Stock */}
        {Stock}

        {/* FE Stock Ceiling */}
        <div className={styles.flexboxContainer}>
          <span className={styles.label}>
            {'Stock ceiling'}
            <Tooltip
              title={
                'The stock ceiling will reduce available stock on PDP, to edit this config please go to FE Stock Ceiling v2 page'
              }
              className={styles.tooltip}
            >
              <InfoCircleOutlined />
            </Tooltip>
            {' :'}
          </span>
          {props.item.feStockCeiling === undefined
            ? '-'
            : props.item.feStockCeiling}
        </div>

        {/* Minimum Purchase Quantity (MPQ) */}
        {MPQ}

        {/* Overall Purchase Limit (Order Level)
          This is OPL case 1, for items with `max_purchase_limit` without `customizedPurchaseLimit`
        */}
        {OverallPurchaseLimitForOrder}

        {/* Overall Purchase Limit (Customised Level)
            This is OPL case 2, for items with `customizedPurchaseLimit`
          */}
        {OverallPurchaseLimitForCustomised}


        {/* Condition */}
        {Conditions}

        {/* GTIN */}
        {GTIN}

        {/* Product Attributes */}
        {ProductAttributesComponent}

        {/* Global Category */}
        {GlobalCategory}

        {/* Global Brand */}
        {GlobalBrandComponent}

        {/* Global Attribute */}
        {GlobalAttributeComponent}

        {/* Shipping Information */}
        {ShippingInfo}

        {/* Shipping Information(new) */}
        <NewShippingInfo
          item={props.item}
          region={props.region}
          env={props.env}
        />

        {/* Complaint Policy */}
        {ComplaintPolicyComponent}

        {/* Payment(new) */}
        {['TH', 'MY'].includes(props.region) && <NewPayment form={props.form} item={props.item} isEditing={props.isEditing} />}
        {/* Item Models */}
        {ItemModelsComponent}
        {/* Stock Setting */}
        {StockSetting}

        {/* Stock Breakdown Table */}
        {/* prevent edit if not use_new_stock_structure */}
        {StockBreakdownTable}

        {/* Wholesale */}
        {props.item.canUseWholesale && (
          <Wholesale item={props.item} region={props.region} />
        )}

        {/* Original SKU Info*/}
        {SKU}

        {/* Tax Information */}
        {getCountry() === 'BR' && (
          <Card title="Tax Information" size="small">
            <Form labelAlign="left" labelCol={{ span: 3 }}>
              <Form.Item label="NCM">
                {props.item.brGetItemInvoiceOptions?.ncm || '-'}
              </Form.Item>
              <Form.Item label="CEST">
                {props.item.brGetItemInvoiceOptions?.cest || '-'}
              </Form.Item>
              <Form.Item label="Measure Unit">
                {props.item.brGetItemInvoiceOptions?.measureUnit || '-'}
              </Form.Item>
              <Form.Item label="CFOP Same Statue">
                {props.item.brGetItemInvoiceOptions?.sameStateCfop || '-'}
              </Form.Item>
              <Form.Item label="CFOP Difference State">
                {props.item.brGetItemInvoiceOptions?.diffStateCfop || '-'}
              </Form.Item>
              <Form.Item label="Origin">
                {props.item.brGetItemInvoiceOptions?.origin || '-'}
              </Form.Item>
              <Form.Item label="CSOSN">
                {props.item.brGetItemInvoiceOptions?.csosn || '-'}
              </Form.Item>
            </Form>
          </Card>
        )}
        {getCountry() === 'TW' && (
          <Card title="Tax Information" size="small">
            <Form.Item label="Tax Type" name="taxType">
              <Select
                style={{ width: '160px' }}
                disabled={true}
                size={'small'}
                defaultValue={0}
              >
                {Object.keys(PRODUCT_TAX_TYPE_MAP).map((type, idx) => (
                  <Select.Option value={Number(type)} key={idx}>
                    {
                      PRODUCT_TAX_TYPE_MAP[
                      Number(type) as keyof typeof PRODUCT_TAX_TYPE_MAP
                      ]
                    }
                  </Select.Option>
                ))}
              </Select>
            </Form.Item>
          </Card>
        )}

        {/* Description */}
        <div style={{ marginTop: 16 }}>
          <span className={styles.label}>Description:</span>
          <Description
            env={props.env}
            region={props.region}
            descriptionType={props.item.descriptionType}
            description={props.item.description}
            itemId={props.item.itemId}
            shopId={props.item.shopId}
            disabled={!props.isEditing}
            onChange={params => {
              props.form.setFieldsValue(params);
            }}
          />
        </div>
        {/* Permit Certification */}
        {
          !!props.item?.certificationInfo?.certificationList?.length &&
          <div style={{ marginTop: 16 }} className={styles.flexboxContainer}>
            <span style={{ marginRight: 16 }}>Product Certification:</span>
            <div>{props.item.certificationInfo?.certificationList?.map((item) => item.certificationNo).join()}</div>
          </div>
        }

      </Form>
    </Card>
  );
}

export default ProductDetails;
