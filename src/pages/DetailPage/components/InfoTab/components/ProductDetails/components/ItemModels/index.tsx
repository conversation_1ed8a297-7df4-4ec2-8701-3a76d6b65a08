import { InfoCircleFilled } from '@ant-design/icons';
import type { IExportCSVConfig } from '@classification/export-csv';
import ExportCSV from '@classification/export-csv';
import { getCountryConfig } from '@shopee_common/currency';
import type { CountryCode } from '@shopee_common/currency/dist/typings/typings';
import { Button, Card, Form, Input, InputNumber, Modal, Space,Table, Tooltip } from 'antd';
import type { ColumnProps } from 'antd/es/table';
import type { FormInstance } from 'antd/lib/form';
import type { Dictionary } from 'lodash';
import _ from 'lodash';
import type { Dispatch} from 'react';
import React, { useEffect,useState } from 'react';

import { Status } from 'src/api/PBData/constants';
import type { uploadAdmin } from 'src/api/uploadAdmin/uploadAdmin';
import {
  FEtoBEMultiplier,
  integerValueRule,
  itemModelStatusString,
  oneDigitDecimalRule,
  priceRegexRule,
  promotionTypeString,
  regionsWithTax,
  threeDigitDecimalRule,
} from 'src/constants';
import { formatCurrencyForItem } from 'src/utils';
import styles from '../../../../../../styles.module.scss';

interface ItemModelsProps {
  item: uploadAdmin.IGetItemDetailResponse;
  region: string;
  itemModelList: uploadAdmin.IItemModel[];
  variationList: uploadAdmin.ITierVariation[];
  setVariationList: Dispatch<uploadAdmin.ITierVariation[]>;
  form: FormInstance;
  isEditing: boolean;
}

interface ItemModelColumnProps<T> extends ColumnProps<T> {
  csvTitle?: string;
}

function ItemModels(props: ItemModelsProps) {
  const [openEditModal1, setOpenEditModal1] = useState(false);
  // const [openEditModal2, setOpenEditModal2] = useState(false);
  const [modelDataSource, setModelDataSource] = useState(props.itemModelList);
  const [oldVariationList, setOldVariationList] = useState(props.variationList);
  const numberInputWidth = '80px';
  const dimensionsRules = props.form.getFieldValue('isSupportDecimal')
    ? oneDigitDecimalRule
    : integerValueRule;
  // re-order model list to match variation names on load
  useEffect(() => {
    if (props.variationList) {
      let modelsToSet: uploadAdmin.IItemModel[] = [];
      const normalModels = props.itemModelList.filter(
        (model) => model.status === Status.MODEL_NORMAL,
      );
      // DELETED, UNAVAILABLE
      const abnormalModels = props.itemModelList.filter(
        (model) => model.status !== Status.MODEL_NORMAL,
      );
      if (props.variationList.length > 1) {
        // two tier variation
        props.variationList[0]?.options?.forEach((t0) => {
          props.variationList[1]?.options?.forEach((t1) => {
            const modelToAdd = normalModels.find(
              (model) => model.modelName === `${t0},${t1}`,
            );
            if (modelToAdd) {
              modelsToSet.push(modelToAdd);
            }
          });
        });
      } else {
        // one tier variation
        props.variationList[0]?.options?.forEach((t0) => {
          const modelToAdd = normalModels.find(
            (model) => model.modelName === `${t0}`,
          );
          if (modelToAdd) {
            modelsToSet.push(modelToAdd);
          }
        });
      }
      modelsToSet = modelsToSet.concat(abnormalModels);
      setModelDataSource(modelsToSet);
    }
  }, [props.itemModelList]);

  // update model names on variation change
  useEffect(() => {
    if (modelDataSource && oldVariationList) {
      const normalModels = modelDataSource.filter(
        (model) => model.status === Status.MODEL_NORMAL,
      );
      if (props.variationList?.length > 1) {
        // two tier variation
        oldVariationList[0]?.options?.forEach((t0, idx0) => {
          oldVariationList[1]?.options?.forEach((t1, idx1) => {
            const modelToEdit = normalModels.find(
              (model) => model.modelName === `${t0},${t1}`,
            );
            if (modelToEdit) {
              modelToEdit.modelName = `${
                props.variationList[0].options![idx0]
              },${props.variationList[1].options![idx1]}`;
            }
          });
        });
      } else {
        // one tier variation
        props.variationList &&
          props.variationList[0]?.options?.forEach((t0, idx0) => {
            const modelToEdit = normalModels.find(
              (model) => model.modelName === `${t0}`,
            );
            if (modelToEdit) {
              modelToEdit.modelName = `${
                props.variationList[0].options![idx0]
              }`;
            }
          });
      }
    }
    setOldVariationList(props.variationList);
  }, [props.variationList]);

  function addColumns<T extends uploadAdmin.IItemModel, P extends ItemModelColumnProps<T>>(cols: P[] | P, isShow = true): P[] {
    const newCols = Array.isArray(cols) ? cols : [cols];
    return isShow ? newCols : [];
  }

  const specialFieldFormat: {
    fieldName: string;
    formatType: number;
    formatData?: { enumData?: Dictionary<string> };
  }[] = [];
  const columns: ItemModelColumnProps<uploadAdmin.IItemModel>[] = [
    {
      title: 'Model ID',
      dataIndex: 'modelId',
      fixed: true
    },
    {
      title: 'Model Name',
      dataIndex: 'modelName',
      fixed: true
    },
    {
      title: <>
        <Tooltip title="FE price after mark-up">
          <InfoCircleFilled className={styles.infoIcon} />
        </Tooltip>
        {`Price(${
        getCountryConfig(props.region as CountryCode).defaultCurrency
        })`}
      </>,
      dataIndex: 'originalPrice',
      render: (price, model) => {
        return !regionsWithTax.includes(props.region) &&
          model.status !== Status.MODEL_DELETE ? (
            <Form.Item
              name={`model${model.modelId}_original_price`}
              rules={[priceRegexRule]}
            >
              <Input
                disabled={!props.isEditing}
                style={{ width: numberInputWidth }}
              />
            </Form.Item>
          ) : price ? (
            formatCurrencyForItem(
              props.region as CountryCode,
              price / FEtoBEMultiplier,
            )
          ) : (
            '0'
          );
      },
    },
    ...addColumns(
      [
        {
          title: `itemPrice(${modelDataSource[0]?.itemPriceCurrency})`,
          dataIndex: 'itemPrice',
          render: (itemPrice) => {
            if (itemPrice === undefined) return '-';
            return itemPrice / FEtoBEMultiplier;
          },
        },
      ],
      !!modelDataSource[0]?.itemPriceCurrency,
    ),
    ...addColumns(
      [
        {
          title: <>
          <Tooltip title="Seller's original price">
            <InfoCircleFilled className={styles.infoIcon} />
          </Tooltip>
          {`Price Before Tax(${
            getCountryConfig(props.region as CountryCode).defaultCurrency
          })`}
        </> ,
          dataIndex: 'originalPriceBeforeTax',
          render: (price, model) => {
            return model.status !== Status.MODEL_DELETE ? (
              <Form.Item
                name={`model${model.modelId}_original_price_before_tax`}
                rules={[priceRegexRule]}
              >
                <Input
                  disabled={!props.isEditing}
                  style={{ width: numberInputWidth }}
                />
              </Form.Item>
            ) : price ? (
              formatCurrencyForItem(
                props.region as CountryCode,
                price / FEtoBEMultiplier,
              )
            ) : (
              '0'
            );
          },
        },
      ],
      regionsWithTax.includes(props.region),
    ),
    ...addColumns(
      [
        {
          title: `SIP Selling Price(${
            getCountryConfig(props.region as CountryCode).defaultCurrency
          })`,
          dataIndex: 'sellingPrice',
          render: (price) =>
            price
              ? formatCurrencyForItem(
                props.region as CountryCode,
                price / FEtoBEMultiplier,
              )
              : '',
        },
      ],
      props.item.cbOption === 1,
    ),
    ...addColumns(
      [
        {
          title: `SIP Selling Price Before Tax(${
            getCountryConfig(props.region as CountryCode).defaultCurrency
          })`,
          dataIndex: 'sellingPriceBeforeTax',
          render: (price) =>
            price
              ? formatCurrencyForItem(
                props.region as CountryCode,
                price / FEtoBEMultiplier,
              )
              : '',
        },
      ],
      props.item.cbOption === 1 && regionsWithTax.includes(props.region),
    ),
    {
      title: `Ongoing Promotion Price After Tax(${
        getCountryConfig(props.region as CountryCode).defaultCurrency
      })`,
      dataIndex: 'promotionPrice',
      render: (price, model) =>
        price
          ? `${formatCurrencyForItem(
            props.region as CountryCode,
            price / FEtoBEMultiplier,
          )} (${promotionTypeString[model.promotionType!]})`
          : '-',
    },
    ...addColumns(
      [
        {
          title: `Ongoing Promotion Price Before Tax(${
            getCountryConfig(props.region as CountryCode).defaultCurrency
          })`,
          dataIndex: 'promotionPriceBeforeTax',
          render: (price, model) =>
            price
              ? `${formatCurrencyForItem(
                props.region as CountryCode,
                price / FEtoBEMultiplier,
              )} (${promotionTypeString[model.promotionType!]})`
              : '-',
        },
      ],
      regionsWithTax.includes(props.region),
    ),
    {
      title: 'Stock',
      dataIndex: 'stock',
      render: (stock, model) => (
        <>
          {props.item.useNewStockStructure ? (
            `${stock}`
          ) : (
            <Form.Item
              name={`model${model.modelId}_stock`}
              rules={[integerValueRule]}
            >
              <Input
                disabled={!props.isEditing}
                style={{ width: numberInputWidth }}
              />
            </Form.Item>
          )}
        </>
      ),
    },
    {
      title: (
        <>
          <Tooltip title="The stock ceiling will reduce available stock on PDP, to edit this config please go to FE Stock Ceiling v2 page">
            <InfoCircleFilled className={styles.infoIcon} />
          </Tooltip>
          {'Stock ceiling'}
        </>
      ),
      csvTitle: 'Stock ceiling',
      dataIndex: 'feStockCeiling',
      render: (stockCeiling) =>
        stockCeiling === undefined ? '-' : stockCeiling,
    },
    ...addColumns([{
      title: 'Pre-Order',
      dataIndex: 'modelDtsSetting',
      render(_, model) {
        const PreOrderDisplay = ({ value }: {value?: boolean}) => <div>{value ? 'Yes' : 'No'}</div>;
        return <Form.Item noStyle name={[`model${model.modelId}_dts_setting`, 'isPreOrder']}>
          <PreOrderDisplay />
        </Form.Item>;
      }
    }, {
      title: 'Days to Ship',
      dataIndex: 'modelDtsSetting',
      render(_, model) {
        if (!props.isEditing) {
          return props.form.getFieldValue(`model${model.modelId}_dts_setting`)?.dayToShip;
        }
        return  <Form.Item name={[`model${model.modelId}_dts_setting`, 'dayToShip']}>
          <InputNumber
            min={0}
            style={{ width: numberInputWidth }}
            onChange={(value: number) => {
              props.form.setFieldsValue({
                [`model${model.modelId}_dts_setting`]: {
                  ...props.form.getFieldValue(`model${model.modelId}_dts_setting`),
                  isPreOrder: +value !== +(props.item.nonPreOrderLimit ?? -1)
                },
              });
            }}
          />
        </Form.Item>;
      }
    }], modelDataSource.some(model => model.modelDtsSetting)),
    {
      title: 'Seller Promotion ID',
      dataIndex: 'currentPromotionId',
      render: (id) => (id ? id : '-'),
    },
    {
      title: 'Status',
      dataIndex: 'status',
      render: (status: keyof typeof itemModelStatusString) =>
        itemModelStatusString[status],
      filters: [
        {
          text: itemModelStatusString[Status.MODEL_NORMAL],
          value: Status.MODEL_NORMAL,
        },
        {
          text: itemModelStatusString[Status.MODEL_UNAVAILABLE],
          value: Status.MODEL_UNAVAILABLE,
        },
      ],
      onFilter: (value, record) => record.status === value,
    },
    {
      title: 'Weight(Kg)',
      dataIndex: 'weight',
      render: (_, model) => (
        <Form.Item name={`model${model.modelId}_weight`} rules={[threeDigitDecimalRule]}>
          <Input
            disabled={!props.isEditing}
          />
        </Form.Item>
      ),
    },
    {
      title: 'Dimensions(W x L x H)(cm)',
      width: 300,
      render: (_, model) => (
        <Space.Compact>
          <Form.Item name={[`model${model.modelId}_dimension`, 'width']} rules={[dimensionsRules]} noStyle>
            <Input disabled={!props.isEditing} placeholder="Weight" />
          </Form.Item>
          <Form.Item name={[`model${model.modelId}_dimension`, 'length']} rules={[dimensionsRules]} noStyle>
            <Input disabled={!props.isEditing} placeholder="Length" />
          </Form.Item>
          <Form.Item name={[`model${model.modelId}_dimension`, 'height']} rules={[dimensionsRules]} noStyle>
            <Input disabled={!props.isEditing} placeholder="Height" />
          </Form.Item>
        </Space.Compact>
      ),
    },
    {
      title: 'SKU',
      dataIndex: 'sku',
    },
    {
      title: 'GTIN',
      dataIndex: 'gtin',
      render: (_, model) => (
        <Form.Item name={`model${model.modelId}_gtin`}>
          <Input
            disabled={!props.isEditing}
            style={{ width: numberInputWidth }}
          />
        </Form.Item>
      ),
    },
    {
      title: 'Default Model',
      dataIndex: 'isDefault',
      render: (isDefault: boolean) => {
        return isDefault ? 'True' : 'False';
      },
    },
  ];

  specialFieldFormat.push({
    fieldName: 'model_list.original_price',
    formatType: 0,
  });
  if (regionsWithTax.includes(props.region)) {
    specialFieldFormat.push({
      fieldName: 'model_list.original_price_before_tax',
      formatType: 0,
    });
  }
  if (
    // 1 if item is CB
    props.item.cbOption === 1
  ) {
    specialFieldFormat.push({
      fieldName: 'model_list.selling_price',
      formatType: 0,
    });
    if (regionsWithTax.includes(props.region)) {
      specialFieldFormat.push({
        fieldName: 'model_list.selling_price_before_tax',
        formatType: 0,
      });
    }
  }
  specialFieldFormat.push({
    fieldName: 'model_list.promotion_price',
    formatType: 0,
  });
  if (regionsWithTax.includes(props.region)) {
    specialFieldFormat.push({
      fieldName: 'model_list.promotion_price_before_tax',
      formatType: 0,
    });
  }
  specialFieldFormat.push({
    fieldName: 'model_list.status',
    formatType: 5,
    formatData: {
      enumData: {
        0: itemModelStatusString[0],
        1: itemModelStatusString[1],
        2: itemModelStatusString[2],
      },
    },
  });

  const getExportData = () => {
    return Promise.resolve({ data: modelDataSource });
  };
  const getExportFields = (): IExportCSVConfig => {
    const showCol = (
      fieldName: string,
      value: IExportCSVConfig['cols'][keyof IExportCSVConfig['cols']],
      toggle = false,
    ) => {
      if (!toggle) return;
      return { [fieldName]: value };
    };
    const formatPrice = (price: number, defaultPrice = '') => {
      return price
        ? formatCurrencyForItem(
          props.region as CountryCode,
          price / FEtoBEMultiplier,
        )
        : defaultPrice;
    };

    const cols: IExportCSVConfig['cols'] = {
      modelId: 'Model ID',
      modelName: 'Model Name',
      originalPrice: {
        header: `Price(${
          getCountryConfig(props.region as CountryCode).defaultCurrency
        })`,
        format: (item) => formatPrice(item.originalPrice, '0'),
      },
      ...showCol(
        'itemPrice',
        {
          header: `itemPrice(${modelDataSource[0]?.itemPriceCurrency})`,
          format(item) {
            const price = item.itemPrice;
            if (price === undefined) return '-';
            return price / FEtoBEMultiplier;
          },
        },
        !!modelDataSource[0]?.itemPriceCurrency,
      ),
      ...showCol(
        'originalPriceBeforeTax',
        {
          header: `Price Before Tax(${
            getCountryConfig(props.region as CountryCode).defaultCurrency
          })`,
          format: (item) => formatPrice(item.originalPriceBeforeTax, '0'),
        },
        regionsWithTax.includes(props.region),
      ),
      ...showCol(
        'sellingPrice',
        {
          header: `SIP Selling Price(${
            getCountryConfig(props.region as CountryCode).defaultCurrency
          })`,
          format: (item) => formatPrice(item.sellingPrice),
        },
        props.item.cbOption === 1,
      ),
      promotionPrice: {
        header: `Ongoing Promotion Price After Tax(${
          getCountryConfig(props.region as CountryCode).defaultCurrency
        })`,
        format: (item) => formatPrice(item.promotionPrice, '-'),
      },
      ...showCol(
        'promotionPriceBeforeTax',
        {
          header: `Ongoing Promotion Price Before Tax(${
            getCountryConfig(props.region as CountryCode).defaultCurrency
          })`,
          format: (item) => formatPrice(item.promotionPriceBeforeTax, '-'),
        },
        regionsWithTax.includes(props.region),
      ),
      stock: 'Stock',
      feStockCeiling: {
        header: 'Stock ceiling',
        format(item) {
          return item.stockCeiling === undefined ? '-' : item.stockCeiling;
        },
      },
      ...showCol('preOrder', {
        header: 'Pre-Order',
        format(item) {
          return item.modelDtsSetting.isPreOrder ? 'Yes' : 'No';
        }
      }, modelDataSource.some(model => model.modelDtsSetting)),
      ...showCol('daysToShip', {
        header: 'Days to Ship',
        format(item) {
          return item.modelDtsSetting.dayToShip;
        }
      }, modelDataSource.some(model => model.modelDtsSetting)),
      weight: {
        header: 'Weight(Kg)',
        format(model) {
          return (model.weight ?? props.item.weight) / FEtoBEMultiplier;
        },
      },
      dimension: {
        header: 'Dimensions(W x L x H)(cm)',
        format(model) {
          const dimensions = model.dimension ?? props.item.dimensions;
          return [dimensions.width, dimensions.length, dimensions.height].map(v => v / FEtoBEMultiplier).join('x');
        },
      },
      currentPromotionId: {
        header: 'Seller Promotion ID',
        format: (item) => item.id || '-',
      },
      status: {
        header: 'Status',
        format: (item) =>
          itemModelStatusString[
            item.status as keyof typeof itemModelStatusString
          ],
      },
      sku: 'SKU',
      gtin: 'GTIN',
      isDefault: {
        header: 'Default Model',
        format: (item) => (item.isDefault ? 'True' : 'False'),
      },
    };

    return {
      cols,
      fileName: `item_model_${props.item.shopId}_${props.item.itemId}`,
    };
  };

  return (
    <Card
      title={`Item Models${
        props.item.hasTierVariation ? ' (2-tier variation enabled)' : ''
      }`}
      size="small"
    >
      {(props.item.hasTierVariation || modelDataSource.length) && (
        <>
          <Modal
            open={openEditModal1}
            onCancel={() => setOpenEditModal1(false)}
            onOk={() => {
              setOpenEditModal1(false);
              // setOpenEditModal2(true);
              props.setVariationList(
                props.variationList.map((variation, variationNumber) => {
                  return {
                    ...variation,
                    options: variation.options?.map((option, idx) =>
                      props.form.getFieldValue(
                        `option_${variationNumber}_${idx}`,
                      ),
                    ),
                  };
                }),
              );
            }}
            title={'Edit Model Name for 2nd-tier Enabled Item'}
            // okText={'Next'}
          >
            <Table
              dataSource={
                props.variationList
                  ? props.variationList[0]
                    ? props.variationList[0].options
                      ?.map((option, idx) => {
                        return {
                          option: option,
                          variation: props.variationList[0].name,
                          key: `${0}_${idx}`,
                          variationNumber: 0,
                          optionNumber: idx,
                        };
                      })
                      .concat(
                        props.variationList[1] &&
                            props.variationList[1].options
                          ? props.variationList[1].options?.map(
                            (option, idx) => {
                              return {
                                option: option,
                                variation: props.variationList[1].name,
                                key: `${1}_${idx}`,
                                variationNumber: 1,
                                optionNumber: idx,
                              };
                            },
                          )
                          : [],
                      )
                    : []
                  : []
              }
              pagination={false}
              rowKey={'key'}
              columns={[
                {
                  title: 'Variation',
                  dataIndex: 'variation',
                  rowSpan: 2,
                  render: (variationOption, row, index) => {
                    const obj = {
                      children: variationOption,
                      props: { rowSpan: undefined as number | undefined },
                    };
                    if (row.optionNumber === 0) {
                      obj.props.rowSpan =
                        props.variationList[
                          row.variationNumber
                        ].options?.length;
                    } else {
                      obj.props.rowSpan = 0;
                    }
                    return obj;
                  },
                },
                {
                  title: 'Option',
                  dataIndex: 'option',
                  render: (option, row, idx) => (
                    <Form.Item name={`option_${row.key}`}>
                      <Input size={'small'} />
                    </Form.Item>
                  ),
                },
              ]}
            />
          </Modal>
          <div>
            <Button
              disabled={
                !props.isEditing ||
                !props.itemModelList.some((model) => !model.isDefault)
              }
              onClick={() => setOpenEditModal1(true)}
            >
              Edit model name
            </Button>
            <ExportCSV
              requestFunc={getExportData}
              exportConfig={getExportFields()}
            />
          </div>
          <Table
            pagination={false}
            dataSource={modelDataSource}
            columns={columns.map((item) => ({ ...item, width: item.width ?? 120 }))}
            rowKey={'modelId'}
            className={styles.table}
            scroll={{ x: 'max-content', y: 770 }}
          />
        </>
      )}
    </Card>
  );
}

export default ItemModels;
