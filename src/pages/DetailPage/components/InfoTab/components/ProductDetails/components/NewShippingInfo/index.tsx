import { CheckOutlined, CloseOutlined } from '@ant-design/icons';
import { getUserAdminHost } from 'admin-upload-common';
import { Card, Table } from 'antd';
import React from 'react';

import type { uploadAdmin } from 'src/api/uploadAdmin/uploadAdmin';

interface NewShippingInfoProps {
  item: uploadAdmin.IGetItemDetailResponse;
  env: string;
  region: string;
}

function NewShippingInfo(props: NewShippingInfoProps) {
  return (
    <Card title={'Shipping Information(new)'} size="small">
      For sellers whitelisted to masking, please check the shop enabled
      fulfilment channels on the corresponding user information page{' '}
      <a
        href={`${getUserAdminHost()}/detail?userid=${props.item.userId}&region=${props.region.toUpperCase()}`}
        target={'_top'}
      >
        link
      </a>
      <Table
        showHeader={false}
        pagination={false}
        dataSource={
          props.item.newShippingList ? props.item.newShippingList : []
        }
        rowKey={(record) => `${record.channelId}+${record.channelName}`}
        columns={[
          {
            dataIndex: 'channelId',
            render: (channelId, info) => {
              return `[${channelId}] ${info.channelName}`;
            },
          },
          {
            dataIndex: 'shopStatus',
            render: (_, info) => {
              return (
                <>
                  Shop Status:{' '}
                  {info.shopLogisticsInfo?.enabled ? (
                    <CheckOutlined style={{ color: '#00CC00' }} />
                  ) : (
                    <CloseOutlined style={{ color: '#CC0000' }} />
                  )}
                </>
              );
            },
          },
          {
            dataIndex: 'itemStatus',
            render: (_, info) => {
              return (
                <>
                  Item Status:{' '}
                  {info.itemLogisticsInfo?.enabled ? (
                    <CheckOutlined style={{ color: '#00CC00' }} />
                  ) : (
                    <CloseOutlined style={{ color: '#CC0000' }} />
                  )}
                </>
              );
            },
          },
        ]}
      />
    </Card>
  );
}

export default NewShippingInfo;
