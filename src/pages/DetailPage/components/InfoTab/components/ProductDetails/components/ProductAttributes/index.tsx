import { ComboBox } from 'admin-upload-common';
import { Card, DatePicker, Form, Input, Select, Table, Typography } from 'antd';
import type { FormInstance } from 'antd/lib/form';
import moment from 'moment';
import React, { useEffect,useState  } from 'react';

import { ItemAttributeInputType,ItemAttributeType } from 'src/api/PBData/constants';
import type { uploadAdmin } from 'src/api/uploadAdmin/uploadAdmin';
import {
  attributeSnapshotStatusString,
  attributeTypeString,
} from 'src/constants';

const { TIMESTAMP_TYPE } = ItemAttributeType;

interface ProductDetailsProps {
  item: uploadAdmin.IGetItemDetailResponse;
  productAttributeData:
  | uploadAdmin.IGetProductAttributesResponse
  | undefined;
  form: FormInstance;
  editModeDataSource: uploadAdmin.IProductAttr[];
  isEditing: boolean;
}

function ProductDetails(props: ProductDetailsProps) {
  const [title, setTitle] = useState<string>('Product Attributes');

  useEffect(() => {
    setTitle(
      `Product Attributes ${
        props.isEditing
          ? props.productAttributeData?.productAttrModel
            ? `(snapshot attribute model: ${props.productAttributeData.productAttrModel.modelName}, ID ${props.productAttributeData.productAttrModel.modelId})`
            : '(no model)'
          : props.productAttributeData?.attrModelSnapshot
            ? `(snapshot attribute model: ${props.productAttributeData.attrModelSnapshot.modelName}, ID: ${props.productAttributeData.attrModelSnapshot.modelId})`
            : '(no snapshot)'
      }`,
    );
  }, [props.isEditing, props.productAttributeData]);

  return (
    <Card title={title} size="small">
      {props.isEditing ? (
        props.editModeDataSource && props.editModeDataSource.length > 0 ? (
          <Table
            bordered
            pagination={false}
            showHeader={false}
            dataSource={props.editModeDataSource}
            rowKey={'attrId'}
            columns={[
              {
                dataIndex: 'attrName',
                width: '40%',
              },
              {
                dataIndex: 'values',
                width: '30%',
                render: (values, attribute) => {
                  const convertedValues =
                    values && attribute.validateType === TIMESTAMP_TYPE
                      ? values.map((value: number) =>
                        moment.unix(value).format('YYYY-MM-DD HH:mm:ss'),
                      )
                      : values;
                  switch (attribute.inputType) {
                    case ItemAttributeInputType.DROP_DOWN:
                      return (
                        <Form.Item name={`attribute${attribute.attrId}_value`}>
                          <Select size={'small'}>
                            {convertedValues &&
                              convertedValues.map((value: string | number) => (
                                <Select.Option key={value} value={value}>
                                  {value}
                                </Select.Option>
                              ))}
                          </Select>
                        </Form.Item>
                      );
                    case ItemAttributeInputType.COMBO_BOX:
                      return (
                        <Form.Item
                          name={`attribute${attribute.attrId}_value`}
                          valuePropName={'selectedValues'}
                        >
                          <ComboBox
                            id={attribute.attrId!}
                            form={props.form}
                            existingList={convertedValues}
                            onValuesChange={(values) => {
                              props.form.setFieldsValue({
                                [`attribute${attribute.attrId}_value`]: values[0],
                              });
                            }}
                            selectedValues={[
                              props.form.getFieldValue(
                                `attribute${attribute.attrId}_value`,
                              ),
                            ]}
                            isMultiple={false}
                            dateFormat={
                              attribute.attrType === TIMESTAMP_TYPE
                                ? 'YYYY-MM-DD HH:mm:ss'
                                : undefined
                            }
                            size={'small'}
                          ></ComboBox>
                        </Form.Item>
                      );
                    case ItemAttributeInputType.TEXT_FILED:
                    default:
                      return attribute.attrType === TIMESTAMP_TYPE ? (
                        <DatePicker
                          size={'small'}
                          picker={'date'}
                          format={'YYYY-MM-DD HH:mm:ss'}
                          defaultValue={moment(
                            props.form.getFieldValue(
                              `attribute${attribute.attrId}_value`,
                            ),
                          )}
                          showTime={true}
                          onSelect={(value) =>
                            props.form.setFieldsValue({
                              [`attribute${attribute.attrId}_value`]: value?.format(
                                'YYYY-MM-DD HH:mm:ss',
                              ),
                            })
                          }
                        />
                      ) : (
                        <Form.Item name={`attribute${attribute.attrId}_value`}>
                          <Input size={'small'} />
                        </Form.Item>
                      );
                  }
                },
              },
              {
                dataIndex: 'attrType',
                width: '30%',
                render: (value) =>
                  `${
                    value != null &&
                    (Object.values(ItemAttributeType).includes(value) ||
                      value === 0)
                      ? attributeTypeString[
                        value as keyof typeof attributeTypeString
                      ]
                      : `Unknown (${value})`
                  }`,
              },
            ]}
          />
        ) : (
          <Typography.Title level={2}> No model linked to this category</Typography.Title>
        )
      ) : (
        props.productAttributeData?.attrSnapshotList &&
        props.productAttributeData.attrSnapshotList.length > 0 && (
          <Table
            bordered
            pagination={false}
            showHeader={false}
            dataSource={props.productAttributeData?.attrSnapshotList}
            columns={[
              {
                dataIndex: 'attribute',
                render: (attribute) => attribute?.attrName ?? '',
                width: '40%',
              },
              {
                dataIndex: 'value',
                width: '30%',
                render: (value, snapshot) => {
                  return snapshot.attribute?.attrType === TIMESTAMP_TYPE
                    ? moment.unix(value).format('YYYY-MM-DD HH:mm:ss')
                    : value;
                },
              },
              {
                dataIndex: 'status',
                render: (status) =>
                  `Status: ${
                    status != null &&
                    Object.keys(attributeSnapshotStatusString).includes(
                      status.toString(),
                    )
                      ? attributeSnapshotStatusString[
                        status as keyof typeof attributeSnapshotStatusString
                      ]
                      : `Unknown (${status})`
                  }`,
                width: '30%',
              },
            ]}
          />
        )
      )}
    </Card>
  );
}

export default ProductDetails;
