import { getCountryConfig } from '@shopee_common/currency';
import type { CountryCode } from '@shopee_common/currency/dist/typings/typings';
import { Card, Table,Tag } from 'antd';
import React from 'react';

import type { uploadAdmin } from 'src/api/uploadAdmin/uploadAdmin';
import { FEtoBEMultiplier,regionsWithTax } from 'src/constants';
import { formatCurrencyForItem } from 'src/utils';

interface WholesaleProps {
  item: uploadAdmin.IGetItemDetailResponse;
  region: string;
}

function Wholesale(props: WholesaleProps) {
  const columns = [
    {
      title: '',
      // eslint-disable-next-line @typescript-eslint/no-explicit-any
      render: (val: any, record: any, idx: number) => `Tier ${idx}`,
    },
    {
      title: 'Min Quantity',
      dataIndex: 'minCount',
    },
    {
      title: 'Max Quantity',
      dataIndex: 'maxCount',
    },
    {
      title: `Price(${
        getCountryConfig(props.region as CountryCode).defaultCurrency
      })/Unit`,
      dataIndex: 'price',
      render: (price: number) => {
        return formatCurrencyForItem(
          props.region as CountryCode,
          price / FEtoBEMultiplier,
        );
      },
    },
  ];

  if (regionsWithTax.includes(props.region)) {
    columns.push({
      title: `Price Before Tax(${
        getCountryConfig(props.region as CountryCode).defaultCurrency
      })/Unit`,
      dataIndex: 'inputPrice',
      render: (price: number) => {
        return formatCurrencyForItem(
          props.region as CountryCode,
          price / FEtoBEMultiplier,
        );
      },
    });
  }

  return (
    <Card
      title={
        <span>
          <span style={{ paddingRight: '10px' }}>Wholesale</span>
          <Tag color="green">Active</Tag>
        </span>
      }
    >
      <Table dataSource={props.item.wholesaleTierList} columns={columns} />
    </Card>
  );
}

export default Wholesale;
