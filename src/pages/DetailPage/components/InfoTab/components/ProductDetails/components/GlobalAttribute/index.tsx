import { ComboBox } from 'admin-upload-common';
import { Card, Form, Select,Table } from 'antd';
import type { FormInstance } from 'antd/lib/form';
import moment from 'moment';
import React, { useEffect,useState } from 'react';

import * as uploadAdminConstant from 'src/api/uploadAdmin/constants';
import type { uploadAdmin } from 'src/api/uploadAdmin/uploadAdmin';
import {
  globalAttributeStatusString,
  globalAttributeValidateTypeString,
  maxNumOfValuesInMultiValueAttribute,
} from 'src/constants';
import getDisplayName from '../../utils/localName';
import { FreeTextField } from './components/FreeTextField';
import { validationRules } from './constants';

interface GlobalAttributeProps {
  globalProductAttributeData:
  | uploadAdmin.IGetGlobalProductAttributesResponse
  | undefined;
  form: FormInstance;
  editModeGlobalDataSource:
  | uploadAdmin.IGlobalProductAttrList
  | undefined;
  isEditing: boolean;
  getBooksInfoByIsbnData: ()=> Promise<
  uploadAdmin.IGetBooksInfoByIsbnResponse
  >;
}

const {
  AttrDateTimeFormat,
  AttrInputType,
  AttrInputValidatorType,
  AttrStatus,
} = uploadAdminConstant;

const {
  SINGLE_DROP_DOWN,
  SINGLE_COMBO_BOX,
  FREE_TEXT_FILED,
  MULTI_DROP_DOWN,
  MULTI_COMBO_BOX,
} = AttrInputType;

const { VALIDATOR_DATE } = AttrInputValidatorType;

const { YEAR_MONTH_DATE, YEAR_MONTH } = AttrDateTimeFormat;

function GlobalAttribute(props: GlobalAttributeProps) {
  const [dataSource, setDataSource] = useState<
  uploadAdmin.IGlobalProductAttr[]
  >([]);
  const [needResetDataSource, setNeedResetDataSource] = useState(true);

  useEffect(() => {
    if (props.editModeGlobalDataSource) {
      const dataSourceToSet: uploadAdmin.IGlobalProductAttr[] = [];
      props.editModeGlobalDataSource?.globalAttributes?.map((attr) => {
        if (!attr.isChild) {
          dataSourceToSet.push(attr);
        } else {
          const childValues: string[] = [];
          // For every mapping that has this child
          props.editModeGlobalDataSource?.parentChildMappingAttrs
            ?.filter((mapping) => mapping.childAttrId === attr.attrId)
            .forEach((mapping) => {
              const parentFormValue = props.form.getFieldValue(
                `global_attribute${mapping.parentAttrId}_value`,
              );

              if (parentFormValue === undefined) {
                return;
              }

              const parentAttribute = props.editModeGlobalDataSource?.globalAttributes?.find(
                (attr) => attr.attrId === mapping.parentAttrId,
              );
              const childAttribute = props.editModeGlobalDataSource?.globalAttributes?.find(
                (attr) => attr.attrId === mapping.childAttrId,
              );

              // convert to array
              const parentValuesArray = Array.isArray(parentFormValue)
                ? // multi: multi_dropdown or multi_combo_box
                parentFormValue
                : // single
                [parentFormValue];

              let parentValueString = parentAttribute?.attrValueList?.find(
                (value) => value.id === mapping.parentValueId,
              )?.value;

              if (
                parentValueString &&
                parentAttribute?.validateType === VALIDATOR_DATE
              ) {
                parentValueString = moment
                  .unix(parseInt(parentValueString))
                  .format(
                    parentAttribute?.attrExtInfo?.datetimeFormat ===
                      YEAR_MONTH_DATE
                      ? 'DD/MM/YYYY'
                      : 'MM/YYYY',
                  );
              }

              // If there is parent attr value currently selected
              if (parentValuesArray.includes(parentValueString)) {
                // add child value to list of selectable values
                const childValueToAdd = childAttribute?.attrValueList?.find(
                  (value) => value.id === mapping.childValueId!,
                )?.value;
                if (childValueToAdd) {
                  childValues.push(childValueToAdd);
                }
              }
            });
          // If there is at least one selectable child attribute value
          if (childValues && childValues.length > 0) {
            // Add child attribute to list of attributes to render
            // with the child values found in mapping
            dataSourceToSet.push({
              ...attr,
              attrValueList: attr.attrValueList?.filter((value) =>
                childValues.includes(value.value!),
              ),
            });
            // If child value is in attr snapshot, update default selected to that/those.
            const snapshotVals = props.globalProductAttributeData?.attrSnapshotList?.filter((snapshot_attr) => snapshot_attr.attrId === attr.attrId)
              .reduce(
                (vals: string[], curr) => vals.concat(curr.attrValue!),
                [],
              );
            const valuesToSet = childValues
              .map((val) => {
                let convertedValue = val;
                if (attr.validateType === VALIDATOR_DATE) {
                  if (attr.attrExtInfo?.datetimeFormat === YEAR_MONTH_DATE) {
                    convertedValue = moment
                      .unix(parseInt(val))
                      .format('DD/MM/YYYY');
                  } else if (attr.attrExtInfo?.datetimeFormat === YEAR_MONTH) {
                    convertedValue = moment
                      .unix(parseInt(val))
                      .format('MM/YYYY');
                  }
                }
                return convertedValue;
              })
              .filter((val) => snapshotVals?.includes(val));

            if (valuesToSet && valuesToSet.length > 0) {
              if (
                attr.inputType === MULTI_DROP_DOWN ||
                attr.inputType === MULTI_COMBO_BOX
              ) {
                props.form.setFieldsValue({
                  [`global_attribute${attr.attrId}_value`]:
                    attr.inputType === MULTI_DROP_DOWN ||
                    attr.inputType === MULTI_COMBO_BOX
                      ? valuesToSet
                      : valuesToSet[0],
                });
              }
            } else {
              // No value found in snapshot, select first available value in list
              // for single dropdown
              if (attr.inputType === SINGLE_DROP_DOWN) {
                props.form.setFieldsValue({
                  [`global_attribute${attr.attrId}_value`]: childValues[0],
                });
              }
            }
          } else {
            // No selectable child value, ensure remove child attribute from form
            props.form.setFieldsValue({
              [`global_attribute${attr.attrId}_value`]: undefined,
            });
          }
        }
      });
      setDataSource(dataSourceToSet);
      setNeedResetDataSource(false);
    }
  }, [needResetDataSource, props.editModeGlobalDataSource]);

  return (
    <Card title={'Global Attribute'} size="small">
      {props.isEditing ? (
        <Table
          bordered
          pagination={false}
          showHeader={false}
          dataSource={dataSource}
          rowKey={'attrId'}
          columns={[
            {
              dataIndex: 'attrName',
              width: '30%',
              ellipsis: true,
              render: (text, record, index) => {
                return getDisplayName(
                  record.attrDefaultLangName?.val
                    ? record.attrDefaultLangName.val
                    : '',
                  record.attrName || '',
                );
              },
            },
            {
              dataIndex: 'attrValueList',
              width: '50%',
              render: (values, attribute, index) => {
                const maxMultiAttrValue =
                  props.globalProductAttributeData?.productAttrList
                    ?.globalAttributes?.[index]?.maxAttrValue ??
                  maxNumOfValuesInMultiValueAttribute;

                const dateFormat =
                  attribute.validateType === VALIDATOR_DATE
                    ? attribute.attrExtInfo?.datetimeFormat === YEAR_MONTH_DATE
                      ? 'DD/MM/YYYY'
                      : attribute.attrExtInfo?.datetimeFormat === YEAR_MONTH
                        ? 'MM/YYYY'
                        : undefined
                    : undefined;
                // Since id will be set 0 for all custom input, and value names are
                // guaranteed unique on BE, we can use value.value for both keys and values
                const convertedValues = values
                  ? attribute.validateType === VALIDATOR_DATE
                    ? values.map(
                      (
                        value: uploadAdmin.IAttributeValue,
                      ) =>
                        moment
                          .unix(parseInt(value.value!))
                          .format(dateFormat),
                    )
                    : values.map(
                      (
                        value: uploadAdmin.IAttributeValue,
                      ) =>
                        getDisplayName(
                            value.attrDefaultLangValue?.val
                              ? value.attrDefaultLangValue.val
                              : '',
                            value.value || '',
                        ),
                    )
                  : undefined;
                switch (attribute.inputType) {
                  case SINGLE_DROP_DOWN:
                    return (
                      <Form.Item
                        name={`global_attribute${attribute.attrId}_value`}
                      >
                        <Select
                          size={'small'}
                          onSelect={() => {
                            if (!attribute.isChild) {
                              setNeedResetDataSource(true);
                            }
                          }}
                          allowClear={true}
                        >
                          {convertedValues &&
                            convertedValues.map((value: string | number) => (
                              <Select.Option key={value} value={value}>
                                {value}
                              </Select.Option>
                            ))}
                        </Select>
                      </Form.Item>
                    );
                  case SINGLE_COMBO_BOX:
                    return (
                      <Form.Item
                        name={`global_attribute${attribute.attrId}_value`}
                        valuePropName={'selectedValues'}
                      >
                        <ComboBox
                          id={attribute.attrId ? attribute.attrId : 0}
                          form={props.form}
                          existingList={convertedValues}
                          onValuesChange={(values) => {
                            props.form.setFieldsValue({
                              [`global_attribute${attribute.attrId}_value`]: values[0],
                            });
                            if (!attribute.isChild) {
                              setNeedResetDataSource(true);
                            }
                          }}
                          selectedValues={[
                            props.form.getFieldValue(
                              `global_attribute${attribute.attrId}_value`,
                            ),
                          ]}
                          isMultiple={false}
                          dateFormat={dateFormat}
                          unitsList={attribute.attrExtInfo?.unitList?.units}
                          validationRules={
                            validationRules[
                              attribute.validateType as keyof typeof validationRules
                            ]
                          }
                          size={'small'}
                        ></ComboBox>
                      </Form.Item>
                    );
                  case FREE_TEXT_FILED:
                    return (
                      <FreeTextField
                        attribute={attribute}
                        dateFormat={dateFormat}
                        form={props.form}
                        onBlurCallback={() => {
                          if (attribute.isIsbn) {
                            props.getBooksInfoByIsbnData();
                          }
                        }}
                        onChangeCallback={() => {
                          if (!attribute.isChild) {
                            setNeedResetDataSource(true);
                          }
                        }}
                        validationRules={
                          validationRules[
                            attribute.validateType as keyof typeof validationRules
                          ]
                        }
                        size={'small'}
                      />
                    );
                  case MULTI_DROP_DOWN:
                    return (
                      <Form.Item
                        name={`global_attribute${attribute.attrId}_value`}
                      >
                        <Select
                          size={'small'}
                          mode={'multiple'}
                          onSelect={() => {
                            if (!attribute.isChild) {
                              setNeedResetDataSource(true);
                            }
                          }}
                          allowClear={true}
                        >
                          {convertedValues &&
                            convertedValues.map((value: string | number) => (
                              <Select.Option
                                key={value}
                                value={value}
                                disabled={
                                  props.form.getFieldValue(
                                    `global_attribute${attribute.attrId}_value`,
                                  )?.length >= maxMultiAttrValue
                                }
                              >
                                {value}
                              </Select.Option>
                            ))}
                        </Select>
                      </Form.Item>
                    );
                  case MULTI_COMBO_BOX:
                    return (
                      <Form.Item
                        name={`global_attribute${attribute.attrId}_value`}
                        valuePropName={'selectedValues'}
                      >
                        <ComboBox
                          id={attribute.attrId ? attribute.attrId : 0}
                          form={props.form}
                          existingList={convertedValues}
                          onValuesChange={(values) => {
                            props.form.setFieldsValue({
                              [`global_attribute${attribute.attrId}_value`]: values,
                            });
                            if (!attribute.isChild) {
                              setNeedResetDataSource(true);
                            }
                          }}
                          selectedValues={props.form.getFieldValue(
                            `global_attribute${attribute.attrId}_value`,
                          )}
                          isMultiple={true}
                          dateFormat={dateFormat}
                          unitsList={attribute.attrExtInfo?.unitList?.units}
                          maxNumOfValues={maxMultiAttrValue}
                          validationRules={
                            validationRules[
                              attribute.validateType as keyof typeof validationRules
                            ]
                          }
                          size={'small'}
                        ></ComboBox>
                      </Form.Item>
                    );
                  default:
                    return 'Error: Invalid attribute input type';
                }
              },
            },
            {
              dataIndex: 'validateType',
              width: '20%',
              render: (value) =>
                `${
                  value != null &&
                  Object.values(AttrInputValidatorType).includes(value)
                    ? globalAttributeValidateTypeString[
                      value as keyof typeof globalAttributeValidateTypeString
                    ]
                    : `Unknown (${value})`
                }`,
            },
          ]}
        />
      ) : (
        <Table
          bordered
          pagination={false}
          showHeader={false}
          dataSource={
            props.globalProductAttributeData?.attrSnapshotList
              ? [
                // Make a copy of the array to ensure array from data is untouched
                ...props.globalProductAttributeData?.attrSnapshotList,
                // Use .reduce() to pupt all values of same attribute together in display mode
              ]
                .map((item) => {
                  return {
                    ...item,
                    attrValue: getDisplayName(
                        item.attrDefaultLangValue?.val || '',
                        item.attrValue || '',
                    ),
                  };
                })
                .reduce(
                  (
                    arr: uploadAdmin.IGlobalAttrSnapshotInfo[],
                    curr,
                  ) => {
                    const existingAttr = arr.find(
                      (attr) => attr.attrId === curr.attrId,
                    );
                      // Make a copy of the attribute in the array
                    let attrCpy = existingAttr;
                    if (existingAttr) {
                      attrCpy = {
                        ...existingAttr,
                        attrValue: existingAttr.attrValue?.concat(
                            `,${  curr.attrValue}`,
                          ),
                      };
                      arr[arr.indexOf(existingAttr)] = attrCpy;
                    } else {
                      arr = arr.concat(curr);
                    }
                    return arr;
                  },
                  [],
                )
              : []
          }
          rowKey={'attrId'}
          columns={[
            {
              dataIndex: 'attrName',
              width: '30%',
              ellipsis: true,
              render: (text, record, index) => {
                return getDisplayName(
                  record.attrDefaultLangName?.val
                    ? record.attrDefaultLangName.val
                    : '',
                  record.attrName || '',
                );
              },
            },
            {
              dataIndex: 'attrValue',
              width: '50%',
              ellipsis: true,
            },
            {
              dataIndex: 'attrStatus',
              render: (status) =>
                `Status: ${
                  status != null && !Object.keys(AttrStatus).includes(status)
                    ? globalAttributeStatusString[
                      status as keyof typeof globalAttributeStatusString
                    ]
                    : `Unknown (${status})`
                }`,
              width: '20%',
            },
          ]}
        />
      )}
    </Card>
  );
}

export default GlobalAttribute;
