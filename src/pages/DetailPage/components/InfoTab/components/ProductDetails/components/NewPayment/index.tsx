import type { FormInstance} from 'antd';
import { Card, Form,Select, Table } from 'antd';
import React from 'react';

import type { uploadAdmin } from 'src/api/uploadAdmin/uploadAdmin';

interface NewPaymentProps {
  item: uploadAdmin.IGetItemDetailResponse;
  isEditing: boolean;
  form: FormInstance;
}

function NewPayment(props: NewPaymentProps) {
  return (
    <Form form={props.form} component={false}>
      <Card title={'Payment(new)'} size="small">
        {props.item.itemInstallmentTenure ? (
          <Table
            pagination={false}
            showHeader={false}
            dataSource={[props.item.itemInstallmentTenure]}
            columns={[
              {
                key: 'installment',
                render: () => 'Installment',
              },
              {
                dataIndex: 'shopLevelEnabled',
                render: (status) => {
                  return <>Shop Status: {status ? 'On' : 'Off'}</>;
                },
              },
              {
                render: (status, tenure) => {
                  const options =
                    tenure.availableTenure?.sort((a,b)=>a-b).reduce(
                      (pre: number[][], curr: number) => {
                        const currItem = [...(pre[pre.length - 1] || []), curr];
                        pre.push(currItem);
                        return pre;
                      },
                      [],
                    ) ?? [];
                  options.unshift([]);
                  return (
                    <Form.Item
                      name="enabledTenures"
                      label="Item Status"
                      tooltip="if you want to adjust item level cc installment, please turn on shop level cc installment"
                    >
                      <Select
                        disabled={!props.isEditing || !tenure.shopLevelEnabled}
                      >
                        {options.map((item) => {
                          const op = item.join(',') || '';
                          return (
                            <Select.Option key={op} value={op}>
                              {op || 'off'}
                            </Select.Option>
                          );
                        })}
                      </Select>
                    </Form.Item>
                  );
                },
              },
            ]}
          />
        ) : (
          <></>
        )}
      </Card>
    </Form>
  );
}

export default NewPayment;
