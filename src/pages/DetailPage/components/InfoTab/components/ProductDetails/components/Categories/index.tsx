import { useRequest } from 'ahooks';
import { Form, Select } from 'antd';
import type { SizeType } from 'antd/lib/config-provider/SizeContext';
import type { FormInstance } from 'antd/lib/form';
import type { SelectValue } from 'antd/lib/select';
import type { Dispatch} from 'react';
import React, {useEffect , useState  } from 'react';

import {
  getCategoryList,
  getGlobalCategoryList,
} from 'src/api/uploadAdmin';
import * as uploadAdminConstant from 'src/api/uploadAdmin/constants';
import type { uploadAdmin } from 'src/api/uploadAdmin/uploadAdmin';
import { maxCatLevel } from 'src/constants';
import styles from '../../../../../../styles.module.scss';
import getDisplayName from '../../utils/localName';

export enum CategoryType {
  GLOBAL,
  LOCAL,
}

const {
  STATUS_CAT_DELETE,
  STATUS_CAT_NORMAL,
  STATUS_CAT_DISABLE,
  STATUS_CAT_BLOCK,
} = uploadAdminConstant.CategoryStatus;

interface CategoriesProps {
  item: uploadAdmin.IGetItemDetailResponse;
  region: string;
  form: FormInstance;
  setLeafCat: Dispatch<number | undefined>;
  isEditing: boolean;
  categoryType: CategoryType;
}

function Categories(props: CategoriesProps) {
  const { data: localCategoryData, runAsync: getLocalCategoryData } = useRequest(
    () =>
      getCategoryList({
        region: props.region,
      }),
    {
      manual: true,
    },
  );
  const {
    data: globalCategoryData,
    error: globalCategoryListError,
    runAsync: getGlobalCategoryData,
  } = useRequest(
    () =>
      getGlobalCategoryList({
        region: props.region,
        statusList: [STATUS_CAT_NORMAL],
        isCbShop: props.item.isCb,
        filterBlock: true,
      }),
    {
      manual: true,
    },
  );
  // const [globalCategoryData, setGlobalCategoryData] = useState<
  //   uploadAdmin.IGetGlobalCategoryListResponse | undefined
  // >(undefined);
  // useEffect(() => {
  //   setGlobalCategoryData(mockGetGlobalCategoryResponse);
  // }, []);
  // const error = undefined;
  useEffect(() => {
    if (props.isEditing) {
      if (props.categoryType === CategoryType.GLOBAL && !globalCategoryData) {
        getGlobalCategoryData();
      } else if (
        props.categoryType === CategoryType.LOCAL &&
        !localCategoryData
      ) {
        getLocalCategoryData();
      }
    }
  }, [props.isEditing]);

  const formSuffix =
    props.categoryType === CategoryType.GLOBAL
      ? '_global_category'
      : '_category';

  const initCats = [];
  for (let i = 0; i < maxCatLevel; i++) {
    initCats.push(undefined);
  }

  // 0-based indexing 2D array of selectable categories
  // lxCats[0] will be all selectable L1 cats, and
  // lxCats[1] will be all selectable children of currently selected L1 cat (i.e. all selectable L2), etc
  const [lxCatsArr, setLxCatsArr] = useState<
  (uploadAdmin.ICategory[] | undefined)[]
  >(initCats);

  // set this level's lxCats and its children level to be undefined
  function setLxCatsAtLevelAndBelowUndefined(level: number) {
    setLxCatsArr((prevCats) =>
      prevCats.map((currCats, idx) =>
        idx >= level - 1 ? undefined : currCats,
      ),
    );
  }

  // 1-based indexing util function to help set categories at a level.
  // If undefined is passed in as cats, all child levels up to maxCatLevel will be set to undefined
  function setLxCatsAtLevel(
    level: number,
    cats: uploadAdmin.ICategory[],
  ) {
    if (!cats) {
      setLxCatsAtLevelAndBelowUndefined(level);
    }
    setLxCatsArr((prevCats) =>
      prevCats.map((currCats, idx) => (idx === level - 1 ? cats : currCats)),
    );
  }

  useEffect(() => {
    if (
      props.categoryType === CategoryType.GLOBAL &&
      globalCategoryData?.cats
    ) {
      setLxCatsAtLevel(
        1,
        globalCategoryData.cats.filter(
          // For global cats, do not show L1-only cats
          (cat) => cat.subCategories && cat.subCategories.length > 0,
        ),
      );
    } else if (
      props.categoryType === CategoryType.LOCAL &&
      localCategoryData?.cats
    ) {
      setLxCatsAtLevel(1, localCategoryData.cats);
    }
  }, [localCategoryData, globalCategoryData]);

  const performCatChangeAtLevel = (
    level: number,
    catId: SelectValue | undefined,
    categories: uploadAdmin.ICategory[] | undefined,
  ) => {
    // if cat to set is undefined, set undefined and early return
    if (catId == null) {
      setLxCatsAtLevelAndBelowUndefined(level);
      for (let i = level; i <= maxCatLevel; i++) {
        props.form.setFieldsValue({ [`l${i}${formSuffix}`]: undefined });
      }
      return;
    }

    // if there are selectable child categories, recurse
    if (
      categories &&
      categories.some((cat) => cat.status === STATUS_CAT_NORMAL)
    ) {
      const childCat = categories.filter(
        (cat) => cat.status === STATUS_CAT_NORMAL,
      )[0];
      props.form.setFieldsValue({
        [`l${level + 1}${formSuffix}`]: childCat.catId,
      });
      setLxCatsAtLevel(level + 1, categories);
      performCatChangeAtLevel(
        level + 1,
        childCat.catId,
        childCat.subCategories,
      );
    }
    // if there are no child categories, set current cat as the leaf cat
    // and set all deeper level lxCats as undefined
    else {
      props.setLeafCat(catId as number);
      setLxCatsAtLevelAndBelowUndefined(level + 1);
      for (let i = level + 1; i <= maxCatLevel; i++) {
        props.form.setFieldsValue({ [`l${i}${formSuffix}`]: undefined });
      }
    }
  };

  const handleCatChangeAtLevel = (
    level: number,
    catId: SelectValue | undefined,
  ) => {
    const subCats = lxCatsArr[level - 1]?.find((cat) => cat.catId === catId)
      ?.subCategories;
    performCatChangeAtLevel(level, catId, subCats);
  };

  useEffect(() => {
    if (!props.isEditing) {
      return;
    }
    // on edit, change all disabled/deleted/blocked cats to first normal cat
    // as L1 state is set before the rest, need to handle separately
    if (lxCatsArr[0]) {
      if (
        lxCatsArr[0] &&
        (!lxCatsArr[0].find(
          (cat) => cat.catId === props.form.getFieldValue(`l${1}${formSuffix}`),
        ) ||
          lxCatsArr[0].find(
            (cat) =>
              cat.catId === props.form.getFieldValue(`l${1}${formSuffix}`),
          )?.status !== STATUS_CAT_NORMAL)
      ) {
        const cat = lxCatsArr[0].find(
          (cat) => cat.status === STATUS_CAT_NORMAL,
        );
        props.form.setFieldsValue({
          [`l${1}${formSuffix}`]: cat?.catId,
        });
        performCatChangeAtLevel(1, cat?.catId, cat?.subCategories);
      }
    }
    if (lxCatsArr[1]) {
      for (let i = 1; i < maxCatLevel; i++) {
        const levelCats = lxCatsArr[i];
        if (!levelCats) {
          props.form.setFieldsValue({
            [`l${i + 1}${formSuffix}`]: undefined,
          });
          performCatChangeAtLevel(i + 1, undefined, undefined);
          break;
        } else if (
          levelCats &&
          (!levelCats.find(
            (cat) =>
              cat.catId === props.form.getFieldValue(`l${i + 1}${formSuffix}`),
          ) ||
            levelCats.find(
              (cat) =>
                cat.catId ===
                props.form.getFieldValue(`l${i + 1}${formSuffix}`),
            )?.status !== STATUS_CAT_NORMAL)
        ) {
          const cat = levelCats.find((cat) => cat.status === STATUS_CAT_NORMAL);
          props.form.setFieldsValue({
            [`l${i + 1}${formSuffix}`]: cat?.catId,
          });
          performCatChangeAtLevel(i + 1, cat?.catId, cat?.subCategories);
          break;
        }
      }
    }
  }, [props.isEditing, lxCatsArr[0], lxCatsArr[1]]);

  useEffect(() => {
    const listToSet =
      props.categoryType === CategoryType.GLOBAL
        ? props.item.globalCategoryList
        : props.item.categoryList;
    if (props.item && listToSet && listToSet[0]) {
      props.setLeafCat(listToSet[listToSet.length - 1].catId);
      // L1 cats array was retrieved via API call
      const lxCatsToSet: (
        | uploadAdmin.ICategory[]
        | undefined
      )[] = [];
      // current list of cats available for selection, to be used in iteration below (can be undefined)
      // will be set to children categories of the saved item's category listToSet[i] at every level.
      let currCatList:
      | uploadAdmin.ICategory[]
      | undefined = lxCatsArr[0];
      for (let i = 0; i < maxCatLevel; i++) {
        lxCatsToSet.push(currCatList);
        props.form.setFieldsValue({
          [`l${i + 1}${formSuffix}`]: listToSet[i]
            ? listToSet[i].catId
            : undefined,
        });
        currCatList = listToSet[i]
          ? currCatList?.find((cat) => cat.catId === listToSet[i].catId)
              ?.subCategories
          : undefined;
      }
      setLxCatsArr(lxCatsToSet);
    }
  }, [props.item, lxCatsArr[0]]);

  const renderCatOptionInDisplayMode = (
    cat?: uploadAdmin.ICategory,
  ) => {
    if (!cat) {
      return '';
    }
    const displayName = getDisplayName(
      cat.defaultLangName?.val ? cat.defaultLangName.val : '',
      cat.catName || '',
    );
    return (
      // cat.catName +
      displayName +
      (cat.status === STATUS_CAT_DELETE
        ? ' (deleted)'
        : cat.status === STATUS_CAT_DISABLE
          ? ' (disabled)'
          : cat.status === STATUS_CAT_BLOCK
            ? ' (blocked)'
            : '')
    );
  };

  const selectProps = {
    style: { width: '250px' },
    size: 'small' as SizeType,
  };

  const renderErrorGlobalCat = () => {
    const globalCatErrorComponents = [];
    {
      for (let i = 0; i < maxCatLevel; i++) {
        if (
          props.item.globalCategoryList === undefined ||
          props.item.globalCategoryList[i] === undefined
        ) {
          break;
        }
        globalCatErrorComponents.push(
          <div className={styles.flexboxContainer} key={i}>
            <span className={styles.label}>{`L${i + 1} Category:`}</span>
            <Select
              {...selectProps}
              disabled={true}
              defaultValue={props.item.globalCategoryList[i]?.catId}
            >
              <Select.Option
                value={props.item.globalCategoryList[i]?.catId!}
                key={props.item.globalCategoryList[i]?.catId}
              >
                {props.item.globalCategoryList[i]?.catName}
              </Select.Option>
            </Select>
          </div>,
        );
      }
    }
    return (
      <>
        {globalCatErrorComponents}
        {!props.item.globalCategoryList && 'No categories linked to this item'}
      </>
    );
  };

  const catComponents = [];
  for (let i = 0; i < maxCatLevel; i++) {
    const levelCats = lxCatsArr[i];
    catComponents.push(
      <div className={styles.flexboxContainer} key={i}>
        <span className={styles.label}>{`L${i + 1} Category:`}</span>
        <Form.Item name={`l${i + 1}${formSuffix}`}>
          <Select
            {...selectProps}
            disabled={!props.isEditing || !levelCats}
            onChange={(value?: SelectValue) => {
              handleCatChangeAtLevel(i + 1, value);
            }}
          >
            {/* Edit mode: keep currently selected option at the top of dropdown.
            Display mode: show the saved value regardless of whether it is a normal cat */}
            <Select.Option
              value={props.form.getFieldValue(`l${i + 1}${formSuffix}`)}
              key={props.form.getFieldValue(`l${i + 1}${formSuffix}`)}
            >
              {renderCatOptionInDisplayMode(
                props.isEditing === false
                  ? props.categoryType === CategoryType.GLOBAL
                    ? props.item.globalCategoryList
                      ? props.item.globalCategoryList[i]
                      : undefined
                    : props.item.categoryList
                      ? props.item.categoryList[i]
                      : undefined
                  : levelCats?.find(
                      (cat) =>
                        cat.catId ===
                        props.form.getFieldValue(`l${i + 1}${formSuffix}`),
                    )
                    ? levelCats?.find(
                      (cat) =>
                        cat.catId ===
                        props.form.getFieldValue(`l${i + 1}${formSuffix}`),
                    )
                    : undefined,
              )}
            </Select.Option>
            {levelCats &&
              levelCats
                .filter(
                  (cat) =>
                    cat.catId !==
                      props.form.getFieldValue(`l${i + 1}${formSuffix}`) &&
                    cat.status === STATUS_CAT_NORMAL,
                )
                .map((cat) => (
                  <Select.Option value={cat.catId!} key={cat.catId}>
                    {cat.catName}
                  </Select.Option>
                ))}
          </Select>
        </Form.Item>
      </div>,
    );
  }

  return props.categoryType === CategoryType.GLOBAL &&
    globalCategoryListError ? (
      renderErrorGlobalCat()
    ) : (
      <>
        {catComponents.map((catComponent, idx) => {
        // for global cats, show all levels of selectors regardless
          return props.categoryType === CategoryType.GLOBAL ||
          // show L1 selector regardless
          idx === 0 ||
          // in edit mode, show selector if there is at least one selectable cat in this level
          (props.isEditing
            ? lxCatsArr[idx]?.some((cat) => cat.status === STATUS_CAT_NORMAL)
            : // in display mode, show selector if there is a saved cat at this level
            props.item.categoryList &&
              props.item.categoryList[idx] !== undefined)
            ? catComponent
            : undefined;
        })}
      </>
    );
}

export default Categories;
