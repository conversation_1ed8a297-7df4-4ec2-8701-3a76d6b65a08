import { Card, Form,  Select } from 'antd';
import type { FormInstance } from 'antd/es/form/Form';
import React, { useCallback, useEffect,useState } from 'react';

import { getGlobalBrandList } from 'src/api/uploadAdmin';
import * as uploadAdminConstant from 'src/api/uploadAdmin/constants';
import type { uploadAdmin } from 'src/api/uploadAdmin/uploadAdmin';
import styles from '../../../../../../styles.module.scss';
import getDisplayName from '../../utils/localName';

interface GlobalBrandProps {
  form: FormInstance;
  savedBrandId?: number;
  savedBrandName?: string;
  savedBrandLocalName?: string;
  globalLeafCat: number | undefined;
  region: string;
  isEditing: boolean;
}

const { BRAND_NORMAL } = uploadAdminConstant.GlobalBrandStatus;

function GlobalBrand(props: GlobalBrandProps) {
  const [brandList, setBrandList] = useState<
  uploadAdmin.IGlobalBrand[]
  >([]);
  const [hasError, setHasError] = useState<boolean>(false);

  const fetchBrandList = useCallback(async () => {
    const result = await getGlobalBrandList({
      catId: props.globalLeafCat,
      statusList: [BRAND_NORMAL],
      region: props.region,
    });
    if (result) {
      if (result.brandList) {
        setBrandList(result.brandList);
        if (
          !result.brandList.some(
            (brand) =>
              brand.brandId === props.form.getFieldValue('globalBrandId'),
          )
        ) {
          props.form.setFieldsValue({
            globalBrandId: result.brandList[0].brandId,
          });
        }
        setHasError(false);
      } else {
        setBrandList([]);
        if (result.spDebugMsg) {
          setHasError(true);
        } else {
          // set No Brand
          props.form.setFieldsValue({ globalBrandId: 0 });
          setHasError(false);
        }
      }
    } else {
      setBrandList([]);
      setHasError(true);
    }
  }, [props.globalLeafCat]);

  useEffect(() => {
    if (props.isEditing) {
      if (props.globalLeafCat) {
        fetchBrandList();
      } else {
        setBrandList([]);
        setHasError(true);
      }
    }
  }, [props.globalLeafCat, props.isEditing]);

  return (
    <Card title={'Global Brand'} size="small">
      <div className={styles.flexboxContainer}>
        <span className={styles.label}>Brand:</span>
        {props.isEditing ? (
          hasError ? (
            'Error'
          ) : (
            <Form.Item name={'globalBrandId'}>
              <Select style={{ width: '250px' }}>
                {brandList.map((brand) => (
                  <Select.Option key={brand.brandId} value={brand.brandId!}>
                    {getDisplayName(
                      brand.brandLocalDisplayName || '',
                      brand.brandName || '',
                    )}
                  </Select.Option>
                ))}
              </Select>
            </Form.Item>
          )
        ) : props.savedBrandName ? (
          // props.savedBrandName
          getDisplayName(props.savedBrandLocalName || '', props.savedBrandName)
        ) : (
          'No Brand'
        )}
      </div>
    </Card>
  );
}

export default GlobalBrand;
