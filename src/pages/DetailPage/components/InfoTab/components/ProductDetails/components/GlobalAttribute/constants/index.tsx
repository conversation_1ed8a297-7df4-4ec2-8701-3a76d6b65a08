import type { RuleObject } from 'antd/lib/form';

import * as uploadAdminConstant from 'src/api/uploadAdmin/constants';
import { byteLengthValidator } from 'src/utils';

const { AttrInputValidatorType } = uploadAdminConstant;
const {
  VALIDATOR_NOT_REQUIRED,
  VALIDATOR_INTEGERS,
  VALIDATOR_STRING,
  VALIDATOR_NUMBERS,
  VALIDATOR_DATE,
} = AttrInputValidatorType;

export const validationRegExpMap = {
  [VALIDATOR_NOT_REQUIRED]: new RegExp(/\S/),
  [VALIDATOR_INTEGERS]: new RegExp(/^(0|([-]?([1-9]\d*)))$/),
  [VALIDATOR_STRING]: new RegExp(/\S/),
  [VALIDATOR_NUMBERS]: new RegExp(/^\d+(\.\d+)?$/),
};

const maxValueLength = 254;

export const valueLengthRule = {
  validator: (_: RuleObject, value: string) =>
    byteLengthValidator(value, maxValueLength),
};

export const validationRules = {
  [VALIDATOR_NOT_REQUIRED]: [valueLengthRule],
  [VALIDATOR_INTEGERS]: [
    {
      pattern: validationRegExpMap[VALIDATOR_INTEGERS],
      message: 'Please input an integer',
    },
    valueLengthRule,
  ],
  [VALIDATOR_STRING]: [
    {
      pattern: validationRegExpMap[VALIDATOR_STRING],
      message: 'Please input a string',
    },
    valueLengthRule,
  ],
  [VALIDATOR_NUMBERS]: [
    {
      pattern: validationRegExpMap[VALIDATOR_NUMBERS],
      message: 'Please input a decimal number',
    },
    valueLengthRule,
  ],
  [VALIDATOR_DATE]: [],
};
