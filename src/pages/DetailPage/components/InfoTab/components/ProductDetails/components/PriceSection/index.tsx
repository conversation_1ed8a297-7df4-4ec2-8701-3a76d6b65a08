import { getCountryConfig } from '@shopee_common/currency';
import type { CountryCode } from '@shopee_common/currency/dist/typings/typings';
import { Form, Input } from 'antd';
import type  { FormInstance } from 'antd/lib/form';
import React from 'react';

import * as uploadAdminConstant from 'src/api/uploadAdmin/constants';
import type { uploadAdmin } from 'src/api/uploadAdmin/uploadAdmin';
import {
  FEtoBEMultiplier,
  priceRegexRule,
  promotionTypeString,
  regionsWithTax,
} from 'src/constants';
import { formatCurrencyForItem } from 'src/utils';
import styles from '../../../../../../styles.module.scss';
// TODO
const { PromotionStatus } = uploadAdminConstant;

interface PriceSectionProps {
  item: uploadAdmin.IGetItemDetailResponse;
  region: string;
  itemModelList: uploadAdmin.IItemModel[];
  form: FormInstance;
  isEditing: boolean;
}

function PriceSection(props: PriceSectionProps) {
  return props.itemModelList.length === 0 ||
    !props.itemModelList.find(
      (model) => model.status === PromotionStatus.STATUS_PROMOTION_NORMAL,
    ) ? (
      <>
        {/* Price */}
        <div className={styles.flexboxContainer}>
          {!regionsWithTax.includes(props.region) ? (
            <>
              <span className={styles.label}>{`Price(${
                getCountryConfig(props.region as CountryCode).defaultCurrency
              }):`}</span>
              <Form.Item name="originalPrice" rules={[priceRegexRule]}>
                <Input size={'small'} disabled={!props.isEditing} />
              </Form.Item>
            </>
          ) : (
            <>
              <span className={styles.label}>{`Price(${
                getCountryConfig(props.region as CountryCode).defaultCurrency
              }):`}</span>
              {formatCurrencyForItem(
                props.region as CountryCode,
                (props.item.originalPrice ?? 0) / FEtoBEMultiplier,
              )}
            </>
          )}
        </div>

        {/* Price Before Tax */}
        {regionsWithTax.includes(props.region) && (
          <div className={styles.flexboxContainer}>
            <span className={styles.label}>{`Price Before Tax(${
              getCountryConfig(props.region as CountryCode).defaultCurrency
            }):`}</span>
            <Form.Item name="originalPriceBeforeTax" rules={[priceRegexRule]}>
              <Input size={'small'} disabled={!props.isEditing} />
            </Form.Item>
          </div>
        )}

        {/* SIP Selling Price  */}
        {/* cbOption === 1 for all cb type items */}
        {props.item.cbOption === 1 && (
          <>
            <div className={styles.flexboxContainer}>
              <span className={styles.label}>
                {`SIP Selling Price(${
                  getCountryConfig(props.region as CountryCode).defaultCurrency
                }):`}
              </span>
              {formatCurrencyForItem(
                props.region as CountryCode,
                (props.item.sipSellingPrice ?? 0) / FEtoBEMultiplier,
              )}
            </div>
            {regionsWithTax.includes(props.region) && (
              <div className={styles.flexboxContainer}>
                <span className={styles.label}>
                  {`SIP Selling Price Before Tax(${
                    getCountryConfig(props.region as CountryCode).defaultCurrency
                  }):`}
                </span>
                {formatCurrencyForItem(
                  props.region as CountryCode,
                  (props.item.sipSellingPriceBeforeTax ??0) / FEtoBEMultiplier,
                )}
              </div>
            )}
          </>
        )}

        {/* Ongoing Promotion Price After Tax */}
        <div className={styles.flexboxContainer}>
          <span className={styles.label}>
            {`Ongoing Promotion Price After Tax(${
              getCountryConfig(props.region as CountryCode).defaultCurrency
            }):`}
          </span>
          {props.item.promotionPrice != null
            ? `${formatCurrencyForItem(
              props.region as CountryCode,
              props.item.promotionPrice / FEtoBEMultiplier,
            )} (${
              promotionTypeString[
                props.item.promotionType!
              ]
            })`
            : '-'}
        </div>

        {/* Ongoing Promotion Price Before Tax */}
        {regionsWithTax.includes(props.region) && (
          <div className={styles.flexboxContainer}>
            <span className={styles.label}>
              {`Ongoing Promotion Price Before Tax(${
                getCountryConfig(props.region as CountryCode).defaultCurrency
              }):`}
            </span>
            {props.item.promotionPriceBeforeTax != null
              ? formatCurrencyForItem(
                props.region as CountryCode,
                props.item.promotionPriceBeforeTax / FEtoBEMultiplier,
              )
              : '-'}
          </div>
        )}
      </>
    ) : (
      <> </>
    );
}

export default PriceSection;
