import { ProductDescriptionEditor } from '@listing/ls-upload-cmpt-sdk-react';
import React, { useMemo } from 'react';

export interface ImageRecord {
  src: string;
}

export interface ProductDescriptionField {
  type: number;
  value: string;
}

export enum DescriptionFieldType {
  TextField = 0,
  ImageField = 1
}

interface DataChangeParams {
  description: string;
  descriptionType: string;
}

interface DataChangeExtra {
  imageCount: number;
  textLength: number;
  dataChangeSource: string;
}

interface Props {
  env: string;
  region: string;
  descriptionType?: string;
  description?: string;
  itemId?: number;
  shopId?: number;
  disabled: boolean;
  onChange?: (params: {
    description: string;
    descriptionExtra?: { imageList: ImageRecord[] };
  })=> void;
}

enum ProductDescriptionType {
  Normal = 'normal',
  Json = 'json',
  Degrade = 'degrade'
}

function Description(props: Props) {
  const description = useMemo(() => {
    let description = props.descriptionType === 'json' ? '{ field_list: [] }' : '';
    try {
      const fieldList = JSON.parse(props.description ?? '');
      if (Array.isArray(fieldList)) {
        description = JSON.stringify({ field_list: fieldList });
      }
    } catch {
      description = props.description  ?? '';
    }
    return description;
  }, [props.description]);

  const onEditorDataChange = (params: DataChangeParams, extra: DataChangeExtra) => {
    if (params.descriptionType !== 'json') {
      props.onChange?.({ description: params.description });
      return;
    }
    const fieldList = JSON.parse(params.description).field_list as ProductDescriptionField[];
    const descriptionData = fieldList
      .filter(
        (v) =>
          v.type === DescriptionFieldType.TextField ||
          (v.type === DescriptionFieldType.ImageField && !!v.value),
      )
      .map((v) => ({
        type: v.type,
        value: v.value,
      }));
    const imageList = fieldList
      .filter((v) => v.type === DescriptionFieldType.ImageField)
      .map((v) => ({ src: v.value }));
    props.onChange?.({
      description: JSON.stringify(descriptionData),
      descriptionExtra: { imageList },
    });
  };

  return (
    <div>
      <ProductDescriptionEditor
        bizId="1001"
        env={props.env}
        region={props.region}
        locale="en"
        placeholder="Please enter product description characters or add Images"
        descriptionType={props.descriptionType as ProductDescriptionType}
        description={description}
        itemId={props.itemId}
        shopId={props.shopId}
        isDraft={false}
        disabled={props.disabled}
        listingToggle={{
          szDescriptionSupportImage: props.descriptionType === 'json'
        }}
        lockInfo={{}}
        productConstraints={{
          descriptionImageAspectRatioMin: 0.5,
          descriptionImageAspectRatioMax: 32,
        } as any}
        onDataChange={onEditorDataChange}
      />
    </div>
  );
}

export default Description;
