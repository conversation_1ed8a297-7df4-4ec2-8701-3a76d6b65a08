import { DatePicker,Form, Input, Select } from 'antd';
import type { FormInstance, Rule } from 'antd/lib/form';
import { debounce } from 'lodash';
import moment from 'moment';
import React from 'react';

import * as uploadAdminConstant from 'src/api/uploadAdmin/constants';
import type { uploadAdmin } from 'src/api/uploadAdmin/uploadAdmin';

const {
  AttrInputValidatorType,
  AttrFormatType,
} = uploadAdminConstant;

interface FreeTextFieldProps {
  attribute: uploadAdmin.IGlobalProductAttr;
  dateFormat: string | undefined;
  form: FormInstance;
  onChangeCallback: ()=> void;
  onBlurCallback: ()=> void;
  validationRules?: Rule[];
  size?: 'small' | 'middle' | 'large';
}

export function FreeTextField(props: FreeTextFieldProps) {
  const changeCallback = debounce(props.onChangeCallback, 300);

  return props.attribute.validateType ===
    // Date input
    AttrInputValidatorType.VALIDATOR_DATE ? (
      <DatePicker
        size={props.size}
        picker={props.dateFormat === 'MM/YYYY' ? 'month' : 'date'}
        format={props.dateFormat}
        defaultValue={
          props.form.getFieldValue(
            `global_attribute${props.attribute.attrId}_value`,
          )
            ? moment(
              props.form.getFieldValue(
                `global_attribute${props.attribute.attrId}_value`,
              ),
              props.dateFormat,
            )
            : undefined
        }
        onSelect={(value) => {
          props.form.setFieldsValue({
            [`global_attribute${props.attribute.attrId}_value`]: value?.format(
            props.dateFormat,
          ),
          });
          props.onChangeCallback();
        }}
        style={{ width: '100%' }}
      />
    ) : props.attribute.attrFormatType ===
    AttrFormatType.FORMAT_QUANTITATIVE_WITH_UNIT ? (
        <span style={{ display: 'flex', flexWrap: 'nowrap' }}>
          <Form.Item
            name={`global_attribute${props.attribute.attrId}_value`}
            rules={props.validationRules}
            style={{ flex: 1 }}
          >
            <Input size={props.size} />
          </Form.Item>
          <Form.Item
            name={`global_attribute${props.attribute.attrId}_value_unit`}
            style={{ width: '20%' }}
          >
            <Select size={props.size}>
              {props.attribute.attrExtInfo?.unitList?.units?.map((unit) => (
                <Select.Option key={unit} value={unit}>
                  {unit}
                </Select.Option>
              ))}
            </Select>
          </Form.Item>
        </span>
      ) : (
    // Non-quantitative attribute
        <Form.Item
          name={`global_attribute${props.attribute.attrId}_value`}
          rules={props.validationRules}
        >
          <Input
            onBlur={() => {
              props.onBlurCallback();
            }}
            onChange={() => {
              changeCallback();
            }}
            style={{ width: '100%' }}
            size={props.size}
          />
        </Form.Item>
      );
}
