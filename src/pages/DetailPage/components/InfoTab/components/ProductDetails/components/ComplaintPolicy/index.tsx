import { Card } from 'antd';
import React from 'react';

import { getComplaintAddress } from 'src/api/uploadAdmin';
import * as uploadAdminConstant from 'src/api/uploadAdmin/constants';
import type { uploadAdmin } from 'src/api/uploadAdmin/uploadAdmin';
import styles from './styles.module.scss';

interface Props {
  data?: uploadAdmin.IComplaintPolicy;
  shopId?: number;
}
const { TimeForWarrantyClaim } = uploadAdminConstant;

const getWarrantyTime = (key?: number) => {
  switch (key) {
    case TimeForWarrantyClaim.ONE_YEAR:
      return '1 year';
    case TimeForWarrantyClaim.TWO_YEARS:
      return '2 years';
    case TimeForWarrantyClaim.OVER_TWO_YEARS:
      return 'Over 2 years';
    default:
      return '';
  }
};
export default function ComplaintPolicy(props: Props) {
  const [addressData, setAddressData] = React.useState<
  uploadAdmin.IBuyerAddress
  >();
  const addressId = props.data?.addressId;
  React.useEffect(() => {
    if (props.shopId) {
      (async function() {
        const resp = await getComplaintAddress({
          addressId: addressId || 0,
          shopid: props.shopId,
          // @ts-expect-error
          SkipPermissionBD: true,
        });
        setAddressData(resp?.complaintAddress);
      })();
    }
  }, []);

  if (addressData) {
    return (
      <Card title="Complaint Policy">
        <section className={styles.section}>
          <b>Time for A Warranty Claim</b>
          <div>
            {getWarrantyTime(props.data?.timeForWarranty)}{' '}
            {props.data?.excludeEntrepreneursWarranty
              ? '(Exclude warranty complaints for entrepreneur)'
              : null}
          </div>
        </section>
        <section className={styles.section}>
          <b>Complaint Adress</b>
          <div>{addressData.address}</div>
          {addressData.state && addressData.city ? (
            <div>
              {addressData.state}, {addressData?.city}
            </div>
          ) : null}
          <div>{addressData.zipcode}</div>
        </section>
        <section className={styles.section}>
          <b>Additional Information</b>
          <div>{props.data?.additionalInfo}</div>
        </section>
      </Card>
    );
  } else {
    return null;
  }
}
