import { useRoute } from '@classification/admin-solution';
import { useRequest } from 'ahooks';
import React from 'react';

import { getProductTaxRates } from 'src/api/uploadAdmin';
import { TaxRegulationType } from 'src/api/uploadAdmin/constants';


function TaxRateTips(props: { categoryPath: number[]}) {
  const { categoryPath } = props;
  const {shopId} = ((useRoute().params ?? {}) as unknown) as {
    shopId: string;
  };
  const { data: productTaxRates } =
  useRequest(
    () =>
      getProductTaxRates({
     shopId: Number(shopId),
        categoryPath,
      }),
    {
      refreshDeps: [categoryPath],
      debounceWait: 1000,
      onError: () => {
        return {};
      },
    },
  );
  const getDisplayTaxRate = () => {
    const { summaryRates = '', taxRegulationList = [] } = productTaxRates?.data ?? {};
    if (!summaryRates) {
      return '';
    }
    let vat, pit;
    taxRegulationList.forEach((item) => {
      if (item.regulationType === TaxRegulationType.TaxRegulationVnWhtPit) {
        pit = item.taxRates ?? '0';
      } else if (
        item.regulationType === TaxRegulationType.TaxRegulationVnWhtVat
      ) {
        vat = item.taxRates  ?? '0';
      }
    });
    return `A ${summaryRates} tax will be applied to this product, including ${vat} VAT and ${pit} PIT.`;
  }
  return <div style={{background:'#f3f3f3',color: 'red'}}>
    {getDisplayTaxRate()}
  </div>
}

export default TaxRateTips;