import type { FormInstance } from 'antd/lib/form/Form';
import type { Dispatch} from 'react';
import React, { useMemo } from 'react';

import type { uploadAdmin } from 'src/api/uploadAdmin/uploadAdmin';
import type { ImageListsType } from 'src/typings';
import styles from '../../styles.module.scss';
import GeneralInformation from './components/GeneralInformation';
import ProductDetails from './components/ProductDetails';

interface InfoTabProps {
  item: uploadAdmin.IGetItemDetailResponse;
  region: string;
  env: 'test' | 'uat' | 'staging' | 'stable' | 'live';
  stockList: uploadAdmin.IStockBreakDownList[];
  floatingStockLists: uploadAdmin.IFloatingStockList[];
  itemStock: uploadAdmin.IItemStock[];
  itemModelList: uploadAdmin.IItemModel[];
  productAttributeData:
  | uploadAdmin.IGetProductAttributesResponse
  | undefined;
  globalProductAttributeData:
  | uploadAdmin.IGetGlobalProductAttributesResponse
  | undefined;
  setLeafCat: Dispatch<number | undefined>;
  globalLeafCat: number | undefined;
  setGlobalLeafCat: Dispatch<number | undefined>;
  form: FormInstance;
  images: ImageListsType;
  setImages: Dispatch<ImageListsType>;
  isShopWhitelisted: boolean;
  isEditing: boolean;
  editModeDataSource: uploadAdmin.IProductAttr[];
  setEditModeDataSource: Dispatch<
  uploadAdmin.IProductAttr[]
  >;
  editModeGlobalDataSource:
  | uploadAdmin.IGlobalProductAttrList
  | undefined;
  setEditModeGlobalDataSource: Dispatch<
  uploadAdmin.IGlobalProductAttrList | undefined
  >;
  variationList: uploadAdmin.ITierVariation[];
  setVariationList: Dispatch<
  uploadAdmin.ITierVariation[]
  >;
  licenses?: uploadAdmin.ILicenseWithMetaInfo[];
  updateModelSettingV2: (params: {
    modelsSetting: uploadAdmin.IModelSettingV2[];
  })=> void;
  getBooksInfoByIsbnData: ()=> Promise<
  uploadAdmin.IGetBooksInfoByIsbnResponse
  >;
}

function InfoTab(props: InfoTabProps) {
  const GeneralInformationComponent = useMemo(() => {
    return (
      <GeneralInformation
        item={props.item}
        region={props.region}
        env={props.env}
        form={props.form}
        itemModelList={props.itemModelList}
        isEditing={props.isEditing}
        licenses={props.licenses}
      />
    );
  }, [
    props.item,
    props.region,
    props.env,
    props.form,
    props.itemModelList,
    props.isEditing,
    props.licenses,
  ]);
  return (
    <div className={styles.card}>
      {GeneralInformationComponent}
      <ProductDetails
        item={props.item}
        region={props.region}
        env={props.env}
        form={props.form}
        isShopWhitelisted={props.isShopWhitelisted}
        stockList={props.stockList}
        itemStock={props.itemStock}
        floatingStockLists={props.floatingStockLists}
        itemModelList={props.itemModelList}
        productAttributeData={props.productAttributeData}
        globalProductAttributeData={props.globalProductAttributeData}
        setLeafCat={props.setLeafCat}
        globalLeafCat={props.globalLeafCat}
        setGlobalLeafCat={props.setGlobalLeafCat}
        isEditing={props.isEditing}
        images={props.images}
        setImages={props.setImages}
        editModeDataSource={props.editModeDataSource}
        setEditModeDataSource={props.setEditModeDataSource}
        editModeGlobalDataSource={props.editModeGlobalDataSource}
        setEditModeGlobalDataSource={props.setEditModeGlobalDataSource}
        variationList={props.variationList}
        setVariationList={props.setVariationList}
        updateModelSettingV2={props.updateModelSettingV2}
        getBooksInfoByIsbnData={props.getBooksInfoByIsbnData}
      />
    </div>
  );
}

export default InfoTab;
