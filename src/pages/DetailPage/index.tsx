import { useNavigate, useRoute } from '@classification/admin-solution';
import type { CountryCode } from '@shopee_common/currency/dist/typings/typings';
import {
  AuthWrapper,
  checkAndSetRegionURLParams,
  getAdminHost,
  getCountry,
  getCountryForDisplay,
  getEnvironment,
  isPageAnIframe,
  withErrorBoundary} from 'admin-upload-common';
import { useRequest } from 'ahooks';
import { Button, Card, message, Modal, Spin, Tabs, Tag, Tooltip } from 'antd';
import { useForm } from 'antd/lib/form/Form';
import { cloneDeep, isEqual } from 'lodash';
import React, { useEffect,useMemo, useRef,useState  } from 'react';

import { Status } from 'src/api/PBData/constants';
import {
  getBooksInfoByIsbn,
  getEntityAttrVal,
  getGlobalProductAttributes,
  getItemModelList,
  getItemModelStockList,
  getItemTranslation,
  getLicensesForItem,
  getProductAttributes,
  getRegionTranslationLang,
  getStockBatchWithLatestBreakdown,
  getTaxGroupType,
  purgeCsCache,
  purgeMallCache,
  resetItemNormal,
  updateItemTranslation,
  updateModelSettingV2,
} from 'src/api/uploadAdmin';
import {
  mainReporter,
  POINT_FINISH_RENDER,
  POINT_FIRST_RENDER,
  POINT_JS_LOADED,
  POINT_ROUTER_ENTER,
  ProductDetailReporter,
} from 'src/reporter';
import  * as uploadAdminConstant from '../../api/uploadAdmin/constants';
import type { uploadAdmin } from '../../api/uploadAdmin/uploadAdmin';
import { Routes } from '../../config/routes';
import {
  AUTH_CODE,
  FEtoBEMultiplier,
  itemStatusString,
  itemStatusTagColour,
  maxCatLevel,
  ProductGroupType,
  ProductTaxType,
  refreshDelay,
} from '../../constants';
import type { ImageListsType } from '../../typings';
import { formatCurrencyForItem, getRouterEnterTime } from '../../utils';
import InfoTab from './components/InfoTab';
import type { ImageRecord } from './components/InfoTab/components/ProductDetails/components/Description';
import LogTab from './components/LogTab';
import MultiLangTab from './components/MultiLangTab';
import setFormAndState from './functions/setFormAndState';
import submitUpdate from './functions/submitUpdate';
import useRequestItemDetailHook from './hooks/useRequestDataHook';
import styles from './styles.module.scss';

const { ITEM_INVALID, ITEM_INVALID_HIDE, ITEM_OFFENSIVE_HIDE } = Status;

const ItemModelStockListLimit = 600;

export enum TabKey {
  Info = 'Info',
  MultiLangInfo = 'MultiLangInfo',
  Log = 'Log',
}

let leaveTabTime = undefined as any;
let resumeTabTime = undefined as any;
let jsLoadedTime = undefined as any;
function visibilityChange() {
  if (document.visibilityState === 'hidden') {
    leaveTabTime = +new Date();
  } else {
    resumeTabTime = +new Date();
  }
}

function DetailPage() {
  const navTo = useNavigate();
  const needFetchItemInfo =
    window.location.search && window.location.search.includes('onlyitem')
      ? true
      : false;
  const region = getCountry();
  const env = getEnvironment();
  const { routeId, params: routeParams } = useRoute();
  const [isEditing, setIsEditing] = useState(false);
  const [detailChanged, setDetailChanged] = useState(false);
  const [images, setImages] = useState<ImageListsType>({
    productImages: [],
    productLongImages: [],
    productVideos: [],
    variationImages: [],
    whiteBackgroundImages: [],
    sizeChart: [],
  });
  const [stockList, setStockList] = useState<
  uploadAdmin.IStockBreakDownList[]
  >([]);
  const [floatStockList, setFloatStockList] = useState<
  uploadAdmin.IFloatingStockList[]
  >([]);
  const [itemModelList, setItemModelList] = useState<
  uploadAdmin.IItemModel[]
  >([]);

  const [itemStock, setItemStock] = useState<
  uploadAdmin.IItemStock[]
  >([]);

  // for product attributes
  const [editModeDataSource, setEditModeDataSource] = useState<
  uploadAdmin.IProductAttr[]
  >([]);
  // for global product attributes
  const [editModeGlobalDataSource, setEditModeGlobalDataSource] = useState<
  uploadAdmin.IGlobalProductAttrList | undefined
  >();
  const [leafCat, setLeafCat] = useState<number | undefined>(undefined);
  const [globalLeafCat, setGlobalLeafCat] = useState<number | undefined>(
    undefined,
  );

  const [variationList, setVariationList] = useState<
  uploadAdmin.ITierVariation[]
  >([]);
  const [form] = useForm();
  const [langForm] = useForm();
  const [isDefaultDTSPrompted, setIsDefaultDTSPrompted] = useState(false);
  const [isShopWhitelisted, setIsShopWhitelisted] = useState(false);
  const [isbnKey, setIsbnKey] = useState('');
  const [taxGroup, setTaxGroup] = useState<number | undefined>(undefined);
  const [stockBreakDownListData, setStockBreakDownListData] =
    useState<uploadAdmin.IGetItemModelStockListResponse>(
      { stockList: [], floatingStockLists: [] },
    );

  const { data, loading, run, itemId, shopId } = useRequestItemDetailHook(
    routeId,
    routeParams,
    region,
    needFetchItemInfo,
  );

  // Hot keys to switch between info and log tabs
  function keydownHandler(e: { code: string; shiftKey: boolean }) {
    if (e.shiftKey) {
      if (e.code === 'F1') {
        navTo({
          to: routeId,
          params: {
            tab: TabKey.Info,
            region: region,
            shopId: shopId,
            itemId: itemId,
          }
        });
      }
      if (e.code === 'F2') {
        navTo({
          to: routeId,
          params: {
            tab: TabKey.Log,
            region: region,
            shopId: shopId,
            itemId: itemId,
          }
        });
      }
    }
  }
  useEffect(() => {
    document.addEventListener('keydown', keydownHandler);
    return () => {
      document.removeEventListener('keydown', keydownHandler);
    };
  }, []);

  useEffect(() => {
    checkAndSetRegionURLParams(region);
  }, []);

  // mocks
  // const [data, setData] = useState(mockGetItemDetailResponse);
  // const loading = false;
  // useEffect(() => {
  //   setData(mockGetItemDetailResponse);
  // }, []);
  // useEffect(() => {
  //   setStockList(mockItemModelStockListResponse.stockList);
  //   setItemModelList(mockItemModelListResponse.modelList);
  // }, [itemId]);

  const getAllStockBreakDownListData = async (
    offset = 0,
    offsetList?: string[],
  ): Promise<uploadAdmin.IGetItemModelStockListResponse> => {
    const result = await getItemModelStockList({
      itemId: itemId,
      page: { offset, limit: ItemModelStockListLimit, offsetList },
    });
    if (!result) {
      return Promise.resolve({});
    }
    let nextResult: uploadAdmin.IGetItemModelStockListResponse;
    if (result?.page?.hasNext) {
      nextResult = await getAllStockBreakDownListData(
        offset + ItemModelStockListLimit,
        result.page.offsetList,
      );
    } else {
      return Promise.resolve(result);
    }
    return Promise.resolve({
      stockList: nextResult?.stockList
        ? result?.stockList?.concat(nextResult?.stockList)
        : result?.stockList,
      floatingStockLists: nextResult?.floatingStockLists
        ? result?.floatingStockLists?.concat(nextResult?.floatingStockLists)
        : result?.floatingStockLists,
    } as uploadAdmin.IGetItemModelStockListResponse);
  };

  const getStockBreakDownListData = () =>
    getAllStockBreakDownListData().then(
      ({ stockList = [], floatingStockLists = [] }) =>
        setStockBreakDownListData({ stockList, floatingStockLists }),
    );

  useEffect(() => {
    const init = async () => {
      if (shopId > 0) {
        const result = await getEntityAttrVal({
          entity: {
            id: shopId,
            type: 'shop',
          },
          region: region,
          attrKey: 'is_mpq',
        });
        if (result?.value?.attrValue) {
          setIsShopWhitelisted(true);
        }
      }
    };
    init();
  }, [shopId]);

  useEffect(() => {
    if ((!routeParams?.tab || routeParams.tab === TabKey.Info) && isEditing) {
      setDetailChanged(true);
    }
  }, [routeParams?.tab, isEditing]);

  const { data: itemModelListData, runAsync: getItemModelListData } =
    useRequest(
      () =>
        getItemModelList({
          shopId: shopId,
          itemId: itemId,
          region: region,
          status: undefined,
          needLink: false,
        }),
      {
        manual: true,
      },
    );

  const { data: itemStockData, runAsync: getStockBatchWithLatestBreakdownData } =
    useRequest(
      async () => {
        const splitNum = 100;
        const items = itemModelListData?.modelList?.map((item) => ({
          shopId: shopId,
          itemId: itemId,
          modelId: item.modelId,
          promotionId: 0,
          promotionType: 0,
        }));
        if (items && items.length > splitNum) {
          const promises = [
            getStockBatchWithLatestBreakdown({
              items: items?.slice(0, splitNum),
            }),
            getStockBatchWithLatestBreakdown({ items: items?.slice(splitNum) }),
          ];
          const data = await Promise.all(promises);
          const res = data.reduce((pre, item) => {
            if (!pre) {
              pre = item;
            } else {
              pre.stocks = [...(pre.stocks || []), ...(item?.stocks || [])];
            }
            return pre;
          });
          return res;
        }
        return getStockBatchWithLatestBreakdown({ items });
      },
      {
        manual: true,
      },
    );

  useEffect(() => {
    if (itemId > 0 && itemModelListData?.modelList?.length) {
      getStockBreakDownListData();
      getStockBatchWithLatestBreakdownData();
    }
  }, [itemId, itemModelListData]);

  useEffect(() => {
    setStockList(
      stockBreakDownListData?.stockList ? stockBreakDownListData.stockList : [],
    );
    setFloatStockList(
      stockBreakDownListData?.floatingStockLists
        ? stockBreakDownListData.floatingStockLists
        : [],
    );
    setItemStock(itemStockData?.stocks ? itemStockData?.stocks : []);
  }, [stockBreakDownListData, itemStockData]);

  useEffect(() => {
        setItemModelList(
      itemModelListData?.modelList ? itemModelListData.modelList : [],
    );
    if (itemModelListData?.modelList) {
      itemModelListData?.modelList?.map((model) => {
        const formatWeightAndDimension = (modelData?: number, itemData?: number) => {
          const data = modelData ?? itemData;
          return data ? data / FEtoBEMultiplier : data
        }
        form.setFieldsValue({
          [`model${model.modelId}_model_name`]: model.modelName,
          [`model${model.modelId}_original_price`]: model.originalPrice
            ? formatCurrencyForItem(
              region as CountryCode,
              model.originalPrice / FEtoBEMultiplier,
            )
            : undefined,
          [`model${model.modelId}_original_price_before_tax`]:
            model.originalPriceBeforeTax
              ? formatCurrencyForItem(
                region as CountryCode,
                model.originalPriceBeforeTax / FEtoBEMultiplier,
              )
              : undefined,
          [`model${model.modelId}_stock`]: model.stock,
          [`model${model.modelId}_gtin`]: model.gtin ? model.gtin : data?.gtin,
          ...model.modelDtsSetting && {
            [`model${model.modelId}_dts_setting`]: model.modelDtsSetting },
          [`model${model.modelId}_weight`]: formatWeightAndDimension(model.weight, data?.weight),
          [`model${model.modelId}_dimension`]: {
            width: formatWeightAndDimension(model.dimension?.width, data?.dimensions?.width),
            height: formatWeightAndDimension(model.dimension?.height, data?.dimensions?.height),
            length: formatWeightAndDimension(model.dimension?.length, data?.dimensions?.length),
          }
        });
      });
    }
  }, [itemModelListData]);
  useEffect(() => {
    if (itemId > 0 && typeof data !== 'undefined') {
      getItemModelListData();
    }
  }, [data, itemId]);

  const { data: productAttributeData, runAsync: getProductAttributeData } =
    useRequest(
      () =>
        getProductAttributes({
          shopId: data?.shopId,
          itemId: data?.itemId,
          catId: leafCat,
          region: region,
        }),
      {
        manual: true,
      },
    );

  const { runAsync: getTaxGroupTypeData } =
    useRequest(
      (categoryIds?: number[]) => {
        if (region !== 'TW') return Promise.resolve({});

        let categoryIdList: number[] = [];
        if (categoryIds) {
          categoryIdList = categoryIds;
        } else {
          for (let i = 1; i <= maxCatLevel; i++) {
            if (form.getFieldValue(`l${i}_global_category`)) {
              categoryIdList.push(form.getFieldValue(`l${i}_global_category`));
            }
          }
        }

        return getTaxGroupType(
          {
            shopId,
            region,
            categoryIdList,
          },
          { skipError: true, returnFullResponse: true },
        );
      },
      {
        manual: true,
        onSuccess: (res: uploadAdmin.IGetTaxGroupTypeResponse) => {
          if (region !== 'TW') return;
          if (res.spErrorCode) {
            message.error(`OA Tax-Free API error ${  res.spDebugMsg}`);
            form.setFieldsValue({
              taxType: ProductTaxType.NoTaxType,
            });
            return;
          }
          const groupType = res.data?.groupType;
          setTaxGroup(groupType);

          // 每次更新item detail 或者 groupType 没变, 只更新taxGroup不更新taxType
          if (taxGroup === undefined) return;

          if (groupType === ProductGroupType.Book) {
            getBooksInfoByIsbnData();
          } else if (groupType === ProductGroupType.Fresh) {
            form.setFieldsValue({
              taxType: ProductTaxType.TaxAble,
            });
          } else {
            form.setFieldsValue({
              taxType: ProductTaxType.NoTaxType,
            });
          }
        },
      },
    );

  const { runAsync: getBooksInfoByIsbnData } =
    useRequest<uploadAdmin.IGetBooksInfoByIsbnResponse, []>(
      () => {
        // only TW and is BOOK type
        if (region !== 'TW' || taxGroup !== ProductGroupType.Book)
          return Promise.resolve({});

        const isbn = form.getFieldValue(isbnKey);

        if (!isbn) {
          // is isbn is empty set tax-able
          return Promise.resolve({
            spErrorCode: 0,
            taxType: ProductTaxType.TaxAble,
          }) as Promise<uploadAdmin.IGetBooksInfoByIsbnResponse>;
        }

        return getBooksInfoByIsbn(
          {
            region: region,
            isbn,
          },
          { skipError: true, returnFullResponse: true },
        ) as Promise<uploadAdmin.IGetBooksInfoByIsbnResponse>;
      },
      {
        manual: true,
        onSuccess: (res: uploadAdmin.IGetBooksInfoByIsbnResponse) => {
          if (region !== 'TW' || taxGroup !== ProductGroupType.Book) return;
          if (res.spErrorCode === 0) {
            form.setFieldsValue({
              taxType: res.taxType,
            });
          } else {
            message.error(`OA Tax-Free API error ${  res.spDebugMsg}`);
            form.setFieldsValue({
              taxType: ProductTaxType.NoTaxType,
            });
          }
        },
      },
    );

  const { data: licenses } = useRequest(
    async () => {
      if (data?.itemId || data?.shopId) {
        const resp = await getLicensesForItem({
          itemId: data?.itemId,
          limit: 10,
        });
        return resp?.licenses || [];
      }
    },
    { refreshDeps: [data?.itemId, data?.shopId] },
  );
  const handleUpdateModelSettingV2 = async (params: {
    modelsSetting: uploadAdmin.IModelSettingV2[];
  }) => {
    const modelsSetting = params.modelsSetting?.map((v) => ({
      ...v,
      itemId,
      shopId,
    }));
    await updateModelSettingV2({
      region,
      auditParam: {
        reason: 'update mpsku fulfilment config',
        source: 'listing admin',
        auditType: uploadAdminConstant.AuditType.AUDIT_TYPE_TOGGLE_FULFILMENT,
      },
      modelsSetting,
    });
    await getStockBatchWithLatestBreakdownData();
    getStockBreakDownListData();
  };
  useEffect(() => {
    if (leafCat !== undefined && data) {
      getProductAttributeData();
    }
  }, [leafCat, data]);

  const {
    data: globalProductAttributeData,
    runAsync: getGlobalProductAttributeData,
  } = useRequest(
    () =>
      getGlobalProductAttributes({
        itemId: data?.itemId,
        catId: globalLeafCat,
        region: region,
      }),
    {
      manual: true,
    },
  );
  useEffect(() => {
    if (globalLeafCat !== undefined && data) {
      getGlobalProductAttributeData();
      if (data.globalCategoryList) {
        const oldCatList = data.globalCategoryList?.map((c) => c.catId);
        if (!oldCatList.includes(globalLeafCat)) {
          getTaxGroupTypeData();
        }
      }
    }
  }, [globalLeafCat, data]);

  useEffect(() => {
    globalProductAttributeData?.productAttrList?.globalAttributes?.some((g) => {
      if (g?.isIsbn) {
        setIsbnKey(`global_attribute${g.attrId}_value`);
        return true;
      }
    });
  }, [globalProductAttributeData?.productAttrList?.globalAttributes]);

  const { data: langRes, runAsync: getSupportedLangs } =
    useRequest(
      () =>
        getRegionTranslationLang({
          region,
        }),
      {
        manual: true,
      },
    );

  useEffect(() => {
    getSupportedLangs();
  }, []);

  const langs = useMemo(() => {
    if (!langRes) return [];
    return [
      ...(langRes.languageInfo?.supportLanguageList ?? []),
      langRes.languageInfo?.defaultLanguage,
    ];
  }, [langRes]);

  const translationsRef = useRef();
  const { data: translationRes, runAsync: getTranslations } =
    useRequest(
      () =>
        getItemTranslation({
          itemId: itemId,
          shopId: shopId,
          region,
        }),
      {
        manual: true,
      },
    );

  const showLangContent = useMemo(
    () => !!data && !!translationRes,
    [data, translationRes],
  );

  const isDefaultModel = useMemo(
    () => itemModelList.length === 1 && itemModelList[0]?.isDefault,
    [itemModelList],
  );

  const hasTierVariation = useMemo(
    () => data?.tierVariationList?.length,
    [data],
  );

  const showVariation = useMemo(
    () => !isDefaultModel && !!hasTierVariation,
    [isDefaultModel, hasTierVariation],
  );

  useEffect(() => {
    if (!showLangContent) return;

    // 备份初始翻译数据
    translationsRef.current = cloneDeep(langForm.getFieldsValue());
  }, [data, translationRes]);

  useEffect(() => {
    if (itemId > 0) {
      getTranslations();
    }
  }, [itemId]);

  const translationInfo = useMemo(() => {
    return translationRes?.translationInfo;
  }, [translationRes]);

  async function submitDetail() {
    await form.validateFields().catch((e) => {
      // combobox inputs are intermediate and not submitted; do not consider them for errors
      if (
        e.errorFields.some((errorField: { name: string[] }) =>
          errorField.name.some((name) => !name.includes('combobox')),
        )
      ) {
        const errMsg =
          'Failed to submit. Please check your inputs in info tab!';
        message.error(errMsg);
        throw new Error(errMsg);
      }
    });

    if (!detailChanged) {
      return;
    }

    const submitResult = await submitUpdate({
      itemDetail:
        data as uploadAdmin.IGetItemDetailResponse,
      region: region,
      form: form,
      images: images,
      itemModelList: itemModelList,
      editModeDataSource: editModeDataSource,
      productAttributeData: productAttributeData as uploadAdmin.IGetProductAttributesResponse,
      editModeGlobalDataSource: editModeGlobalDataSource,
      globalProductAttributeData: globalProductAttributeData as uploadAdmin.IGetGlobalProductAttributesResponse,
      isDefaultDTSPrompted: isDefaultDTSPrompted,
    });
    if (submitResult?.spDebugMsg || submitResult?.errDetails) {
      const errMsg = submitResult.spDebugMsg;
      if (
        errMsg ===
        'Please take note that since the new categories don\'t support pre-order list. The pre-order will be automatically turned off and DTS will be updated to the default value'
      ) {
        setIsDefaultDTSPrompted(true);
        form.setFieldsValue({
          isPreOrder: false,
          daysToShip: data?.nonPreOrderLimit
        });
      } else {
        throw new Error(errMsg);
      }
    } else if (submitResult) {
      setTimeout(() => {
        run();
      }, refreshDelay);
    }
    setIsDefaultDTSPrompted(false);

    if (!submitResult) {
      throw new Error('submitted with an error!');
    }
  }

  async function submitTranslations() {
    const translationsHasChanged = !isEqual(
      translationsRef.current,
      langForm.getFieldsValue(),
    );

    // let errMsg: string;
    // const formData = await langForm.validateFields().catch(e => {
    //   errMsg = `Failed to submit. ${e.errorFields[0].errors.join(',')}`;

    //   message.error(errMsg);
    //   throw new Error(errMsg);
    // });

    const formData = langForm.getFieldsValue();

    if (!translationsHasChanged) {
      return;
    }

    const basicSettingList = langs?.map((obj) => {
      const lang = obj?.language;

      const tierVariationList = showVariation
        ? data?.tierVariationList?.map((v, i) => {
          const options: string[] =
              v.options?.map(
                (_, j) => formData[`variation.${i}.${j}.${lang}`] ?? '',
              ) ?? [];

          return {
            name: formData[`variation.${i}.${lang}`] ?? '',
            options,
          };
        }) ?? []
        : null;

      return {
        lang,
        name: formData[`productName.${lang}`] ?? '',
        tierVariationList,
      };
    });

    const descSettingList = langs?.map((obj) => {
      const lang = obj?.language;

      return {
        lang,
        description: formData[`description.${lang}`] ?? '',
      };
    });

    const modelSettingList = showVariation
      ? translationInfo?.modelInfoList ?? []
      : null;

    const uploadData = {
      shopId: shopId,
      itemId: itemId,
      region,
      translationSetting: {
        basicSettingList,
        descSettingList,
        modelSettingList,
      },
    };

    const submitResult = await updateItemTranslation(
      uploadData as uploadAdmin.IUpdateItemTranslationRequest,
    );

    if (!submitResult) {
      throw new Error('submitted with an error!');
    }

    getTranslations();
  }

  const submitFn = async () => {
    try {
      await Promise.all([submitDetail(), submitTranslations()]);
      message.success('Item updated successfully!');
      setIsEditing(false);
      setDetailChanged(false);
    } catch (error) {
      console.warn('error', error);
    }
  };

  const submitConfirm = () => {
    const descriptionExtra = form.getFieldValue('descriptionExtra');
    const hasInvalidImage = descriptionExtra?.imageList.some(
      (item: ImageRecord) => !item.src,
    );

    if (hasInvalidImage) {
      const noEmpty = JSON.parse(
        form.getFieldValue('description') || '[]',
      )?.length;
      if (noEmpty) {
        Modal.confirm({
          icon: <></>,
          title: 'Images have not been uploaded yet',
          content:
            'Currently Product Description has unfinished uploading images. If you continue to save, these images will be lost. Are you sure about Save & Publish?',
          okText: 'Confirm',
          onOk() {
            submitFn();
          },
        });
      } else {
        Modal.info({
          icon: <></>,
          title: 'Images have not been uploaded yet',
          content:
            'The Product Description image has not been uploaded yet, please check and try again.',
        });
      }
    }
    return hasInvalidImage;
  };

  async function submit() {
    if (!submitConfirm()) {
      submitFn();
    }
  }

  useEffect(() => {
    if (data) {
      setFormAndState({
        itemDetail: data,
        region: region,
        form: form,
        setImages: setImages,
        setVariationList: setVariationList,
      });
      const { width, length, height } = form.getFieldsValue(true);
      const isSupportDecimal = [width, length, height]
        .map((item) => (item === undefined ? true : !Number.isInteger(+item)))
        .includes(true);
      form.setFieldsValue({ isSupportDecimal });

      if (data.globalCategoryList) {
        // get tax group for every time item detail update
        getTaxGroupTypeData(data.globalCategoryList?.map((g) => g.catId!));
        // rest taxType
        setTaxGroup(undefined);
      }
    }
  }, [data]);

  document.addEventListener('visibilitychange', visibilityChange);
  useEffect(() => {
    ProductDetailReporter.setCustomPointTime(
      POINT_ROUTER_ENTER,
      getRouterEnterTime() || +new Date(),
    );
    let firstRenderTime = +new Date();
    if (leaveTabTime >= jsLoadedTime && resumeTabTime <= firstRenderTime) {
      firstRenderTime -= Math.max(resumeTabTime - leaveTabTime, 0);
    }
    ProductDetailReporter.setCustomPointTime(
      POINT_FIRST_RENDER,
      firstRenderTime,
    );
    return () => {
      document.removeEventListener('visibilitychange', visibilityChange);
      mainReporter.clearAll();
    };
  }, []);

  useEffect(() => {
    if (!loading) {
      let finishedRenderTime = +new Date();
      if (leaveTabTime >= jsLoadedTime && resumeTabTime <= finishedRenderTime) {
        finishedRenderTime -= Math.max(resumeTabTime - leaveTabTime, 0);
      }
      ProductDetailReporter.setCustomPointTime(
        POINT_FINISH_RENDER,
        finishedRenderTime,
      );
      mainReporter.updateSDKconfig({
        region: getCountryForDisplay(),
      });
      ProductDetailReporter.report(0, {
        isApiError: Boolean(!data).toString(),
      });
    }
  }, [loading]);

  return (
    <div className={styles.content}>
      <div className={styles.header}>
        <span className={styles.title}>
          <span style={{ paddingRight: '10px' }}>{`Product Details${
            itemId && itemId >= 0 ? ` (${itemId})` : ''
          }`}</span>
          <Tag
            className={styles.headerTag}
            color={
              itemStatusTagColour[
                data?.itemStatus as keyof typeof itemStatusTagColour
              ]
            }
          >
            Status:{' '}
            {data?.itemStatus != null
              ? itemStatusString[
                data.itemStatus as keyof typeof itemStatusString
              ]
              : '-'}
          </Tag>
          {data?.isCb && (
            <Tag className={styles.headerTag} color={'gold'}>
              CrossBorder Seller
            </Tag>
          )}
          {data?.isChildVsku && (
            <Tag className={styles.headerTag} color={'cyan'}>
              Virtual Package Item
            </Tag>
          )}
          {data?.useNewStockStructure && (
            <Tag className={styles.headerTag} color={'purple'}>
              New Stock Structure
            </Tag>
          )}
            {data?.skuScenario === 1 && (
            <Tag className={styles.headerTag} color={'orange'}>
              Live SKU
            </Tag>
          )}
          {(() => {
            const isTpfPff = data?.shopWarehouseFlag === uploadAdminConstant.Shop3pfWarehouseFlag.SHOP_WAREHOUSE_FLAG_3PF_HYBRID;
            const isTpfOnly = data?.shopWarehouseFlag === uploadAdminConstant.Shop3pfWarehouseFlag.SHOP_WAREHOUSE_FLAG_3PF_ONLY;
            const isFbsPff = data?.isFbsShop;
            if (isTpfPff && isFbsPff) {
              return <Tag className={styles.headerTag} color={'gold'}>
                LFF
              </Tag>;
            }
            if (isTpfPff) {
              return <Tag className={styles.headerTag} color={'gold'}>
                3PF-PFF
              </Tag>;
            }
            if (isTpfOnly) {
              return <Tag className={styles.headerTag} color={'gold'}>
                3PF-Only
              </Tag>;
            }
            return null;
          })()}
        </span>
        <span>
          <Button
            disabled={itemId < 0}
            onClick={async () => {
              const purgeResult = await purgeCsCache({
                itemId: itemId,
                shopId: shopId,
              });
              if (!purgeResult?.spDebugMsg) {
                message.success('CS cache purged successfully!');
              }
            }}
          >
            Purge CS Cache
          </Button>
          <Button
            disabled={itemId < 0}
            onClick={async () => {
              const purgeResult = await purgeMallCache({
                region: region,
                itemId: itemId,
                shopId: shopId,
              });
              if (!purgeResult?.spDebugMsg) {
                message.success('Mall cache purged successfully!');
              }
            }}
          >
            Purge Mall Cache
          </Button>
          <Button disabled={itemId < 0}>
            <a
              href={
                isPageAnIframe
                  ? `${getAdminHost(region)}/system/tools/?entity_id=${itemId}`
                  : `${Routes.CODING_MONKEY_PAGE}?shopId=${shopId}&itemId=${itemId}`
              }
              target={isPageAnIframe ? '_top' : undefined}
            >
              Coding Monkey
            </a>
          </Button>
          <AuthWrapper codes={[AUTH_CODE.ITEM_DETAIL_WRITE]}>
            {!isEditing && (
              <Button
                onClick={() => {
                  setIsEditing(true);
                }}
                disabled={itemId < 0}
              >
                Edit
              </Button>
            )}

            {isEditing && <Button onClick={() => submit()}>Submit</Button>}

            <span no-auth style={{ marginLeft: 10 }}>
              <Tooltip title="No permission">
                <Button disabled>Edit</Button>
              </Tooltip>
            </span>
          </AuthWrapper>

          {(data?.itemStatus === ITEM_INVALID ||
            data?.itemStatus === ITEM_INVALID_HIDE ||
            data?.itemStatus === ITEM_OFFENSIVE_HIDE) && (
            <AuthWrapper codes={[AUTH_CODE.ITEM_DETAIL_SET_NORMAL_WRITE]}>
              <Button
                onClick={async () => {
                  const setNormalResult = await resetItemNormal({
                    shopId: shopId,
                    itemId: itemId,
                  });
                  if (!setNormalResult?.spDebugMsg) {
                    message.success('Item set normal successfully!');
                  }
                }}
              >
                Set Normal
              </Button>

              <span no-auth style={{ marginLeft: 10 }}>
                <Tooltip title="No permission">
                  <Button disabled>Set Normal</Button>
                </Tooltip>
              </span>
            </AuthWrapper>
          )}
        </span>
      </div>
      {loading && <Spin className={styles.center} />}
      {!loading && data && (
        <Card className={styles.reduceCardPaddingTop}>
          <Tabs
            defaultActiveKey="1"
            className={styles.card}
            activeKey={routeParams?.tab as string}
            onChange={(key) =>
              navTo({
                to: routeId,
                params: {
                  tab: key,
                  region: region,
                  shopId: shopId,
                  itemId: itemId,
                }
              })
            }
            items = {[
              {
                label: 'Info',
                key: TabKey.Info,
                children: (
                  <div style={{ height: '100vh', overflowY: 'scroll' }}>
                    <InfoTab
                      item={data}
                      region={region}
                      env={env}
                      stockList={stockList}
                      floatingStockLists={floatStockList}
                      itemModelList={itemModelList}
                      itemStock={itemStock}
                      productAttributeData={productAttributeData}
                      globalProductAttributeData={globalProductAttributeData}
                      isShopWhitelisted={isShopWhitelisted}
                      setLeafCat={setLeafCat}
                      globalLeafCat={globalLeafCat}
                      setGlobalLeafCat={setGlobalLeafCat}
                      form={form}
                      isEditing={isEditing}
                      images={images}
                      setImages={setImages}
                      editModeDataSource={editModeDataSource}
                      setEditModeDataSource={setEditModeDataSource}
                      editModeGlobalDataSource={editModeGlobalDataSource}
                      setEditModeGlobalDataSource={setEditModeGlobalDataSource}
                      variationList={variationList}
                      setVariationList={setVariationList}
                      licenses={licenses}
                      updateModelSettingV2={handleUpdateModelSettingV2}
                      getBooksInfoByIsbnData={getBooksInfoByIsbnData}
                    />
                    <Modal
                      open={isDefaultDTSPrompted}
                      title={'Note'}
                      cancelText={'Cancel'}
                      onCancel={() => setIsDefaultDTSPrompted(false)}
                      okText={'Confirm'}
                      onOk={() => submit()}
                    >
                      Please take note that since the new category doesn&apos;t
                      support pre-order list. The pre-order will be
                      automatically turned off and DTS will be updated to the
                      default value
                    </Modal>
                  </div>
                ),
              },
              {
                label: 'Multi-language info',
                key: TabKey.MultiLangInfo,
                children: (
                  <div style={{ height: '100vh', overflowY: 'scroll' }}>
                    {showLangContent && (
                      <MultiLangTab
                        langs={
                          langs as uploadAdmin.ILanguageBasicInfo[]
                        }
                        showVariation={showVariation}
                        translationInfo={translationInfo}
                        itemDetail={data}
                        form={langForm}
                        isEditing={isEditing}
                      />
                    )}
                  </div>
                ),
              },
              {
                label: 'Logs',
                key: TabKey.Log,
                children: (
                  <div style={{ height: '100vh', overflowY: 'scroll' }}>
                    {showLangContent && (
                      <LogTab itemId={itemId} region={region} />
                    )}
                  </div>
                ),
              },
            ]}
          />
        </Card>
      )}
    </div>
  );
}


export default withErrorBoundary(DetailPage, {
  onComponentDidCatch: (error: Error, _errorInfo: React.ErrorInfo) => {
    mainReporter.errorReport(error);
  },
});

ProductDetailReporter.setCustomPointTime(POINT_JS_LOADED);
jsLoadedTime = +new Date();
