import { getCountry, withErrorBoundary } from 'admin-upload-common';
import MassEditImg from 'admin-upload-common/es/assets/images/edit_mass.png';
import * as React from 'react';

import Instruction from 'src/components/shop-limit-settting/Instruction';
import ShopLimitPage from 'src/containers/shop-limit-settting/ShopLimitPage';
import { mainReporter } from 'src/reporter';

const description =
  'Use this module to update the upload limit of any shops, if a shop do not have any limit set, the default limit will be used:';
const acceptableFile = 'csv / utf8';
const fileHeader = 'shopid, limit';
const instructionMap: {
  [k: string]: {
    description: string;
    limit: string[];
    acceptableFile: string;
    fileHeader: string;
  };
} = {
  TW: {
    description,
    limit: [
      'Seller without ACH Default Limit: 100',
      'Normal Shop Default Limit (with ACH): 1000',
      'Official (Mall) Shop Default Limit (with ACH): 20000',
    ],
    acceptableFile,
    fileHeader,
  },
  TH: {
    description,
    limit: [
      'Normal Seller Default Limit: 1000',
      'Official (Mall) Shop Default Limit: 20000',
    ],
    acceptableFile,
    fileHeader,
  },
  MY: {
    description,
    limit: [
      'Normal Seller Default Limit: 1000',
      'Official (Mall) Shop Default Limit: 20000',
    ],
    acceptableFile,
    fileHeader,
  },
  SG: {
    description,
    limit: [
      'Normal Seller Default Limit: 1000',
      'Official (Mall) Shop Default Limit: 20000',
    ],
    acceptableFile,
    fileHeader,
  },
  ID: {
    description,
    limit: [
      'CB Normal/Preferred Seller Default Limit: 10000',
      'CB Official (Mall)  Seller Default Limit: 50000',
      'Local Seller without IC Default Limit: 100',
      'Local Seller without Bank Account  Default Limit: 100',
      'Local Normal Seller Default Limit (with IC and Bank Account): 2000',
      'Local Preferred Seller Default Limit (with IC and Bank Account): 10000',
      'Local Official (Mall) Seller Default Limit (with IC and Bank Account): 50000',
    ],
    acceptableFile,
    fileHeader,
  },
  PH: {
    description,
    limit: [
      'Normal Seller Default Limit: 1000',
      'Official (Mall) Shop Default Limit: 20000',
    ],
    acceptableFile,
    fileHeader,
  },
  VN: {
    description,
    limit: [
      'Normal Seller Default Limit: 1000',
      'Official (Mall) Shop Default Limit: 20000',
    ],
    acceptableFile,
    fileHeader,
  },
  BR: {
    description,
    limit: [
      'Normal Seller Default Limit: 10000',
      'Official (Mall) Shop Default Limit: 20000',
    ],
    acceptableFile,
    fileHeader,
  },
  MX: {
    description,
    limit: [
      'Normal Seller Default Limit: 10000',
      'Official (Mall) Shop Default Limit: 20000',
    ],
    acceptableFile,
    fileHeader,
  },
  CO: {
    description,
    limit: ['Seller Default Limit: 10000'],
    acceptableFile,
    fileHeader,
  },
  CL: {
    description,
    limit: ['Seller Default Limit: 10000'],
    acceptableFile,
    fileHeader,
  },
  IN: {
    description,
    limit: ['Seller Default Limit: 10000'],
    acceptableFile,
    fileHeader,
  },
  PL: {
    description,
    limit: [
      'Normal Seller Default Limit: 10000',
      'CB Seller Default Limit: 10000',
      'Local Official (Mall) Shop Default Limit: 20000',
      'Local Preferred Shop Default Limit: 10000',
    ],
    acceptableFile,
    fileHeader,
  },
  FR: {
    description,
    limit: [
      'Normal Seller Default Limit: 1000',
      'CB Seller Default Limit: 10000',
      'Local Official (Mall) Shop Default Limit: 20000',
      'Local Preferred Shop Default Limit: 10000',
    ],
    acceptableFile,
    fileHeader,
  },
  ES: {
    description,
    limit: [
      'Normal Seller Default Limit: 1000',
      'CB Seller Default Limit: 10000',
      'Local Official (Mall) Shop Default Limit: 20000',
      'Local Preferred Shop Default Limit: 10000',
    ],
    acceptableFile,
    fileHeader,
  },
  default: {
    description,
    limit: ['Seller Default Limit: 10000'],
    acceptableFile,
    fileHeader,
  },
};

export const downloadTemplate = [
  {
    name: 'Download',
    link:
      'https://docs.google.com/spreadsheets/d/1Z1aMJVzRJLs3durj_RwXn4X3mGZReru1Ybx27Px5aIk/export?format=csv&id=1Z1aMJVzRJLs3durj_RwXn4X3mGZReru1Ybx27Px5aIk&gid=0',
    icon: MassEditImg,
  },
];

const MassEditTypes = {
  'Mass Limit Setting': 'mass_set_shop_item_limit',
};

export const RequiredHeaders = {
  'Mass Limit Setting': 'shopid,limit',
};
function MassUploadVariation() {
  const region = getCountry();
  const regionLimitInfo = instructionMap[region] || instructionMap.default;
  return (
    <ShopLimitPage
      pageTitle="Upload Listing Limit Setting"
      massEditTypes={MassEditTypes}
      customTopCardInstruction={
        <Instruction regionLimitInfo={regionLimitInfo} />
      }
      hideTypeSelect={true}
      templates={downloadTemplate}
      pollingInterval={500}
      validateTemplateHeaders={RequiredHeaders}
      region={region}
    />
  );
}

export default withErrorBoundary(MassUploadVariation, {
  onComponentDidCatch: (error: Error, _errorInfo: React.ErrorInfo) => {
    mainReporter.errorReport(error);
  },
});
