import type {
  ICreateMassEditTaskRequest,
  ICustomColumn,
  IMassEditTask
} from '@classification/mass-upload';
import MassUploadPage, {
  CustomColumnTypes,
  ExportBtn,
  generateMassUploadConfig
} from '@classification/mass-upload';
import { useRequest } from 'ahooks';
import { Button, Cascader,Col,DatePicker, Form, Input, Row, Select, Spin } from 'antd';
import * as React from 'react';

import { getCountry } from 'src/_shared/utils';
import type { validation } from 'src/api/validation/validation';
import {
  getCategoryTreeWithRule
} from '../../api/validation';
import { CONFIGS, OnlineValidationResultsConfigId } from './constants';

import '@classification/mass-upload/dist/style/index.css';

const { RangePicker } = DatePicker;

enum RuleType {
  MANDATORY = 1,
  FLEXIBLE = 2,
  OPTIONAL = 3 
}
const RuleTypeMap = {
  [RuleType.MANDATORY]: 'Mandatory',
  [RuleType.FLEXIBLE]: 'Flexible',
  [RuleType.OPTIONAL]: 'Optional'
}
const ruleType = [{
  key: RuleType.MANDATORY,
  label: RuleTypeMap[RuleType.MANDATORY]
}, {
  key: RuleType.FLEXIBLE,
  label: RuleTypeMap[RuleType.FLEXIBLE]
}, {
  key: RuleType.OPTIONAL,
  label: RuleTypeMap[RuleType.OPTIONAL]
}];

const massEditConfig = generateMassUploadConfig(CONFIGS);
const massEditColums: ICustomColumn[] = [
  {
    type: CustomColumnTypes.TIME,
  },
  {
    type: CustomColumnTypes.TYPE,
    title: 'Filters',
    key: 'filter',
    dataIndex: 'filter',
    width: 280,
    render: (_, row: IMassEditTask) => {
      try {
        const extraData = JSON.parse(row.extraData);
        const displayFilter = [];
        if (extraData.shop_id) {
          displayFilter.push(`Shop ID ${extraData.shop_id}`);
        }
        if (extraData.start_date || extraData.end_date) {
          displayFilter.push(`Date ${extraData.start_date} to ${extraData.end_date}`);
        }
        if (extraData.category_id && extraData.category_id.length) {
          displayFilter.push(`Category ${extraData.category_id}`);
        } 
        if (extraData.rule_names) {
          displayFilter.push(`Rule Type ${extraData.rule_names.map((r: RuleType) => RuleTypeMap[r as RuleType])}`);
        }

        return displayFilter.length ? displayFilter.map(s => <div>{s}</div>) : '-';
      } catch {
        return '-';
      }
    }
  },
  {
    type: CustomColumnTypes.RESULT_FILE,
  },
  {
    type: CustomColumnTypes.PROGRESS,
  },
  {
    type: CustomColumnTypes.OPERATOR,
    render: (addr: string, row: IMassEditTask) => {
      return row.operator;
    },
  },
];

interface GtinReportGeneralFormProps {
  createMassEditTask: (query: ICreateMassEditTaskRequest) => Promise<void>
}

interface SearchCategoryItem extends validation.ICategoryWithRule {
  label: string;
  value: number;
}

function traverseArrayWithChildren(arr: validation.ICategoryWithRule[]): SearchCategoryItem[] {
  return arr.map((item) => {
    const newItem = { ...item } as SearchCategoryItem;
    newItem.label = item.name ?? '';
    newItem.value = item.categoryId ?? 0;
    if (item.children) {
      traverseArrayWithChildren(item.children);
    }
    return newItem;
  });
}
function GtinReportGeneralForm(payload: GtinReportGeneralFormProps) {
  const region = getCountry();
  const [form] = Form.useForm();
  const dateFormat = 'YYYY-MM-DD';
  const [categoryTree, setCategoryTree] = React.useState<SearchCategoryItem[]>([]);

  const { loading } = useRequest(
    () => {
      return getCategoryTreeWithRule({
        region,
      });
    },
    {
      refreshDeps: [region],

      onSuccess: (response: validation.IGetCategoryTreeWithRuleResponse) => {
        setCategoryTree(traverseArrayWithChildren(response?.basicRules ?? []));
      },
    },
  );

  const onGenerate = () => {
    const { createMassEditTask } = payload;
    const { shopId, dateRange, categoryId, ruleType } = form.getFieldsValue();

    const [startDate, endDate] = dateRange || [];
    const extraData = JSON.stringify({
      start_date: startDate ? startDate?.format(dateFormat) : '',
      end_date: endDate ?endDate?.format(dateFormat) : '',
      shop_id: shopId ? +shopId : undefined,
      category_id: categoryId?.map((cat: number[]) => cat[cat.length - 1]),
      rule_names: ruleType,
      region,
      paging: {
        cursor: '',
      },
    });
    createMassEditTask({
      configId: OnlineValidationResultsConfigId,
      executionName: `from ${startDate ?? ''} to ${endDate ?? ''} &t=${Date.now()}`,
      extraData,
      filename: '',
    });
  }

  const onReset = () => {
    form.resetFields();
  }

  return (
    <Form form={form}>
      <Row gutter={16}>
        <Col span={6}>
          <Form.Item label="Shop ID" name="shopId" style={{ display: 'flex' }}>
            <Input />
          </Form.Item>
        </Col>
        <Col span={6}>
        <Form.Item label="Date" name="dateRange" style={{ display: 'flex' }}>
          <RangePicker />
        </Form.Item>
        </Col>
        <Col span={6}>
        <Form.Item label="Category" name="categoryId" style={{ display: 'flex' }}>
          <Cascader
            style={{ width: '100%' }}
            options={categoryTree}
            fieldNames={{ label: 'name', value: 'categoryId', children: 'children' }}
            multiple
            maxTagCount="responsive"
            notFoundContent={loading ? <Spin size="small" /> : null}
          />
        </Form.Item>
        </Col>
        <Col span={6}>
        <Form.Item label="Rule" name="ruleType" style={{ display: 'flex' }}>
          <Select
            style={{ minWidth: '220px' }}
            dropdownMatchSelectWidth={false}
            mode='multiple'
          >
            {ruleType.map((type) => (
              <Select.Option
                value={type.key}
                key={type.key}
              >
                {type.label}
              </Select.Option>
            ))}
          </Select>
        </Form.Item>
        </Col>
      </Row>
      <Row>
        <Col offset={18}>     
         <Form.Item>
          <Button onClick={onReset}>
            Reset
          </Button>
          <Button type="primary" onClick={onGenerate}>
            Generate
          </Button>
        </Form.Item></Col>
      </Row>
    </Form>
  )
}
export default function MassUpload() {
  const region = getCountry();

  return (
    <MassUploadPage
      {...massEditConfig}
      customColumns={massEditColums}
      customTemplateButton={
        <ExportBtn
          templates={massEditConfig.templates}
          icon={MassUploadPage.MASS_EDIT_IMG}
        />
      }
      region={region}
      pageTitle='GTIN Reports'
      customSearchForm={(props: GtinReportGeneralFormProps) => <GtinReportGeneralForm {...props}/>}
    />
  );
}
