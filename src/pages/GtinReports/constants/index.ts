import type { IGenerateParams } from '@classification/mass-upload';

import { getEnvironment } from 'src/_shared/utils';

const env = getEnvironment();

export const OnlineValidationResultsConfigId = { test: 236, stable: 0, uat: 124, staging: 120, live: 139 }[env];
export const CONFIGS: IGenerateParams = {
  groupId: { test: 59, stable: 0, uat: 23, staging: 22, live: 28 }[env],
  tasks: {
    'Online Validation Results': {
      type: 'anatel_attributes_validation_result_report',
      configId: OnlineValidationResultsConfigId,
      headers: [],
      template: '',
      useDateGenerate: true,
    },
  },
};
