import type {
  ICustomColumn,
  IMassEditTask,
} from '@classification/mass-upload';
import MassUploadPage, {
  CustomColumnTypes,
  ExportBtn,
  generateMassUploadConfig
} from '@classification/mass-upload';
import * as React from 'react';

import { getCountry } from 'src/_shared/utils';
import { CONFIGS } from './constants';

import '@classification/mass-upload/dist/style/index.css';

const massEditConfig = generateMassUploadConfig(CONFIGS);
const massEditColums: ICustomColumn[] = [
  {
    type: CustomColumnTypes.TIME,
  },
  {
    type: CustomColumnTypes.TYPE,
  },
  {
    type: CustomColumnTypes.ORIGINAL_FILE,
    render: (addr: string, row: IMassEditTask) => {
      return row.executionName;
    },
  },
  {
    type:   CustomColumnTypes.RESULT_FILE,
  },
  {
    type: CustomColumnTypes.PROGRESS,
  },
  {
    type: CustomColumnTypes.OPERATOR,
    render: (addr: string, row: IMassEditTask) => {
      return row.operator;
    },
  },
];

export default function MassUpload() {
  const region = getCountry();

  return (
    <MassUploadPage
      {...massEditConfig}
      customColumns={massEditColums}
      customTemplateButton={
        <ExportBtn
          templates={massEditConfig.templates}
          icon={MassUploadPage.MASS_EDIT_IMG}
        />
      }
      region={region}
      pageTitle='Anp Reports'
    />
  );
}
