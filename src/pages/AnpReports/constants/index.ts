import type { IGenerateParams } from '@classification/mass-upload';
import MassUploadPage from '@classification/mass-upload';

import { getEnvironment } from 'src/_shared/utils';

const env = getEnvironment();

// TODO: wait for <PERSON><PERSON> to update groupId and configId
export const CONFIGS: IGenerateParams = {
  groupId: { test: 61, stable: 44, uat: 24, staging: 23, live: 29 }[env],
  tasks: {
    'Offline Listing Validation': {
      type: 'mass_validate_anatel_attributes',
      configId: { test: 242, stable: 182, uat: 128, staging: 126, live: 143 }[env],
      headers: [
        'shop_id',
        'item_id',
        'category_id',
        'anp_boolean',
        'anp_process_number',
        'anp_producer',
      ],
      template:
        'https://docs.google.com/spreadsheets/d/1oAIGrSmCI4N-s0U7vMwHyBfr8AmV5CxzKQgIPO0XQuk/export?gid=0&format=csv',
      icon: MassUploadPage.MASS_EDIT_IMG,
      validateRow: {
        // Demo as follow
        // category_id: [
        //   {
        //     checkDuplicate: true,
        //     duplicateDependencies: ['country'],
        //   },
        // ],
      },
    }
  },
};
