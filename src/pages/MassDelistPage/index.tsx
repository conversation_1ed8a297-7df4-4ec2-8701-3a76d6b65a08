import { ExclamationCircleOutlined } from '@ant-design/icons';
import { getCountry, withErrorBoundary } from 'admin-upload-common';
import MassEditImg from 'admin-upload-common/es/assets/images/edit_mass.png';
import { Modal } from 'antd';
import type { ColumnProps } from 'antd/lib/table';
import type { RcFile } from 'antd/lib/upload/interface';
import * as React from 'react';

import Instruction from 'src/components/shop-limit-settting/Instruction';
import { MassUploadPage } from 'src/containers/MassUploadPage';
import { MassEditTaskTableTitle } from 'src/containers/MassUploadPage/constants';
import type {
  ExtraDataObj,
  MassEditTask,
} from 'src/containers/MassUploadPage/typings';
import { mainReporter } from 'src/reporter';
import { getFileDataRows } from '../../utils';

const MassEditTypes = {
  'Mass Delist Item': 'mass_delist_by_shopid',
};

export const downloadTemplate = [
  {
    name: 'Download',
    link:
      'https://docs.google.com/spreadsheets/d/1azGCSekT1vRYD9tRt01P8WRkd3aivuxEp3LMKZ6ve5g/export?format=csv&id=1azGCSekT1vRYD9tRt01P8WRkd3aivuxEp3LMKZ6ve5g&gid=0',
    icon: MassEditImg,
  },
];

const regionLimitInfo = {
  description:
    'Use this function to unlist products which beyond the listing upload limit. ',
  limit: [],
  acceptableFile: 'csv / utf8',
  fileHeader: 'shopid',
};

export const RequiredHeaders = {
  'Mass Delist Item': 'shopid',
};

const uploadWarning = async (file: RcFile | undefined) => {
  if (file) {
    const rows = await getFileDataRows(file);
    const extraData = {
      data: rows,
    };
    return new Promise<ExtraDataObj>((resolve, reject) => {
      Modal.confirm({
        title: 'Upload Warning',
        icon: <ExclamationCircleOutlined />,
        content: `This file has ${rows} rows, are you sure you want to upload it?`,
        okText: 'Confirm',
        centered: true,
        onOk() {
          resolve(extraData);
        },
        onCancel() {
          reject({});
        },
      });
    });
  }
  return Promise.resolve({});
};

const changeTableColumn = (columns: ColumnProps<MassEditTask>[]) => {
  const dataColumn: ColumnProps<MassEditTask> = {
    title: MassEditTaskTableTitle.Data,
    dataIndex: 'extraDataObj',
    key: 'data',
    width: 150,
    render: (extraDataObj: ExtraDataObj) => {
      return extraDataObj?.data || '-';
    },
  };
  const newColumn = columns.filter(
    (v) => v.title !== MassEditTaskTableTitle.Type,
  );
  newColumn.splice(-2, 0, dataColumn);
  return newColumn;
};

function MassDelist() {
  const region = getCountry();

  return (
    <>
      <MassUploadPage
        pageTitle="Mass Delist Item By Shop ID"
        massEditTypes={MassEditTypes}
        customTopCardInstruction={
          <Instruction regionLimitInfo={regionLimitInfo} />
        }
        templates={downloadTemplate}
        hideTypeSelect={true}
        pollingInterval={1000}
        validateTemplateHeaders={RequiredHeaders}
        region={region}
        beforeMassEditTaskCreate={uploadWarning}
        processColumn={changeTableColumn}
      />
    </>
  );
}

export default withErrorBoundary(MassDelist, {
  onComponentDidCatch: (error: Error, _errorInfo: React.ErrorInfo) => {
    mainReporter.errorReport(error);
  },
});
