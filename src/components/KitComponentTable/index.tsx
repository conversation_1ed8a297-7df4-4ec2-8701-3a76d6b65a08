import { EditOutlined } from '@ant-design/icons';
import { Button, Form, Table, Tag, Typography } from 'antd';
import type { FormInstance } from 'antd/es/form/Form';
import type { ColumnsType } from 'antd/lib/table';
import type { ForwardedRef } from 'react';
import React, { forwardRef, useMemo, useState } from 'react';

import type { IVirtualSKUModelInfo, IVskuComponentSku } from 'src/api/uploadBffAdmin/types';
import ProductInfo from 'src/components/ProductInfo';
import Thumbnail from 'src/components/Thumbnail';
import { formatCurrency } from 'src/utils/currency';
import SelectComponents, { STEP } from '../SelectComponents';
import style from './style.module.scss';

const { Paragraph } = Typography;

const KitComponentTable = forwardRef(
  (
    {
      kitComponents = [],
      type = 'edit',
      shopId,
      itemId,
      onChange,
    }: {
      kitComponents?: IVirtualSKUModelInfo[];
      type: 'edit' | 'view';
      shopId?: number;
      itemId?: number;
      onChange?: (kitComponents: IVirtualSKUModelInfo[]) => void;
    },
    ref: ForwardedRef<FormInstance>
  ) => {
    const isEdit = useMemo(() => type === 'edit', [type]);
    const [form] = Form.useForm();
    const [visibleComponents, setVisibleComponents] = useState(false);
    const [step, setStep] = useState(0);
    const [currentComponentList, setCurrentComponentList] = useState<{
      index: number;
      currentComponentList: IVskuComponentSku[];
    }>();

    const mainSkuId = useMemo(() => {
      let id: number | undefined = undefined;
      for (let i = 0; i < kitComponents.length; i++) {
        const ketComponent = kitComponents[i];
        id = ketComponent.componentSkuList?.find(comp => comp.mainSku)?.mpskuModelId;
        if (id) {
          return id
        }
      }
      return id;
    }, [kitComponents]);
    const addKitComponents = (index: number) => {
      setCurrentComponentList({
        index,
        currentComponentList: [],
      });
      setStep(STEP.ONE);
      setVisibleComponents(true);
    };
    const editKitComponents = (index: number, modelList: IVskuComponentSku[]) => {
      setCurrentComponentList({
        index,
        currentComponentList: modelList || [],
      });
      setStep(STEP.TWO);
      setVisibleComponents(true);
    };

    const onConfirm = (list: IVskuComponentSku[]) => {
      const kitComponents: IVirtualSKUModelInfo[] = form.getFieldValue('kitComponents');
      const newKitComponents = [...kitComponents];
      const component = newKitComponents[currentComponentList?.index!];
      newKitComponents[currentComponentList?.index!] = {
        ...component,
        componentSkuList: list,
      };
      let mainId: number | undefined = undefined;
      for (let i = 0; i < list?.length ; i++) {
        const comp = list[i];
        if (comp.mainSku) {
          mainId = comp.mpskuModelId!;
        }
      }
      if (!mainSkuId) {
        mainId = mainSkuId;
      }
      if (mainId) {
        for (let i = 0; i < newKitComponents.length; i++) {
          newKitComponents[i]?.componentSkuList?.forEach(comp => {
            comp.mainSku = comp.mpskuModelId === mainId;
          });
        }
      }
      form.setFieldsValue({ kitComponents: newKitComponents });
      onChange?.(newKitComponents);
      setVisibleComponents(false);
    };

    const columns: ColumnsType<IVirtualSKUModelInfo> = (
      [
        {
          title: 'Kit',
          fixed: 'left',
          width: 150,
          render: (_, record, index) => {
            const name = record.name;
            return <Paragraph ellipsis={{ rows: 2, tooltip: name }}>Kit Migration {index + 1}</Paragraph>;
          },
        },
        {
          title: 'Model Name',
          fixed: 'left',
          width: 150,
          render: (_, record) => {
            const name = record.name;
            return <Paragraph ellipsis={{ rows: 2, tooltip: name }}>{name || '-'}</Paragraph>;
          },
        },
        {
          title: 'Components',
          dataIndex: 'componentSkuList',
          className: style.list,
          width: 210,
          render: (models: IVskuComponentSku[], row, index) => {
            if (!row?.componentSkuList?.length) {
              return type === 'edit' ? (
                <Button
                  onClick={() => addKitComponents(index)}
                  type={'link'}
                  className={style.addKitComponents}
                >
                  Add Kit Components
                </Button>
              ) : null;
            }
            return models?.map(model => {
              let name = model.name;
              if (model.modelName) {
                name += `(${model.modelName})`;
              }
              return (
                <div className={style.item}>
                  <ProductInfo
                    header={model.mainSku ? <Tag color='orange'>Main Component</Tag> : null}
                    product={{
                      name: name,
                      sku: model.modelSku,
                      image: model.imageId,
                    }}
                  />
                </div>
              );
            });
          },
        },
        {
          title: 'Qty',
          dataIndex: 'componentSkuList',
          className: style.list,
          width: 60,
          render: (models: IVskuComponentSku[]) => {
            return models?.map(model => {
              return <div className={style.item}>{model.quantity}</div>;
            });
          },
        },
        {
          title: 'Image',
          dataIndex: 'imageId',
          width: 95,
          render: (image: string, _) => {
            return image ? <Thumbnail width={100} source={image} /> : '-';
          },
        },
        {
          title: 'Price',
          dataIndex: 'price',
          width: 160,
          render: (price: number) => {
            return formatCurrency(price, { hasCurrencySymbol: true })
          },
        },
        {
          title: 'Sku ID',
          dataIndex: 'sku',
          width: 100,
          render: (sku: number, _) => {
            return (
              sku || '-'
            )
          },

        },
        isEdit && {
          title: 'Action',
          width: 80,
          fixed: 'right',
          className: style.action,
          render: (_, row: IVirtualSKUModelInfo, index) => {
            return (
              <>
                <Button
                  disabled={!row.componentSkuList?.length}
                  onClick={() => editKitComponents(index, row.componentSkuList!)}
                  type='link'
                  icon={<EditOutlined />}
                />
              </>
            );
          },

        },
      ] as ColumnsType<IVirtualSKUModelInfo>
    ).filter(Boolean);

    return (
      <>
        <Form
          ref={ref}
          className={style.form}
          form={form}
          labelCol={{ span: 0 }}
          wrapperCol={{ span: 24 }}
          initialValues={{
            kitComponents,
          }}
          onValuesChange={(_, allValues) => {
            onChange?.(allValues.kitComponents);
          }}
        >
          <Form.Item name='kitComponents' valuePropName='dataSource' rules={[
            {
              validator: (_, value: IVirtualSKUModelInfo[]) => {
                const hasEmptyKitComponent = value?.find(item => !item.componentSkuList?.length);
                if (hasEmptyKitComponent) {
                  return Promise.reject(new Error('Please fill kit components'));
                }
                let hasMainSku = false;

                value.forEach(kit => {
                  if (kit.componentSkuList?.find(sku => sku.mainSku)) {
                    hasMainSku = true;
                  }
                });
                if (!hasMainSku) {
                  return Promise.reject(new Error('Please select main component.'));
                }
                return Promise.resolve();
              }
            }
          ]}>
            <Table bordered pagination={false} className={style.table} columns={columns}></Table>
          </Form.Item>
        </Form>
        {visibleComponents ? (
          <SelectComponents
            itemId={itemId}
            mainSkuId={mainSkuId}
            visible={visibleComponents}
            currentComponentList={currentComponentList?.currentComponentList || []}
            step={step}
            shopId={shopId}
            onOk={onConfirm}
            onCancel={() => setVisibleComponents(false)}
          />
        ) : null}
      </>
    );
  }
);

export default KitComponentTable;
