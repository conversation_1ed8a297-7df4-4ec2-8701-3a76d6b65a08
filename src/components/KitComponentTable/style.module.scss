.form {
  width: 100%;
  :global {
    .ant-form-item {
      margin-bottom: 0;
    }
    .ant-table-cell {
      padding: 8px;
    }
    .ant-upload.ant-upload-select-picture-card, .ant-upload-list-picture-card-container {
      width: 100px;
      height: 100px;
    }
    .ant-input-number-group-addon,
    .ant-input-group-addon {
      padding: 0 4px;
    }
  }
}
.mainComponent {
  color: #ff831d;
  background-color: #feece1;
  border: none;
}

.table {
  .list {
    padding: 0 !important;
  }
  th + .list {
    padding: 16px !important;
  }
  .item {
    padding: 8px;
    min-height: 130px;
    display: flex;
    align-items: center;
    & + .item {
      border-top: 1px solid #f0f0f0;
    }
  }
}

.addKitComponents {
  margin: 16px !important;
}

.addVariation {
  margin-top: 24px;
}
.uploadSelectRemove {
  :global {
    .ant-upload-select {
      display: none;
    }
  }
}

.warn {
  color: #ffa30d;
}

.action {
  button {
    width: 20px;
  }
  button + button {
    margin-left: 4px;
  }
}
