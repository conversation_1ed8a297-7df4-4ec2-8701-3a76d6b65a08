import { Typography } from 'antd';
import React from 'react';

import Thumbnail from 'src/components/Thumbnail';
import style from './style.module.scss';

const { Paragraph } = Typography;
const ProductInfo = ({ product, children, header, productWidth }: {
  children?: React.ReactNode;
  header?: React.ReactNode;
  productWidth?: number;
  product: {
    image?: string;
    name?: string;
    category?: string;
    sku?: string;
    variations?: {
      image?: string;
      name?: string;
    }[];
  },
}) => {
  return <>
    <div className={ style.container }>
      <Thumbnail source={ product.image! } width={ productWidth || 56 }/>
      <div className={style.product}>
        {header}
        <Paragraph ellipsis={ { tooltip: product.name, rows: 2 } }>{ product.name || '-' }</Paragraph>
        { product.category && <div className={ style.category }>Category: { product.category }</div> }
        { product.sku && <div className={ style.sku }>Sku: { product.sku }</div> }
      </div>
    </div>
    { children }

    {product.variations?.length ? <div className={ style.variations }>
      { product.variations?.map(variation => {
        return <div className={ style.variation }>
          <Thumbnail source={ variation.image! } width={ 36 }/>
          <div>
            <Paragraph ellipsis={ { tooltip: variation.name, rows: 2 } }>{ variation.name || '-' }</Paragraph>
          </div>
        </div>
      }) }
    </div> : null }
  </>
}
export default ProductInfo;
