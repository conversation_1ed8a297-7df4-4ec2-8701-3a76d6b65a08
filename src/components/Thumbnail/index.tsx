import { getCountry } from '@classification/admin-solution';
import { imageHosts } from 'admin-upload-common';
import { Image } from 'antd';
import React from 'react';

import DefaultProduct from 'src/assets/images/default-product.png'

export function getImageTnLink(imgHash: string, country = getCountry().toUpperCase()) {
  if (country in imageHosts)
    return `${imageHosts[country as keyof typeof imageHosts]}/file/${imgHash}_tn`;
  else {
    return `${imageHosts.SG}/file/${imgHash}_tn`;
  }
}
const Thumbnail = ({
  width = 48, source, lazyLoad = true, preview = false, className
}:
{
  width?: number;
  source: string;
  lazyLoad?: boolean;
  preview?: boolean;
  className?: string;
}) => {
  return <Image className={className} preview={preview} width={ width } src={ source ? getImageTnLink(source) : DefaultProduct } loading={ lazyLoad ? 'lazy' : undefined }></Image>;
}
export default Thumbnail;
