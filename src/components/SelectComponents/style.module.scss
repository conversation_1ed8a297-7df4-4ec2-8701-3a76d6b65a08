.search {
  padding: 0;
  margin-top: 20px;
  :global {
    .ant-input, .ant-select-selector {
      width: 300px;
    }
  }
}

.selectTip {
  margin-top: 12px;
}

.expand {
  display: none;
  position: relative;
  float: left;
  width: 17px;
  height: 17px;
  padding: 0;
  line-height: 17px;
  transition: all .3s;
  margin-top: 10px;
  margin-right: 10px;
}
.footer {
  display: flex;
  justify-content: space-between;
}

.disabledNext {
  margin-left: 16px;
}
.table {
  :global {
    .ant-form-item {
      margin-bottom: 0;
    }
    .ant-table-tbody>tr>td {
      vertical-align: top;
    }
  }
}
.productList {
  .expandIcon {
    color: #999;
    display: flex;
    align-items: center;
    margin-left: -10px;
    margin-right: 10px;
  }
  :global {
    .ant-table-selection-column {
      padding: 0;
    }
    .ant-table-row-level-1 {
      .ant-checkbox {
        position: relative;
        z-index: 9999;
        left: 65px;
      }
    }
    .ant-table-row-level-0 {
      .ant-table-cell-with-append {
        &::before {
          position: absolute;
          bottom: -4px;
          left: 11px;
          z-index: 1;
          width: 8px;
          height: 8px;
          background-color: #fafafa;
          border: 1px solid;
          border-right: 0;
          border-bottom: 0;
          border-color: #f0f0f0;
          content: "";
          transform: rotate(45deg);
        }
        &::after {
          position: absolute;
          top: 0;
          right: 0;
          left: 0;
          pointer-events: none;
          content: "";
        }
      }
    }
    .ant-table-row-level-1 {
      .ant-table-cell-with-append {
        padding-left: 56px;
      }
    }
  }

}
.inline {
  display: flex;
  gap: 8px;
}
.modal {
  height: 500px;
  :global {
    .ant-modal-body {
      height: 500px;
      overflow: auto;
    }
  }
}

.category {
  width: 150px;
}
