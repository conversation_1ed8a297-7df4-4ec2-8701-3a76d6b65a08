import {
  DownOutlined,
  MinusOutlined,
  PlusOutlined,
} from '@ant-design/icons';
import { getCountry } from '@classification/admin-solution';
import { useTableHooks } from '@classification/table';
import { useMount } from 'ahooks';
import { Button, Form, InputNumber, Modal, Popover, Radio, Steps, Table } from 'antd';
import type { FormInstance } from 'antd/lib/form';
import type { ColumnsType, TableProps } from 'antd/lib/table';
import type {
  ForwardedRef,
} from 'react';
import React, { forwardRef, useCallback, useEffect, useMemo, useRef, useState } from 'react';

import { searchProductForKitComponent } from 'src/api/uploadBffAdmin';
import type { uploadBffAdmin } from 'src/api/uploadBffAdmin/uploadBffAdmin';
import InputCategorySelector from 'src/components/InputCategorySelector';
import ProductInfo from 'src/components/ProductInfo';
import {
  computedDisplayPrice,
  formatStockNumber,
  transformPriceRange,
} from 'src/pages/AddEditKitMigration/utils';
import CategoryDataControl from 'src/utils/category-datacontrol';
import { isDefaultModel } from 'src/utils/kit-migration';
import style from './style.module.scss';

export enum STEP {
  ONE = 0,
  TWO = 1,
}

interface IModel extends uploadBffAdmin.IModel {
  item: IProduct;
  key: string;
  isDefaultModel?: boolean;
}

interface IProduct extends uploadBffAdmin.IProduct {
  children: IModel[];
  key: string;
  modelList: uploadBffAdmin.IModel[];
  isDefaultModel?: boolean;
}

const getModelPrice = (model: IModel) => {
  if (typeof model?.priceDetail?.originPrice === 'string') {
    return transformPriceRange(model.priceDetail.originPrice);
  }
  return '-';
};

const getItemPrice = (product: IProduct) => {
  return computedDisplayPrice(product?.aggregatedPrice);
};

const getItemStock = (product: IProduct) => {
  if (typeof product?.aggregatedStock?.totalSellerStock === 'number') {
    return formatStockNumber(product.aggregatedStock.totalSellerStock);
  }
  return '-';
};

const getModelStock = (model: IModel) => {
  if (typeof model?.stockDetail?.totalSellerStock === 'number') {
    return formatStockNumber(model.stockDetail.totalSellerStock);
  }
  return '-';
};

const ProductTable = ({
  selected,
  shopId,
  itemId,
  setSelected,
}: {
  selected: any[];
  shopId?: number;
  itemId?: number;
  setSelected: (data: any[]) => void;
}) => {
  const dataControlRef = useRef(new CategoryDataControl());

  const rowSelection: TableProps<any>['rowSelection'] = {
    columnWidth: 10,
    hideSelectAll: true,
    selectedRowKeys: selected.map(item => item.key),
    type: 'checkbox',
    getCheckboxProps: record => {
      const product = record.item || record;
      return {
        disabled: product?.id === itemId || !product?.enableKitMigration,
      }
    },
    onSelect: (record: any, isSelected: boolean, selectedRows: any[]) => {
      if (isSelected) {
        const list = [...selected];
        selectedRows.forEach(item => {
          if (item.key === record.key || item?.item?.key === record?.key) {
            list.push(item);
          }
        });
        setSelected([...list]);
        return;
      }

      const list = selected.filter(item => {
        // 将当前元素取消选中
        if (item.key === record.key) {
          return false;
        }
        // 如果取消的是Model，将Item选中状态也取消
        if (record?.item && item.key === record?.item?.key) {
          return false;
        }
        const itemKey = item.key.split('-')[0];
        // 取消选择的是Item, 则将所有model子元素取消选中
        if (itemKey === record.key) {
          return false;
        }
        return true;
      });
      setSelected([...list]);
    },
    checkStrictly: false,
  };

  const { table, expandAll } = useTableHooks({
    searchProps: {
      className: style.search,
      form: {
        items: [
          {
            inputType: 'Input',
            name: 'keyword',
            inputCompProps: {
              placeholder: 'Search Product Name, Parent SKU, SKU, Item Id',
            },
          },
          {
            className: style.category,
            component: <InputCategorySelector maxLevel={3} dataControl={dataControlRef.current} />,
            name: 'categoryId',
          },
        ],
      },
    },
    table: {
      rowKey: 'key',
      rowSelection,
      dataFilterConfig: {
        nextOffsetFieldName: 'cursor',
        offsetFieldName: 'cursor',
        cursorOffset: true,
        limitFieldName: 'pageSize',
      },
      pagination: {
        defaultPageSize: 12,
        showSizeChanger: true,
        pageSizeOptions: [12, 24, 48],
        showQuickJumper: false,
      },
      getData: async (params: Record<string, any>) => {
        const { categoryId = [], ...rest } = params;
        const id = categoryId[categoryId?.length - 1];
        const query = {
          ...rest,
          categoryId: id,
          shopId,
          region: getCountry(),
        };
        const res = await searchProductForKitComponent(query);
        const data = res?.data?.products?.map(item => {
          const { modelList,id: itemId, ...rest } = item;

          const models: uploadBffAdmin.IModel[] = [];
          const isDefault = isDefaultModel(item);
          const key = isDefault ? `${itemId}-${modelList?.[0].id}` : `${itemId}`;

          if (!isDefault) {
            modelList?.forEach(model => {
              const modelId = model.id;
              const modelInfo = {
                item: {
                  ...rest,
                  id: itemId,
                  key,
                },
                ...model,
                id: modelId,
                key: `${itemId}-${modelId}`,
                isDefaultModel: false,
              };
              models?.push(modelInfo);
            });
          }
          setTimeout(() => {
            expandAll();
          }, 5);
          return {
            ...rest,
            id: itemId,
            key,
            children: models,
            modelList,
            isDefaultModel: isDefault,
          };
        }) || [];

        return {
          data,
          cursor: res.data?.pageInfo?.cursor!,
        };
      },
      expandable: {
        childrenColumnName: 'children',
        rowExpandable: record => {
          return record.children.length > 0;
        },
        expandIcon: ({ onExpand, record, expanded }) => {
          return record.children?.length ? (
            !expanded ? <PlusOutlined className={style.expand} onClick={e => onExpand(record, e)} /> : <MinusOutlined className={style.expand} onClick={e => onExpand(record, e)} />
          ) : null;
        },
      },
      columns: [
        {
          title: 'Kit Components',
          width: 100,
          render: (text, record) => {
            return <div className={style.inline}>
              { (!record.item && !record.isDefaultModel) ? <DownOutlined className={style.expandIcon}/> : null }
              <ProductInfo
                productWidth={32}
                product={{
                  image: record.image || record.coverImage,
                  name: record.name,
                }}
              />
            </div>;
          },
        },
        {
          title: 'Price',
          width: 100,
          render: (text, record) => {
            return record.item ? getModelPrice(record) : getItemPrice(record);
          },
        },
        {
          title: 'Stock',
          width: 100,
          render: (text, record) => {
            return record.item ? getModelStock(record) : getItemStock(record);
          },
        },
        {
          title: 'SKU ID',
          width: 100,
          render: (text, record) => {
            return (record.item ? record.sku : record.parentSku) || '-';
          },
        },
      ],
    },
  });
  return <div className={style.productList}>{table}</div>;
};

const InputComponentsQuantityTable = forwardRef(
  (
    {
      data,
      onChange,
    }: {
      data: uploadBffAdmin.IVskuComponentSku[];
      onChange: (val: uploadBffAdmin.IVskuComponentSku[]) => void;
    },
    ref: ForwardedRef<FormInstance>
  ) => {
    const [form] = Form.useForm();
    const columns: ColumnsType<uploadBffAdmin.IVskuComponentSku> = [
      {
        title: 'Kit Components',
        render: (text, record: any) => {
          return (
            <ProductInfo
              productWidth={32}
              product={{
                image: record.imageId,
                name: record.name || '-',
                sku: !record.isDefault ? record.modelName || '-' : undefined,
              }}
            />
          );
        },
      },
      {
        title: 'Main Component',
        render: (_, record, index) => {
          const onChange = () => {
            form.setFieldsValue({
              kitComponents: form.getFieldValue('kitComponents').map((item: any) => {
                return {
                  ...item,
                  mainSku: record?.mpskuModelId === item.mpskuModelId,
                };
              }),
            });
          };
          const Check = ({ value }: { value?: boolean }) => {
            return (
              <>
                <Radio checked={value} onChange={onChange} />
              </>
            );
          };
          return (
            <Form.Item name={['kitComponents', index, 'mainSku']}>
              <Check />
            </Form.Item>
          );
        },
      },
      {
        title: 'Qty per Kit',
        width: 200,
        render: (_, _1, index) => {
          return (
            <Form.Item
              rules={[
                {
                  validator: (_, value: number) => {
                    const list = form.getFieldValue(['kitComponents']);
                    if (list?.length === 1 && value <= 1) {
                      return Promise.reject(new Error('Quantity needs to be more than 1'));
                    }
                    if (!value || value < 1) {
                      return Promise.reject(new Error('Please input a positive integer'));
                    }
                    return Promise.resolve();
                  },
                },
              ]}
              name={['kitComponents', index, 'quantity']}
            >
              <InputNumber precision={0} min={1} />
            </Form.Item>
          );
        },
      },
      {
        title: 'Action',
        render: (_, record) => {
          const onDelete = () => {
            const data = form
              .getFieldValue('kitComponents')
              .filter(
                (item: uploadBffAdmin.IVskuComponentSku) =>
                  item.mpskuModelId !== record?.mpskuModelId
              );
            form.setFieldsValue({
              kitComponents: data,
            });
            onChange(data);
          };
          return (
            <Button type={'link'} onClick={onDelete}>
              Delete
            </Button>
          );
        },
      },
    ];
    const Components = ({ value }: { value?: uploadBffAdmin.IVskuComponentSku[] }) => {
      return (
        <Table
          className={style.table}
          pagination={false}
          dataSource={value}
          rowKey={'mpskuModelId'}
          columns={columns}
        />
      );
    };
    return (
      <Form
        ref={ref}
        form={form}
        labelCol={{ span: 0 }}
        wrapperCol={{ span: 24 }}
        initialValues={{
          kitComponents: data,
        }}
      >
        <Form.Item name='kitComponents'>
          <Components />
        </Form.Item>
      </Form>
    );
  }
);

const SelectComponents = ({
  step,
  visible = true,
  mainSkuId,
  currentComponentList = [],
  onOk,
  onCancel,
  constraints = {
    componentSkuNumForEachModelMin: 1,
    componentSkuNumForEachModelMax: 30,
  },
  shopId,
  itemId,
}: {
  step: STEP;
  visible: boolean;
  mainSkuId?: number;
  itemId?: number;
  shopId?: number;
  currentComponentList: uploadBffAdmin.IVskuComponentSku[];
  constraints?: {
    componentSkuNumForEachModelMin: number;
    componentSkuNumForEachModelMax: number;
  };
  onOk: (currentComponentList: uploadBffAdmin.IVskuComponentSku[]) => void;
  onCancel: () => void;
}) => {
  const [currentStep, setCurrentStep] = useState(step | 0);
  const [selected, setSelected] = useState<any[]>([]);
  const [selectedItems, setSelectedItems] = useState<any[]>([]);
  const onPre = useCallback(() => {
    const data = tableRef.current?.getFieldValue?.('kitComponents');
    setSelectedItems(data);
    setCurrentStep(currentStep - 1);
  }, [currentStep]);
  const onNext = useCallback(() => {
    setCurrentStep(currentStep + 1);
  }, [currentStep]);

  const tableRef = useRef<FormInstance>(null);
  const onChange = (val: any[]) => {
    if (val?.length !== selectedItems?.length) {
      const newValueMap = val.reduce((pre, item) => {
        const key = `${item.mpskuItemId}-${item.mpskuModelId}`;
        pre[key] = item;
        return pre;
      }, {});

      const list = selected.filter(item => {
        const key = `${item.key}`;
        if (!newValueMap[key]) {
          return false;
        }
        return true;
      });
      setSelected(list);
    }
    setSelectedItems(val);
  };
  const computedSelectedItems = (selected: any[]) => {
    const valueMap = (selectedItems || []).reduce((pre, item) => {
      const key = `${item.mpskuItemId}-${item.mpskuModelId}`;
      pre[key] = item;
      return pre;
    }, {});
    const list = selected?.reduce((pre: any[], item) => {
      if (item.item || item.isDefaultModel || valueMap[item.key]) {
        const product = item.isDefaultModel ? item : item.item;
        const model = item.isDefaultModel ? item.modelList?.[0] : item;
        const data = valueMap[item.key]
          ? valueMap[item.key]
          : {
              mpskuItemId: product.id,
              mpskuModelId: model.id,
              quantity: undefined,
              mainSku: undefined,
              name: product.name,
              modelName: model.name,
              modelSku: model.sku,
              imageId: model.image || product.coverImage,
              isDefault: item.isDefaultModel,
              inputPrice: model.priceDetail?.originPrice,
            };
        if (data.mpskuModelId === mainSkuId) {
          data.mainSku = true;
        }
        pre.push(data);
      }
      return pre;
    }, []);
    if (list.length && !mainSkuId) {
      list[0].mainSku = true;
    }
    setSelectedItems(list);
  };
  useEffect(() => {
    computedSelectedItems(selected);
  }, [selected]);

  useMount(() => {
    const list = currentComponentList.map(item => {
      return {
        key: `${item.mpskuItemId}-${item.mpskuModelId}`,
      };
    });
    setSelected(list);
    setSelectedItems(currentComponentList);
  });
  const onConfirm = async () => {
    try {
      await tableRef.current?.validateFields();
      const data = tableRef.current?.getFieldValue?.('kitComponents');
      onOk(data);
      onCancel();
    } catch (e) {
      console.log(e);
    }
  };

  const disableTips = useMemo(() => {
    const len = !!selectedItems && selectedItems?.length;
    if (
      len < constraints.componentSkuNumForEachModelMin ||
      len > constraints.componentSkuNumForEachModelMax
    ) {
      return `Please select ${constraints.componentSkuNumForEachModelMin} to ${constraints.componentSkuNumForEachModelMax} Kit Components`;
    }
  }, [selectedItems]);
  return (
    <Modal
      title='Selected Components'
      open={visible}
      width={1000}
      className={style.modal}
      onCancel={onCancel}
      footer={
        currentStep === STEP.ONE ? (
          <div className={style.footer}>
            <div>{selectedItems?.length} Selected</div>
            <div>
              <Button onClick={onCancel}>Cancel</Button>
              {disableTips ? (
                <Popover className={style.disabledNext} title={disableTips}>
                  <Button disabled={!!disableTips} type='primary' onClick={onNext}>
                    Next
                  </Button>
                </Popover>
              ) : (
                <Button disabled={!!disableTips} type='primary' onClick={onNext}>
                  Next
                </Button>
              )}
            </div>
          </div>
        ) : (
          <div>
            <Button onClick={onPre}>Previous</Button>
            {disableTips ? (
              <Popover className={style.disabledNext} title={disableTips}>
                <Button disabled={selectedItems?.length < 1} type='primary' onClick={onConfirm}>
                  Confirm
                </Button>
              </Popover>
            ) : (
              <Button disabled={selectedItems?.length < 1} type='primary' onClick={onConfirm}>
                Confirm
              </Button>
            )}
          </div>
        )
      }
    >
      <Steps
        current={currentStep}
        items={[
          {
            title: 'Selected Components',
          },
          {
            title: 'Input Components Quantity',
          },
        ]}
      />
      {currentStep === STEP.ONE ? (
        <>
          <div className={style.selectTip}>
            Please select from {constraints.componentSkuNumForEachModelMin} to{' '}
            {constraints.componentSkuNumForEachModelMax} Kit Components
          </div>
          <ProductTable selected={selected} shopId={shopId} itemId={itemId} setSelected={setSelected} />
        </>
      ) : (
        <>
          {selectedItems?.length ? (
            <InputComponentsQuantityTable ref={tableRef} data={selectedItems} onChange={onChange} />
          ) : null}
        </>
      )}
    </Modal>
  );
};

export default SelectComponents;
