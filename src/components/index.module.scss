.pageContainer {
  background: #f0f2f5;
  height: calc(100vh - 88px);
  overflow-x: hidden;
  overflow-y: scroll;
  width: 100%;
}

.searchWrapper {
  @extend .basePannel;
  box-shadow: 0 3px 6px #ccc;
  display: flex;
  margin-bottom: 16px;
  position: sticky;
  top: 0;
  z-index: 99;
}

.basePannel {
  background: #fff;
  padding: 16px;
}

.container {
  @extend .basePannel;
  padding: 0 16px;
  width: 100%;
}

.header {
  align-items: center;
  background: #fff;
  display: flex;
  padding: 20px 0;
}

.title {
  color: rgba(0, 0, 0, 0.87);
  flex: 1;
  font-size: 18px;
  font-weight: 500;
  line-height: 22px;
}

.actions {
  button + button {
    margin-left: 8px;
  }

  button > i {
    font-size: 16px;
  }
}

.customRow {
  :global {
    .ant-btn > .anticon {
      font-size: 16px;
    }

    .ant-btn-circle {
      background: none;
      border: none;
      color: rgba(0, 0, 0, 0.15);
    }
  }

  &:hover {
    :global {
      .ant-btn > .anticon {
        font-size: 16px;
      }

      .ant-btn-circle {
        background: none;
        border: none;
        color: inherit;

        &:hover {
          background: #2673dd;
          color: #fff;
        }
      }
    }
  }
}
