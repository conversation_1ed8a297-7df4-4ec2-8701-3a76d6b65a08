import { ReactNode } from 'react';

// Card size variants
export type CardSize = 'small' | 'default' | 'large';

// Card theme variants
export type CardTheme = 'default' | 'primary' | 'success' | 'warning' | 'danger' | 'info';

// Card border styles
export type CardBorderStyle = 'solid' | 'dashed' | 'dotted' | 'none';

// Card shadow levels
export type CardShadow = 'none' | 'small' | 'medium' | 'large';

// Header configuration
export interface CardHeaderConfig {
  title?: ReactNode;
  extra?: ReactNode;
  icon?: ReactNode;
  backgroundColor?: string;
  textColor?: string;
  borderBottom?: boolean;
  padding?: string | number;
}

// Content configuration
export interface CardContentConfig {
  padding?: string | number;
  backgroundColor?: string;
  minHeight?: string | number;
}

// Border configuration
export interface CardBorderConfig {
  style?: CardBorderStyle;
  width?: string | number;
  color?: string;
  radius?: string | number;
}

// Hover effects configuration
export interface CardHoverConfig {
  enabled?: boolean;
  shadow?: CardShadow;
  transform?: string;
  borderColor?: string;
  backgroundColor?: string;
}

// Main card configuration interface
export interface CardConfig {
  // Basic properties
  size?: CardSize;
  theme?: CardTheme;
  shadow?: CardShadow;
  
  // Layout
  header?: CardHeaderConfig;
  content?: CardContentConfig;
  border?: CardBorderConfig;
  
  // Behavior
  hoverable?: boolean;
  hoverConfig?: CardHoverConfig;
  collapsible?: boolean;
  defaultCollapsed?: boolean;
  
  // Styling
  className?: string;
  style?: React.CSSProperties;
  
  // Accessibility
  role?: string;
  'aria-label'?: string;
}

// Props for the ConfigurableCard component
export interface ConfigurableCardProps extends CardConfig {
  children: ReactNode;
  onCollapse?: (collapsed: boolean) => void;
  onClick?: (event: React.MouseEvent<HTMLDivElement>) => void;
}

// Predefined theme configurations
export interface ThemeConfig {
  backgroundColor: string;
  borderColor: string;
  headerBackgroundColor: string;
  headerTextColor: string;
  textColor: string;
  shadow: CardShadow;
}

// Default theme configurations
export const defaultThemes: Record<CardTheme, ThemeConfig> = {
  default: {
    backgroundColor: '#ffffff',
    borderColor: '#d9d9d9',
    headerBackgroundColor: '#fafafa',
    headerTextColor: '#262626',
    textColor: '#262626',
    shadow: 'small',
  },
  primary: {
    backgroundColor: '#f0f8ff',
    borderColor: '#1890ff',
    headerBackgroundColor: '#e6f7ff',
    headerTextColor: '#1890ff',
    textColor: '#262626',
    shadow: 'medium',
  },
  success: {
    backgroundColor: '#f6ffed',
    borderColor: '#52c41a',
    headerBackgroundColor: '#f0f9e8',
    headerTextColor: '#52c41a',
    textColor: '#262626',
    shadow: 'small',
  },
  warning: {
    backgroundColor: '#fffbe6',
    borderColor: '#faad14',
    headerBackgroundColor: '#fef9e6',
    headerTextColor: '#faad14',
    textColor: '#262626',
    shadow: 'small',
  },
  danger: {
    backgroundColor: '#fff2f0',
    borderColor: '#ff4d4f',
    headerBackgroundColor: '#ffece8',
    headerTextColor: '#ff4d4f',
    textColor: '#262626',
    shadow: 'small',
  },
  info: {
    backgroundColor: '#f0f8ff',
    borderColor: '#1890ff',
    headerBackgroundColor: '#e6f7ff',
    headerTextColor: '#1890ff',
    textColor: '#262626',
    shadow: 'small',
  },
};
