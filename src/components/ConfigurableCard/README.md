# ConfigurableCard Component

A highly configurable card component that provides extensive customization options for appearance, behavior, and layout.

## Features

- **Multiple Themes**: Default, Primary, Success, Warning, Danger, Info
- **Flexible Sizing**: Small, Default, Large
- **Shadow Levels**: None, Small, Medium, Large
- **Border Customization**: Style, width, color, radius
- **Collapsible Content**: Built-in expand/collapse functionality
- **Hover Effects**: Configurable hover animations and styles
- **Header Configuration**: Title, icon, extra content, styling
- **Content Customization**: Padding, background, minimum height
- **Predefined Configurations**: Ready-to-use card types
- **Dark Theme Support**: Automatic dark mode adaptation
- **Responsive Design**: Mobile-friendly layouts

## Basic Usage

```tsx
import { ConfigurableCard } from 'src/components/ConfigurableCard';

// Simple card with title
<ConfigurableCard header={{ title: "My Card" }}>
  <p>Card content goes here</p>
</ConfigurableCard>
```

## Using Predefined Configurations

```tsx
import { ConfigurableCard, cardConfigs } from 'src/components/ConfigurableCard';

// Section card (most common)
<ConfigurableCard 
  {...cardConfigs.section}
  header={{ title: "Section Title" }}
>
  Content here
</ConfigurableCard>

// Success notification
<ConfigurableCard 
  {...cardConfigs.success}
  header={{ title: "Success!" }}
>
  Operation completed successfully
</ConfigurableCard>

// Collapsible advanced settings
<ConfigurableCard
  {...cardConfigs.advancedSettings}
  header={{ title: "Advanced Settings" }}
  onCollapse={(collapsed) => console.log('Collapsed:', collapsed)}
>
  Advanced configuration options
</ConfigurableCard>
```

## Custom Configuration

```tsx
import { ConfigurableCard, mergeCardConfig, cardConfigs } from 'src/components/ConfigurableCard';

// Merge with existing configuration
<ConfigurableCard 
  {...mergeCardConfig(cardConfigs.section, {
    theme: 'primary',
    hoverable: true,
    border: { radius: 16 }
  })}
  header={{ title: "Custom Card" }}
>
  Custom styled content
</ConfigurableCard>

// Fully custom configuration
<ConfigurableCard
  size="large"
  theme="warning"
  shadow="medium"
  border={{
    style: 'dashed',
    width: 2,
    color: '#faad14',
    radius: 12
  }}
  header={{
    title: "Warning",
    icon: <ExclamationCircleOutlined />,
    backgroundColor: '#fef9e6'
  }}
  hoverable={true}
>
  Warning message content
</ConfigurableCard>
```

## Available Configurations

### Predefined Card Types

- `cardConfigs.section` - Standard section card
- `cardConfigs.advancedSettings` - Collapsible settings card
- `cardConfigs.rule` - Individual rule/item card
- `cardConfigs.success` - Success notification card
- `cardConfigs.warning` - Warning notification card
- `cardConfigs.error` - Error notification card
- `cardConfigs.info` - Information card
- `cardConfigs.compact` - Space-efficient card
- `cardConfigs.elevated` - Card with hover effects
- `cardConfigs.minimal` - Borderless, shadowless card
- `cardConfigs.headerOnly` - Card with no content padding

### Themes

- `default` - Standard white/gray theme
- `primary` - Blue accent theme
- `success` - Green success theme
- `warning` - Orange warning theme
- `danger` - Red error theme
- `info` - Blue information theme

### Sizes

- `small` - Compact padding and font sizes
- `default` - Standard sizing
- `large` - Generous padding and larger fonts

### Shadow Levels

- `none` - No shadow
- `small` - Subtle shadow
- `medium` - Moderate shadow
- `large` - Prominent shadow

## Props Interface

```tsx
interface ConfigurableCardProps {
  children: ReactNode;
  
  // Basic properties
  size?: 'small' | 'default' | 'large';
  theme?: 'default' | 'primary' | 'success' | 'warning' | 'danger' | 'info';
  shadow?: 'none' | 'small' | 'medium' | 'large';
  
  // Header configuration
  header?: {
    title?: ReactNode;
    extra?: ReactNode;
    icon?: ReactNode;
    backgroundColor?: string;
    textColor?: string;
    borderBottom?: boolean;
    padding?: string | number;
  };
  
  // Content configuration
  content?: {
    padding?: string | number;
    backgroundColor?: string;
    minHeight?: string | number;
  };
  
  // Border configuration
  border?: {
    style?: 'solid' | 'dashed' | 'dotted' | 'none';
    width?: string | number;
    color?: string;
    radius?: string | number;
  };
  
  // Behavior
  hoverable?: boolean;
  hoverConfig?: {
    enabled?: boolean;
    shadow?: 'none' | 'small' | 'medium' | 'large';
    transform?: string;
    borderColor?: string;
    backgroundColor?: string;
  };
  collapsible?: boolean;
  defaultCollapsed?: boolean;
  
  // Events
  onCollapse?: (collapsed: boolean) => void;
  onClick?: (event: React.MouseEvent<HTMLDivElement>) => void;
  
  // Styling
  className?: string;
  style?: React.CSSProperties;
  
  // Accessibility
  role?: string;
  'aria-label'?: string;
}
```

## Migration from Ant Design Card

Replace existing Ant Design Card usage:

```tsx
// Before
<Card title="My Title" className="my-class">
  Content
</Card>

// After
<ConfigurableCard 
  {...cardConfigs.section}
  header={{ title: "My Title" }}
  className="my-class"
>
  Content
</ConfigurableCard>
```

## Best Practices

1. **Use predefined configurations** when possible for consistency
2. **Merge configurations** rather than creating from scratch
3. **Test with dark mode** to ensure proper contrast
4. **Consider mobile responsiveness** when customizing padding/sizing
5. **Use semantic themes** (success for positive actions, warning for cautions, etc.)
6. **Provide meaningful aria-labels** for accessibility

## Examples

See `examples.tsx` for comprehensive usage examples demonstrating all features and configurations.
