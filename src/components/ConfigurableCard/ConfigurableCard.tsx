import { DownOutlined, RightOutlined } from '@ant-design/icons';
import cx from 'classnames';
import React, { useState, useMemo, CSSProperties } from 'react';

import {
  ConfigurableCardProps,
  CardSize,
  CardTheme,
  CardShadow,
  CardBorderStyle,
  defaultThemes,
} from './types';
import styles from './style.module.scss';

const ConfigurableCard: React.FC<ConfigurableCardProps> = ({
  children,
  size = 'default',
  theme = 'default',
  shadow = 'small',
  header,
  content,
  border,
  hoverable = false,
  hoverConfig,
  collapsible = false,
  defaultCollapsed = false,
  className,
  style,
  onCollapse,
  onClick,
  role,
  'aria-label': ariaLabel,
}) => {
  const [collapsed, setCollapsed] = useState(defaultCollapsed);

  // Generate CSS classes
  const cardClasses = useMemo(() => {
    const classes = [styles.card];
    
    // Size classes
    classes.push(styles[size]);
    
    // Shadow classes
    const shadowClass = `shadow${shadow.charAt(0).toUpperCase() + shadow.slice(1)}`;
    classes.push(styles[shadowClass]);
    
    // Theme classes
    const themeClass = `theme${theme.charAt(0).toUpperCase() + theme.slice(1)}`;
    classes.push(styles[themeClass]);
    
    // Border style classes
    if (border?.style) {
      const borderClass = `border${border.style.charAt(0).toUpperCase() + border.style.slice(1)}`;
      classes.push(styles[borderClass]);
    }
    
    // Behavior classes
    if (hoverable) {
      classes.push(styles.hoverable);
    }
    
    if (collapsible) {
      classes.push(styles.collapsible);
    }
    
    if (collapsed) {
      classes.push(styles.collapsed);
    }
    
    // Custom className
    if (className) {
      classes.push(className);
    }
    
    return cx(classes);
  }, [size, shadow, theme, border?.style, hoverable, collapsible, collapsed, className]);

  // Generate inline styles
  const cardStyle = useMemo((): CSSProperties => {
    const computedStyle: CSSProperties = { ...style };
    
    // Apply theme colors if not using predefined themes
    const themeConfig = defaultThemes[theme];
    if (themeConfig) {
      computedStyle.backgroundColor = themeConfig.backgroundColor;
      computedStyle.borderColor = themeConfig.borderColor;
    }
    
    // Apply border configuration
    if (border) {
      if (border.width !== undefined) {
        computedStyle.borderWidth = typeof border.width === 'number' ? `${border.width}px` : border.width;
      }
      if (border.color) {
        computedStyle.borderColor = border.color;
      }
      if (border.radius !== undefined) {
        computedStyle.borderRadius = typeof border.radius === 'number' ? `${border.radius}px` : border.radius;
      }
    }
    
    // Apply content background if specified
    if (content?.backgroundColor) {
      computedStyle.backgroundColor = content.backgroundColor;
    }
    
    return computedStyle;
  }, [style, theme, border, content?.backgroundColor]);

  // Generate header styles
  const headerStyle = useMemo((): CSSProperties => {
    const computedStyle: CSSProperties = {};
    
    if (header?.backgroundColor) {
      computedStyle.backgroundColor = header.backgroundColor;
    }
    
    if (header?.textColor) {
      computedStyle.color = header.textColor;
    }
    
    if (header?.padding !== undefined) {
      computedStyle.padding = typeof header.padding === 'number' ? `${header.padding}px` : header.padding;
    }
    
    return computedStyle;
  }, [header]);

  // Generate content styles
  const contentStyle = useMemo((): CSSProperties => {
    const computedStyle: CSSProperties = {};
    
    if (content?.backgroundColor) {
      computedStyle.backgroundColor = content.backgroundColor;
    }
    
    if (content?.padding !== undefined) {
      computedStyle.padding = typeof content.padding === 'number' ? `${content.padding}px` : content.padding;
    }
    
    if (content?.minHeight !== undefined) {
      computedStyle.minHeight = typeof content.minHeight === 'number' ? `${content.minHeight}px` : content.minHeight;
    }
    
    return computedStyle;
  }, [content]);

  // Handle collapse toggle
  const handleCollapseToggle = () => {
    const newCollapsed = !collapsed;
    setCollapsed(newCollapsed);
    onCollapse?.(newCollapsed);
  };

  // Handle card click
  const handleCardClick = (event: React.MouseEvent<HTMLDivElement>) => {
    onClick?.(event);
  };

  // Render header
  const renderHeader = () => {
    if (!header?.title && !header?.extra && !header?.icon) {
      return null;
    }

    const headerClasses = cx(
      styles.header,
      header?.borderBottom === false && styles.noBorder
    );

    return (
      <div 
        className={headerClasses} 
        style={headerStyle}
        onClick={collapsible ? handleCollapseToggle : undefined}
      >
        <div className={styles.headerContent}>
          {header?.icon && (
            <div className={styles.headerIcon}>
              {header.icon}
            </div>
          )}
          {header?.title && (
            <div className={styles.headerTitle}>
              {header.title}
            </div>
          )}
        </div>
        
        <div className={styles.headerExtra}>
          {header?.extra}
          {collapsible && (
            <div className={cx(styles.collapseIcon, collapsed && styles.collapsed)}>
              {collapsed ? <RightOutlined /> : <DownOutlined />}
            </div>
          )}
        </div>
      </div>
    );
  };

  return (
    <div
      className={cardClasses}
      style={cardStyle}
      onClick={handleCardClick}
      role={role}
      aria-label={ariaLabel}
    >
      {renderHeader()}
      <div className={styles.content} style={contentStyle}>
        {children}
      </div>
    </div>
  );
};

export default ConfigurableCard;
