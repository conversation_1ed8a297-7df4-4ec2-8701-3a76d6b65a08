// Base card styles
.card {
  background: #ffffff;
  border-radius: 8px;
  border: 1px solid #d9d9d9;
  transition: all 0.3s ease;
  overflow: hidden;
  position: relative;
  
  // Size variants
  &.small {
    .header {
      padding: 8px 12px;
      font-size: 14px;
    }
    
    .content {
      padding: 12px;
    }
  }
  
  &.default {
    .header {
      padding: 12px 16px;
      font-size: 16px;
    }
    
    .content {
      padding: 16px;
    }
  }
  
  &.large {
    .header {
      padding: 16px 24px;
      font-size: 18px;
    }
    
    .content {
      padding: 24px;
    }
  }
  
  // Shadow variants
  &.shadowNone {
    box-shadow: none;
  }
  
  &.shadowSmall {
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  }
  
  &.shadowMedium {
    box-shadow: 0 4px 16px rgba(0, 0, 0, 0.15);
  }
  
  &.shadowLarge {
    box-shadow: 0 8px 24px rgba(0, 0, 0, 0.2);
  }
  
  // Hoverable styles
  &.hoverable {
    cursor: pointer;
    
    &:hover {
      transform: translateY(-2px);
      box-shadow: 0 4px 16px rgba(0, 0, 0, 0.15);
    }
  }
  
  // Collapsible styles
  &.collapsible {
    .header {
      cursor: pointer;
      user-select: none;
      
      &:hover {
        background-color: rgba(0, 0, 0, 0.02);
      }
    }
  }
  
  &.collapsed {
    .content {
      display: none;
    }
  }
}

// Header styles
.header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  background-color: #fafafa;
  border-bottom: 1px solid #f0f0f0;
  font-weight: 600;
  color: #262626;
  transition: all 0.3s ease;
  
  &.noBorder {
    border-bottom: none;
  }
}

.headerContent {
  display: flex;
  align-items: center;
  gap: 8px;
  flex: 1;
}

.headerIcon {
  display: flex;
  align-items: center;
  font-size: 16px;
}

.headerTitle {
  flex: 1;
  margin: 0;
}

.headerExtra {
  display: flex;
  align-items: center;
  gap: 8px;
}

.collapseIcon {
  transition: transform 0.3s ease;
  
  &.collapsed {
    transform: rotate(-90deg);
  }
}

// Content styles
.content {
  background-color: #ffffff;
  transition: all 0.3s ease;
}

// Border style variants
.borderSolid {
  border-style: solid;
}

.borderDashed {
  border-style: dashed;
}

.borderDotted {
  border-style: dotted;
}

.borderNone {
  border: none;
}

// Theme variants
.themeDefault {
  background-color: #ffffff;
  border-color: #d9d9d9;
  
  .header {
    background-color: #fafafa;
    color: #262626;
  }
  
  .content {
    background-color: #ffffff;
    color: #262626;
  }
}

.themePrimary {
  background-color: #f0f8ff;
  border-color: #1890ff;
  
  .header {
    background-color: #e6f7ff;
    color: #1890ff;
  }
  
  .content {
    background-color: #f0f8ff;
    color: #262626;
  }
}

.themeSuccess {
  background-color: #f6ffed;
  border-color: #52c41a;
  
  .header {
    background-color: #f0f9e8;
    color: #52c41a;
  }
  
  .content {
    background-color: #f6ffed;
    color: #262626;
  }
}

.themeWarning {
  background-color: #fffbe6;
  border-color: #faad14;
  
  .header {
    background-color: #fef9e6;
    color: #faad14;
  }
  
  .content {
    background-color: #fffbe6;
    color: #262626;
  }
}

.themeDanger {
  background-color: #fff2f0;
  border-color: #ff4d4f;
  
  .header {
    background-color: #ffece8;
    color: #ff4d4f;
  }
  
  .content {
    background-color: #fff2f0;
    color: #262626;
  }
}

.themeInfo {
  background-color: #f0f8ff;
  border-color: #1890ff;
  
  .header {
    background-color: #e6f7ff;
    color: #1890ff;
  }
  
  .content {
    background-color: #f0f8ff;
    color: #262626;
  }
}

// Dark theme support
@media (prefers-color-scheme: dark) {
  .themeDefault {
    background-color: #1f1f1f;
    border-color: #434343;
    
    .header {
      background-color: #262626;
      color: #ffffff;
    }
    
    .content {
      background-color: #1f1f1f;
      color: #ffffff;
    }
  }
  
  .themePrimary {
    background-color: #0d1117;
    border-color: #1890ff;
    
    .header {
      background-color: #161b22;
      color: #58a6ff;
    }
    
    .content {
      background-color: #0d1117;
      color: #ffffff;
    }
  }
}

// Responsive design
@media (max-width: 768px) {
  .card {
    &.large {
      .header {
        padding: 12px 16px;
        font-size: 16px;
      }
      
      .content {
        padding: 16px;
      }
    }
  }
  
  .headerContent {
    gap: 4px;
  }
  
  .headerExtra {
    gap: 4px;
  }
}
