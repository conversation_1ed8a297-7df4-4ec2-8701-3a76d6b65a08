import { SettingOutlined, InfoCircleOutlined, CheckCircleOutlined, ExclamationCircleOutlined } from '@ant-design/icons';
import { Button, Typography, Space } from 'antd';
import React, { useState } from 'react';

import { ConfigurableCard, cardConfigs, mergeCardConfig, createCardConfig } from './index';

const { Title, Text } = Typography;

// Example component demonstrating various ConfigurableCard configurations
const ConfigurableCardExamples: React.FC = () => {
  const [collapsed, setCollapsed] = useState(false);

  return (
    <div style={{ padding: '24px', backgroundColor: '#f5f5f5' }}>
      <Title level={2}>ConfigurableCard Examples</Title>
      
      <Space direction="vertical" size="large" style={{ width: '100%' }}>
        
        {/* Basic Section Card */}
        <ConfigurableCard 
          {...cardConfigs.section}
          header={{ title: "Basic Section Card" }}
        >
          <Text>This is a basic section card using the predefined section configuration.</Text>
        </ConfigurableCard>

        {/* Collapsible Advanced Settings Card */}
        <ConfigurableCard
          {...cardConfigs.advancedSettings}
          header={{ 
            title: "Advanced Settings",
            icon: <SettingOutlined />,
          }}
          collapsible={true}
          defaultCollapsed={false}
          onCollapse={(collapsed) => console.log('Collapsed:', collapsed)}
        >
          <Text>This card is collapsible and has an icon in the header.</Text>
          <br />
          <Text>Click the header to expand/collapse the content.</Text>
        </ConfigurableCard>

        {/* Success Theme Card */}
        <ConfigurableCard 
          {...cardConfigs.success}
          header={{ 
            title: "Success Notification",
            icon: <CheckCircleOutlined />,
          }}
        >
          <Text>This is a success-themed card with green colors.</Text>
        </ConfigurableCard>

        {/* Warning Theme Card */}
        <ConfigurableCard 
          {...cardConfigs.warning}
          header={{ 
            title: "Warning Alert",
            icon: <ExclamationCircleOutlined />,
          }}
        >
          <Text>This is a warning-themed card with orange colors.</Text>
        </ConfigurableCard>

        {/* Info Theme Card */}
        <ConfigurableCard 
          {...cardConfigs.info}
          header={{ 
            title: "Information",
            icon: <InfoCircleOutlined />,
          }}
        >
          <Text>This is an info-themed card with blue colors.</Text>
        </ConfigurableCard>

        {/* Hoverable Elevated Card */}
        <ConfigurableCard 
          {...cardConfigs.elevated}
          header={{ title: "Hoverable Card" }}
          hoverable={true}
        >
          <Text>This card has hover effects. Try hovering over it!</Text>
        </ConfigurableCard>

        {/* Compact Card */}
        <ConfigurableCard 
          {...cardConfigs.compact}
          header={{ title: "Compact Card" }}
        >
          <Text>This is a compact card with smaller padding.</Text>
        </ConfigurableCard>

        {/* Minimal Card */}
        <ConfigurableCard 
          {...cardConfigs.minimal}
          header={{ title: "Minimal Card" }}
        >
          <Text>This is a minimal card with no borders or shadows.</Text>
        </ConfigurableCard>

        {/* Custom Configuration Example */}
        <ConfigurableCard 
          {...mergeCardConfig(cardConfigs.section, {
            theme: 'primary',
            size: 'large',
            border: {
              radius: 16,
              width: 2,
            },
            header: {
              title: "Custom Configuration",
              backgroundColor: '#e6f7ff',
              textColor: '#1890ff',
            },
            hoverable: true,
          })}
        >
          <Text>This card uses a custom configuration by merging the section config with custom overrides.</Text>
          <br />
          <Text>It has a primary theme, large size, custom border radius, and hover effects.</Text>
        </ConfigurableCard>

        {/* Fully Custom Card */}
        <ConfigurableCard
          size="default"
          theme="danger"
          shadow="large"
          border={{
            style: 'dashed',
            width: 2,
            radius: 12,
          }}
          header={{
            title: "Fully Custom Card",
            extra: <Button size="small">Action</Button>,
            backgroundColor: '#ffece8',
            textColor: '#ff4d4f',
          }}
          content={{
            padding: 24,
            backgroundColor: '#fff2f0',
          }}
          hoverable={true}
          hoverConfig={{
            enabled: true,
            shadow: 'large',
            transform: 'scale(1.02)',
          }}
        >
          <Text>This card is fully customized with individual properties.</Text>
          <br />
          <Text>It demonstrates the flexibility of the ConfigurableCard component.</Text>
        </ConfigurableCard>

        {/* Rule Card Example */}
        <ConfigurableCard 
          {...cardConfigs.rule}
          header={{
            title: "Rule Card Example",
            extra: <Button type="text" danger>Delete</Button>,
          }}
        >
          <Text>This is an example of a rule card configuration.</Text>
          <br />
          <Text>It's designed for displaying individual rules or items in a list.</Text>
        </ConfigurableCard>

      </Space>
    </div>
  );
};

export default ConfigurableCardExamples;
