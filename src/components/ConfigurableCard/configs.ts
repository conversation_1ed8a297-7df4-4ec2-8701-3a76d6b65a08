import { SettingOutlined, InfoCircleOutlined, CheckCircleOutlined, ExclamationCircleOutlined, CloseCircleOutlined } from '@ant-design/icons';
import React from 'react';

import { CardConfig } from './types';

// Predefined card configurations for common use cases
export const cardConfigs = {
  // Basic section card (similar to current sectionCard)
  section: {
    size: 'default',
    theme: 'default',
    shadow: 'small',
    border: {
      radius: 8,
    },
    header: {
      borderBottom: true,
    },
    className: 'section-card',
  } as CardConfig,

  // Advanced settings card with collapsible functionality
  advancedSettings: {
    size: 'default',
    theme: 'default',
    shadow: 'small',
    border: {
      radius: 8,
    },
    header: {
      icon: React.createElement(SettingOutlined),
      borderBottom: true,
    },
    collapsible: true,
    defaultCollapsed: false,
    className: 'advanced-settings-card',
  } as CardConfig,

  // Rule card for individual rules
  rule: {
    size: 'small',
    theme: 'default',
    shadow: 'none',
    border: {
      style: 'solid',
      width: 1,
      color: '#d9d9d9',
      radius: 6,
    },
    className: 'rule-card',
  } as CardConfig,

  // Success notification card
  success: {
    size: 'default',
    theme: 'success',
    shadow: 'small',
    border: {
      radius: 6,
    },
    header: {
      icon: React.createElement(CheckCircleOutlined),
      borderBottom: false,
    },
    className: 'success-card',
  } as CardConfig,

  // Warning notification card
  warning: {
    size: 'default',
    theme: 'warning',
    shadow: 'small',
    border: {
      radius: 6,
    },
    header: {
      icon: React.createElement(ExclamationCircleOutlined),
      borderBottom: false,
    },
    className: 'warning-card',
  } as CardConfig,

  // Error notification card
  error: {
    size: 'default',
    theme: 'danger',
    shadow: 'small',
    border: {
      radius: 6,
    },
    header: {
      icon: React.createElement(CloseCircleOutlined),
      borderBottom: false,
    },
    className: 'error-card',
  } as CardConfig,

  // Info card
  info: {
    size: 'default',
    theme: 'info',
    shadow: 'small',
    border: {
      radius: 6,
    },
    header: {
      icon: React.createElement(InfoCircleOutlined),
      borderBottom: false,
    },
    className: 'info-card',
  } as CardConfig,

  // Compact card for tight spaces
  compact: {
    size: 'small',
    theme: 'default',
    shadow: 'none',
    border: {
      radius: 4,
    },
    content: {
      padding: 8,
    },
    className: 'compact-card',
  } as CardConfig,

  // Elevated card with hover effects
  elevated: {
    size: 'default',
    theme: 'default',
    shadow: 'medium',
    hoverable: true,
    hoverConfig: {
      enabled: true,
      shadow: 'large',
      transform: 'translateY(-2px)',
    },
    border: {
      radius: 12,
    },
    className: 'elevated-card',
  } as CardConfig,

  // Minimal card with no borders or shadows
  minimal: {
    size: 'default',
    theme: 'default',
    shadow: 'none',
    border: {
      style: 'none',
    },
    header: {
      borderBottom: false,
      backgroundColor: 'transparent',
    },
    className: 'minimal-card',
  } as CardConfig,

  // Header-only card (no content padding)
  headerOnly: {
    size: 'default',
    theme: 'default',
    shadow: 'small',
    border: {
      radius: 8,
    },
    content: {
      padding: 0,
    },
    className: 'header-only-card',
  } as CardConfig,
};

// Helper function to merge configurations
export const mergeCardConfig = (baseConfig: CardConfig, overrides: Partial<CardConfig>): CardConfig => {
  return {
    ...baseConfig,
    ...overrides,
    header: {
      ...baseConfig.header,
      ...overrides.header,
    },
    content: {
      ...baseConfig.content,
      ...overrides.content,
    },
    border: {
      ...baseConfig.border,
      ...overrides.border,
    },
    hoverConfig: {
      ...baseConfig.hoverConfig,
      ...overrides.hoverConfig,
    },
  };
};

// Helper function to create a custom card configuration
export const createCardConfig = (config: Partial<CardConfig>): CardConfig => {
  return mergeCardConfig(cardConfigs.section, config);
};
