import React from 'react';

import styles from './index.module.scss';

export interface InstructionProps {
  regionLimitInfo: {
    description: string;
    limit: string[];
    acceptableFile: string;
    fileHeader: string;
  };
}
export default function Instruction({ regionLimitInfo }: InstructionProps) {
  return (
    <div className={styles.instructionWrapper}>
      <div>
        <span>Description:</span>
        {regionLimitInfo.description}
      </div>
      <ul>
        {regionLimitInfo.limit.map((info, index) => {
          return <li key={index}>{info}</li>;
        })}
      </ul>
      <div>
        <span>Acceptable file:</span>
        {regionLimitInfo.acceptableFile}
      </div>
      <div>
        <span>File header:</span>
        {regionLimitInfo.fileHeader}
      </div>
    </div>
  );
}
