import { RightOutlined } from '@ant-design/icons';
import type {
  ICategory,
  ICategorySelectorProps,
} from '@classification/category-selector';
import CategorySelector from '@classification/category-selector';
import {
  Button,
  Space,
} from 'antd';
import React, { useCallback, useEffect, useState } from 'react';

import type DataControl from 'src/utils/category-datacontrol';
import styles from './style.module.scss';

import '@classification/category-selector/dist/style/index.css';

// @ts-expect-error
interface IExpandCategorySelect extends ICategorySelectorProps {
  value?: number[];
  dataControl?: DataControl;
  onChange?: (value: number[]) => void;
  onCancel?: () => void;
}
const itemRender = (item: ICategory) => {
  return (
    <div className={styles.itemRender}>
      <div className={styles.itemName}>{item.name}</div>
      {item.hasChild && item.children && item.children?.length > 0 && (
        <RightOutlined color="rgba(0, 0, 0, 0.45)" size={12} />
      )}
    </div>
  );
};
const InputCategorySelect: React.FC<IExpandCategorySelect> = ({
  value,
  dataControl,
  checkedOnlyParent,
  onChange,
  multipleSelect,
  onCancel,
  ...props
}) => {
  const [curValue, setCurValue] = useState<number[]>(value || []);
  useEffect(() => {
    setCurValue(value || []);
  }, [value]);

  const onSelected = useCallback((categoryList: ICategory[]) => {
    const value = categoryList.map((cat) => cat.catId!);
    setCurValue(value);
    return true;
  }, []);

  const onConfirm = useCallback(() => {
    onChange && onChange(curValue);
    return true;
  }, [onChange, curValue]);

  return (
    <div className={styles.dropdownRender}>
      <div className={styles.categorySelectorWarp}>
        <CategorySelector
          loadAllCategory
          multipleSelect={multipleSelect}
          checkedOnlyParent={checkedOnlyParent}
          onSelected={(_, path) => onSelected(path)}
          itemRender={itemRender}
          itemClassName={styles.itemClassName}
          levelStyle={{
            width: 160,
          }}
          searchable
          // @ts-expect-error
          dataControl={dataControl}
          checked={curValue}
          selected={curValue}
          selectedStyle={{
            fontWeight: 'bold',
            background: '#E6F7FF',
          }}
          {...props}
        />
      </div>
      <Space className={styles.actions}>
        <Button onClick={onCancel}>Cancel</Button>
        <Button type="primary" onClick={onConfirm}>Confirm</Button>
      </Space>
    </div>
  );
};

export default InputCategorySelect;
