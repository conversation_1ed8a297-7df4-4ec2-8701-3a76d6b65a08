import { getCountry } from '@classification/admin-solution';
import type { ICategory  } from '@classification/category-selector';
import { Select } from 'antd';
import React, {
  useCallback,
  useRef,
  useState,
} from 'react';

import type DataControl from 'src/utils/category-datacontrol';
import ExpandCategorySelect from './ExpandCategory';
import styles from './style.module.scss';

interface ICategorySelector {
  value?: number[];
  maxLevel?: number;
  dataControl: DataControl;
  onChange?: (value: number[]) => void;
  placement?: 'leftBottom' | 'rightBottom';
}

const RootCatId = 0;

const CategorySelector: React.FC<ICategorySelector> = ({
  value,
  dataControl,
  onChange,
  placement,
  maxLevel,
}) => {
  const ref = useRef<any>(null);
  const [open, setOpen] = useState<boolean | undefined>(undefined);

  const [options, setOptions] = useState<{ label: string; value: number }[]>(
    [],
  );
  const handleCategoryListChange = useCallback((categoryList: ICategory[]) => {
    const options = categoryList?.map((cat) => ({
      label: cat.name!,
      value: cat.catId!,
    }));
    setOptions(options);
  }, []);
  const handleClear = useCallback(() => {
    onChange && onChange([]);
  }, [onChange]);
  const handleDropdownVisibleChange = useCallback((open: boolean) => {
    if (
      open &&
      !dataControl.getLoadedParentCategoryMap().get(RootCatId)
    ) {
      dataControl.loadData({ maxLevel });
    }
    setOpen(open);
  }, [open]);
  const onCategoryChange = useCallback((value: number[]) => {
    setOpen?.(false);
    onChange?.(value);
  }, [onChange, setOpen]);
  const dropdownRender = useCallback(() => {
    return (
      <ExpandCategorySelect
        value={value}
        dataControl={dataControl}
        onChange={onCategoryChange}
        region={getCountry()}
        maxLevel={maxLevel}
        onCategoryListChange={handleCategoryListChange}
        onCancel={() => setOpen(false)}
      ></ExpandCategorySelect>
    );
  }, [value, dataControl, onChange, handleCategoryListChange]);
  return (
    <Select
      ref={ref}
      open={open}
      value={value?.[value?.length - 1]}
      options={options}
      placeholder="Select Category"
      getPopupContainer={(triggerNode) => triggerNode.parentNode}
      dropdownRender={dropdownRender}
      removeIcon={null}
      popupClassName={
        placement === 'rightBottom' ? styles.dropdownBottomRight : ''
      }
      style={{ width: '100%' }}
      dropdownMatchSelectWidth={false}
      maxTagCount={3}
      allowClear
      onClear={handleClear}
      onDropdownVisibleChange={handleDropdownVisibleChange}
    ></Select>
  );
};

export default CategorySelector;
