.dropdownRender {
  height: 268px;
  min-width: 640px;

  & .searchInput {
    border: 1px solid rgb(0 0 0 / 6%);
    border-bottom: none;
    padding: 12px;

    & .searchIcon {
      color: #bfbfbf;
    }
  }

  :global {
    .ant-spin-container {
      overflow: hidden;
    }
  }

  & .categorySelectorWarp {
    height: 216px;
  }

  & .itemClassName {
    font-size: 14px;
    padding: 8px 12px;

    &:hover {
      background: #e6f7ff;
    }
  }

  & .itemRender {
    align-items: center;
    display: flex;

    & .itemName {
      flex: 1;
      overflow: hidden;
      text-overflow: ellipsis;
      white-space: nowrap;
      word-break: break-all;
    }
  }

  & .searchRender {
    color: #333;
    height: 100%;
    overflow-y: scroll;
    padding: 12px;

    & .categoryItem {
      cursor: pointer;
      margin-bottom: 12px;
      width: 100%;

      &:last-child {
        margin-bottom: 0;
      }
    }
  }

  & .highlight {
    background: #ddefff;
    border-radius: 2px;
    color: #2673dd;
  }
}

.dropdownBottomRight {
  left: auto !important;
  right: 0 !important;
}

.actions {
  margin-top: 8px;
  margin-right: 16px;
  display: flex;
  justify-content: flex-end;
}
