import { CountriesOfRoute, CountryOfRoute } from '../config';

function getRawCountry() {
  const url = window.location.pathname;
  const country = window.localStorage.getItem('country');
  for (const route of Object.keys(CountryOfRoute)) {
    if (url.includes(route)) {
      return CountryOfRoute[route];
    }
  }
  return country;
}

function getRawCountries(countries: string[]): string[] {
  const url = window.location.pathname;

  // if url belongs to CountryOfRoute, return empty country list
  for (const route of Object.keys(CountryOfRoute)) {
    if (url.includes(route)) {
      return [];
    }
  }
  // if url belongs to CountriesOfRoute, return corresponding country list
  for (const route of Object.keys(CountriesOfRoute)) {
    if (url.includes(route)) {
      return CountriesOfRoute[route](countries);
    }
  }
  // if url neither belongs to CountryOfRoute nor CountriesOfRoute, return the totally country list
  return countries;
}

export function getCountries(countries: string[]) {
  const _countries = getRawCountries(countries);
  return _countries.filter(
    (country) => countries.includes(country) || country === 'ALL',
  );
}

// Do not return 'ALL' as it will cause some API to fail when injected into headers.
export function getCountry() {
  let country = getRawCountry();
  if (country === null) {
    country = '';
  } else if (country === 'ALL') {
    country = 'WW';
  }
  return country;
}

// Same as getCountry, but will return 'ALL' as is for display purposes.
export function getCountryForDisplay() {
  let country = getRawCountry();

  if (country === null) {
    country = '';
  }
  return country;
}

export function setCountry(country: string) {
  window.localStorage.setItem('country', country);
}

export function getEnvironment() {
  const domain = window.location.hostname;
  if (domain.includes('test')) return 'test';
  else if (domain.includes('uat')) return 'uat';
  else if (domain.includes('staging')) return 'staging';
  else if (domain.includes('localhost')) return 'test';
  else if (domain.includes('stable')) return 'stable';
  else return 'live';
}
