import { getEnvironment } from '../utils';

type Countries = {
  [key: string]: (countries: string[])=> string[];
};
const multiRepoCountryRoute = window.__COUNTRY_ROUTE__ || {};
const CUSTOMIZE_COMMAND = 'includeALL';
const autoGenCountryOfRoute: Record<string, string> = {};
const autoGenCountriesOfRoute: Countries = {};
const includeFn = (countries: string[]) =>
  countries.length > 0 ? ['ALL', ...countries] : [];
for (const routePath in multiRepoCountryRoute) {
  const element = multiRepoCountryRoute[routePath];
  const isInclude = element === CUSTOMIZE_COMMAND;
  if (typeof element === 'string' && !isInclude) {
    autoGenCountryOfRoute[routePath] = element;
  } else if (isInclude) {
    autoGenCountriesOfRoute[routePath] = includeFn;
  } else if (Array.isArray(element)) {
    autoGenCountriesOfRoute[routePath] = () => element;
  }
}

export const CountryOfRoute = autoGenCountryOfRoute;

export const CountriesOfRoute: Countries = autoGenCountriesOfRoute;

export const SoupURL =
  getEnvironment() === 'live'
    ? 'https://soup.shopee.io'
    : `https://soup.${getEnvironment()}.shopee.io`;
