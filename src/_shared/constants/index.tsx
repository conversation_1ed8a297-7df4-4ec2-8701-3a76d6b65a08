export const TELEPORT_TOKEN_KEY = 'TELEPORT_TOKEN';
export const REGION_KEY = 'REGION';
export const PFB_KEY = 'SPC_PFB_LISTINGADMIN_STATIC';

// supposed to be 18446744073709551615, but Javascript will have rounding errors
// causing numbers around there to become 18446744073709552000, which is bigger than
// the actual MAX_UINT_64
export const MAX_UINT_64 = 18446744073709550000;
export const MAX_INT_32 = 2147483647;
export const MAX_UINT_32 = 4294967295;

export const auditLogUrl = '/item/shopee_admin/proxy/item/audit/api/v1/get';

export const DOMAIN_REGION = {
  WW: '.sg',
  SG: '.sg',
  PH: '.ph',
  MY: '.com.my',
  BR: '.com.br',
  TW: '.tw',
  ID: '.co.id',
  VN: '.vn',
  TH: '.co.th',
  MX: '.com.mx',
  CO: '.com.co',
  CL: '.cl',
  AR: '.com.ar',
  PL: '.pl',
  ES: '.es',
  FR: '.fr',
  IN: '.in',
  US: '.com',
};
export const TEST_REGION_MASK: { [k: string]: string } = {
  AR: 'TESTV5',
  US: 'TESTV10',
};
export const displayFormatWithTimezoneOffset = 'YYYY-MM-DD HH:mm:ss ZZ';
