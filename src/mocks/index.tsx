import { deepCamel } from 'src/api/helper';
import {
  ItemCondition,
  ItemFlags,
  Status,
} from '../api/PBData/constants';
import * as uploadAdminConstant from '../api/uploadAdmin/constants';
import type { uploadAdmin } from '../api/uploadAdmin/uploadAdmin';
import {
  QcMode,
  RuleModelQcStatus,
} from '../typings';

const {
  PromotionType,
  PromotionStatus,
} = uploadAdminConstant;
const {
  AttrDateTimeFormat,
  AttrInputType,
  AttrInputValidatorType,
  AttrFormatType,
  AttrStatus,
} = uploadAdminConstant;
const {
  STATUS_CAT_NORMAL,
} = uploadAdminConstant.CategoryStatus;

export const mockGetItemDetailResponse: Partial<uploadAdmin.IGetItemDetailResponse> = {
  itemId: 3300139077,
  productName: 'asd',
  itemStatus: Status.ITEM_NORMAL_UNLIST,
  currency: 'SGD',
  brand: 'brand',
  description: 'help me aaaaaaa',
  itemLikedCount: 69,
  createTime: 1606811275,
  updateTime: 1606984075,
  sold: 1,
  stock: 420,
  condition: ItemCondition.NEW_WITH_TAGS,
  parentSku: 'hehe',
  daysToShip: 2,
  weight: 123000,
  dimensions: {
    width: 120000,
    length: 2222,
    height: 1234,
    // enum?
    unit: 1,
  },
  attrQcStatus: Status.ATTR_VALUE_NORMAL,
  qcMode: QcMode.CB,
  productImages: {
    images: ['7adf197442a5914bba652420905e965e'],
    options: [],
  },
  variationImages: {
    images: [
      '7adf197442a5914bba652420905e965e',
      'a970be2adbf647574c3023e2e82e29ea',
    ],
    options: ['white', 'big long image so wide aaaaaaaaaaaaaaaaaaaaaaaa'],
  },
  whiteBackgroundImage: '7adf197442a5914bba652420905e965e',
  sizeChart: '7adf197442a5914bba652420905e965e',
  videoInfoList: [
    {
      videoId: '1f7f48d9a20887a96495b6b5aecba770',
      url: 'https://cvf.shopee.sg/file/1f7f48d9a20887a96495b6b5aecba770',
      thumbUrl: '',
      duration: 16,
      version: 1,
    },
    {
      videoId: 'KXICj3V73aTjMKLQ',
      url: 'http://cv.shopee.sg/video/KXICj3V73aTjMKLQ',
      thumbUrl: 'http://cv.shopee.sg/preview/KXICj3V73aTjMKLQ?time=1',
      duration: 12,
      version: 0,
    },
  ],
  hasTierVariation: true,
  tierVariationList: [
    {
      name: 'aaa',
      options: [],
      images: [],
      properties: [
        {
          color: 'black',
        },
        {
          color: 'blue',
        },
      ],
      // enum?
      type: -1,
    },
  ],
  canUseWholesale: true,
  wholesaleTierList: [
    {
      minCount: 0,
      maxCount: 6,
      price: 1230,
      inputPrice: 6969,
    },
  ],
  showItemGtin: true,
  gtin: 'peeee',
  isChildVsku: true,
  parentVskuList: [
    {
      parentItemId: 12,
      parentModelId: 34,
      parentShopId: 56,
      parentRegion: 'SG',
      quantity: 78,
      costPrice: 90,
      mainSku: true,
    },
  ],
  flagList: [
    ItemFlags.BADGE_TYPE_24H,
    ItemFlags.FREE_SHIPPING,
    ItemFlags.IS_SYSTEM_UNLIST,
    ItemFlags.SERVICE_BY_SHOPEE_3,
  ],
  cbOption:
  uploadAdminConstant.CBType.CB_TYPE_LOCAL,
  isCounterfeit: false,
  seoDescription: true,
  nonSearchable: false,
  isHidden: false,
  restock: false,
  isUnlisted: false,
  visibleInFe: true,
  useNewStockStructure: true,
  showItemPricePanel: true,
  isItemOriTaxed: true,
  originalPrice: 1111,
  originalPriceBeforeTax: *********,
  isItemSipSellingTaxed: true,
  sipSellingPrice: 1111,
  sipSellingPriceBeforeTax: *********,
  isItemPromoTaxed: true,
  sellerPromotionId: 1,
  promotionPrice: 1111,
  promotionPriceBeforeTax: *********,
  categoryList: [
    {
      catId: 20484,
      catName: 'cat',
    },
    {
      catId: 20485,
      catName: 'dog',
    },
  ],
  categoryHsCode: '2fdci43bf2',
  isOldQcEditable: true,
  ruleQcStatus: RuleModelQcStatus.NEW,
  modelQcStatus: RuleModelQcStatus.ABANDON,
  shopId: *********,
  shopName: 'big shop',
  shopStatus: Status.SHOP_DELETE,
  userId: 123,
  userName: 'asdfman',
  userStatus: Status.ACCOUNT_NORMAL,
  isPreferred: true,
  isOfficial: true,
  isCb: true,
  oldShippingList: [
    {
      channelName: 'qwe',
      property: 'aaa',
      value: 'hehehehe',
    },
  ],
  newShippingList: [
    {
      channelId: 1,
      channelName: 'qqq',
      shopLogisticsInfo: {
        enabled: true,
      },
      itemLogisticsInfo: {
        enabled: false,
      },
    },
    {
      channelId: 2,
      channelName: 'www',
      shopLogisticsInfo: {
        enabled: false,
      },
      itemLogisticsInfo: {
        enabled: true,
      },
    },
  ],
  productPageView: 'qwedqe',
  itemInstallmentTenure: {
    shopLevelEnabled: true,
    itemLevelEnabled: true,
    tenures: [1, 2],
    errMsg: '',
  },
  spDebugMsg: '',
  promotionType: PromotionType.PROMOTION_TYPE_BUNDLE_DEAL,
  isPreOrder: false,
  minPurchaseLimit: 1,
  globalBrandId: 1,
  globalBrandName: 'testbrand',
  globalCategoryList: [
    {
      catId: 1,
      catName: 'cat1',
    },
    {
      catId: 2,
      catName: 'cat1_1',
    },
    {
      catId: 3,
      catName: 'cat1_1_1',
    },
  ],
  dangerousGoods: 0,
  orderMaxPurchaseLimit: 2,
  errDetails: [],
};

export const mockSearchItemResponse: uploadAdmin.ISearchItemsResponse = {
  itemPreviews: [
    {
      itemId: 1,
      productName:
        'longlonglonglonglonglonglonglonglonglonglonglonglonglonglonglonglonglonglonglonglonglonglonglonglonglonglonglonglonglonglonglonglonglonglonglonglonglonglong',
      shopId: undefined,
      image: 'e6c7f3a67bbc44504aaff617ef372548',
      userId: 1,
      userName: 'asd',
      shopName: 'shop',
      category: 'aa',
      brand: 'aaaa',
      itemPrice: 1000000,
      currency: 'SGD',
      sold: 1,
      stock: 2,
      ruleQcStatus: 1,
      modelQcStatus: 1,
      itemStatus: 1,
      cbType:
      uploadAdminConstant.CBType.CB_TYPE_LOCAL,
      officialType:
      uploadAdminConstant.OfficialType
        .OFFICIAL_TYPE_NORMAL,
      isManaged: false,
      itemCreatedTime: 1000000000,
      itemUpdatedTime: 1606463886,
      weight: 1000000,
    },
    {
      itemId: 2,
      productName:
        'longlonglonglonglonglonglonglonglonglonglonglonglonglonglonglonglonglonglonglonglonglonglonglonglonglonglonglonglonglonglonglonglonglonglonglonglonglonglong',
      shopId: 2,
      image: 'e6c7f3a67bbc44504aaff617ef372548',
      userId: 2,
      userName: 'asd',
      shopName: 'shop',
      category: 'aa',
      brand: 'aaaa',
      itemPrice: 1000,
      currency: 'SGD',
      sold: 1,
      stock: 2,
      ruleQcStatus: 1,
      modelQcStatus: 1,
      itemStatus: 1,
      cbType:
      uploadAdminConstant.CBType.CB_TYPE_LOCAL,
      officialType:
      uploadAdminConstant.OfficialType
        .OFFICIAL_TYPE_NORMAL,
      isManaged: false,
      itemCreatedTime: 1000000000,
      itemUpdatedTime: 1606463886,
      weight: 1000,
    },
  ],
  total: 2,
  spDebugMsg: '',
  errDetails: [],
  spErrorCode: 0,
};

export const mockItemModelStockListResponse: uploadAdmin.IGetItemModelStockListResponse = {
  spErrorCode: 0,
  spDebugMsg: '',
  page: {
    offsetList: [],
    hasNext: false,
  },
  stockList: [
    {
      modelId: 12345,
      locationId: 'here',
      sellableStock: 123,
      reservedStock: undefined,
      lockedStock: 2,
    },
    {
      modelId: 22222,
      locationId: 'here',
      sellableStock: 2,
      reservedStock: 1,
      lockedStock: 60,
    },
    {
      modelId: 99999,
      locationId: 'there',
      sellableStock: 123,
      reservedStock: 22,
      lockedStock: 4,
    },
  ],
  floatingStockLists: [],
};

export const mockProductAttributesResponse: uploadAdmin.IGetProductAttributesResponse = {
  spDebugMsg: '',
  errDetails: [],
  attrModelSnapshot: {
    modelId: 18117,
    modelName: 'L2 Susu \u0026 Olahan: L3 Susu Bubuk (Update)',
  },
  attrSnapshotList: [
    {
      attribute: {
        attrId: 21538,
        attrName: 'Brand: L2 Susu \u0026 Olahan: L3 Susu \u0026 Olahan',
        attrType: 2,
      },
      status: 1,
      value: 'Ensure',
    },
    {
      attribute: {
        attrId: 20465,
        attrName: 'Expiry Date: L2 Susu \u0026 Olahan: L3 Susu Bubuk (Update)',
        attrType: 3,
      },
      status: 2,
      value: 'November 2022',
    },
    {
      attribute: {
        attrId: 22123,
        attrName: 'Shelf Life: L2 Susu \u0026 Olahan: L3 Susu Bubuk',
        attrType: 3,
      },
      status: 2,
      value: '18 Bulan',
    },
  ],
  productAttrModel: {
    attributes: {
      independentAttrs: [
        {
          attrId: 14707,
          attrName: 'Brand: L2 Suplemen Makanan: L3 Lain-lain (Update)',
          attrType: 2,
          inputType: 3,
          validateType: 2,
          values: [
            'Tidak Ada Merek',
            '13 Honey',
            '21 Herbal',
            '21st Century',
            '3 Green',
            '3 Miracles',
            '3M',
            '3uglena',
            '4DN',
            '4Life',
            '4Life Transfer Factor',
            '5-Hour Energy',
            '7Monday',
            'A.Vogel',
            'Aarushi',
            'Abetens',
            'ABG Moment',
          ],
        },
        {
          attrId: 22209,
          attrName:
            'Expiry Date: L2 Suplemen Makanan: L3 Suplemen Makanan Lainnya',
          attrType: 3,
          inputType: 1,
          validateType: 3,
          values: [
            'Januari 2021',
            'Februari 2021',
            'Maret 2021',
            'April 2021',
            'Mei 2021',
            'Juni 2021',
            'Juli 2021',
            'Februari 2027',
            'Maret 2027',
            'April 2027',
            'Mei 2027',
            'Juni 2027',
            'Juli 2027',
            'Agustus 2027',
            'September 2027',
            'Oktober 2027',
            'November 2027',
            'Desember 2027',
          ],
        },
        {
          attrId: 22224,
          attrName:
            'Shelf Life: L2 Suplemen Makanan: L3 Suplemen Makanan Lainnya',
          attrType: 3,
          inputType: 1,
          validateType: 3,
          values: [
            '1 Hari',
            '2 Hari',
            '3 Hari',
            '4 Hari',
            '5 Hari',
            '32 Bulan',
            '33 Bulan',
            '34 Bulan',
            '35 Bulan',
            '36 Bulan',
            '4 Tahun',
            '5 Tahun',
          ],
        },
      ],
    },
    modelId: 17774,
    modelName: 'L2 Suplemen Makanan: L3 Lain-lain (Update)',
  },
  spErrorCode: 0,
};

export const mockItemModelListResponse: uploadAdmin.IGetItemModelListResponse = {
  spDebugMsg: '',
  modelList: [
    {
      modelId: 13425,
      modelName: 'MODEL 1',
      originalPrice: 10,
      originalPriceBeforeTax: 9,
      promotionPrice: 8,
      promotionPriceBeforeTax: 7,
      promotionType: PromotionType.PROMOTION_TYPE_IN_SHOP_FLASH_SALE,
      sellingPrice: 11,
      sellingPriceBeforeTax: 10,
      stock: 100,
      currentPromotionId: 22,
      status: PromotionStatus.STATUS_PROMOTION_DELETED,
      sku: 'aaa',
      gtin: 'wedqwfrf',
      isDefault: true,
      link: 'no link here',
    },
    {
      modelId: 23455,
      modelName: 'MODEL 2',
      originalPrice: 11,
      originalPriceBeforeTax: 9,
      promotionPrice: 3,
      promotionPriceBeforeTax: 44,
      promotionType: PromotionType.PROMOTION_TYPE_BUNDLE_DEAL,
      sellingPrice: 11,
      sellingPriceBeforeTax: 21,
      stock: 2,
      currentPromotionId: 33,
      status: PromotionStatus.STATUS_PROMOTION_DELETED,
      sku: 'aaa',
      gtin: 'wedqwfrf',
      isDefault: false,
      link: 'no link here',
    },
  ],
  spErrorCode: 0,
};

export const mockGetGlobalCategoryResponse: uploadAdmin.IGetGlobalCategoryListResponse = {
  cats: [
    {
      catId: 1,
      catName: 'cat1',
      status: STATUS_CAT_NORMAL,
      subCategories: [
        {
          catId: 2,
          catName: 'cat1_1',
          status: STATUS_CAT_NORMAL,
          subCategories: [
            {
              catId: 3,
              catName: 'cat1_1_1',
              status: STATUS_CAT_NORMAL,
            },
          ],
        },
        {
          catId: 8,
          catName: 'cat1_2',
          status: STATUS_CAT_NORMAL,
        },
      ],
    },
    {
      catId: 11,
      catName: 'cat2',
      status: STATUS_CAT_NORMAL,
      subCategories: [
        {
          catId: 12,
          catName: 'cat2_1',
          status: STATUS_CAT_NORMAL,
        },
      ],
    },
    {
      catId: 21,
      catName: 'cat3',
      status: STATUS_CAT_NORMAL,
    },
  ],
};

export const mockGetGlobalProductAttributesResponse: uploadAdmin.IGetGlobalProductAttributesResponse = {
  spDebugMsg: '',
  errDetails: [],
  attrSnapshotList: [
    {
      attrId: 1,
      attrName: 'attr_1',
      attrStatus: AttrStatus.ATTR_NORMAL,
      attrValue: 'a1v1',
    },
    {
      attrId: 2,
      attrName: 'attr_2',
      attrStatus: AttrStatus.ATTR_NORMAL,
      attrValue: 'a2v1',
    },
    {
      attrId: 3,
      attrName: 'attr_3',
      attrStatus: AttrStatus.ATTR_NORMAL,
      attrValue: '12kg',
    },
    {
      attrId: 4,
      attrName: 'attr_4',
      attrStatus: AttrStatus.ATTR_NORMAL,
      attrValue: 'a4v1',
    },
    {
      attrId: 5,
      attrName: 'attr_5',
      attrStatus: AttrStatus.ATTR_NORMAL,
      attrValue: '34.5',
    },
    {
      attrId: 6,
      attrName: 'attr_6',
      attrStatus: AttrStatus.ATTR_NORMAL,
      attrValue: '2g',
    },
    {
      attrId: 7,
      attrName: 'attr_7',
      attrStatus: AttrStatus.ATTR_NORMAL,
      attrValue: '02/2021',
    },
    {
      attrId: 8,
      attrName: 'attr_8',
      attrStatus: AttrStatus.ATTR_NORMAL,
      attrValue: '123',
    },
    {
      attrId: 8,
      attrName: 'attr_8',
      attrStatus: AttrStatus.ATTR_NORMAL,
      attrValue: '456',
    },
    {
      attrId: 9,
      attrName: 'attr_9',
      attrStatus: AttrStatus.ATTR_NORMAL,
      attrValue: '456',
    },
    {
      attrId: 10,
      attrName: 'attr_10',
      attrStatus: AttrStatus.ATTR_NORMAL,
      attrValue: '05/02/2021',
    },
    {
      attrId: 11,
      attrName: 'attr_11',
      attrStatus: AttrStatus.ATTR_NORMAL,
      attrValue: '07/01/2021',
    },
    {
      attrId: 12,
      attrName: 'attr_12',
      attrStatus: AttrStatus.ATTR_NORMAL,
      attrValue: '1kg',
    },
    {
      attrId: 12,
      attrName: 'attr_12',
      attrStatus: AttrStatus.ATTR_NORMAL,
      attrValue: '2kg',
    },
  ],
  productAttrList: {
    globalAttributes: [
      {
        attrId: 1,
        attrName: 'attr_1',
        inputType: AttrInputType.SINGLE_DROP_DOWN,
        validateType: AttrInputValidatorType.VALIDATOR_NOT_REQUIRED,
        attrFormatType: AttrFormatType.FORMAT_NORMAL,
        attrExtInfo: undefined,
        attrValueList: [
          {
            id: 1,
            value: 'a1v1',
          },
          {
            id: 2,
            value: 'a1v2',
          },
        ],
        isChild: false,
      },
      {
        attrId: 2,
        attrName: 'attr_2',
        inputType: AttrInputType.SINGLE_COMBO_BOX,
        validateType: AttrInputValidatorType.VALIDATOR_NOT_REQUIRED,
        attrFormatType: AttrFormatType.FORMAT_NORMAL,
        attrExtInfo: undefined,
        attrValueList: [
          {
            id: 1,
            value: 'a2v1',
          },
          {
            id: 2,
            value: 'a2v2',
          },
        ],
        isChild: false,
      },
      {
        attrId: 3,
        attrName: 'attr_3',
        inputType: AttrInputType.SINGLE_COMBO_BOX,
        validateType: AttrInputValidatorType.VALIDATOR_INTEGERS,
        attrFormatType: AttrFormatType.FORMAT_QUANTITATIVE_WITH_UNIT,
        attrExtInfo: {
          unitList: {
            units: ['kg', 'g'],
          },
        },
        attrValueList: [
          {
            id: 1,
            value: '12kg',
          },
          {
            id: 2,
            value: '34kg',
          },
        ],
        isChild: false,
      },
      {
        attrId: 4,
        attrName: 'attr_4',
        inputType: AttrInputType.FREE_TEXT_FILED,
        validateType: AttrInputValidatorType.VALIDATOR_STRING,
        attrFormatType: AttrFormatType.FORMAT_NORMAL,
        attrExtInfo: undefined,
        attrValueList: [
          {
            id: 1,
            value: 'a4v1',
          },
          {
            id: 2,
            value: 'a4v2',
          },
        ],
        isChild: false,
      },
      {
        attrId: 5,
        attrName: 'attr_5',
        inputType: AttrInputType.FREE_TEXT_FILED,
        validateType: AttrInputValidatorType.VALIDATOR_NUMBERS,
        attrFormatType: AttrFormatType.FORMAT_NORMAL,
        attrExtInfo: undefined,
        attrValueList: [
          {
            id: 1,
            value: '12.0',
          },
          {
            id: 2,
            value: '34.5',
          },
        ],
        isChild: false,
      },
      {
        attrId: 6,
        attrName: 'attr_6',
        inputType: AttrInputType.FREE_TEXT_FILED,
        validateType: AttrInputValidatorType.VALIDATOR_NUMBERS,
        attrFormatType: AttrFormatType.FORMAT_QUANTITATIVE_WITH_UNIT,
        attrExtInfo: {
          unitList: {
            units: ['kg', 'g'],
          },
        },
        attrValueList: [
          {
            id: 1,
            value: '1kg',
          },
          {
            id: 2,
            value: '2g',
          },
        ],
        isChild: false,
      },
      {
        attrId: 7,
        attrName: 'attr_7',
        inputType: AttrInputType.FREE_TEXT_FILED,
        validateType: AttrInputValidatorType.VALIDATOR_DATE,
        attrFormatType: AttrFormatType.FORMAT_NORMAL,
        attrExtInfo: {
          datetimeFormat: AttrDateTimeFormat.YEAR_MONTH,
        },
        attrValueList: [
          {
            id: 1,
            value: '1612506667',
          },
          {
            id: 2,
            value: '1610006667',
          },
        ],
        isChild: false,
      },
      {
        attrId: 8,
        attrName: 'attr_8',
        inputType: AttrInputType.MULTI_DROP_DOWN,
        validateType: AttrInputValidatorType.VALIDATOR_INTEGERS,
        attrFormatType: AttrFormatType.FORMAT_NORMAL,
        attrExtInfo: undefined,
        attrValueList: [
          {
            id: 1,
            value: '123',
          },
          {
            id: 2,
            value: '456',
          },
        ],
        isChild: false,
      },
      {
        attrId: 9,
        attrName: 'attr_9',
        inputType: AttrInputType.MULTI_COMBO_BOX,
        validateType: AttrInputValidatorType.VALIDATOR_INTEGERS,
        attrFormatType: AttrFormatType.FORMAT_NORMAL,
        attrExtInfo: undefined,
        attrValueList: [
          {
            id: 1,
            value: '123',
          },
          {
            id: 2,
            value: '456',
          },
        ],
        isChild: false,
      },
      {
        attrId: 10,
        attrName: 'attr_10',
        inputType: AttrInputType.MULTI_COMBO_BOX,
        validateType: AttrInputValidatorType.VALIDATOR_DATE,
        attrFormatType: AttrFormatType.FORMAT_NORMAL,
        attrExtInfo: {
          datetimeFormat: AttrDateTimeFormat.YEAR_MONTH,
        },
        attrValueList: [
          {
            id: 1,
            value: '1612506667',
          },
          {
            id: 2,
            value: '1610006667',
          },
        ],
        isChild: false,
      },
      {
        attrId: 11,
        attrName: 'attr_11',
        inputType: AttrInputType.MULTI_COMBO_BOX,
        validateType: AttrInputValidatorType.VALIDATOR_DATE,
        attrFormatType: AttrFormatType.FORMAT_NORMAL,
        attrExtInfo: {
          datetimeFormat: AttrDateTimeFormat.YEAR_MONTH_DATE,
        },
        attrValueList: [
          {
            id: 1,
            value: '1612506667',
          },
          {
            id: 2,
            value: '1610006667',
          },
        ],
        isChild: true,
      },
      {
        attrId: 12,
        attrName: 'attr_12',
        inputType: AttrInputType.MULTI_COMBO_BOX,
        validateType: AttrInputValidatorType.VALIDATOR_INTEGERS,
        attrFormatType: AttrFormatType.FORMAT_QUANTITATIVE_WITH_UNIT,
        attrExtInfo: {
          unitList: { units: ['kg', 'g', 'mg'] },
        },
        attrValueList: [
          {
            id: 1,
            value: '1kg',
          },
          {
            id: 2,
            value: '2kg',
          },
        ],
        isChild: true,
      },
      {
        attrId: 13,
        attrName: 'attr_13',
        inputType: AttrInputType.SINGLE_DROP_DOWN,
        validateType: AttrInputValidatorType.VALIDATOR_INTEGERS,
        attrFormatType: AttrFormatType.FORMAT_QUANTITATIVE_WITH_UNIT,
        attrExtInfo: {
          unitList: { units: ['kg', 'g', 'mg'] },
        },
        attrValueList: [
          {
            id: 1,
            value: '1kg',
          },
          {
            id: 2,
            value: '2kg',
          },
        ],
        isChild: true,
      },
      {
        attrId: 14,
        attrName: 'attr_14',
        inputType: AttrInputType.SINGLE_COMBO_BOX,
        validateType: AttrInputValidatorType.VALIDATOR_NOT_REQUIRED,
        attrFormatType: AttrFormatType.FORMAT_NORMAL,
        attrExtInfo: undefined,
        attrValueList: [
          {
            id: 1,
            value: 'wwww',
          },
          {
            id: 2,
            value: 'aaaa',
          },
        ],
        isChild: true,
      },
    ],
    parentChildMappingAttrs: [
      {
        parentAttrId: 1,
        parentValueId: 1,
        childAttrId: 11,
        childValueId: 2,
      },
      {
        parentAttrId: 1,
        parentValueId: 1,
        childAttrId: 12,
        childValueId: 1,
      },
      {
        parentAttrId: 1,
        parentValueId: 1,
        childAttrId: 12,
        childValueId: 2,
      },
      {
        parentAttrId: 1,
        parentValueId: 2,
        childAttrId: 12,
        childValueId: 1,
      },
      {
        parentAttrId: 1,
        parentValueId: 1,
        childAttrId: 13,
        childValueId: 1,
      },
      {
        parentAttrId: 1,
        parentValueId: 1,
        childAttrId: 13,
        childValueId: 2,
      },
      {
        parentAttrId: 9,
        parentValueId: 1,
        childAttrId: 14,
        childValueId: 1,
      },
      {
        parentAttrId: 10,
        parentValueId: 1,
        childAttrId: 14,
        childValueId: 2,
      },
    ],
  },
  spErrorCode: 0,
};

export const kitComponents = [
  {
    name: 'name',
    price: 10,
    modelList: [
      {
        name: '1',
        quantity: 1,
        image: '',
        mainSku: false,
        sku: '12',
      }, {
        name: '1',
        quantity: 1,
        image: '',
        mainSku: false,
        sku: '12',
      }, {
        name: '1',
        quantity: 1,
        image: '',
        mainSku: true,
        sku: '12',
      },
    ],
    dimension: {
      height: 1,
      length: 2,
      width: 3,
    },
    weight: {
      value: 1,
      unit: 1,
    },
  }, {
    name: 'name11',
    modelList: [
      {
        name: '1',
        quantity: 1,
        image: '',
        mainSku: false,
        sku: '12',
      }, {
        name: '1',
        quantity: 1,
        image: '',
        mainSku: false,
        sku: '12',
      }, {
        name: '1',
        quantity: 1,
        image: '',
        mainSku: true,
        sku: '12',
      },
    ],
    dimension: {
      height: 1,
      length: 2,
      width: 3,
    },
    weight: {
      value: 1,
      unit: 1,
    },
  },
]

export const searchProductListMock = deepCamel({
  'code': 0,
  'message': 'success',
  'user_message': 'success',
  'data': {
    'page_info': {
      'cursor': '1717694308,2792768858',
      'total': 227,
    },
    'products': [
      {
        'id': 2892827753,
        'name': 'ANP optional item test',
        'status': 1,
        'cover_image': 'sg-11134207-7req4-m292gw1makrnf5',
        'parent_sku': 'psku',
        'aggregated_price': {
          'price_min': '1000.00',
          'price_max': '1000.00',
        },
        'aggregated_stock': {
          'total_available_stock': 400,
          'total_seller_stock': 400,
          'total_shopee_stock': 0,
        },
        'modify_time': 1741084053,
        'create_time': 1741056529,
        'model_list': [
          {
            'id': 9401833348,
            'name': 'red,XL',
            'sku': 'psku',
            'tier_index': [
              0,
              1,
            ],
            'is_default': false,
            'image': '',
            'stock_detail': {
              'advanced_stock': {
                'sellable_stock': 0,
                'in_transit_stock': 0,
              },
              'total_available_stock': 100,
              'total_seller_stock': 100,
              'total_shopee_stock': 0,
            },
            'price_detail': {
              'origin_price': '1000.00',
              'promotion_price': '0.00',
            },
          },
          {
            'id': 9401833349,
            'name': 'blue,L',
            'sku': 'psku',
            'tier_index': [
              1,
              0,
            ],
            'is_default': false,
            'image': '',
            'stock_detail': {
              'advanced_stock': {
                'sellable_stock': 0,
                'in_transit_stock': 0,
              },
              'total_available_stock': 100,
              'total_seller_stock': 100,
              'total_shopee_stock': 0,
            },
            'price_detail': {
              'origin_price': '1000.00',
              'promotion_price': '0.00',
            },
          },
          {
            'id': 9401833350,
            'name': 'blue,XL',
            'sku': 'psku',
            'tier_index': [
              1,
              1,
            ],
            'is_default': false,
            'image': '',
            'stock_detail': {
              'advanced_stock': {
                'sellable_stock': 0,
                'in_transit_stock': 0,
              },
              'total_available_stock': 100,
              'total_seller_stock': 100,
              'total_shopee_stock': 0,
            },
            'price_detail': {
              'origin_price': '1000.00',
              'promotion_price': '0.00',
            },
          },
          {
            'id': 9401833351,
            'name': 'red,L',
            'sku': 'psku',
            'tier_index': [
              0,
              0,
            ],
            'is_default': false,
            'image': '',
            'stock_detail': {
              'advanced_stock': {
                'sellable_stock': 0,
                'in_transit_stock': 0,
              },
              'total_available_stock': 100,
              'total_seller_stock': 100,
              'total_shopee_stock': 0,
            },
            'price_detail': {
              'origin_price': '1000.00',
              'promotion_price': '0.00',
            },
          },
        ],
      },
      {
        'id': 2892827755,
        'name': 'ANP fixiable item test-update',
        'status': 1,
        'cover_image': 'sg-11134207-7req4-m292gw1makrnf5',
        'parent_sku': 'psku',
        'aggregated_price': {
          'price_min': '1000.00',
          'price_max': '1000.00',
        },
        'aggregated_stock': {
          'total_available_stock': 400,
          'total_seller_stock': 400,
          'total_shopee_stock': 0,
        },
        'modify_time': 1741076104,
        'create_time': 1741056529,
        'model_list': [
          {
            'id': 9401833358,
            'name': 'red,L',
            'sku': 'psku',
            'tier_index': [
              0,
              0,
            ],
            'is_default': false,
            'image': '',
            'stock_detail': {
              'advanced_stock': {
                'sellable_stock': 0,
                'in_transit_stock': 0,
              },
              'total_available_stock': 100,
              'total_seller_stock': 100,
              'total_shopee_stock': 0,
            },
            'price_detail': {
              'origin_price': '1000.00',
              'promotion_price': '0.00',
            },
          },
          {
            'id': 9401833359,
            'name': 'red,XL',
            'sku': 'psku',
            'tier_index': [
              0,
              1,
            ],
            'is_default': false,
            'image': '',
            'stock_detail': {
              'advanced_stock': {
                'sellable_stock': 0,
                'in_transit_stock': 0,
              },
              'total_available_stock': 100,
              'total_seller_stock': 100,
              'total_shopee_stock': 0,
            },
            'price_detail': {
              'origin_price': '1000.00',
              'promotion_price': '0.00',
            },
          },
          {
            'id': 9401833356,
            'name': 'blue,L',
            'sku': 'psku',
            'tier_index': [
              1,
              0,
            ],
            'is_default': false,
            'image': '',
            'stock_detail': {
              'advanced_stock': {
                'sellable_stock': 0,
                'in_transit_stock': 0,
              },
              'total_available_stock': 100,
              'total_seller_stock': 100,
              'total_shopee_stock': 0,
            },
            'price_detail': {
              'origin_price': '1000.00',
              'promotion_price': '0.00',
            },
          },
          {
            'id': 9401833357,
            'name': 'blue,XL',
            'sku': 'psku',
            'tier_index': [
              1,
              1,
            ],
            'is_default': false,
            'image': '',
            'stock_detail': {
              'advanced_stock': {
                'sellable_stock': 0,
                'in_transit_stock': 0,
              },
              'total_available_stock': 100,
              'total_seller_stock': 100,
              'total_shopee_stock': 0,
            },
            'price_detail': {
              'origin_price': '1000.00',
              'promotion_price': '0.00',
            },
          },
        ],
      },
      {
        'id': 2892827750,
        'name': 'ANP mandatory item test',
        'status': 1,
        'cover_image': 'sg-11134207-7req4-m292gw1makrnf5',
        'parent_sku': 'psku',
        'aggregated_price': {
          'price_min': '1000.00',
          'price_max': '1000.00',
        },
        'aggregated_stock': {
          'total_available_stock': 400,
          'total_seller_stock': 400,
          'total_shopee_stock': 0,
        },
        'modify_time': 1741075169,
        'create_time': 1741056517,
        'model_list': [
          {
            'id': 9401833337,
            'name': 'red,XL',
            'sku': 'psku',
            'tier_index': [
              0,
              1,
            ],
            'is_default': false,
            'image': '',
            'stock_detail': {
              'advanced_stock': {
                'sellable_stock': 0,
                'in_transit_stock': 0,
              },
              'total_available_stock': 100,
              'total_seller_stock': 100,
              'total_shopee_stock': 0,
            },
            'price_detail': {
              'origin_price': '1000.00',
              'promotion_price': '0.00',
            },
          },
          {
            'id': 9401833338,
            'name': 'blue,L',
            'sku': 'psku',
            'tier_index': [
              1,
              0,
            ],
            'is_default': false,
            'image': '',
            'stock_detail': {
              'advanced_stock': {
                'sellable_stock': 0,
                'in_transit_stock': 0,
              },
              'total_available_stock': 100,
              'total_seller_stock': 100,
              'total_shopee_stock': 0,
            },
            'price_detail': {
              'origin_price': '1000.00',
              'promotion_price': '0.00',
            },
          },
          {
            'id': 9401833339,
            'name': 'blue,XL',
            'sku': 'psku',
            'tier_index': [
              1,
              1,
            ],
            'is_default': false,
            'image': '',
            'stock_detail': {
              'advanced_stock': {
                'sellable_stock': 0,
                'in_transit_stock': 0,
              },
              'total_available_stock': 100,
              'total_seller_stock': 100,
              'total_shopee_stock': 0,
            },
            'price_detail': {
              'origin_price': '1000.00',
              'promotion_price': '0.00',
            },
          },
          {
            'id': 9401833336,
            'name': 'red,L',
            'sku': 'psku',
            'tier_index': [
              0,
              0,
            ],
            'is_default': false,
            'image': '',
            'stock_detail': {
              'advanced_stock': {
                'sellable_stock': 0,
                'in_transit_stock': 0,
              },
              'total_available_stock': 100,
              'total_seller_stock': 100,
              'total_shopee_stock': 0,
            },
            'price_detail': {
              'origin_price': '1000.00',
              'promotion_price': '0.00',
            },
          },
        ],
      },
      {
        'id': 2892827752,
        'name': 'create item by shark tool_1741056524',
        'status': 1,
        'cover_image': 'sg-11134207-7req4-m292gw1makrnf5',
        'parent_sku': 'psku',
        'aggregated_price': {
          'price_min': '1000.00',
          'price_max': '1000.00',
        },
        'aggregated_stock': {
          'total_available_stock': 400,
          'total_seller_stock': 400,
          'total_shopee_stock': 0,
        },
        'modify_time': 1741056536,
        'create_time': 1741056529,
        'model_list': [
          {
            'id': 9401833347,
            'name': 'red,XL',
            'sku': 'psku',
            'tier_index': [
              0,
              1,
            ],
            'is_default': false,
            'image': '',
            'stock_detail': {
              'advanced_stock': {
                'sellable_stock': 0,
                'in_transit_stock': 0,
              },
              'total_available_stock': 100,
              'total_seller_stock': 100,
              'total_shopee_stock': 0,
            },
            'price_detail': {
              'origin_price': '1000.00',
              'promotion_price': '0.00',
            },
          },
          {
            'id': 9401833344,
            'name': 'blue,L',
            'sku': 'psku',
            'tier_index': [
              1,
              0,
            ],
            'is_default': false,
            'image': '',
            'stock_detail': {
              'advanced_stock': {
                'sellable_stock': 0,
                'in_transit_stock': 0,
              },
              'total_available_stock': 100,
              'total_seller_stock': 100,
              'total_shopee_stock': 0,
            },
            'price_detail': {
              'origin_price': '1000.00',
              'promotion_price': '0.00',
            },
          },
          {
            'id': 9401833345,
            'name': 'blue,XL',
            'sku': 'psku',
            'tier_index': [
              1,
              1,
            ],
            'is_default': false,
            'image': '',
            'stock_detail': {
              'advanced_stock': {
                'sellable_stock': 0,
                'in_transit_stock': 0,
              },
              'total_available_stock': 100,
              'total_seller_stock': 100,
              'total_shopee_stock': 0,
            },
            'price_detail': {
              'origin_price': '1000.00',
              'promotion_price': '0.00',
            },
          },
          {
            'id': 9401833346,
            'name': 'red,L',
            'sku': 'psku',
            'tier_index': [
              0,
              0,
            ],
            'is_default': false,
            'image': '',
            'stock_detail': {
              'advanced_stock': {
                'sellable_stock': 0,
                'in_transit_stock': 0,
              },
              'total_available_stock': 100,
              'total_seller_stock': 100,
              'total_shopee_stock': 0,
            },
            'price_detail': {
              'origin_price': '1000.00',
              'promotion_price': '0.00',
            },
          },
        ],
      },
      {
        'id': 2892827751,
        'name': 'create item by shark tool_1741056524',
        'status': 1,
        'cover_image': 'sg-11134207-7req4-m292gw1makrnf5',
        'parent_sku': 'psku',
        'aggregated_price': {
          'price_min': '1000.00',
          'price_max': '1000.00',
        },
        'aggregated_stock': {
          'total_available_stock': 400,
          'total_seller_stock': 400,
          'total_shopee_stock': 0,
        },
        'modify_time': 1741056535,
        'create_time': 1741056529,
        'model_list': [
          {
            'id': 9401833341,
            'name': 'red,XL',
            'sku': 'psku',
            'tier_index': [
              0,
              1,
            ],
            'is_default': false,
            'image': '',
            'stock_detail': {
              'advanced_stock': {
                'sellable_stock': 0,
                'in_transit_stock': 0,
              },
              'total_available_stock': 100,
              'total_seller_stock': 100,
              'total_shopee_stock': 0,
            },
            'price_detail': {
              'origin_price': '1000.00',
              'promotion_price': '0.00',
            },
          },
          {
            'id': 9401833342,
            'name': 'blue,L',
            'sku': 'psku',
            'tier_index': [
              1,
              0,
            ],
            'is_default': false,
            'image': '',
            'stock_detail': {
              'advanced_stock': {
                'sellable_stock': 0,
                'in_transit_stock': 0,
              },
              'total_available_stock': 100,
              'total_seller_stock': 100,
              'total_shopee_stock': 0,
            },
            'price_detail': {
              'origin_price': '1000.00',
              'promotion_price': '0.00',
            },
          },
          {
            'id': 9401833343,
            'name': 'blue,XL',
            'sku': 'psku',
            'tier_index': [
              1,
              1,
            ],
            'is_default': false,
            'image': '',
            'stock_detail': {
              'advanced_stock': {
                'sellable_stock': 0,
                'in_transit_stock': 0,
              },
              'total_available_stock': 100,
              'total_seller_stock': 100,
              'total_shopee_stock': 0,
            },
            'price_detail': {
              'origin_price': '1000.00',
              'promotion_price': '0.00',
            },
          },
          {
            'id': 9401833340,
            'name': 'red,L',
            'sku': 'psku',
            'tier_index': [
              0,
              0,
            ],
            'is_default': false,
            'image': '',
            'stock_detail': {
              'advanced_stock': {
                'sellable_stock': 0,
                'in_transit_stock': 0,
              },
              'total_available_stock': 100,
              'total_seller_stock': 100,
              'total_shopee_stock': 0,
            },
            'price_detail': {
              'origin_price': '1000.00',
              'promotion_price': '0.00',
            },
          },
        ],
      },
      {
        'id': 2892827754,
        'name': 'create item by shark tool_1741056524',
        'status': 1,
        'cover_image': 'sg-11134207-7req4-m292gw1makrnf5',
        'parent_sku': 'psku',
        'aggregated_price': {
          'price_min': '1000.00',
          'price_max': '1000.00',
        },
        'aggregated_stock': {
          'total_available_stock': 400,
          'total_seller_stock': 400,
          'total_shopee_stock': 0,
        },
        'modify_time': 1741056535,
        'create_time': 1741056529,
        'model_list': [
          {
            'id': 9401833355,
            'name': 'blue,XL',
            'sku': 'psku',
            'tier_index': [
              1,
              1,
            ],
            'is_default': false,
            'image': '',
            'stock_detail': {
              'advanced_stock': {
                'sellable_stock': 0,
                'in_transit_stock': 0,
              },
              'total_available_stock': 100,
              'total_seller_stock': 100,
              'total_shopee_stock': 0,
            },
            'price_detail': {
              'origin_price': '1000.00',
              'promotion_price': '0.00',
            },
          },
          {
            'id': 9401833352,
            'name': 'red,L',
            'sku': 'psku',
            'tier_index': [
              0,
              0,
            ],
            'is_default': false,
            'image': '',
            'stock_detail': {
              'advanced_stock': {
                'sellable_stock': 0,
                'in_transit_stock': 0,
              },
              'total_available_stock': 100,
              'total_seller_stock': 100,
              'total_shopee_stock': 0,
            },
            'price_detail': {
              'origin_price': '1000.00',
              'promotion_price': '0.00',
            },
          },
          {
            'id': 9401833353,
            'name': 'red,XL',
            'sku': 'psku',
            'tier_index': [
              0,
              1,
            ],
            'is_default': false,
            'image': '',
            'stock_detail': {
              'advanced_stock': {
                'sellable_stock': 0,
                'in_transit_stock': 0,
              },
              'total_available_stock': 100,
              'total_seller_stock': 100,
              'total_shopee_stock': 0,
            },
            'price_detail': {
              'origin_price': '1000.00',
              'promotion_price': '0.00',
            },
          },
          {
            'id': 9401833354,
            'name': 'blue,L',
            'sku': 'psku',
            'tier_index': [
              1,
              0,
            ],
            'is_default': false,
            'image': '',
            'stock_detail': {
              'advanced_stock': {
                'sellable_stock': 0,
                'in_transit_stock': 0,
              },
              'total_available_stock': 100,
              'total_seller_stock': 100,
              'total_shopee_stock': 0,
            },
            'price_detail': {
              'origin_price': '1000.00',
              'promotion_price': '0.00',
            },
          },
        ],
      },
      {
        'id': 2785592182,
        'name': 'create item by shark tool_1740997689',
        'status': 1,
        'cover_image': 'sg-11134207-7req4-m292gw1makrnf5',
        'parent_sku': 'psku',
        'aggregated_price': {
          'price_min': '1000.00',
          'price_max': '1000.00',
        },
        'aggregated_stock': {
          'total_available_stock': 400,
          'total_seller_stock': 400,
          'total_shopee_stock': 0,
        },
        'modify_time': 1740997700,
        'create_time': 1740997692,
        'model_list': [
          {
            'id': 9401833291,
            'name': 'red,XL',
            'sku': 'psku',
            'tier_index': [
              0,
              1,
            ],
            'is_default': false,
            'image': '',
            'stock_detail': {
              'advanced_stock': {
                'sellable_stock': 0,
                'in_transit_stock': 0,
              },
              'total_available_stock': 100,
              'total_seller_stock': 100,
              'total_shopee_stock': 0,
            },
            'price_detail': {
              'origin_price': '1000.00',
              'promotion_price': '0.00',
            },
          },
          {
            'id': 9401833292,
            'name': 'blue,L',
            'sku': 'psku',
            'tier_index': [
              1,
              0,
            ],
            'is_default': false,
            'image': '',
            'stock_detail': {
              'advanced_stock': {
                'sellable_stock': 0,
                'in_transit_stock': 0,
              },
              'total_available_stock': 100,
              'total_seller_stock': 100,
              'total_shopee_stock': 0,
            },
            'price_detail': {
              'origin_price': '1000.00',
              'promotion_price': '0.00',
            },
          },
          {
            'id': 9401833293,
            'name': 'blue,XL',
            'sku': 'psku',
            'tier_index': [
              1,
              1,
            ],
            'is_default': false,
            'image': '',
            'stock_detail': {
              'advanced_stock': {
                'sellable_stock': 0,
                'in_transit_stock': 0,
              },
              'total_available_stock': 100,
              'total_seller_stock': 100,
              'total_shopee_stock': 0,
            },
            'price_detail': {
              'origin_price': '1000.00',
              'promotion_price': '0.00',
            },
          },
          {
            'id': 9401833294,
            'name': 'red,L',
            'sku': 'psku',
            'tier_index': [
              0,
              0,
            ],
            'is_default': false,
            'image': '',
            'stock_detail': {
              'advanced_stock': {
                'sellable_stock': 0,
                'in_transit_stock': 0,
              },
              'total_available_stock': 100,
              'total_seller_stock': 100,
              'total_shopee_stock': 0,
            },
            'price_detail': {
              'origin_price': '1000.00',
              'promotion_price': '0.00',
            },
          },
        ],
      },
      {
        'id': 2892768719,
        'name': 'wsy with mandatory attribute',
        'status': 1,
        'cover_image': 'br-11134207-7r98o-ly24est7o9qee8',
        'parent_sku': '',
        'aggregated_price': {
          'price_min': '33.00',
          'price_max': '33.00',
        },
        'aggregated_stock': {
          'total_available_stock': 2,
          'total_seller_stock': 2,
          'total_shopee_stock': 0,
        },
        'modify_time': 1721981733,
        'create_time': 1721728609,
        'model_list': [
          {
            'id': 18503788976,
            'name': '',
            'sku': '',
            'tier_index': [
              0,
            ],
            'is_default': true,
            'image': '',
            'stock_detail': {
              'advanced_stock': {
                'sellable_stock': 0,
                'in_transit_stock': 0,
              },
              'total_available_stock': 2,
              'total_seller_stock': 2,
              'total_shopee_stock': 0,
            },
            'price_detail': {
              'origin_price': '33.00',
              'promotion_price': '0.00',
            },
          },
        ],
      },
      {
        'id': 3085532779,
        'name': 'wsy with mandatory attribute',
        'status': 1,
        'cover_image': 'br-11134207-7r98o-ly24est7o9qee8',
        'parent_sku': '',
        'aggregated_price': {
          'price_min': '33.00',
          'price_max': '33.00',
        },
        'aggregated_stock': {
          'total_available_stock': 2,
          'total_seller_stock': 2,
          'total_shopee_stock': 0,
        },
        'modify_time': 1721981733,
        'create_time': 1721796107,
        'model_list': [
          {
            'id': 8803503089,
            'name': '',
            'sku': '',
            'tier_index': [
              0,
            ],
            'is_default': true,
            'image': '',
            'stock_detail': {
              'advanced_stock': {
                'sellable_stock': 0,
                'in_transit_stock': 0,
              },
              'total_available_stock': 2,
              'total_seller_stock': 2,
              'total_shopee_stock': 0,
            },
            'price_detail': {
              'origin_price': '33.00',
              'promotion_price': '0.00',
            },
          },
        ],
      },
      {
        'id': 2792768855,
        'name': 'test auto parts openapi',
        'status': 1,
        'cover_image': 'c54265d475b85e00ffb2404585e32b6f',
        'parent_sku': 'PARENT SKU',
        'aggregated_price': {
          'price_min': '2000.00',
          'price_max': '2000.00',
        },
        'aggregated_stock': {
          'total_available_stock': 1000,
          'total_seller_stock': 1000,
          'total_shopee_stock': 0,
        },
        'modify_time': 1717694308,
        'create_time': 1717644109,
        'model_list': [
          {
            'id': 9401758547,
            'name': '',
            'sku': '',
            'tier_index': [
              0,
            ],
            'is_default': true,
            'image': '',
            'stock_detail': {
              'advanced_stock': {
                'sellable_stock': 0,
                'in_transit_stock': 0,
              },
              'total_available_stock': 1000,
              'total_seller_stock': 1000,
              'total_shopee_stock': 0,
            },
            'price_detail': {
              'origin_price': '2000.00',
              'promotion_price': '0.00',
            },
          },
        ],
      },
      {
        'id': 2792768857,
        'name': 'test auto parts mass',
        'status': 1,
        'cover_image': 'br-11134207-7r98o-lw6us4oez86707',
        'parent_sku': '',
        'aggregated_price': {
          'price_min': '213.00',
          'price_max': '213.00',
        },
        'aggregated_stock': {
          'total_available_stock': 11,
          'total_seller_stock': 11,
          'total_shopee_stock': 0,
        },
        'modify_time': 1717694308,
        'create_time': 1717656295,
        'model_list': [
          {
            'id': 9401758551,
            'name': '',
            'sku': '',
            'tier_index': [
              0,
            ],
            'is_default': true,
            'image': '',
            'stock_detail': {
              'advanced_stock': {
                'sellable_stock': 0,
                'in_transit_stock': 0,
              },
              'total_available_stock': 11,
              'total_seller_stock': 11,
              'total_shopee_stock': 0,
            },
            'price_detail': {
              'origin_price': '213.00',
              'promotion_price': '0.00',
            },
          },
        ],
      },
      {
        'id': 2792768858,
        'name': 'test auto parts mass publish',
        'status': 1,
        'cover_image': 'br-11134201-7r98o-lw6uulcn7smgb3',
        'parent_sku': '',
        'aggregated_price': {
          'price_min': '213.00',
          'price_max': '213.00',
        },
        'aggregated_stock': {
          'total_available_stock': 11,
          'total_seller_stock': 11,
          'total_shopee_stock': 0,
        },
        'modify_time': 1717694308,
        'create_time': 1717656423,
        'model_list': [
          {
            'id': 9401758552,
            'name': '',
            'sku': '',
            'tier_index': [
              0,
            ],
            'is_default': true,
            'image': '',
            'stock_detail': {
              'advanced_stock': {
                'sellable_stock': 0,
                'in_transit_stock': 0,
              },
              'total_available_stock': 11,
              'total_seller_stock': 11,
              'total_shopee_stock': 0,
            },
            'price_detail': {
              'origin_price': '213.00',
              'promotion_price': '0.00',
            },
          },
        ],
      },
    ],
  },
})

export const KitConstrains = {
  'code': 0,
  'message': 'success',
  'user_message': 'success',
  'data': {
    'titleLengthMin': 5,
    'titleLengthMax': 40,
    'descriptionLengthMin': 10,
    'descriptionLengthMax': 500,
    'imageNumMin': 1,
    'imageNumMax': 12,
    'imageWidthMin': 0,
    'imageHeightMin': 0,
    'imageMandatory': true,
    'imageSizeLimit': 2,
    'imageFormat': 'jpg,jpeg,png',
    'priceMin': '0.40',
    'priceMax': '500000.00',
    'stockMin': 0,
    'stockMax': 999999,
    'inStockDts': 2,
    'oneTierOptionCountLimit': 100,
    'totalOptionCountLimit': 100,
    'totalModelCountLimit': 9,
    'variationNameLengthMin': 1,
    'variationNameLengthMax': 50,
    'variationOptionLengthMin': 1,
    'variationOptionLengthMax': 50,
    'maxPriceRatio': 2,
    'descriptionTextLengthMin': 10,
    'descriptionTextLengthMax': 500,
    'descriptionImageNumMin': 1,
    'descriptionImageNumMax': 5,
    'descriptionImageWidthMin': 1,
    'descriptionImageHeightMin': 1,
    'descriptionImageAspectRatioMin': 0.5,
    'descriptionImageAspectRatioMax': 32,
    'weightMandatory': true,
    'weightUnit': 1,
    'weightMin': 0,
    'weightMax': 100000,
    'dimensionLengthMin': 0,
    'dimensionLengthMax': 10000000,
    'dimensionWidthMin': 0,
    'dimensionWidthMax': 10000000,
    'dimensionHeightMin': 0,
    'dimensionHeightMax': 10000000,
    'parentSkuMin': 0,
    'parentSkuMax': 100,
    'skuMin': 0,
    'skuMax': 100,
    'perorderMaxpqMin': 1,
    'perorderMaxpqMax': 999999,
    'periodMaxpqMin': 1,
    'periodMaxpqMax': 999999,
    'minpqMin': 1,
    'minpqMax': 999999,
    'daysMaxpqMin': 1,
    'daysMaxpqMax': 365,
    'videoSizeLimit': 30,
    'videoFormat': 'mp4',
    'videoDurationMin': 10,
    'videoDurationMax': 60,
    'videoResolutionWidthMin': 1,
    'videoResolutionWidthMax': 1280,
    'videoResolutionHeightMin': 1,
    'videoResolutionHeightMax': 1280,
    'componentSkuNumForEachModelMin': 1,
    'componentSkuNumForEachModelMax': 30,
  },
}

export const mockDisclaimers = [
  {
    id: '1',
    name: 'ANVISA - Expiry Date',
    description: 'Check the expiration date of perishable products before consuming.',
    approval: 'anvisa_appr...',
    approvalType: 'approved',
    disclaimerStatus: 'active',
  },
  {
    id: '2',
    name: 'Baby Formula Warning',
    description: 'This product should only be administered under the supervision of a qualified he...',
    approval: 'Upload',
    approvalType: 'upload',
    disclaimerStatus: 'active',
  },
  {
    id: '3',
    name: 'Alcohol Consumption',
    description: 'The sale and consumption of alcoholic beverages are prohibited for minors. Drink...',
    approval: 'alcohol_leg...',
    approvalType: 'approved',
    disclaimerStatus: 'active',
  },
  {
    id: '4',
    name: 'Test Warning',
    description: 'Test',
    approval: 'Upload',
    approvalType: 'upload',
    disclaimerStatus: 'inactive',
  },
  {
    id: '5',
    name: 'Airsoft Safety Notice',
    description: 'This product requires emotional control and must be stored in a safe place, out ...',
    approval: 'Upload',
    approvalType: 'upload',
    disclaimerStatus: 'invalid',
  },
];
