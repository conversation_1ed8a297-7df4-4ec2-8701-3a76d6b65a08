stages:
  - release_verify  
 
 
release_verify:
  stage: release_verify
  before_script: []
  cache: {}
  dependencies: []
  script: |-
    result=`curl --request POST 'http://deploy-platform.shopee.io/apis/release/v1/gitlab/merge_request/verify' --header 'Content-Type: application/json' -d "{\"merge_request_id\": $CI_MERGE_REQUEST_IID,\"project_id\": $CI_PROJECT_ID}"`
    echo $result;
    echo $result | grep '"pass":true'
  only:
    - merge_requests