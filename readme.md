## 应用使用说明
- yarn dev
  - 本地启动开发服务
- yarn build:test
  - 以production模式编译
### lfet.json配置说明
- scanEntry
  - type: `Object`
  - 告诉脚手架按什么规则扫描pages目录
- autoGenIndex
  - type: `boolean`
  - default：`true`
  - 是否自动生成应用index.tsx文件
- isMainApp
  - type：`boolean`
  - 是否是主应用，子应用需要设置为false
  - **必填**
- `routePrefix`
  - type：`string`
  - 该子应用的路由前缀
  - **必填**
- lazyLoad
  - type：`boolean`
  - default：`false`
  - 子应用单独运行时是否使用懒加载入口js
- country
  - type：`boolean`
  - default：`true`
  - 是否关闭国家/地区选择入口
- menuMap
  - type：`Array`
  - 当前应用的菜单配置，参考[脚手架使用说明](https://confluence.shopee.io/pages/viewpage.action?pageId=712618698)
  - **必填**