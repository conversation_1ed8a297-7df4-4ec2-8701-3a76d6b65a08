{"project_dir_depth": 2, "project_name": "listingadmin", "module_name": "product", "install": {"commands": ["git rev-parse --verify HEAD > hash"]}, "build": {"docker_image": {"base_image": "harbor.shopeemobile.com/seller-center/listing-admin-cli:node18-pnpm7-cli1.0", "dependent_libraries_files": ["package.json"], "run_commands": []}, "commands": ["pnpm install", "pnpm run build:${ENV} --outDir=./dist/product"], "upload_static": {"static_dir": "./dist", "enable_cdn": true, "cdn_envs": ["live"], "exclude_patterns": ["*.map"]}, "timeout": 3600}, "deploy": {"idcs": {"live": {"sg": ["sg", "sg10"]}}}, "per_feature_branch": {"enable_container_pfb": false, "enable_static_pfb": true, "hostname_formats": ["admin.listing.${DOMAIN_ENV_FLAG}shopee.com"]}}