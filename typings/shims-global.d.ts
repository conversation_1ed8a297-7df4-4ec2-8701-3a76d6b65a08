import type { SideNavOptions } from '@classification/admin-solution';

interface SideItem {
  title: string;
  type: string;
  routeId: string;
}
interface SideNav {
  title: string;
  type: string;
  defaultOpen: boolean;
  items?: SideItem[];
}
declare global {
  interface Window {
    __SIDE_NAV__: SideNavOptions;
    __COUNTRY_ROUTE__: Record<string, string>;
    __ROUTE_MAP__: Record<string, string>;
  }
}
